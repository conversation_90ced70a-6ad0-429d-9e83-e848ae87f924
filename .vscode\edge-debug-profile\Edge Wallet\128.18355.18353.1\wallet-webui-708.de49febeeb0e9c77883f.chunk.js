"use strict";(self.webpackChunk_xpay_wallet_hub=self.webpackChunk_xpay_wallet_hub||[]).push([[708,101],{5101:(t,e,a)=>{a.d(e,{Ix:()=>w,Dh:()=>u,Ay:()=>m});const s=(t,e)=>{const{regex:a,keys:s}=(t=>{const e=[],a=t.replace(/\//g,"\\/").replace(/\*/g,".*").replace(/:(\w+)/g,((t,a)=>(e.push(a),"([^\\/]+)")));return{regex:new RegExp(`^${a}/?$`),keys:e}})(e),n=t.match(a);if(!n)return null;const r={};return s.forEach(((t,e)=>{r[t]=n[e+1]})),r};var n=Object.defineProperty,r=Object.defineProperties,h=Object.getOwnPropertyDescriptors,o=Object.getOwnPropertySymbols,i=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable,l=(t,e,a)=>e in t?n(t,e,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[e]=a,d=(t,e)=>{for(var a in e||(e={}))i.call(e,a)&&l(t,a,e[a]);if(o)for(var a of o(e))c.call(e,a)&&l(t,a,e[a]);return t},u=(t=>(t.PATH_CHANGED="cr-router-path-changed",t.QUERY_PARAMS_CHANGED="cr-router-query-params-changed",t.HASH_CHANGED="cr-router-hash-changed",t.ROUTE_CHANGED="cr-router-changed",t.CHANGE="router-all-change",t.CHANGE_REACT="react-component-path-changed",t))(u||{});class p extends EventTarget{constructor(){super(),this.path_=window.decodeURIComponent(window.location.pathname),this.query_=window.location.search.slice(1),this.hash_=window.decodeURIComponent(window.location.hash.slice(1)),this.state_={},this.dwellTime_=2e3,this.lastChangedAt_=window.performance.now()-(this.dwellTime_-200),window.addEventListener("hashchange",(()=>this.hashChanged_())),window.addEventListener("popstate",(t=>{this.state_=t.state,this.urlChanged_()}))}setDwellTime(t){this.dwellTime_=t,this.lastChangedAt_=window.performance.now()-this.dwellTime_}getPath(){return this.path_}getQueryParams(){return new URLSearchParams(this.query_)}getHash(){return this.hash_}setHash(t){this.hash_=t,this.updateState_()}setQueryParams(t){let e={};if(t instanceof URLSearchParams)this.query_=t.toString(),e=Object.fromEntries(t.entries());else{const a=new URLSearchParams(t);this.query_=a.toString(),e=Object.fromEntries(a.entries())}return e}getRealPath(t){return t?t.startsWith("/")?t:`${window.location.pathname}/${t}`:""}navigate(t,e,a){let s=null,n="";"string"==typeof t?(n=this.getRealPath(t),s={path:n,hash:"",params:{},state:e||{}}):s=t;const{path:r,params:h={},hash:o="",state:i}=s||{},c=this.setQueryParams(h);n=this.getRealPath(r),this.hash_=o,this.path_=n,this.state_=d(d(d({},this.state_),c),i),this.updateState_(a)}get eventDetail(){return{path:this.path_,state:this.state_,hash:this.hash_,query:this.query_}}hashChanged_(){const t=this.hash_;return this.hash_=window.decodeURIComponent(window.location.hash.substring(1)),this.hash_!==t&&(this.dispatchEvent(new CustomEvent("cr-router-hash-changed",{bubbles:!0,composed:!0,detail:this.eventDetail})),!0)}urlChanged_(){this.hashChanged_();const t=this.path_;this.path_=window.decodeURIComponent(window.location.pathname);const e=this.query_;this.query_=window.location.search.substring(1),t===this.path_&&e===this.query_||this.dispatchEvent(new CustomEvent("cr-router-path-changed",{bubbles:!0,composed:!0,detail:this.eventDetail}))}updateState_(t=!1){const e=new URL(window.location.origin),a=this.path_.split("/");e.pathname=a.map((t=>window.encodeURIComponent(t))).join("/"),this.query_&&(e.search=this.query_),this.hash_&&(e.hash=window.encodeURIComponent(this.hash_));const s=window.performance.now(),n=this.lastChangedAt_+this.dwellTime_>s;this.lastChangedAt_=s,n?window.history.replaceState(d({},this.state_),"",e.href):window.history.pushState(d({},this.state_),"",e.href);const r=document.getElementById("wallet-hub-content-body-web-components"),h=document.getElementById("wallet-hub-content-body-react");if(r&&h){if(t)return r.style.display="none",h.style.display="block",void window.dispatchEvent(new CustomEvent("react-component-path-changed",{bubbles:!0,composed:!0,detail:this.eventDetail}));r.style.display="block",h.style.display="none",this.dispatchEvent(new CustomEvent("cr-router-changed",{bubbles:!0,composed:!0,detail:this.eventDetail}))}}}class w extends p{constructor(){super(),this.routes=[],this.addEventListener("cr-router-path-changed",this.onRouteChanged.bind(this)),this.addEventListener("cr-router-changed",this.onRouteChanged.bind(this))}static getInstance(){return w.instance_||(w.instance_=new w),w.instance_}onRouteChanged(t){this.dispatchEvent(new CustomEvent("router-all-change",{bubbles:!0,composed:!0,detail:t.detail}))}registerRoutes(t){this.routes=this.enhanceRoutes(t)}registerFallbackHandler(t){this.fallbackComponent=t}matchRoute(t,e=this.routes){for(const a of e){const e=s(t,a.path);if(e)return{route:a,params:e,queryParams:this.getQueryParams()}}return null}enhanceRoutes(t,e="/"){let a=[];return t.forEach((t=>{const s=`${e}/${t.path}`.replace(/(\/)+/gi,"$1");var n,o;a.push((n=d({},t),o={path:s,_path:t.path,parentPath:e||"/"},r(n,h(o)))),t.children&&(a=a.concat(this.enhanceRoutes(t.children,s)))})),a}}const m=w.getInstance()},82708:(t,e,a)=>{a.r(e),a.d(e,{RouteFastElement:()=>d,RouterView:()=>p,SettingsRedirect:()=>w});var s=a(90283),n=a(88988),r=a(857),h=a(5101),o=Object.defineProperty,i=Object.getOwnPropertyDescriptor,c=(t,e,a,s)=>{for(var n,r=s>1?void 0:s?i(e,a):e,h=t.length-1;h>=0;h--)(n=t[h])&&(r=(s?n(e,a,r):n(r))||r);return s&&r&&o(e,a,r),r},l=(t,e,a)=>new Promise(((s,n)=>{var r=t=>{try{o(a.next(t))}catch(t){n(t)}},h=t=>{try{o(a.throw(t))}catch(t){n(t)}},o=t=>t.done?s(t.value):Promise.resolve(t.value).then(r,h);o((a=a.apply(t,e)).next())}));class d extends s.L{constructor(t){super(),this.routerData=t,this.router=h.Ix.getInstance()}}const u=n.qy`
  <template>
    <slot></slot>
  </template>
`;let p=class extends s.L{constructor(){super(...arguments),this.router=h.Ix.getInstance()}connectedCallback(){super.connectedCallback(),this.router.addEventListener(h.Dh.CHANGE,this.onRouteChanged.bind(this)),this.onRouteChanged()}clear(){for(;this.firstChild;)this.removeChild(this.firstChild)}onRouteChanged(t){return l(this,null,(function*(){const e=this.router.getPath(),a=this.router.matchRoute(e);let s,n={};if(t&&(n=null==t?void 0:t.detail.state),this.clear(),a){const{route:t,params:e,queryParams:r}=a;let h;t.loadData&&"function"==typeof t.loadData&&(h=yield t.loadData({params:e||{},queryParams:r,state:n})),s=yield this.getRouteFastElementInstance(t.component,{routeParams:e,queryParams:r,loadData:h})}!s&&this.router.fallbackComponent&&(s=yield this.getRouteFastElementInstance(this.router.fallbackComponent)),s&&this.appendChild(s)}))}getRouteFastElementInstance(t,e){return l(this,null,(function*(){if(t&&"function"==typeof t){const a=yield t();try{return new a(e||{})}catch(t){return null}}}))}};p=c([(0,s.E)({name:"router-view",template:u})],p);let w=class extends s.L{connectedCallback(){super.connectedCallback(),this.to&&h.Ay.navigate(this.to)}};c([r.CF],w.prototype,"to",2),w=c([(0,s.E)({name:"settings-redirect"})],w)}}]);