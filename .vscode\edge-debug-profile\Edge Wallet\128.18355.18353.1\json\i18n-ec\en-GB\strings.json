{"WalletECCashbackGiftCardOnlyRedeemFailureMessage": "An unexpected error occurred, and we couldn’t redeem a gift card.", "WalletECCashbackGiftCardOnlyRedeemFailureTitle": "We were unable to process this redemption", "WalletECCashbackRedeemGiftCardSuccessMessage": "Your cash back balance of $1 was redeemed as a gift card.", "WalletECInvalidEmailError": "Invalid email", "WalletECInvalidPhoneNumberError": "Invalid phone number", "WalletECNewCtaDescriptionCashback": "activate the cashback programme", "WalletECNewCtaDescriptionCoupon": "automatically apply coupons", "WalletECNewCtaDescriptionFillAddress": "autofill your shipping information", "WalletECNewCtaDescriptionFillDetail": "fill in your shipping and billing information", "WalletECNewCtaDescriptionFillPayment": "fill in your billing information", "WalletECNewCtaDescriptionGiftCard": "redeem gift card", "WalletECNewCtaDescriptionTakeToBNPL": "take you to $1", "WalletECNewUXErrorMessageForAutofill": "The autofill couldn't complete all your details. Just enter them manually to complete your purchase.", "WalletECNewUXErrorMessageForCashback": "The cash back couldn't be applied to your purchase right now, try again later.", "WalletECNewUXErrorMessageForCoupon": "The coupon testing isn't available right now, but you can still test coupons manually.", "WalletECNewUXForNoSuccessMessage": "Sorry, we can't help out right now", "WalletECNewUXForNoSuccessMessageOptimized": "Hmm. This isn't working, try again.", "WalletECNewUXForNoWorkingCoupon": "We didn't find any working coupons", "WalletECNewUXMultiErrorMessageForAutofill": "autofill", "WalletECNewUXMultiErrorMessageForCashback": "cash back", "WalletECNewUXMultiErrorMessageForCoupon": "coupons", "WalletECNewUXPartialSuccessMessageForAutofill": "Some of your details were filled in. Enter the remaining info to complete your purchase", "WalletECNewUXSSuccessMessageForCouponAndGiftCard": "You've already saved $1 with the best coupon and gift card", "WalletECNewUXSSuccessMessagegiftCardInfo": "You redeemed $1 Microsoft Rewards points for a $2.", "WalletECNewUXSuccessMessageForAutofill": "Your checkout details were filled in", "WalletECNewUXSuccessMessageForCashback": "You'll get up to $1 in cash back $2", "WalletECNewUXSuccessMessageForCoupon": "You've already saved $1 with the best coupon", "WalletECNewUXSuccessMessageForGiftCard": "You've already saved $1 with gift card", "WalletECNewUXSuccessMessageWithPlusForCashback": "Plus, you'll get up to $1 in cash back $2", "WalletECRequiredFieldMissingError": "Required", "WalletECSaveAddressError": "Failed to save address", "WalletECSaveCardError": "Failed to save card", "WalletECSimplifyAutofillSubTitle": "Turn on to enable autofill", "WalletECSimplifyAutofillTitle": "Autofill checkout details", "WalletECSimplifyCardBillingAddress": "Billing address", "WalletECSimplifyCardCVV": "CVV", "WalletECSimplifyCardExpiration": "Expiration", "WalletECSimplifyCardName": "Name on card", "WalletECSimplifyCardNickNumber": "Nickname", "WalletECSimplifyCardNumber": "Card number", "WalletECSimplifyDrawerCountryLabel": "Country/Reigon", "WalletECSimplifyDrawerEmailLabel": "Email", "WalletECSimplifyDrawerFirstNameLabel": "First name", "WalletECSimplifyDrawerLastNameLabel": "Surname", "WalletECSimplifyDrawerPhoneLabel": "Phone number", "WalletECSimplifyDrawerPrimaryTitle": "Save", "WalletECSimplifyDrawerSubTitle": "Cancel", "WalletECSimplifyEditCreditCardTitle": "Add a new card", "WalletECSimplifyEditNewAddressTitle": "Add a new address", "WalletECUXSimplifyCtaDescriptionSaving": "apply savings", "wallecECNewCTAButtonLabelAutofillAll": "Autofill all", "wallecECTimeSavingDescription": "Estimated to save you $1", "walletBNPLSmallCardBottomMessagePayInFour": "$1 also offers monthly payments with interest.", "walletBNPLSmallCardBottomMessageZip": "Zip also offers monthly payments with interest.", "walletBNPLSmallCardFooterMessage": "Microsoft does not charge you or get paid by these providers.", "walletBNPLSmallCardTitle": "Purchase will be split into 4 instalments", "walletBNPLSmallCardTitleBothFinancedAndPayInFour": "Purchase can be split into 4 instalments", "walletChangeYourSettings": "change your settings", "walletDrawerFormPageMicroFeedbackPrompt": "Satisfied with <PERSON><PERSON>?", "walletDrawerGiftCardAuofillMultipleFailureTitle": "Auto apply your gift card to this order. Check your email for the gift card number, then enter it manually to use it.", "walletDrawerGiftCardAutofillFailureTitle": "We couldn't automatically apply your gift card to this order. Check your email for the gift card number, then enter it manually to use it.", "walletDrawerGiftCardRedeemFailureTitle": "We couldn't redeem your <PERSON><PERSON><PERSON> points as a gift card. You can continue with the order or try the transaction again later.", "walletDrawerGiftCardRedeemMultipleFailureTitle": "Redeem your Re<PERSON><PERSON> points as a gift card.", "walletECAllFailureMessageBottom": "Don't worry, you can still continue your transaction.", "walletECAllFailureTitle": "The following features aren't available right now:", "walletECApplyCashBackTitle": "Cash back", "walletECApplyCashCackTitle": "You will get up to $1 in cash back.", "walletECApplyGiftCardTitle": "Apply gift cards", "walletECApplySavingTitle": "You'll save $1 and will get up to $2 in cash back.", "walletECAutoApplyApplyingBestCouponsNew": "Applying the best coupon we found...", "walletECAutoApplyFailureTitle": "Coupon testing isn't available right now, but you can still test coupons manually.", "walletECAutoApplyFeature": "Test coupons", "walletECAutoApplyTryingCoupons": "Trying $1...", "walletECAutoFillTitleTemplate": "Autofill $1 details", "walletECAutofillErrorMessage": "Autofill details", "walletECAutofillPartialSuccessMessageTemplate": "Some of your details were filled in. Enter the remaining info to complete your $1", "walletECAutofillPaymentOnlySingleErrorMessage": "Autofill couldn't complete all your details. Enter them manually to continue your transaction.", "walletECAutofillPaymentOnlySuccessMessage": "We automatically filled your payment details for a quick checkout.", "walletECAutofillSuccessMessageTemplate": "Your $1 details were filled in", "walletECAutofillToggleTurnOffText": "Turn off autofill", "walletECAutofillToggleTurnOnText": "Turn on autofill", "walletECBookingSiteTypeLable": "booking", "walletECBrandFooter": "Powered by Microsoft Wallet", "walletECCTAButtonDescriptionDismissLabel": "There are no available forms to autofill right now.", "walletECCTAButtonDescriptionProcessingLabel": "Processing", "walletECCTAButtonDismissLabel": "<PERSON><PERSON><PERSON>", "walletECCancelCouponButton": "Skip coupons", "walletECCancelCouponFooter": "45% of users saved with coupons", "walletECCancelCouponStatus": "Coupons applying cancelled", "walletECCashBackAndCouponTitle": "Cash back and coupons", "walletECCashBackDisplayValue": "Up to $1", "walletECCashBackEnableErrorMessage": "Cashback couldn't be applied to your purchase right now. Try again later.", "walletECCashBackEnableErrorMessageTemplate": "The cash back couldn't be applied to your $1 right now, try again later.", "walletECCashBackEnableSubErrorMessage": "Activate cashback", "walletECCashBackInfo": "You'll receive email updates and track cash back detail on $1.", "walletECCashBackOptionText": "Get cash back", "walletECCashBackUpto": "Up to $1", "walletECCashbackActivateFailedLabel": "We couldn't activate cash back for this purchase, try again later.", "walletECCashbackActivateLabel": "Activate cash back", "walletECCashbackEnrollDisclaimer": "You will be signed in to Microsoft Edge which will back up your browsing data and let you see your favourites, passwords, history and more on all your devices. You will also join and receive emails about $1, which include offers about partner products. $2 | $3", "walletECCashbackGiftCardOnlyAutofillFailureCopyMessage": "Copy and paste this gift card code during checkout.", "walletECCashbackGiftCardOnlyTitle": "Save $1 on your $2’s order by redeeming your Microsoft Cashback balance as a gift card.", "walletECCashbackGiftCardSubTitle": "Balance applied as a gift card.", "walletECCashbackGiftCardTitle": "Apply $1 in cash back balance", "walletECCashbackLinkLabel": "See offer details on $1", "walletECCashbackPrivacyLinkLabel": "Privacy & Cookies", "walletECCashbackSigninLabel": "Join now and activate cash back", "walletECCashbackStatus": "Cash back activated", "walletECCashbackTermsLinkLabel": "Terms", "walletECContinueSavingsAndFillDetailsButtonLabel": "Apply savings and continue", "walletECContinueSavingsAndFillDetailsLabel": "By continuing, we'll activate the cashback programme, automatically apply coupons, and fill in your shipping and billing information.", "walletECContinueSavingsAndFillDetailsLabelBNPL": "By continuing, we'll activate the cashback programme, automatically apply coupons, and autofill your shipping and take you to $1", "walletECContinueSavingsAndFillDetailsNoCouponLabel": "By continuing, we'll activate the cashback programme and fill in your shipping and billing information.", "walletECContinueSavingsAndFillDetailsNoCouponLabelBNPL": "By continuing, we'll activate the cashback programme and autofill your shipping and take you to $1", "walletECContinueSavingsButtonLabel": "Apply savings", "walletECContinueSavingsLabel": "By continuing, we'll activate the cashback programme and automatically apply coupons.", "walletECContinueSavingsNoCouponLabel": "By continuing, we'll activate the cashback programme.", "walletECContinueToLable": "Continue to $1", "walletECCopyButtonCopied": "Copied!", "walletECCouponAverageSaving": "On average saved $1", "walletECCouponOptionText": "Use coupon", "walletECCouponSkipAndNoSavingMessage": "Try more coupons for savings", "walletECCouponSkipAndNoSavingTitle": "You may have skipped valid coupons.", "walletECCouponsCookieDisclaimer": "I allow placing of $1 on my device to help billing with the coupon provider as per $2", "walletECCouponsCookieDisclaimerPrivacyLinkLabel": "Microsoft Privacy Statement", "walletECDismiss": "<PERSON><PERSON><PERSON>", "walletECDropDownButtonLabel": "Change", "walletECEnjoyMessage": "Enjoy the rest of your shopping journey.", "walletECFillDetailsButtonLabelUXSimplify": "Fill details", "walletECFreBannerContent": "Find the best deals with coupons and cash back, all before you pay.", "walletECFreBannerTitle": "Save money and time with <PERSON><PERSON> in Microsoft Edge", "walletECGetCashbackLabel": "You can get up to $1 cashback on $2", "walletECGiftCardCodeTitle": "Gift card code", "walletECGiftCardErrorMessageTemplate": "Copy gift card details below to paste during $1.", "walletECGiftCardRedemptionHistoryLink": "Gift card redemption history", "walletECGiftCardTerms": "Terms", "walletECGiftCardTitle": "Gift card", "walletECHumanInteractionContentForA": "Please fill in required fields on the merchant site to continue", "walletECHumanInteractionContentForB": "There are still required field that need to be filled on the merchant site.", "walletECHumanInteractionErrorMessage": "Complete any missing details to continue", "walletECHumanInteractionTitleForA": "Complete details", "walletECHumanInteractionTitleForB": "More details required", "walletECManagePersonalInfo": "Manage personal info", "walletECManagePersonalInfoNewVersion": "Add new address", "walletECMaskedCardDetailFetching": "Enter your CVC to continue", "walletECMaskedFetchErrorMessage": "Try again and enter your CVC when prompted. Or, select another payment method to continue.", "walletECMoneySavingAnd": "and", "walletECMultipleErrorMessageTemplate": "The autofill couldn't complete all your details. Just enter them manually to complete your $1.", "walletECMultipleErrorMessageTemplateFive": "The $1, $2, $3, $4 and $5 aren't available right now. Don't worry, you can still complete your $6.", "walletECMultipleErrorMessageTemplateFour": "The $1, $2, $3 and $4 aren't available right now. Don't worry, you can still complete your $5.", "walletECMultipleErrorMessageTemplateThree": "The $1, $2 and $3 aren't available right now. Don't worry, you can still complete your $4.", "walletECMultipleErrorMessageTemplateTwo": "The $1 and $2 aren't available right now. Don't worry, you can still complete your $3.", "walletECNewCtaDescriptionTemplateFive": "By continuing, we'll $1, $2, $3, $4 and $5.", "walletECNewCtaDescriptionTemplateFour": "By continuing, we'll $1, $2, $3 and $4.", "walletECNewCtaDescriptionTemplateOne": "By continuing, we'll $1.", "walletECNewCtaDescriptionTemplateThree": "By continuing, we'll $1, $2 and $3.", "walletECNewCtaDescriptionTemplateTwo": "By continuing, we'll $1 and $2.", "walletECNewPayment": "I'd like to use new payment", "walletECNewPersonalInfo": "I'd like to use new personal information", "walletECNewShippingAddressTitle": "Shipping details", "walletECNewUXErrorMessageTemplateFive": "The $1, $2, $3, $4 and $5 aren't available right now. Don't worry, you can still complete your transaction.", "walletECNewUXErrorMessageTemplateFour": "The $1, $2, $3 and $4 aren't available right now. Don't worry, you can still complete your transaction.", "walletECNewUXErrorMessageTemplateThree": "The $1, $2 and $3 aren't available right now. Don't worry, you can still complete your transaction.", "walletECNewUXErrorMessageTemplateTwo": "The $1 and $2 aren't available right now. Don't worry, you can still complete your transaction.", "walletECNewUXSSuccessMessageForCouponAndCashbackGiftCard": "You saved $1 with coupon $2 and your cash back balance!", "walletECNewUXSSuccessMessageForCouponAndGiftCardOptimized": "You saved $1 with the best coupon $2 and gift card!", "walletECNewUXSSuccessMessageForCouponAndRewards": "You saved $1 with coupon $2 and Microsoft Rewards!", "walletECNewUXSSuccessMessageForCouponSkipButSucceedAndCashbackGiftCard": "You saved $1 with coupon $2 and your cash back balance, but you could save more if you try more coupons.", "walletECNewUXSSuccessMessageForCouponSkipButSucceedAndGiftCard": "You saved $1 with the best coupon $2 and gift card, but you could save more if you try more coupons.", "walletECNewUXSSuccessMessageForCouponSkipButSucceedAndRewards": "You saved $1 with coupon $2 and Microsoft Rewards, but you could save more if you try more coupons.", "walletECNewUXSuccessMessageForCashbackGiftCard": "You saved $1 with your cash back balance!", "walletECNewUXSuccessMessageForCashbackGiftCardOnly": "Success! You’ve redeemed a $1 $2’s gift card.", "walletECNewUXSuccessMessageForCouponOptimized": "You saved $1 with the best coupon, $2!", "walletECNewUXSuccessMessageForCouponSkipButSucceed": "You saved $1 using $2 but you could save more if you try more coupons.", "walletECNewUXSuccessMessageForGiftCardOptimized": "You saved $1 with gift card!", "walletECNewUXSuccessMessageForRewards": "You saved $1 with Microsoft Rewards!", "walletECNoFormToastLable": "No available forms to autofill", "walletECNoSaveInfo": "I don't want to save my information under Microsoft account", "walletECNoSavedAddress": "No Saved Address", "walletECNoSavedName": "No Saved Name", "walletECNoSavings": "It doesn't have any money savings", "walletECPersonalInfoTitle": "Personal info", "walletECProceedToBestPriceLabel": "Proceed to Best Price", "walletECProtectYourInfo": "Your information is protected by Microsoft security", "walletECRebatesCouponsSelectorDescription": "We've applied the best savings for you and found that you can use only one for this purchase. Select one of the below to continue.", "walletECRebatesCouponsSelectorTitle": "Choose one to continue", "walletECRefreshingLable": "Refreshing...", "walletECReturnToCheckoutButtonAria": "Close the pane and return to checkout page", "walletECReturnToLable": "Return to $1", "walletECReview": "Review", "walletECReviewDetailsLable": "Review $1 details", "walletECRewardsAutofillFailureCopyMessage": "Copy gift card details below to paste during checkout.", "walletECRewardsAutofillFailureMessage": "You redeemed $1 Microsoft Rewards points for a $2.", "walletECRewardsCheckboxAriaLabel": "Enable gift cards", "********************************": "Copy gift card", "walletECRewardsCopyPinTitle": "<PERSON><PERSON>", "walletECRewardsGiftCardSubtitlePoints": "$1 points applied as gift card. $2", "walletECRewardsGiftCardTitle": "Apply $1 in Microsoft Rewards", "walletECRewardsGiftcardDropdownAriaLabel": "Select a gift card", "walletECRewardsPinTitle": "<PERSON>n", "walletECRewardsPointsAndAvailableRewardsPoints": "$1/$2 points", "walletECRewardsPointsAndAvailableRewardsPointsNew": "Redeem $1 / $2 Microsoft Rewards points for a gift card. $3", "walletECRewardsRedeemMessage": "You redeemed $1 Microsoft Rewards points for a $2 gift card.", "walletECRewardsRedeemOptionPoints": "$1 pts", "walletECRewardsRedeemOptionsLabel": "Get cards available to redeem", "walletECRewardsRedeemOtherFailureMessage": "Due to an unexpected error, we couldn't redeem your gift card. Try again by visiting $1", "walletECRewardsRedeemRiskReviewFailureMessage": "Your Microsoft Rewards redemption is processing and may take up to 24 hours to complete. Check your $1 for updates.", "walletECRewardsRedeemRiskReviewFailureMessageLink": "Microsoft Rewards order history", "walletECRewardsRedeemRiskReviewFailureTitle": "Microsoft Rewards processing", "walletECRewardsRedemptionHistoryLink": "Rewards redemption history", "walletECRewardsTermsApply": "Terms apply", "walletECRewardsTermsDialogTitle": "Terms and conditions", "walletECRewardsTitle": "Gift cards", "walletECSavingsButtonLabelUXSimplify": "Apply savings and fill details", "walletECSavingsDisableMessage": "$1 to activate the cashback and apply coupons automatically. This can save you time and money when you shop on Edge.", "walletECShoppingInfo": "You're using Microsoft shopping cash back and coupons.", "walletECShoppingInfoForCoupon": "and coupons", "walletECShoppingInfoNew": "You're using Microsoft shopping cash back$1.", "walletECShoppingSitePaymentLable": "payment", "walletECShoppingSiteTypeLable": "checkout", "walletECSkipCouponProgressStatus": "Skipping coupons...", "walletECThirdPartyCookies": "third-party cookies", "walletECTimeSavingSeconds": "$1 seconds", "walletECTokenizationCardDetailFetching": "We are connecting with your bank to generate card details for this transaction. This may take a few seconds.", "walletECTokenizationFetchErrorMessage": "Unable to generate virtual card details, please try again or choose another card.", "walletECTravelSiteTypeLable": "reservation", "walletECUXAffirmDescription": "Split payments over 8 weeks", "walletECUXAffirmDescriptionFinanced": "Buy now, pay over time with Affirm.", "walletECUXBNPLDropdownTitle": "Pay over time", "walletECUXCardKlarnaDescription": "No interest fees, late fees may apply", "walletECUXKlarnaDescription": "Shop now. Pay over time with <PERSON><PERSON><PERSON>.", "walletECUXManagePaymentButtonTitle": "Manage payment methods", "walletECUXNewAddressDropdownLabel": "Select shipping address", "walletECUXNewOnlyCardDropdownTitle": "Pay by card", "walletECUXNewPaymentDropdownLabel": "Select payment method", "walletECUXNewPaymentDropdownTitle": "Pay by card or pay over time", "walletECUXPersonalInfoLabel": "Select personal info", "walletECUXRebatesOnlyTreatmentADescription": "Earn up to $1 cash back on your purchases. Any cash back you earn can be transferred to PayPal.", "walletECUXRebatesOnlyTreatmentCMerchantName": "on $1", "walletECUXRebatesOnlyTreatmentCTitle": "Get up to $1", "walletECUXSBillingTheSameWithShipAddress": "Billing address is the same as shipping address", "walletECUXSInEligibleCardLogoContent": "This website doesn't support virtual card", "walletECUXSTokenCardLogoContent": "Virtual card number •••• $1 activated to protect your payment information", "walletECUXSmallCardAffirmDescription": "No late fees, no interest", "walletECUXSmallCardZipDescription": "No impact on credit score, late fees may apply", "walletECUXZipDescription": "Split payments over 6 weeks", "walletECUXsimplifyBGAANegativeContent": "Wallet tested all coupons but didn't find any savings", "walletECUXsimplifyBGAANegativeTitle": "You have the best price", "walletECUXsimplifyBGAAPostiveCouponContent": "Wallet tested all available coupons", "walletECUXsimplifyBGAAPostiveTitle": "You're saving $1", "walletECUXsimplifyCashbackEnrollText": "Get up to $1 with $2", "walletECUXsimplifyCashbackValue": "Up to $1", "walletECUXsimplifyCouponEstimatedTime": "est. $1", "walletECUXsimplifyCouponTitleImprovement": "Test $1", "walletECUXsimplifyMutipleCoupon": "$1 coupons", "walletECUXsimplifyOneCoupon": "1 coupon", "walletECUXsimplifySavingTitle": "Apply savings", "walletECUXsimplifyShoppingToggleOffCashback": "Activate Microsoft Cashback", "walletECUXsimplifyShoppingToggleOffCashbackAndCoupon": "Activate Microsoft Cashback and apply coupons automatically", "walletECUXsimplifyShoppingToggleOffContentLabel": "$1 to save time and money when you shop online.", "walletECUXsimplifyShoppingToggleOffCoupon": "Apply coupons automatically", "walletECUXsimplifyShoppingToggleOffTitle": "Turn on shopping in Microsoft Edge", "walletECUXsimplifyStandardCouponContent": "Average savings: $1 on $2", "walletECUXsimplifyStandardCouponContentForBestOffer": "Wallet will find the best offer for you", "walletECUXsimplifyStandardCouponTitle": "$1 will be tested", "walletECWrongInfo": "The saved information is wrong or out of date", "walletProceedAndReviewButtonLabel": "Proceed and review", "walletSavingOnlyOCVAutofillFailure": "Gift card autofill failed", "walletSavingOnlyOCVNotRedeem": "I don’t want to redeem Microsoft Cashback as a gift card", "walletSavingOnlyOCVNotTrust": "I have security concerns about gift card redemption", "walletSavingOnlyOCVNotUnderstand": "I don’t understand how it works", "walletSavingOnlyOCVOther": "Other", "walletSavingOnlyOCVRedeemFailure": "Gift card redemption didn’t work", "walletSavingOnlyOCVRedemptionAmount": "Redemption amount is incorrect", "walletSavingOnlyOCVRedemptionSlow": "Redemption took too long", "walletSelectString": "$1 or $2", "walletUXRemainingTime": "Estimated time: $1", "walletUXRemainingTimeDefault": "The process will complete soon", "walletUXTimeInMinutes": "$1 minutes", "walletUXTimeInSeconds": "$1 seconds"}