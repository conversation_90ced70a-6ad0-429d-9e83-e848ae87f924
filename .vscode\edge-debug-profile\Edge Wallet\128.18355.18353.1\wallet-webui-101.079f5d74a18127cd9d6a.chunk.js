"use strict";(self.webpackChunk_xpay_wallet_hub=self.webpackChunk_xpay_wallet_hub||[]).push([[101],{5101:(t,e,s)=>{s.d(e,{Ix:()=>w,Dh:()=>u,Ay:()=>_});const a=(t,e)=>{const{regex:s,keys:a}=(t=>{const e=[],s=t.replace(/\//g,"\\/").replace(/\*/g,".*").replace(/:(\w+)/g,((t,s)=>(e.push(s),"([^\\/]+)")));return{regex:new RegExp(`^${s}/?$`),keys:e}})(e),n=t.match(s);if(!n)return null;const h={};return a.forEach(((t,e)=>{h[t]=n[e+1]})),h};var n=Object.defineProperty,h=Object.defineProperties,r=Object.getOwnPropertyDescriptors,i=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable,d=(t,e,s)=>e in t?n(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s,l=(t,e)=>{for(var s in e||(e={}))o.call(e,s)&&d(t,s,e[s]);if(i)for(var s of i(e))c.call(e,s)&&d(t,s,e[s]);return t},u=(t=>(t.PATH_CHANGED="cr-router-path-changed",t.QUERY_PARAMS_CHANGED="cr-router-query-params-changed",t.HASH_CHANGED="cr-router-hash-changed",t.ROUTE_CHANGED="cr-router-changed",t.CHANGE="router-all-change",t.CHANGE_REACT="react-component-path-changed",t))(u||{});class p extends EventTarget{constructor(){super(),this.path_=window.decodeURIComponent(window.location.pathname),this.query_=window.location.search.slice(1),this.hash_=window.decodeURIComponent(window.location.hash.slice(1)),this.state_={},this.dwellTime_=2e3,this.lastChangedAt_=window.performance.now()-(this.dwellTime_-200),window.addEventListener("hashchange",(()=>this.hashChanged_())),window.addEventListener("popstate",(t=>{this.state_=t.state,this.urlChanged_()}))}setDwellTime(t){this.dwellTime_=t,this.lastChangedAt_=window.performance.now()-this.dwellTime_}getPath(){return this.path_}getQueryParams(){return new URLSearchParams(this.query_)}getHash(){return this.hash_}setHash(t){this.hash_=t,this.updateState_()}setQueryParams(t){let e={};if(t instanceof URLSearchParams)this.query_=t.toString(),e=Object.fromEntries(t.entries());else{const s=new URLSearchParams(t);this.query_=s.toString(),e=Object.fromEntries(s.entries())}return e}getRealPath(t){return t?t.startsWith("/")?t:`${window.location.pathname}/${t}`:""}navigate(t,e,s){let a=null,n="";"string"==typeof t?(n=this.getRealPath(t),a={path:n,hash:"",params:{},state:e||{}}):a=t;const{path:h,params:r={},hash:i="",state:o}=a||{},c=this.setQueryParams(r);n=this.getRealPath(h),this.hash_=i,this.path_=n,this.state_=l(l(l({},this.state_),c),o),this.updateState_(s)}get eventDetail(){return{path:this.path_,state:this.state_,hash:this.hash_,query:this.query_}}hashChanged_(){const t=this.hash_;return this.hash_=window.decodeURIComponent(window.location.hash.substring(1)),this.hash_!==t&&(this.dispatchEvent(new CustomEvent("cr-router-hash-changed",{bubbles:!0,composed:!0,detail:this.eventDetail})),!0)}urlChanged_(){this.hashChanged_();const t=this.path_;this.path_=window.decodeURIComponent(window.location.pathname);const e=this.query_;this.query_=window.location.search.substring(1),t===this.path_&&e===this.query_||this.dispatchEvent(new CustomEvent("cr-router-path-changed",{bubbles:!0,composed:!0,detail:this.eventDetail}))}updateState_(t=!1){const e=new URL(window.location.origin),s=this.path_.split("/");e.pathname=s.map((t=>window.encodeURIComponent(t))).join("/"),this.query_&&(e.search=this.query_),this.hash_&&(e.hash=window.encodeURIComponent(this.hash_));const a=window.performance.now(),n=this.lastChangedAt_+this.dwellTime_>a;this.lastChangedAt_=a,n?window.history.replaceState(l({},this.state_),"",e.href):window.history.pushState(l({},this.state_),"",e.href);const h=document.getElementById("wallet-hub-content-body-web-components"),r=document.getElementById("wallet-hub-content-body-react");if(h&&r){if(t)return h.style.display="none",r.style.display="block",void window.dispatchEvent(new CustomEvent("react-component-path-changed",{bubbles:!0,composed:!0,detail:this.eventDetail}));h.style.display="block",r.style.display="none",this.dispatchEvent(new CustomEvent("cr-router-changed",{bubbles:!0,composed:!0,detail:this.eventDetail}))}}}class w extends p{constructor(){super(),this.routes=[],this.addEventListener("cr-router-path-changed",this.onRouteChanged.bind(this)),this.addEventListener("cr-router-changed",this.onRouteChanged.bind(this))}static getInstance(){return w.instance_||(w.instance_=new w),w.instance_}onRouteChanged(t){this.dispatchEvent(new CustomEvent("router-all-change",{bubbles:!0,composed:!0,detail:t.detail}))}registerRoutes(t){this.routes=this.enhanceRoutes(t)}registerFallbackHandler(t){this.fallbackComponent=t}matchRoute(t,e=this.routes){for(const s of e){const e=a(t,s.path);if(e)return{route:s,params:e,queryParams:this.getQueryParams()}}return null}enhanceRoutes(t,e="/"){let s=[];return t.forEach((t=>{const a=`${e}/${t.path}`.replace(/(\/)+/gi,"$1");var n,i;s.push((n=l({},t),i={path:a,_path:t.path,parentPath:e||"/"},h(n,r(i)))),t.children&&(s=s.concat(this.enhanceRoutes(t.children,a)))})),s}}const _=w.getInstance()}}]);