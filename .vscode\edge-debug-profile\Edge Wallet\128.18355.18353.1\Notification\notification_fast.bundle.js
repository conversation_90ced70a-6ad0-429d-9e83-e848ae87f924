/*! For license information please see notification_fast.bundle.js.LICENSE.txt */
(()=>{"use strict";var e,t,o={5236:(e,t)=>{if("function"==typeof Symbol&&Symbol.for){var o=Symbol.for;o("react.element"),o("react.portal"),o("react.fragment"),o("react.strict_mode"),o("react.profiler"),o("react.provider"),o("react.context"),o("react.forward_ref"),o("react.suspense"),o("react.suspense_list"),o("react.memo"),o("react.lazy"),o("react.block"),o("react.server.block"),o("react.fundamental"),o("react.debug_trace_mode"),o("react.legacy_hidden")}},5732:(e,t,o)=>{o(5236)},9533:e=>{var t=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,i=Object.prototype.propertyIsEnumerable;function n(e){if(null==e)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(e)}e.exports=function(){try{if(!Object.assign)return!1;var e=new String("abc");if(e[5]="de","5"===Object.getOwnPropertyNames(e)[0])return!1;for(var t={},o=0;o<10;o++)t["_"+String.fromCharCode(o)]=o;if("**********"!==Object.getOwnPropertyNames(t).map((function(e){return t[e]})).join(""))return!1;var i={};return"abcdefghijklmnopqrst".split("").forEach((function(e){i[e]=e})),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},i)).join("")}catch(e){return!1}}()?Object.assign:function(e,r){for(var a,s,l=n(e),c=1;c<arguments.length;c++){for(var d in a=Object(arguments[c]))o.call(a,d)&&(l[d]=a[d]);if(t){s=t(a);for(var u=0;u<s.length;u++)i.call(a,s[u])&&(l[s[u]]=a[s[u]])}}return l}},2873:(e,t,o)=>{var i=o(274),n=60103;if(t.Fragment=60107,"function"==typeof Symbol&&Symbol.for){var r=Symbol.for;n=r("react.element"),t.Fragment=r("react.fragment")}var a=i.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s=Object.prototype.hasOwnProperty,l={key:!0,ref:!0,__self:!0,__source:!0};function c(e,t,o){var i,r={},c=null,d=null;for(i in void 0!==o&&(c=""+o),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(d=t.ref),t)s.call(t,i)&&!l.hasOwnProperty(i)&&(r[i]=t[i]);if(e&&e.defaultProps)for(i in t=e.defaultProps)void 0===r[i]&&(r[i]=t[i]);return{$$typeof:n,type:e,key:c,ref:d,props:r,_owner:a.current}}t.jsx=c,t.jsxs=c},7493:(e,t,o)=>{var i=o(9533),n="function"==typeof Symbol&&Symbol.for,r=n?Symbol.for("react.element"):60103,a=n?Symbol.for("react.portal"):60106,s=n?Symbol.for("react.fragment"):60107,l=n?Symbol.for("react.strict_mode"):60108,c=n?Symbol.for("react.profiler"):60114,d=n?Symbol.for("react.provider"):60109,u=n?Symbol.for("react.context"):60110,p=n?Symbol.for("react.forward_ref"):60112,h=n?Symbol.for("react.suspense"):60113,f=n?Symbol.for("react.memo"):60115,g=n?Symbol.for("react.lazy"):60116,E="function"==typeof Symbol&&Symbol.iterator;function T(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,o=1;o<arguments.length;o++)t+="&args[]="+encodeURIComponent(arguments[o]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var N={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m={};function _(e,t,o){this.props=e,this.context=t,this.refs=m,this.updater=o||N}function O(){}function A(e,t,o){this.props=e,this.context=t,this.refs=m,this.updater=o||N}_.prototype.isReactComponent={},_.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error(T(85));this.updater.enqueueSetState(this,e,t,"setState")},_.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},O.prototype=_.prototype;var I=A.prototype=new O;I.constructor=A,i(I,_.prototype),I.isPureReactComponent=!0;var b={current:null},S=Object.prototype.hasOwnProperty,R={key:!0,ref:!0,__self:!0,__source:!0};function y(e,t,o){var i,n={},a=null,s=null;if(null!=t)for(i in void 0!==t.ref&&(s=t.ref),void 0!==t.key&&(a=""+t.key),t)S.call(t,i)&&!R.hasOwnProperty(i)&&(n[i]=t[i]);var l=arguments.length-2;if(1===l)n.children=o;else if(1<l){for(var c=Array(l),d=0;d<l;d++)c[d]=arguments[d+2];n.children=c}if(e&&e.defaultProps)for(i in l=e.defaultProps)void 0===n[i]&&(n[i]=l[i]);return{$$typeof:r,type:e,key:a,ref:s,props:n,_owner:b.current}}function v(e){return"object"==typeof e&&null!==e&&e.$$typeof===r}var C=/\/+/g,w=[];function M(e,t,o,i){if(w.length){var n=w.pop();return n.result=e,n.keyPrefix=t,n.func=o,n.context=i,n.count=0,n}return{result:e,keyPrefix:t,func:o,context:i,count:0}}function D(e){e.result=null,e.keyPrefix=null,e.func=null,e.context=null,e.count=0,10>w.length&&w.push(e)}function P(e,t,o,i){var n=typeof e;"undefined"!==n&&"boolean"!==n||(e=null);var s=!1;if(null===e)s=!0;else switch(n){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case r:case a:s=!0}}if(s)return o(i,e,""===t?"."+k(e,0):t),1;if(s=0,t=""===t?".":t+":",Array.isArray(e))for(var l=0;l<e.length;l++){var c=t+k(n=e[l],l);s+=P(n,c,o,i)}else if("function"==typeof(c=null===e||"object"!=typeof e?null:"function"==typeof(c=E&&e[E]||e["@@iterator"])?c:null))for(e=c.call(e),l=0;!(n=e.next()).done;)s+=P(n=n.value,c=t+k(n,l++),o,i);else if("object"===n)throw o=""+e,Error(T(31,"[object Object]"===o?"object with keys {"+Object.keys(e).join(", ")+"}":o,""));return s}function x(e,t,o){return null==e?0:P(e,"",t,o)}function k(e,t){return"object"==typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+(""+e).replace(/[=:]/g,(function(e){return t[e]}))}(e.key):t.toString(36)}function L(e,t){e.func.call(e.context,t,e.count++)}function F(e,t,o){var i=e.result,n=e.keyPrefix;e=e.func.call(e.context,t,e.count++),Array.isArray(e)?G(e,i,o,(function(e){return e})):null!=e&&(v(e)&&(e=function(e,t){return{$$typeof:r,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(e,n+(!e.key||t&&t.key===e.key?"":(""+e.key).replace(C,"$&/")+"/")+o)),i.push(e))}function G(e,t,o,i,n){var r="";null!=o&&(r=(""+o).replace(C,"$&/")+"/"),x(e,F,t=M(t,r,i,n)),D(t)}var B={current:null};function W(){var e=B.current;if(null===e)throw Error(T(321));return e}var U={ReactCurrentDispatcher:B,ReactCurrentBatchConfig:{suspense:null},ReactCurrentOwner:b,IsSomeRendererActing:{current:!1},assign:i};t.Children={map:function(e,t,o){if(null==e)return e;var i=[];return G(e,i,null,t,o),i},forEach:function(e,t,o){if(null==e)return e;x(e,L,t=M(null,null,t,o)),D(t)},count:function(e){return x(e,(function(){return null}),null)},toArray:function(e){var t=[];return G(e,t,null,(function(e){return e})),t},only:function(e){if(!v(e))throw Error(T(143));return e}},t.Component=_,t.Fragment=s,t.Profiler=c,t.PureComponent=A,t.StrictMode=l,t.Suspense=h,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=U,t.cloneElement=function(e,t,o){if(null==e)throw Error(T(267,e));var n=i({},e.props),a=e.key,s=e.ref,l=e._owner;if(null!=t){if(void 0!==t.ref&&(s=t.ref,l=b.current),void 0!==t.key&&(a=""+t.key),e.type&&e.type.defaultProps)var c=e.type.defaultProps;for(d in t)S.call(t,d)&&!R.hasOwnProperty(d)&&(n[d]=void 0===t[d]&&void 0!==c?c[d]:t[d])}var d=arguments.length-2;if(1===d)n.children=o;else if(1<d){c=Array(d);for(var u=0;u<d;u++)c[u]=arguments[u+2];n.children=c}return{$$typeof:r,type:e.type,key:a,ref:s,props:n,_owner:l}},t.createContext=function(e,t){return void 0===t&&(t=null),(e={$$typeof:u,_calculateChangedBits:t,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider={$$typeof:d,_context:e},e.Consumer=e},t.createElement=y,t.createFactory=function(e){var t=y.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:p,render:e}},t.isValidElement=v,t.lazy=function(e){return{$$typeof:g,_ctor:e,_status:-1,_result:null}},t.memo=function(e,t){return{$$typeof:f,type:e,compare:void 0===t?null:t}},t.useCallback=function(e,t){return W().useCallback(e,t)},t.useContext=function(e,t){return W().useContext(e,t)},t.useDebugValue=function(){},t.useEffect=function(e,t){return W().useEffect(e,t)},t.useImperativeHandle=function(e,t,o){return W().useImperativeHandle(e,t,o)},t.useLayoutEffect=function(e,t){return W().useLayoutEffect(e,t)},t.useMemo=function(e,t){return W().useMemo(e,t)},t.useReducer=function(e,t,o){return W().useReducer(e,t,o)},t.useRef=function(e){return W().useRef(e)},t.useState=function(e){return W().useState(e)},t.version="16.14.0"},274:(e,t,o)=>{e.exports=o(7493)},2668:(e,t,o)=>{e.exports=o(2873)}},i={};function n(e){var t=i[e];if(void 0!==t)return t.exports;var r=i[e]={exports:{}};return o[e](r,r.exports,n),r.exports}t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,n.t=function(o,i){if(1&i&&(o=this(o)),8&i)return o;if("object"==typeof o&&o){if(4&i&&o.__esModule)return o;if(16&i&&"function"==typeof o.then)return o}var r=Object.create(null);n.r(r);var a={};e=e||[null,t({}),t([]),t(t)];for(var s=2&i&&o;"object"==typeof s&&!~e.indexOf(s);s=t(s))Object.getOwnPropertyNames(s).forEach((e=>a[e]=()=>o[e]));return a.default=()=>o,n.d(r,a),r},n.d=(e,t)=>{for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{class e{createCSS(){return""}createBehavior(){}}const t=function(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof global)return global;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;try{return new Function("return this")()}catch(e){return{}}}();void 0===t.trustedTypes&&(t.trustedTypes={createPolicy:(e,t)=>t});const o={configurable:!1,enumerable:!1,writable:!1};void 0===t.FAST&&Reflect.defineProperty(t,"FAST",Object.assign({value:Object.create(null)},o));const i=t.FAST;if(void 0===i.getById){const e=Object.create(null);Reflect.defineProperty(i,"getById",Object.assign({value(t,o){let i=e[t];return void 0===i&&(i=o?e[t]=o():null),i}},o))}const r=Object.freeze([]);function a(){const e=new WeakMap;return function(t){let o=e.get(t);if(void 0===o){let i=Reflect.getPrototypeOf(t);for(;void 0===o&&null!==i;)o=e.get(i),i=Reflect.getPrototypeOf(i);o=void 0===o?[]:o.slice(0),e.set(t,o)}return o}}const s=t.FAST.getById(1,(()=>{const e=[],o=[];function i(){if(o.length)throw o.shift()}function n(e){try{e.call()}catch(e){o.push(e),setTimeout(i,0)}}function r(){let t=0;for(;t<e.length;)if(n(e[t]),t++,t>1024){for(let o=0,i=e.length-t;o<i;o++)e[o]=e[o+t];e.length-=t,t=0}e.length=0}return Object.freeze({enqueue:function(o){e.length<1&&t.requestAnimationFrame(r),e.push(o)},process:r})})),l=t.trustedTypes.createPolicy("fast-html",{createHTML:e=>e});let c=l;const d=`fast-${Math.random().toString(36).substring(2,8)}`,u=`${d}{`,p=`}${d}`,h=Object.freeze({supportsAdoptedStyleSheets:Array.isArray(document.adoptedStyleSheets)&&"replace"in CSSStyleSheet.prototype,setHTMLPolicy(e){if(c!==l)throw new Error("The HTML policy can only be set once.");c=e},createHTML:e=>c.createHTML(e),isMarker:e=>e&&8===e.nodeType&&e.data.startsWith(d),extractDirectiveIndexFromMarker:e=>parseInt(e.data.replace(`${d}:`,"")),createInterpolationPlaceholder:e=>`${u}${e}${p}`,createCustomAttributePlaceholder(e,t){return`${e}="${this.createInterpolationPlaceholder(t)}"`},createBlockPlaceholder:e=>`\x3c!--${d}:${e}--\x3e`,queueUpdate:s.enqueue,processUpdates:s.process,nextUpdate:()=>new Promise(s.enqueue),setAttribute(e,t,o){null==o?e.removeAttribute(t):e.setAttribute(t,o)},setBooleanAttribute(e,t,o){o?e.setAttribute(t,""):e.removeAttribute(t)},removeChildNodes(e){for(let t=e.firstChild;null!==t;t=e.firstChild)e.removeChild(t)},createTemplateWalker:e=>document.createTreeWalker(e,133,null,!1)});class f{constructor(){this.targets=new WeakSet}addStylesTo(e){this.targets.add(e)}removeStylesFrom(e){this.targets.delete(e)}isAttachedTo(e){return this.targets.has(e)}withBehaviors(...e){return this.behaviors=null===this.behaviors?e:this.behaviors.concat(e),this}}function g(e){return e.map((e=>e instanceof f?g(e.styles):[e])).reduce(((e,t)=>e.concat(t)),[])}function E(e){return e.map((e=>e instanceof f?e.behaviors:null)).reduce(((e,t)=>null===t?e:(null===e&&(e=[]),e.concat(t))),null)}f.create=(()=>{if(h.supportsAdoptedStyleSheets){const e=new Map;return t=>new m(t,e)}return e=>new O(e)})();let T=(e,t)=>{e.adoptedStyleSheets=[...e.adoptedStyleSheets,...t]},N=(e,t)=>{e.adoptedStyleSheets=e.adoptedStyleSheets.filter((e=>-1===t.indexOf(e)))};if(h.supportsAdoptedStyleSheets)try{document.adoptedStyleSheets.push(),document.adoptedStyleSheets.splice(),T=(e,t)=>{e.adoptedStyleSheets.push(...t)},N=(e,t)=>{for(const o of t){const t=e.adoptedStyleSheets.indexOf(o);-1!==t&&e.adoptedStyleSheets.splice(t,1)}}}catch(e){}class m extends f{constructor(e,t){super(),this.styles=e,this.styleSheetCache=t,this._styleSheets=void 0,this.behaviors=E(e)}get styleSheets(){if(void 0===this._styleSheets){const e=this.styles,t=this.styleSheetCache;this._styleSheets=g(e).map((e=>{if(e instanceof CSSStyleSheet)return e;let o=t.get(e);return void 0===o&&(o=new CSSStyleSheet,o.replaceSync(e),t.set(e,o)),o}))}return this._styleSheets}addStylesTo(e){T(e,this.styleSheets),super.addStylesTo(e)}removeStylesFrom(e){N(e,this.styleSheets),super.removeStylesFrom(e)}}let _=0;class O extends f{constructor(e){super(),this.styles=e,this.behaviors=null,this.behaviors=E(e),this.styleSheets=g(e),this.styleClass="fast-style-class-"+ ++_}addStylesTo(e){const t=this.styleSheets,o=this.styleClass;e=this.normalizeTarget(e);for(let i=0;i<t.length;i++){const n=document.createElement("style");n.innerHTML=t[i],n.className=o,e.append(n)}super.addStylesTo(e)}removeStylesFrom(e){const t=(e=this.normalizeTarget(e)).querySelectorAll(`.${this.styleClass}`);for(let o=0,i=t.length;o<i;++o)e.removeChild(t[o]);super.removeStylesFrom(e)}isAttachedTo(e){return super.isAttachedTo(this.normalizeTarget(e))}normalizeTarget(e){return e===document?document.body:e}}function A(t,o){const i=[];let n="";const r=[];for(let a=0,s=t.length-1;a<s;++a){n+=t[a];let s=o[a];if(s instanceof e){const e=s.createBehavior();s=s.createCSS(),e&&r.push(e)}s instanceof f||s instanceof CSSStyleSheet?(""!==n.trim()&&(i.push(n),n=""),i.push(s)):n+=s}return n+=t[t.length-1],""!==n.trim()&&i.push(n),{styles:i,behaviors:r}}function I(e,...t){const{styles:o,behaviors:i}=A(e,t),n=f.create(o);return i.length&&n.withBehaviors(...i),n}class b extends e{constructor(e,t){super(),this.behaviors=t,this.css="";const o=e.reduce(((e,t)=>("string"==typeof t?this.css+=t:e.push(t),e)),[]);o.length&&(this.styles=f.create(o))}createBehavior(){return this}createCSS(){return this.css}bind(e){this.styles&&e.$fastController.addStyles(this.styles),this.behaviors.length&&e.$fastController.addBehaviors(this.behaviors)}unbind(e){this.styles&&e.$fastController.removeStyles(this.styles),this.behaviors.length&&e.$fastController.removeBehaviors(this.behaviors)}}class S{constructor(e,t){this.sub1=void 0,this.sub2=void 0,this.spillover=void 0,this.source=e,this.sub1=t}has(e){return void 0===this.spillover?this.sub1===e||this.sub2===e:-1!==this.spillover.indexOf(e)}subscribe(e){const t=this.spillover;if(void 0===t){if(this.has(e))return;if(void 0===this.sub1)return void(this.sub1=e);if(void 0===this.sub2)return void(this.sub2=e);this.spillover=[this.sub1,this.sub2,e],this.sub1=void 0,this.sub2=void 0}else-1===t.indexOf(e)&&t.push(e)}unsubscribe(e){const t=this.spillover;if(void 0===t)this.sub1===e?this.sub1=void 0:this.sub2===e&&(this.sub2=void 0);else{const o=t.indexOf(e);-1!==o&&t.splice(o,1)}}notify(e){const t=this.spillover,o=this.source;if(void 0===t){const t=this.sub1,i=this.sub2;void 0!==t&&t.handleChange(o,e),void 0!==i&&i.handleChange(o,e)}else for(let i=0,n=t.length;i<n;++i)t[i].handleChange(o,e)}}class R{constructor(e){this.subscribers={},this.sourceSubscribers=null,this.source=e}notify(e){var t;const o=this.subscribers[e];void 0!==o&&o.notify(e),null===(t=this.sourceSubscribers)||void 0===t||t.notify(e)}subscribe(e,t){var o;if(t){let o=this.subscribers[t];void 0===o&&(this.subscribers[t]=o=new S(this.source)),o.subscribe(e)}else this.sourceSubscribers=null!==(o=this.sourceSubscribers)&&void 0!==o?o:new S(this.source),this.sourceSubscribers.subscribe(e)}unsubscribe(e,t){var o;if(t){const o=this.subscribers[t];void 0!==o&&o.unsubscribe(e)}else null===(o=this.sourceSubscribers)||void 0===o||o.unsubscribe(e)}}const y=i.getById(2,(()=>{const e=/(:|&&|\|\||if)/,t=new WeakMap,o=h.queueUpdate;let i,n=e=>{throw new Error("Must call enableArrayObservation before observing arrays.")};function r(e){let o=e.$fastController||t.get(e);return void 0===o&&(Array.isArray(e)?o=n(e):t.set(e,o=new R(e))),o}const s=a();class l{constructor(e){this.name=e,this.field=`_${e}`,this.callback=`${e}Changed`}getValue(e){return void 0!==i&&i.watch(e,this.name),e[this.field]}setValue(e,t){const o=this.field,i=e[o];if(i!==t){e[o]=t;const n=e[this.callback];"function"==typeof n&&n.call(e,i,t),r(e).notify(this.name)}}}class c extends S{constructor(e,t,o=!1){super(e,t),this.binding=e,this.isVolatileBinding=o,this.needsRefresh=!0,this.needsQueue=!0,this.first=this,this.last=null,this.propertySource=void 0,this.propertyName=void 0,this.notifier=void 0,this.next=void 0}observe(e,t){this.needsRefresh&&null!==this.last&&this.disconnect();const o=i;i=this.needsRefresh?this:void 0,this.needsRefresh=this.isVolatileBinding;const n=this.binding(e,t);return i=o,n}disconnect(){if(null!==this.last){let e=this.first;for(;void 0!==e;)e.notifier.unsubscribe(this,e.propertyName),e=e.next;this.last=null,this.needsRefresh=this.needsQueue=!0}}watch(e,t){const o=this.last,n=r(e),a=null===o?this.first:{};if(a.propertySource=e,a.propertyName=t,a.notifier=n,n.subscribe(this,t),null!==o){if(!this.needsRefresh){let t;i=void 0,t=o.propertySource[o.propertyName],i=this,e===t&&(this.needsRefresh=!0)}o.next=a}this.last=a}handleChange(){this.needsQueue&&(this.needsQueue=!1,o(this))}call(){null!==this.last&&(this.needsQueue=!0,this.notify(this))}records(){let e=this.first;return{next:()=>{const t=e;return void 0===t?{value:void 0,done:!0}:(e=e.next,{value:t,done:!1})},[Symbol.iterator]:function(){return this}}}}return Object.freeze({setArrayObserverFactory(e){n=e},getNotifier:r,track(e,t){void 0!==i&&i.watch(e,t)},trackVolatile(){void 0!==i&&(i.needsRefresh=!0)},notify(e,t){r(e).notify(t)},defineProperty(e,t){"string"==typeof t&&(t=new l(t)),s(e).push(t),Reflect.defineProperty(e,t.name,{enumerable:!0,get:function(){return t.getValue(this)},set:function(e){t.setValue(this,e)}})},getAccessors:s,binding(e,t,o=this.isVolatileBinding(e)){return new c(e,t,o)},isVolatileBinding:t=>e.test(t.toString())})}));function v(e,t){y.defineProperty(e,t)}const C=i.getById(3,(()=>{let e=null;return{get:()=>e,set(t){e=t}}}));class w{constructor(){this.index=0,this.length=0,this.parent=null,this.parentContext=null}get event(){return C.get()}get isEven(){return this.index%2==0}get isOdd(){return this.index%2!=0}get isFirst(){return 0===this.index}get isInMiddle(){return!this.isFirst&&!this.isLast}get isLast(){return this.index===this.length-1}static setEvent(e){C.set(e)}}y.defineProperty(w.prototype,"index"),y.defineProperty(w.prototype,"length");const M=Object.seal(new w);class D{constructor(){this.targetIndex=0}}class P extends D{constructor(){super(...arguments),this.createPlaceholder=h.createInterpolationPlaceholder}}class x extends D{constructor(e,t,o){super(),this.name=e,this.behavior=t,this.options=o}createPlaceholder(e){return h.createCustomAttributePlaceholder(this.name,e)}createBehavior(e){return new this.behavior(e,this.options)}}function k(e,t){this.source=e,this.context=t,null===this.bindingObserver&&(this.bindingObserver=y.binding(this.binding,this,this.isBindingVolatile)),this.updateTarget(this.bindingObserver.observe(e,t))}function L(e,t){this.source=e,this.context=t,this.target.addEventListener(this.targetName,this)}function F(){this.bindingObserver.disconnect(),this.source=null,this.context=null}function G(){this.bindingObserver.disconnect(),this.source=null,this.context=null;const e=this.target.$fastView;void 0!==e&&e.isComposed&&(e.unbind(),e.needsBindOnly=!0)}function B(){this.target.removeEventListener(this.targetName,this),this.source=null,this.context=null}function W(e){h.setAttribute(this.target,this.targetName,e)}function U(e){h.setBooleanAttribute(this.target,this.targetName,e)}function V(e){if(null==e&&(e=""),e.create){this.target.textContent="";let t=this.target.$fastView;void 0===t?t=e.create():this.target.$fastTemplate!==e&&(t.isComposed&&(t.remove(),t.unbind()),t=e.create()),t.isComposed?t.needsBindOnly&&(t.needsBindOnly=!1,t.bind(this.source,this.context)):(t.isComposed=!0,t.bind(this.source,this.context),t.insertBefore(this.target),this.target.$fastView=t,this.target.$fastTemplate=e)}else{const t=this.target.$fastView;void 0!==t&&t.isComposed&&(t.isComposed=!1,t.remove(),t.needsBindOnly?t.needsBindOnly=!1:t.unbind()),this.target.textContent=e}}function $(e){this.target[this.targetName]=e}function H(e){const t=this.classVersions||Object.create(null),o=this.target;let i=this.version||0;if(null!=e&&e.length){const n=e.split(/\s+/);for(let e=0,r=n.length;e<r;++e){const r=n[e];""!==r&&(t[r]=i,o.classList.add(r))}}if(this.classVersions=t,this.version=i+1,0!==i){i-=1;for(const e in t)t[e]===i&&o.classList.remove(e)}}class z extends P{constructor(e){super(),this.binding=e,this.bind=k,this.unbind=F,this.updateTarget=W,this.isBindingVolatile=y.isVolatileBinding(this.binding)}get targetName(){return this.originalTargetName}set targetName(e){if(this.originalTargetName=e,void 0!==e)switch(e[0]){case":":if(this.cleanedTargetName=e.substr(1),this.updateTarget=$,"innerHTML"===this.cleanedTargetName){const e=this.binding;this.binding=(t,o)=>h.createHTML(e(t,o))}break;case"?":this.cleanedTargetName=e.substr(1),this.updateTarget=U;break;case"@":this.cleanedTargetName=e.substr(1),this.bind=L,this.unbind=B;break;default:this.cleanedTargetName=e,"class"===e&&(this.updateTarget=H)}}targetAtContent(){this.updateTarget=V,this.unbind=G}createBehavior(e){return new j(e,this.binding,this.isBindingVolatile,this.bind,this.unbind,this.updateTarget,this.cleanedTargetName)}}class j{constructor(e,t,o,i,n,r,a){this.source=null,this.context=null,this.bindingObserver=null,this.target=e,this.binding=t,this.isBindingVolatile=o,this.bind=i,this.unbind=n,this.updateTarget=r,this.targetName=a}handleChange(){this.updateTarget(this.bindingObserver.observe(this.source,this.context))}handleEvent(e){w.setEvent(e);const t=this.binding(this.source,this.context);w.setEvent(null),!0!==t&&e.preventDefault()}}let Y=null;class Z{addFactory(e){e.targetIndex=this.targetIndex,this.behaviorFactories.push(e)}captureContentBinding(e){e.targetAtContent(),this.addFactory(e)}reset(){this.behaviorFactories=[],this.targetIndex=-1}release(){Y=this}static borrow(e){const t=Y||new Z;return t.directives=e,t.reset(),Y=null,t}}function K(e){if(1===e.length)return e[0];let t;const o=e.length,i=e.map((e=>"string"==typeof e?()=>e:(t=e.targetName||t,e.binding))),n=new z(((e,t)=>{let n="";for(let r=0;r<o;++r)n+=i[r](e,t);return n}));return n.targetName=t,n}const q=p.length;function X(e,t){const o=t.split(u);if(1===o.length)return null;const i=[];for(let t=0,n=o.length;t<n;++t){const n=o[t],r=n.indexOf(p);let a;if(-1===r)a=n;else{const t=parseInt(n.substring(0,r));i.push(e.directives[t]),a=n.substring(r+q)}""!==a&&i.push(a)}return i}function Q(e,t,o=!1){const i=t.attributes;for(let n=0,r=i.length;n<r;++n){const a=i[n],s=a.value,l=X(e,s);let c=null;null===l?o&&(c=new z((()=>s)),c.targetName=a.name):c=K(l),null!==c&&(t.removeAttributeNode(a),n--,r--,e.addFactory(c))}}function J(e,t,o){const i=X(e,t.textContent);if(null!==i){let n=t;for(let r=0,a=i.length;r<a;++r){const a=i[r],s=0===r?t:n.parentNode.insertBefore(document.createTextNode(""),n.nextSibling);"string"==typeof a?s.textContent=a:(s.textContent=" ",e.captureContentBinding(a)),n=s,e.targetIndex++,s!==t&&o.nextNode()}e.targetIndex--}}const ee=document.createRange();class te{constructor(e,t){this.fragment=e,this.behaviors=t,this.source=null,this.context=null,this.firstChild=e.firstChild,this.lastChild=e.lastChild}appendTo(e){e.appendChild(this.fragment)}insertBefore(e){if(this.fragment.hasChildNodes())e.parentNode.insertBefore(this.fragment,e);else{const t=this.lastChild;if(e.previousSibling===t)return;const o=e.parentNode;let i,n=this.firstChild;for(;n!==t;)i=n.nextSibling,o.insertBefore(n,e),n=i;o.insertBefore(t,e)}}remove(){const e=this.fragment,t=this.lastChild;let o,i=this.firstChild;for(;i!==t;)o=i.nextSibling,e.appendChild(i),i=o;e.appendChild(t)}dispose(){const e=this.firstChild.parentNode,t=this.lastChild;let o,i=this.firstChild;for(;i!==t;)o=i.nextSibling,e.removeChild(i),i=o;e.removeChild(t);const n=this.behaviors,r=this.source;for(let e=0,t=n.length;e<t;++e)n[e].unbind(r)}bind(e,t){const o=this.behaviors;if(this.source!==e)if(null!==this.source){const i=this.source;this.source=e,this.context=t;for(let n=0,r=o.length;n<r;++n){const r=o[n];r.unbind(i),r.bind(e,t)}}else{this.source=e,this.context=t;for(let i=0,n=o.length;i<n;++i)o[i].bind(e,t)}}unbind(){if(null===this.source)return;const e=this.behaviors,t=this.source;for(let o=0,i=e.length;o<i;++o)e[o].unbind(t);this.source=null}static disposeContiguousBatch(e){if(0!==e.length){ee.setStartBefore(e[0].firstChild),ee.setEndAfter(e[e.length-1].lastChild),ee.deleteContents();for(let t=0,o=e.length;t<o;++t){const o=e[t],i=o.behaviors,n=o.source;for(let e=0,t=i.length;e<t;++e)i[e].unbind(n)}}}}class oe{constructor(e,t){this.behaviorCount=0,this.hasHostBehaviors=!1,this.fragment=null,this.targetOffset=0,this.viewBehaviorFactories=null,this.hostBehaviorFactories=null,this.html=e,this.directives=t}create(e){if(null===this.fragment){let e;const t=this.html;if("string"==typeof t){e=document.createElement("template"),e.innerHTML=h.createHTML(t);const o=e.content.firstElementChild;null!==o&&"TEMPLATE"===o.tagName&&(e=o)}else e=t;const o=function(e,t){const o=e.content;document.adoptNode(o);const i=Z.borrow(t);Q(i,e,!0);const n=i.behaviorFactories;i.reset();const r=h.createTemplateWalker(o);let a;for(;a=r.nextNode();)switch(i.targetIndex++,a.nodeType){case 1:Q(i,a);break;case 3:J(i,a,r);break;case 8:h.isMarker(a)&&i.addFactory(t[h.extractDirectiveIndexFromMarker(a)])}let s=0;(h.isMarker(o.firstChild)||1===o.childNodes.length&&t.length)&&(o.insertBefore(document.createComment(""),o.firstChild),s=-1);const l=i.behaviorFactories;return i.release(),{fragment:o,viewBehaviorFactories:l,hostBehaviorFactories:n,targetOffset:s}}(e,this.directives);this.fragment=o.fragment,this.viewBehaviorFactories=o.viewBehaviorFactories,this.hostBehaviorFactories=o.hostBehaviorFactories,this.targetOffset=o.targetOffset,this.behaviorCount=this.viewBehaviorFactories.length+this.hostBehaviorFactories.length,this.hasHostBehaviors=this.hostBehaviorFactories.length>0}const t=this.fragment.cloneNode(!0),o=this.viewBehaviorFactories,i=new Array(this.behaviorCount),n=h.createTemplateWalker(t);let r=0,a=this.targetOffset,s=n.nextNode();for(let e=o.length;r<e;++r){const e=o[r],t=e.targetIndex;for(;null!==s;){if(a===t){i[r]=e.createBehavior(s);break}s=n.nextNode(),a++}}if(this.hasHostBehaviors){const t=this.hostBehaviorFactories;for(let o=0,n=t.length;o<n;++o,++r)i[r]=t[o].createBehavior(e)}return new te(t,i)}render(e,t,o){"string"==typeof t&&(t=document.getElementById(t)),void 0===o&&(o=t);const i=this.create(o);return i.bind(e,M),i.appendTo(t),i}}const ie=/([ \x09\x0a\x0c\x0d])([^\0-\x1F\x7F-\x9F "'>=/]+)([ \x09\x0a\x0c\x0d]*=[ \x09\x0a\x0c\x0d]*(?:[^ \x09\x0a\x0c\x0d"'`<>=]*|"[^"]*|'[^']*))$/;function ne(e,...t){const o=[];let i="";for(let n=0,r=e.length-1;n<r;++n){const r=e[n];let a=t[n];if(i+=r,a instanceof oe){const e=a;a=()=>e}if("function"==typeof a&&(a=new z(a)),a instanceof P){const e=ie.exec(r);null!==e&&(a.targetName=e[2])}a instanceof D?(i+=a.createPlaceholder(o.length),o.push(a)):i+=a}return i+=e[e.length-1],new oe(i,o)}const re=e=>"function"==typeof e,ae=()=>null;function se(e){return void 0===e?ae:re(e)?e:()=>e}function le(e,t,o){const i=re(e)?e:()=>e,n=se(t),r=se(o);return(e,t)=>i(e,t)?n(e,t):r(e,t)}const ce=Object.freeze({locate:a()}),de={toView:e=>e?"true":"false",fromView:e=>null!=e&&"false"!==e&&!1!==e&&0!==e},ue={toView(e){if(null==e)return null;const t=1*e;return isNaN(t)?null:t.toString()},fromView(e){if(null==e)return null;const t=1*e;return isNaN(t)?null:t}};class pe{constructor(e,t,o=t.toLowerCase(),i="reflect",n){this.guards=new Set,this.Owner=e,this.name=t,this.attribute=o,this.mode=i,this.converter=n,this.fieldName=`_${t}`,this.callbackName=`${t}Changed`,this.hasCallback=this.callbackName in e.prototype,"boolean"===i&&void 0===n&&(this.converter=de)}setValue(e,t){const o=e[this.fieldName],i=this.converter;void 0!==i&&(t=i.fromView(t)),o!==t&&(e[this.fieldName]=t,this.tryReflectToAttribute(e),this.hasCallback&&e[this.callbackName](o,t),e.$fastController.notify(this.name))}getValue(e){return y.track(e,this.name),e[this.fieldName]}onAttributeChangedCallback(e,t){this.guards.has(e)||(this.guards.add(e),this.setValue(e,t),this.guards.delete(e))}tryReflectToAttribute(e){const t=this.mode,o=this.guards;o.has(e)||"fromView"===t||h.queueUpdate((()=>{o.add(e);const i=e[this.fieldName];switch(t){case"reflect":const t=this.converter;h.setAttribute(e,this.attribute,void 0!==t?t.toView(i):i);break;case"boolean":h.setBooleanAttribute(e,this.attribute,i)}o.delete(e)}))}static collect(e,...t){const o=[];t.push(ce.locate(e));for(let i=0,n=t.length;i<n;++i){const n=t[i];if(void 0!==n)for(let t=0,i=n.length;t<i;++t){const i=n[t];"string"==typeof i?o.push(new pe(e,i)):o.push(new pe(e,i.property,i.attribute,i.mode,i.converter))}}return o}}function he(e,t){let o;function i(e,t){arguments.length>1&&(o.property=t),ce.locate(e.constructor).push(o)}return arguments.length>1?(o={},void i(e,t)):(o=void 0===e?{}:e,i)}const fe={mode:"open"},ge={},Ee=i.getById(4,(()=>{const e=new Map;return Object.freeze({register:t=>!e.has(t.type)&&(e.set(t.type,t),!0),getByType:t=>e.get(t)})}));class Te{constructor(e,t=e.definition){"string"==typeof t&&(t={name:t}),this.type=e,this.name=t.name,this.template=t.template;const o=pe.collect(e,t.attributes),i=new Array(o.length),n={},r={};for(let e=0,t=o.length;e<t;++e){const t=o[e];i[e]=t.attribute,n[t.name]=t,r[t.attribute]=t}this.attributes=o,this.observedAttributes=i,this.propertyLookup=n,this.attributeLookup=r,this.shadowOptions=void 0===t.shadowOptions?fe:null===t.shadowOptions?void 0:Object.assign(Object.assign({},fe),t.shadowOptions),this.elementOptions=void 0===t.elementOptions?ge:Object.assign(Object.assign({},ge),t.elementOptions),this.styles=void 0===t.styles?void 0:Array.isArray(t.styles)?f.create(t.styles):t.styles instanceof f?t.styles:f.create([t.styles])}get isDefined(){return!!Ee.getByType(this.type)}define(e=customElements){const t=this.type;if(Ee.register(this)){const e=this.attributes,o=t.prototype;for(let t=0,i=e.length;t<i;++t)y.defineProperty(o,e[t]);Reflect.defineProperty(t,"observedAttributes",{value:this.observedAttributes,enumerable:!0})}return e.get(this.name)||e.define(this.name,t,this.elementOptions),this}}Te.forType=Ee.getByType;const Ne=new WeakMap,me={bubbles:!0,composed:!0,cancelable:!0};function _e(e){return e.shadowRoot||Ne.get(e)||null}class Oe extends R{constructor(e,t){super(e),this.boundObservables=null,this.behaviors=null,this.needsInitialization=!0,this._template=null,this._styles=null,this._isConnected=!1,this.$fastController=this,this.view=null,this.element=e,this.definition=t;const o=t.shadowOptions;if(void 0!==o){const t=e.attachShadow(o);"closed"===o.mode&&Ne.set(e,t)}const i=y.getAccessors(e);if(i.length>0){const t=this.boundObservables=Object.create(null);for(let o=0,n=i.length;o<n;++o){const n=i[o].name,r=e[n];void 0!==r&&(delete e[n],t[n]=r)}}}get isConnected(){return y.track(this,"isConnected"),this._isConnected}setIsConnected(e){this._isConnected=e,y.notify(this,"isConnected")}get template(){return this._template}set template(e){this._template!==e&&(this._template=e,this.needsInitialization||this.renderTemplate(e))}get styles(){return this._styles}set styles(e){this._styles!==e&&(null!==this._styles&&this.removeStyles(this._styles),this._styles=e,this.needsInitialization||null===e||this.addStyles(e))}addStyles(e){const t=_e(this.element)||this.element.getRootNode();if(e instanceof HTMLStyleElement)t.append(e);else if(!e.isAttachedTo(t)){const o=e.behaviors;e.addStylesTo(t),null!==o&&this.addBehaviors(o)}}removeStyles(e){const t=_e(this.element)||this.element.getRootNode();if(e instanceof HTMLStyleElement)t.removeChild(e);else if(e.isAttachedTo(t)){const o=e.behaviors;e.removeStylesFrom(t),null!==o&&this.removeBehaviors(o)}}addBehaviors(e){const t=this.behaviors||(this.behaviors=new Map),o=e.length,i=[];for(let n=0;n<o;++n){const o=e[n];t.has(o)?t.set(o,t.get(o)+1):(t.set(o,1),i.push(o))}if(this._isConnected){const e=this.element;for(let t=0;t<i.length;++t)i[t].bind(e,M)}}removeBehaviors(e,t=!1){const o=this.behaviors;if(null===o)return;const i=e.length,n=[];for(let r=0;r<i;++r){const i=e[r];if(o.has(i)){const e=o.get(i)-1;0===e||t?o.delete(i)&&n.push(i):o.set(i,e)}}if(this._isConnected){const e=this.element;for(let t=0;t<n.length;++t)n[t].unbind(e)}}onConnectedCallback(){if(this._isConnected)return;const e=this.element;this.needsInitialization?this.finishInitialization():null!==this.view&&this.view.bind(e,M);const t=this.behaviors;if(null!==t)for(const[o]of t)o.bind(e,M);this.setIsConnected(!0)}onDisconnectedCallback(){if(!this._isConnected)return;this.setIsConnected(!1);const e=this.view;null!==e&&e.unbind();const t=this.behaviors;if(null!==t){const e=this.element;for(const[o]of t)o.unbind(e)}}onAttributeChangedCallback(e,t,o){const i=this.definition.attributeLookup[e];void 0!==i&&i.onAttributeChangedCallback(this.element,o)}emit(e,t,o){return!!this._isConnected&&this.element.dispatchEvent(new CustomEvent(e,Object.assign(Object.assign({detail:t},me),o)))}finishInitialization(){const e=this.element,t=this.boundObservables;if(null!==t){const o=Object.keys(t);for(let i=0,n=o.length;i<n;++i){const n=o[i];e[n]=t[n]}this.boundObservables=null}const o=this.definition;null===this._template&&(this.element.resolveTemplate?this._template=this.element.resolveTemplate():o.template&&(this._template=o.template||null)),null!==this._template&&this.renderTemplate(this._template),null===this._styles&&(this.element.resolveStyles?this._styles=this.element.resolveStyles():o.styles&&(this._styles=o.styles||null)),null!==this._styles&&this.addStyles(this._styles),this.needsInitialization=!1}renderTemplate(e){const t=this.element,o=_e(t)||t;null!==this.view?(this.view.dispose(),this.view=null):this.needsInitialization||h.removeChildNodes(o),e&&(this.view=e.render(t,o,t))}static forCustomElement(e){const t=e.$fastController;if(void 0!==t)return t;const o=Te.forType(e.constructor);if(void 0===o)throw new Error("Missing FASTElement definition.");return e.$fastController=new Oe(e,o)}}function Ae(e){return class extends e{constructor(){super(),Oe.forCustomElement(this)}$emit(e,t,o){return this.$fastController.emit(e,t,o)}connectedCallback(){this.$fastController.onConnectedCallback()}disconnectedCallback(){this.$fastController.onDisconnectedCallback()}attributeChangedCallback(e,t,o){this.$fastController.onAttributeChangedCallback(e,t,o)}}}const Ie=Object.assign(Ae(HTMLElement),{from:e=>Ae(e),define:(e,t)=>new Te(e,t).define().type});function be(e){return function(t){new Te(t,e).define()}}function Se(e,t,o,i){var n,r=arguments.length,a=r<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,o):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,o,i);else for(var s=e.length-1;s>=0;s--)(n=e[s])&&(a=(r<3?n(a):r>3?n(t,o,a):n(t,o))||a);return r>3&&a&&Object.defineProperty(t,o,a),a}const Re=new Map;"metadata"in Reflect||(Reflect.metadata=function(e,t){return function(o){Reflect.defineMetadata(e,t,o)}},Reflect.defineMetadata=function(e,t,o){let i=Re.get(o);void 0===i&&Re.set(o,i=new Map),i.set(e,t)},Reflect.getOwnMetadata=function(e,t){const o=Re.get(t);if(void 0!==o)return o.get(e)});class ye{constructor(e,t){this.container=e,this.key=t}instance(e){return this.registerResolver(0,e)}singleton(e){return this.registerResolver(1,e)}transient(e){return this.registerResolver(2,e)}callback(e){return this.registerResolver(3,e)}cachedCallback(e){return this.registerResolver(3,Qe(e))}aliasTo(e){return this.registerResolver(5,e)}registerResolver(e,t){const{container:o,key:i}=this;return this.container=this.key=void 0,o.registerResolver(i,new We(i,e,t))}}function ve(e){const t=e.slice(),o=Object.keys(e),i=o.length;let n;for(let r=0;r<i;++r)n=o[r],at(n)||(t[n]=e[n]);return t}const Ce=Object.freeze({none(e){throw Error(`${e.toString()} not registered, did you forget to add @singleton()?`)},singleton:e=>new We(e,1,e),transient:e=>new We(e,2,e)}),we=Object.freeze({default:Object.freeze({parentLocator:()=>null,responsibleForOwnerRequests:!1,defaultResolver:Ce.singleton})}),Me=new Map;function De(e){return t=>Reflect.getOwnMetadata(e,t)}let Pe=null;const xe=Object.freeze({createContainer:e=>new qe(null,Object.assign({},we.default,e)),findResponsibleContainer(e){const t=e.$$container$$;return t&&t.responsibleForOwnerRequests?t:xe.findParentContainer(e)},findParentContainer(e){const t=new CustomEvent(Ze,{bubbles:!0,composed:!0,cancelable:!0,detail:{container:void 0}});return e.dispatchEvent(t),t.detail.container||xe.getOrCreateDOMContainer()},getOrCreateDOMContainer:(e,t)=>e?e.$$container$$||new qe(e,Object.assign({},we.default,t,{parentLocator:xe.findParentContainer})):Pe||(Pe=new qe(null,Object.assign({},we.default,t,{parentLocator:()=>null}))),getDesignParamtypes:De("design:paramtypes"),getAnnotationParamtypes:De("di:paramtypes"),getOrCreateAnnotationParamTypes(e){let t=this.getAnnotationParamtypes(e);return void 0===t&&Reflect.defineMetadata("di:paramtypes",t=[],e),t},getDependencies(e){let t=Me.get(e);if(void 0===t){const o=e.inject;if(void 0===o){const o=xe.getDesignParamtypes(e),i=xe.getAnnotationParamtypes(e);if(void 0===o)if(void 0===i){const o=Object.getPrototypeOf(e);t="function"==typeof o&&o!==Function.prototype?ve(xe.getDependencies(o)):[]}else t=ve(i);else if(void 0===i)t=ve(o);else{t=ve(o);let e,n=i.length;for(let o=0;o<n;++o)e=i[o],void 0!==e&&(t[o]=e);const r=Object.keys(i);let a;n=r.length;for(let e=0;e<n;++e)a=r[e],at(a)||(t[a]=i[a])}}else t=ve(o);Me.set(e,t)}return t},defineProperty(e,t,o,i=!1){const n=`$di_${t}`;Reflect.defineProperty(e,t,{get:function(){let e=this[n];if(void 0===e){const r=this instanceof HTMLElement?xe.findResponsibleContainer(this):xe.getOrCreateDOMContainer();if(e=r.get(o),this[n]=e,i&&this instanceof Ie){const i=this.$fastController,r=()=>{xe.findResponsibleContainer(this).get(o)!==this[n]&&(this[n]=e,i.notify(t))};i.subscribe({handleChange:r},"isConnected")}}return e}})},createInterface(e,t){const o="function"==typeof e?e:t,i="string"==typeof e?e:e&&"friendlyName"in e&&e.friendlyName||ot,n="string"!=typeof e&&(e&&"respectConnection"in e&&e.respectConnection||!1),r=function(e,t,o){if(null==e||void 0!==new.target)throw new Error(`No registration for interface: '${r.friendlyName}'`);t?xe.defineProperty(e,t,r,n):xe.getOrCreateAnnotationParamTypes(e)[o]=r};return r.$isInterface=!0,r.friendlyName=null==i?"(anonymous)":i,null!=o&&(r.register=function(e,t){return o(new ye(e,null!=t?t:r))}),r.toString=function(){return`InterfaceSymbol<${r.friendlyName}>`},r},inject:(...e)=>function(t,o,i){if("number"==typeof i){const o=xe.getOrCreateAnnotationParamTypes(t),n=e[0];void 0!==n&&(o[i]=n)}else if(o)xe.defineProperty(t,o,e[0]);else{const o=i?xe.getOrCreateAnnotationParamTypes(i.value):xe.getOrCreateAnnotationParamTypes(t);let n;for(let t=0;t<e.length;++t)n=e[t],void 0!==n&&(o[t]=n)}},transient:e=>(e.register=function(t){return Je.transient(e,e).register(t)},e.registerInRequestor=!1,e),singleton:(e,t=Fe)=>(e.register=function(t){return Je.singleton(e,e).register(t)},e.registerInRequestor=t.scoped,e)}),ke=xe.createInterface("Container");function Le(e){return function(t){const o=function(e,t,i){xe.inject(o)(e,t,i)};return o.$isResolver=!0,o.resolve=function(o,i){return e(t,o,i)},o}}xe.inject;const Fe={scoped:!1};function Ge(e,t,o){xe.inject(Ge)(e,t,o)}function Be(e,t){return t.getFactory(e).construct(t)}Le(((e,t,o)=>()=>o.get(e))),Le(((e,t,o)=>o.has(e,!0)?o.get(e):void 0)),Ge.$isResolver=!0,Ge.resolve=()=>{},Le(((e,t,o)=>{const i=Be(e,t),n=new We(e,0,i);return o.registerResolver(e,n),i})),Le(((e,t,o)=>Be(e,t)));class We{constructor(e,t,o){this.key=e,this.strategy=t,this.state=o,this.resolving=!1}get $isResolver(){return!0}register(e){return e.registerResolver(this.key,this)}resolve(e,t){switch(this.strategy){case 0:return this.state;case 1:if(this.resolving)throw new Error(`Cyclic dependency found: ${this.state.name}`);return this.resolving=!0,this.state=e.getFactory(this.state).construct(t),this.strategy=0,this.resolving=!1,this.state;case 2:{const o=e.getFactory(this.state);if(null===o)throw new Error(`Resolver for ${String(this.key)} returned a null factory`);return o.construct(t)}case 3:return this.state(e,t,this);case 4:return this.state[0].resolve(e,t);case 5:return t.get(this.state);default:throw new Error(`Invalid resolver strategy specified: ${this.strategy}.`)}}getFactory(e){var t,o,i;switch(this.strategy){case 1:case 2:return e.getFactory(this.state);case 5:return null!==(i=null===(o=null===(t=e.getResolver(this.state))||void 0===t?void 0:t.getFactory)||void 0===o?void 0:o.call(t,e))&&void 0!==i?i:null;default:return null}}}function Ue(e){return this.get(e)}function Ve(e,t){return t(e)}class $e{constructor(e,t){this.Type=e,this.dependencies=t,this.transformers=null}construct(e,t){let o;return o=void 0===t?new this.Type(...this.dependencies.map(Ue,e)):new this.Type(...this.dependencies.map(Ue,e),...t),null==this.transformers?o:this.transformers.reduce(Ve,o)}registerTransformer(e){(this.transformers||(this.transformers=[])).push(e)}}const He={$isResolver:!0,resolve:(e,t)=>t};function ze(e){return"function"==typeof e.register}function je(e){return function(e){return ze(e)&&"boolean"==typeof e.registerInRequestor}(e)&&e.registerInRequestor}const Ye=new Set(["Array","ArrayBuffer","Boolean","DataView","Date","Error","EvalError","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Number","Object","Promise","RangeError","ReferenceError","RegExp","Set","SharedArrayBuffer","String","SyntaxError","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","URIError","WeakMap","WeakSet"]),Ze="__DI_LOCATE_PARENT__",Ke=new Map;class qe{constructor(e,t){this.owner=e,this.config=t,this._parent=void 0,this.registerDepth=0,this.context=null,null!==e&&(e.$$container$$=this),this.resolvers=new Map,this.resolvers.set(ke,He),e instanceof Node&&e.addEventListener(Ze,(e=>{e.composedPath()[0]!==this.owner&&(e.detail.container=this,e.stopImmediatePropagation())}))}get parent(){return void 0===this._parent&&(this._parent=this.config.parentLocator(this.owner)),this._parent}get depth(){return null===this.parent?0:this.parent.depth+1}get responsibleForOwnerRequests(){return this.config.responsibleForOwnerRequests}registerWithContext(e,...t){return this.context=e,this.register(...t),this.context=null,this}register(...e){if(100==++this.registerDepth)throw new Error("Unable to autoregister dependency");let t,o,i,n,r;const a=this.context;for(let s=0,l=e.length;s<l;++s)if(t=e[s],it(t))if(ze(t))t.register(this,a);else if(void 0!==t.prototype)Je.singleton(t,t).register(this);else for(o=Object.keys(t),n=0,r=o.length;n<r;++n)i=t[o[n]],it(i)&&(ze(i)?i.register(this,a):this.register(i));return--this.registerDepth,this}registerResolver(e,t){et(e);const o=this.resolvers,i=o.get(e);return null==i?o.set(e,t):i instanceof We&&4===i.strategy?i.state.push(t):o.set(e,new We(e,4,[i,t])),t}registerTransformer(e,t){const o=this.getResolver(e);if(null==o)return!1;if(o.getFactory){const e=o.getFactory(this);return null!=e&&(e.registerTransformer(t),!0)}return!1}getResolver(e,t=!0){if(et(e),void 0!==e.resolve)return e;let o,i=this;for(;null!=i;){if(o=i.resolvers.get(e),null!=o)return o;if(null==i.parent){const o=je(e)?this:i;return t?this.jitRegister(e,o):null}i=i.parent}return null}has(e,t=!1){return!!this.resolvers.has(e)||!(!t||null==this.parent)&&this.parent.has(e,!0)}get(e){if(et(e),e.$isResolver)return e.resolve(this,this);let t,o=this;for(;null!=o;){if(t=o.resolvers.get(e),null!=t)return t.resolve(o,this);if(null==o.parent){const i=je(e)?this:o;return t=this.jitRegister(e,i),t.resolve(o,this)}o=o.parent}throw new Error(`Unable to resolve key: ${e}`)}getAll(e,t=!1){et(e);const o=this;let i,n=o;if(t){let t=r;for(;null!=n;)i=n.resolvers.get(e),null!=i&&(t=t.concat(tt(i,n,o))),n=n.parent;return t}for(;null!=n;){if(i=n.resolvers.get(e),null!=i)return tt(i,n,o);if(n=n.parent,null==n)return r}return r}getFactory(e){let t=Ke.get(e);if(void 0===t){if(nt(e))throw new Error(`${e.name} is a native function and therefore cannot be safely constructed by DI. If this is intentional, please use a callback or cachedCallback resolver.`);Ke.set(e,t=new $e(e,xe.getDependencies(e)))}return t}registerFactory(e,t){Ke.set(e,t)}createChild(e){return new qe(null,Object.assign({},this.config,e,{parentLocator:()=>this}))}jitRegister(e,t){if("function"!=typeof e)throw new Error(`Attempted to jitRegister something that is not a constructor: '${e}'. Did you forget to register this dependency?`);if(Ye.has(e.name))throw new Error(`Attempted to jitRegister an intrinsic type: ${e.name}. Did you forget to add @inject(Key)`);if(ze(e)){const o=e.register(t);if(!(o instanceof Object)||null==o.resolve){const o=t.resolvers.get(e);if(null!=o)return o;throw new Error("A valid resolver was not returned from the static register method")}return o}if(e.$isInterface)throw new Error(`Attempted to jitRegister an interface: ${e.friendlyName}`);{const o=this.config.defaultResolver(e,t);return t.resolvers.set(e,o),o}}}const Xe=new WeakMap;function Qe(e){return function(t,o,i){if(Xe.has(i))return Xe.get(i);const n=e(t,o,i);return Xe.set(i,n),n}}const Je=Object.freeze({instance:(e,t)=>new We(e,0,t),singleton:(e,t)=>new We(e,1,t),transient:(e,t)=>new We(e,2,t),callback:(e,t)=>new We(e,3,t),cachedCallback:(e,t)=>new We(e,3,Qe(t)),aliasTo:(e,t)=>new We(t,5,e)});function et(e){if(null==e)throw new Error("key/value cannot be null or undefined. Are you trying to inject/register something that doesn't exist with DI?")}function tt(e,t,o){if(e instanceof We&&4===e.strategy){const i=e.state;let n=i.length;const r=new Array(n);for(;n--;)r[n]=i[n].resolve(t,o);return r}return[e.resolve(t,o)]}const ot="(anonymous)";function it(e){return"object"==typeof e&&null!==e||"function"==typeof e}const nt=function(){const e=new WeakMap;let t=!1,o="",i=0;return function(n){return t=e.get(n),void 0===t&&(o=n.toString(),i=o.length,t=i>=29&&i<=100&&125===o.charCodeAt(i-1)&&o.charCodeAt(i-2)<=32&&93===o.charCodeAt(i-3)&&101===o.charCodeAt(i-4)&&100===o.charCodeAt(i-5)&&111===o.charCodeAt(i-6)&&99===o.charCodeAt(i-7)&&32===o.charCodeAt(i-8)&&101===o.charCodeAt(i-9)&&118===o.charCodeAt(i-10)&&105===o.charCodeAt(i-11)&&116===o.charCodeAt(i-12)&&97===o.charCodeAt(i-13)&&110===o.charCodeAt(i-14)&&88===o.charCodeAt(i-15),e.set(n,t)),t}}(),rt={};function at(e){switch(typeof e){case"number":return e>=0&&(0|e)===e;case"string":{const t=rt[e];if(void 0!==t)return t;const o=e.length;if(0===o)return rt[e]=!1;let i=0;for(let t=0;t<o;++t)if(i=e.charCodeAt(t),0===t&&48===i&&o>1||i<48||i>57)return rt[e]=!1;return rt[e]=!0}default:return!1}}function st(e){return`${e.toLowerCase()}:presentation`}const lt=new Map,ct=Object.freeze({define(e,t,o){const i=st(e);void 0===lt.get(i)?lt.set(i,t):lt.set(i,!1),o.register(Je.instance(i,t))},forTag(e,t){const o=st(e),i=lt.get(o);return!1===i?xe.findResponsibleContainer(t).get(o):i||null}});class dt{constructor(e,t){this.template=e||null,this.styles=void 0===t?null:Array.isArray(t)?f.create(t):t instanceof f?t:f.create([t])}applyTo(e){const t=e.$fastController;null===t.template&&(t.template=this.template),null===t.styles&&(t.styles=this.styles)}}class ut extends Ie{constructor(){super(...arguments),this._presentation=void 0}get $presentation(){return void 0===this._presentation&&(this._presentation=ct.forTag(this.tagName,this)),this._presentation}templateChanged(){void 0!==this.template&&(this.$fastController.template=this.template)}stylesChanged(){void 0!==this.styles&&(this.$fastController.styles=this.styles)}connectedCallback(){null!==this.$presentation&&this.$presentation.applyTo(this),super.connectedCallback()}static compose(e){return(t={})=>new ht(this===ut?class extends ut{}:this,e,t)}}function pt(e,t,o){return"function"==typeof e?e(t,o):e}Se([v],ut.prototype,"template",void 0),Se([v],ut.prototype,"styles",void 0);class ht{constructor(e,t,o){this.type=e,this.elementDefinition=t,this.overrideDefinition=o,this.definition=Object.assign(Object.assign({},this.elementDefinition),this.overrideDefinition)}register(e,t){const o=this.definition,i=this.overrideDefinition,n=`${o.prefix||t.elementPrefix}-${o.baseName}`;t.tryDefineElement({name:n,type:this.type,baseClass:this.elementDefinition.baseClass,callback:e=>{const t=new dt(pt(o.template,e,o),pt(o.styles,e,o));e.definePresentation(t);let n=pt(o.shadowOptions,e,o);e.shadowRootMode&&(n?i.shadowOptions||(n.mode=e.shadowRootMode):null!==n&&(n={mode:e.shadowRootMode})),e.defineElement({elementOptions:pt(o.elementOptions,e,o),shadowOptions:n,attributes:pt(o.attributes,e,o)})}})}}function ft(e){const t=e.parentElement;if(t)return t;{const t=e.getRootNode();if(t.host instanceof HTMLElement)return t.host}return null}const gt=document.createElement("div");class Et{setProperty(e,t){h.queueUpdate((()=>this.target.setProperty(e,t)))}removeProperty(e){h.queueUpdate((()=>this.target.removeProperty(e)))}}class Tt extends Et{constructor(){super();const e=new CSSStyleSheet;this.target=e.cssRules[e.insertRule(":root{}")].style,document.adoptedStyleSheets=[...document.adoptedStyleSheets,e]}}class Nt extends Et{constructor(){super(),this.style=document.createElement("style"),document.head.appendChild(this.style);const{sheet:e}=this.style;if(e){const t=e.insertRule(":root{}",e.cssRules.length);this.target=e.cssRules[t].style}}}class mt{constructor(e){this.store=new Map,this.target=null;const t=e.$fastController;this.style=document.createElement("style"),t.addStyles(this.style),y.getNotifier(t).subscribe(this,"isConnected"),this.handleChange(t,"isConnected")}targetChanged(){if(null!==this.target)for(const[e,t]of this.store.entries())this.target.setProperty(e,t)}setProperty(e,t){this.store.set(e,t),h.queueUpdate((()=>{null!==this.target&&this.target.setProperty(e,t)}))}removeProperty(e){this.store.delete(e),h.queueUpdate((()=>{null!==this.target&&this.target.removeProperty(e)}))}handleChange(e,t){const{sheet:o}=this.style;if(o){const e=o.insertRule(":host{}",o.cssRules.length);this.target=o.cssRules[e].style}else this.target=null}}Se([v],mt.prototype,"target",void 0);class _t{constructor(e){this.target=e.style}setProperty(e,t){h.queueUpdate((()=>this.target.setProperty(e,t)))}removeProperty(e){h.queueUpdate((()=>this.target.removeProperty(e)))}}class Ot{setProperty(e,t){Ot.properties[e]=t;for(const o of Ot.roots.values())bt.getOrCreate(Ot.normalizeRoot(o)).setProperty(e,t)}removeProperty(e){delete Ot.properties[e];for(const t of Ot.roots.values())bt.getOrCreate(Ot.normalizeRoot(t)).removeProperty(e)}static registerRoot(e){const{roots:t}=Ot;if(!t.has(e)){t.add(e);const o=bt.getOrCreate(this.normalizeRoot(e));for(const e in Ot.properties)o.setProperty(e,Ot.properties[e])}}static unregisterRoot(e){const{roots:t}=Ot;if(t.has(e)){t.delete(e);const o=bt.getOrCreate(Ot.normalizeRoot(e));for(const e in Ot.properties)o.removeProperty(e)}}static normalizeRoot(e){return e===gt?document:e}}Ot.roots=new Set,Ot.properties={};const At=new WeakMap,It=h.supportsAdoptedStyleSheets?class extends Et{constructor(e){super();const t=new CSSStyleSheet;this.target=t.cssRules[t.insertRule(":host{}")].style,e.$fastController.addStyles(f.create([t]))}}:mt,bt=Object.freeze({getOrCreate(e){if(At.has(e))return At.get(e);let t;return t=e===gt?new Ot:e instanceof Document?h.supportsAdoptedStyleSheets?new Tt:new Nt:e instanceof Ie?new It(e):new _t(e),At.set(e,t),t}});class St extends e{constructor(e){super(),this.subscribers=new WeakMap,this._appliedTo=new Set,this.name=e.name,null!==e.cssCustomPropertyName&&(this.cssCustomProperty=`--${e.cssCustomPropertyName}`,this.cssVar=`var(${this.cssCustomProperty})`),this.id=St.uniqueId(),St.tokensById.set(this.id,this)}get appliedTo(){return[...this._appliedTo]}static from(e){return new St({name:"string"==typeof e?e:e.name,cssCustomPropertyName:"string"==typeof e?e:void 0===e.cssCustomPropertyName?e.name:e.cssCustomPropertyName})}static isCSSDesignToken(e){return"string"==typeof e.cssCustomProperty}static isDerivedDesignTokenValue(e){return"function"==typeof e}static getTokenById(e){return St.tokensById.get(e)}getOrCreateSubscriberSet(e=this){return this.subscribers.get(e)||this.subscribers.set(e,new Set)&&this.subscribers.get(e)}createCSS(){return this.cssVar||""}getValueFor(e){const t=wt.getOrCreate(e).get(this);if(void 0!==t)return t;throw new Error(`Value could not be retrieved for token named "${this.name}". Ensure the value is set for ${e} or an ancestor of ${e}.`)}setValueFor(e,t){return this._appliedTo.add(e),t instanceof St&&(t=this.alias(t)),wt.getOrCreate(e).set(this,t),this}deleteValueFor(e){return this._appliedTo.delete(e),wt.existsFor(e)&&wt.getOrCreate(e).delete(this),this}withDefault(e){return this.setValueFor(gt,e),this}subscribe(e,t){const o=this.getOrCreateSubscriberSet(t);t&&!wt.existsFor(t)&&wt.getOrCreate(t),o.has(e)||o.add(e)}unsubscribe(e,t){const o=this.subscribers.get(t||this);o&&o.has(e)&&o.delete(e)}notify(e){const t=Object.freeze({token:this,target:e});this.subscribers.has(this)&&this.subscribers.get(this).forEach((e=>e.handleChange(t))),this.subscribers.has(e)&&this.subscribers.get(e).forEach((e=>e.handleChange(t)))}alias(e){return t=>e.getValueFor(t)}}St.uniqueId=(()=>{let e=0;return()=>(e++,e.toString(16))})(),St.tokensById=new Map;class Rt{constructor(e,t,o){this.source=e,this.token=t,this.node=o,this.dependencies=new Set,this.observer=y.binding(e,this,!1),this.observer.handleChange=this.observer.call,this.handleChange()}disconnect(){this.observer.disconnect()}handleChange(){this.node.store.set(this.token,this.observer.observe(this.node.target,M))}}class yt{constructor(){this.values=new Map}set(e,t){this.values.get(e)!==t&&(this.values.set(e,t),y.getNotifier(this).notify(e.id))}get(e){return y.track(this,e.id),this.values.get(e)}delete(e){this.values.delete(e)}all(){return this.values.entries()}}const vt=new WeakMap,Ct=new WeakMap;class wt{constructor(e){this.target=e,this.store=new yt,this.children=[],this.assignedValues=new Map,this.reflecting=new Set,this.bindingObservers=new Map,this.tokenValueChangeHandler={handleChange:(e,t)=>{const o=St.getTokenById(t);if(o&&(o.notify(this.target),St.isCSSDesignToken(o))){const t=this.parent,i=this.isReflecting(o);if(t){const n=t.get(o),r=e.get(o);n===r||i?n===r&&i&&this.stopReflectToCSS(o):this.reflectToCSS(o)}else i||this.reflectToCSS(o)}}},vt.set(e,this),y.getNotifier(this.store).subscribe(this.tokenValueChangeHandler),e instanceof Ie?e.$fastController.addBehaviors([this]):e.isConnected&&this.bind()}static getOrCreate(e){return vt.get(e)||new wt(e)}static existsFor(e){return vt.has(e)}static findParent(e){if(gt!==e.target){let t=ft(e.target);for(;null!==t;){if(vt.has(t))return vt.get(t);t=ft(t)}return wt.getOrCreate(gt)}return null}static findClosestAssignedNode(e,t){let o=t;do{if(o.has(e))return o;o=o.parent?o.parent:o.target!==gt?wt.getOrCreate(gt):null}while(null!==o);return null}get parent(){return Ct.get(this)||null}has(e){return this.assignedValues.has(e)}get(e){const t=this.store.get(e);if(void 0!==t)return t;const o=this.getRaw(e);return void 0!==o?(this.hydrate(e,o),this.get(e)):void 0}getRaw(e){var t;return this.assignedValues.has(e)?this.assignedValues.get(e):null===(t=wt.findClosestAssignedNode(e,this))||void 0===t?void 0:t.getRaw(e)}set(e,t){St.isDerivedDesignTokenValue(this.assignedValues.get(e))&&this.tearDownBindingObserver(e),this.assignedValues.set(e,t),St.isDerivedDesignTokenValue(t)?this.setupBindingObserver(e,t):this.store.set(e,t)}delete(e){this.assignedValues.delete(e),this.tearDownBindingObserver(e);const t=this.getRaw(e);t?this.hydrate(e,t):this.store.delete(e)}bind(){const e=wt.findParent(this);e&&e.appendChild(this);for(const e of this.assignedValues.keys())e.notify(this.target)}unbind(){this.parent&&Ct.get(this).removeChild(this)}appendChild(e){e.parent&&Ct.get(e).removeChild(e);const t=this.children.filter((t=>e.contains(t)));Ct.set(e,this),this.children.push(e),t.forEach((t=>e.appendChild(t))),y.getNotifier(this.store).subscribe(e);for(const[t,o]of this.store.all())e.hydrate(t,this.bindingObservers.has(t)?this.getRaw(t):o)}removeChild(e){const t=this.children.indexOf(e);return-1!==t&&this.children.splice(t,1),y.getNotifier(this.store).unsubscribe(e),e.parent===this&&Ct.delete(e)}contains(e){return function(e,t){let o=t;for(;null!==o;){if(o===e)return!0;o=ft(o)}return!1}(this.target,e.target)}reflectToCSS(e){this.isReflecting(e)||(this.reflecting.add(e),wt.cssCustomPropertyReflector.startReflection(e,this.target))}stopReflectToCSS(e){this.isReflecting(e)&&(this.reflecting.delete(e),wt.cssCustomPropertyReflector.stopReflection(e,this.target))}isReflecting(e){return this.reflecting.has(e)}handleChange(e,t){const o=St.getTokenById(t);o&&this.hydrate(o,this.getRaw(o))}hydrate(e,t){if(!this.has(e)){const o=this.bindingObservers.get(e);St.isDerivedDesignTokenValue(t)?o?o.source!==t&&(this.tearDownBindingObserver(e),this.setupBindingObserver(e,t)):this.setupBindingObserver(e,t):(o&&this.tearDownBindingObserver(e),this.store.set(e,t))}}setupBindingObserver(e,t){const o=new Rt(t,e,this);return this.bindingObservers.set(e,o),o}tearDownBindingObserver(e){return!!this.bindingObservers.has(e)&&(this.bindingObservers.get(e).disconnect(),this.bindingObservers.delete(e),!0)}}wt.cssCustomPropertyReflector=new class{startReflection(e,t){e.subscribe(this,t),this.handleChange({token:e,target:t})}stopReflection(e,t){e.unsubscribe(this,t),this.remove(e,t)}handleChange(e){const{token:t,target:o}=e;this.add(t,o)}add(e,t){bt.getOrCreate(t).setProperty(e.cssCustomProperty,this.resolveCSSValue(wt.getOrCreate(t).get(e)))}remove(e,t){bt.getOrCreate(t).removeProperty(e.cssCustomProperty)}resolveCSSValue(e){return e&&"function"==typeof e.createCSS?e.createCSS():e}},Se([v],wt.prototype,"children",void 0);const Mt=Object.freeze({create:function(e){return St.from(e)},notifyConnection:e=>!(!e.isConnected||!wt.existsFor(e)||(wt.getOrCreate(e).bind(),0)),notifyDisconnection:e=>!(e.isConnected||!wt.existsFor(e)||(wt.getOrCreate(e).unbind(),0)),registerRoot(e=gt){Ot.registerRoot(e)},unregisterRoot(e=gt){Ot.unregisterRoot(e)}}),Dt=Object.freeze({definitionCallbackOnly:null,ignoreDuplicate:Symbol()}),Pt=new Map,xt=new Map;let kt=null;const Lt=xe.createInterface((e=>e.cachedCallback((e=>(null===kt&&(kt=new Gt(null,e)),kt))))),Ft=Object.freeze({tagFor:e=>xt.get(e),responsibleFor(e){const t=e.$$designSystem$$;return t||xe.findResponsibleContainer(e).get(Lt)},getOrCreate(e){if(!e)return null===kt&&(kt=xe.getOrCreateDOMContainer().get(Lt)),kt;const t=e.$$designSystem$$;if(t)return t;const o=xe.getOrCreateDOMContainer(e);if(o.has(Lt,!1))return o.get(Lt);{const t=new Gt(e,o);return o.register(Je.instance(Lt,t)),t}}});class Gt{constructor(e,t){this.owner=e,this.container=t,this.designTokensInitialized=!1,this.prefix="fast",this.shadowRootMode=void 0,this.disambiguate=()=>Dt.definitionCallbackOnly,null!==e&&(e.$$designSystem$$=this)}withPrefix(e){return this.prefix=e,this}withShadowRootMode(e){return this.shadowRootMode=e,this}withElementDisambiguation(e){return this.disambiguate=e,this}withDesignTokenRoot(e){return this.designTokenRoot=e,this}register(...e){const t=this.container,o=[],i=this.disambiguate,n=this.shadowRootMode,r={elementPrefix:this.prefix,tryDefineElement(e,r,a){const s=function(e,t,o){return"string"==typeof e?{name:e,type:t,callback:o}:e}(e,r,a),{name:l,callback:c,baseClass:d}=s;let{type:u}=s,p=l,h=Pt.get(p),f=!0;for(;h;){const e=i(p,u,h);switch(e){case Dt.ignoreDuplicate:return;case Dt.definitionCallbackOnly:f=!1,h=void 0;break;default:p=e,h=Pt.get(p)}}f&&((xt.has(u)||u===ut)&&(u=class extends u{}),Pt.set(p,u),xt.set(u,p),d&&xt.set(d,p)),o.push(new Bt(t,p,u,n,c,f))}};this.designTokensInitialized||(this.designTokensInitialized=!0,null!==this.designTokenRoot&&Mt.registerRoot(this.designTokenRoot)),t.registerWithContext(r,...e);for(const e of o)e.callback(e),e.willDefine&&null!==e.definition&&e.definition.define();return this}}class Bt{constructor(e,t,o,i,n,r){this.container=e,this.name=t,this.type=o,this.shadowRootMode=i,this.callback=n,this.willDefine=r,this.definition=null}definePresentation(e){ct.define(this.name,e,this.container)}defineElement(e){this.definition=new Te(this.type,Object.assign(Object.assign({},e),{name:this.name}))}tagFor(e){return Ft.tagFor(e)}}function Wt(e,t,o,i){var n,r=arguments.length,a=r<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,o):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,o,i);else for(var s=e.length-1;s>=0;s--)(n=e[s])&&(a=(r<3?n(a):r>3?n(t,o,a):n(t,o))||a);return r>3&&a&&Object.defineProperty(t,o,a),a}function Ut(e,t,o){return isNaN(e)||e<=t?t:e>=o?o:e}function Vt(e,t,o){return isNaN(e)||e<=t?0:e>=o?1:e/(o-t)}function $t(e,t,o){return isNaN(e)?t:t+e*(o-t)}function Ht(e){return e*(Math.PI/180)}function zt(e,t,o){return isNaN(e)||e<=0?t:e>=1?o:t+e*(o-t)}function jt(e,t,o){if(e<=0)return t%360;if(e>=1)return o%360;const i=(t-o+360)%360;return i<=(o-t+360)%360?(t-i*e+360)%360:(t+i*e+360)%360}function Yt(e,t){const o=Math.pow(10,t);return Math.round(e*o)/o}Math.PI;class Zt{constructor(e,t,o,i){this.r=e,this.g=t,this.b=o,this.a="number"!=typeof i||isNaN(i)?1:i}static fromObject(e){return!e||isNaN(e.r)||isNaN(e.g)||isNaN(e.b)?null:new Zt(e.r,e.g,e.b,e.a)}equalValue(e){return this.r===e.r&&this.g===e.g&&this.b===e.b&&this.a===e.a}toStringHexRGB(){return"#"+[this.r,this.g,this.b].map(this.formatHexValue).join("")}toStringHexRGBA(){return this.toStringHexRGB()+this.formatHexValue(this.a)}toStringHexARGB(){return"#"+[this.a,this.r,this.g,this.b].map(this.formatHexValue).join("")}toStringWebRGB(){return`rgb(${Math.round($t(this.r,0,255))},${Math.round($t(this.g,0,255))},${Math.round($t(this.b,0,255))})`}toStringWebRGBA(){return`rgba(${Math.round($t(this.r,0,255))},${Math.round($t(this.g,0,255))},${Math.round($t(this.b,0,255))},${Ut(this.a,0,1)})`}roundToPrecision(e){return new Zt(Yt(this.r,e),Yt(this.g,e),Yt(this.b,e),Yt(this.a,e))}clamp(){return new Zt(Ut(this.r,0,1),Ut(this.g,0,1),Ut(this.b,0,1),Ut(this.a,0,1))}toObject(){return{r:this.r,g:this.g,b:this.b,a:this.a}}formatHexValue(e){return function(e){const t=Math.round(Ut(e,0,255)).toString(16);return 1===t.length?"0"+t:t}($t(e,0,255))}}const Kt=/^#((?:[0-9a-f]{6}|[0-9a-f]{3}))$/i;function qt(e){const t=Kt.exec(e);if(null===t)return null;let o=t[1];if(3===o.length){const e=o.charAt(0),t=o.charAt(1),i=o.charAt(2);o=e.concat(e,t,t,i,i)}const i=parseInt(o,16);return isNaN(i)?null:new Zt(Vt((16711680&i)>>>16,0,255),Vt((65280&i)>>>8,0,255),Vt(255&i,0,255),1)}class Xt extends class{constructor(e){this.listenerCache=new WeakMap,this.query=e}bind(e){const{query:t}=this,o=this.constructListener(e);o.bind(t)(),t.addListener(o),this.listenerCache.set(e,o)}unbind(e){const t=this.listenerCache.get(e);t&&(this.query.removeListener(t),this.listenerCache.delete(e))}}{constructor(e,t){super(e),this.styles=t}static with(e){return t=>new Xt(e,t)}constructListener(e){let t=!1;const o=this.styles;return function(){const{matches:i}=this;i&&!t?(e.$fastController.addStyles(o),t=i):!i&&t&&(e.$fastController.removeStyles(o),t=i)}}unbind(e){super.unbind(e),e.$fastController.removeStyles(this.styles)}}const Qt=Xt.with(window.matchMedia("(forced-colors)"));function Jt(e){return`:host([hidden]){display:none}:host{display:${e}}`}var eo,to;Xt.with(window.matchMedia("(prefers-color-scheme: dark)")),Xt.with(window.matchMedia("(prefers-color-scheme: light)")),(to=eo||(eo={})).Canvas="Canvas",to.CanvasText="CanvasText",to.LinkText="LinkText",to.VisitedText="VisitedText",to.ActiveText="ActiveText",to.ButtonFace="ButtonFace",to.ButtonText="ButtonText",to.Field="Field",to.FieldText="FieldText",to.Highlight="Highlight",to.HighlightText="HighlightText",to.GrayText="GrayText";class oo{constructor(e,t,o){this.h=e,this.s=t,this.l=o}static fromObject(e){return!e||isNaN(e.h)||isNaN(e.s)||isNaN(e.l)?null:new oo(e.h,e.s,e.l)}equalValue(e){return this.h===e.h&&this.s===e.s&&this.l===e.l}roundToPrecision(e){return new oo(Yt(this.h,e),Yt(this.s,e),Yt(this.l,e))}toObject(){return{h:this.h,s:this.s,l:this.l}}}class io{constructor(e,t,o){this.h=e,this.s=t,this.v=o}static fromObject(e){return!e||isNaN(e.h)||isNaN(e.s)||isNaN(e.v)?null:new io(e.h,e.s,e.v)}equalValue(e){return this.h===e.h&&this.s===e.s&&this.v===e.v}roundToPrecision(e){return new io(Yt(this.h,e),Yt(this.s,e),Yt(this.v,e))}toObject(){return{h:this.h,s:this.s,v:this.v}}}class no{constructor(e,t,o){this.l=e,this.a=t,this.b=o}static fromObject(e){return!e||isNaN(e.l)||isNaN(e.a)||isNaN(e.b)?null:new no(e.l,e.a,e.b)}equalValue(e){return this.l===e.l&&this.a===e.a&&this.b===e.b}roundToPrecision(e){return new no(Yt(this.l,e),Yt(this.a,e),Yt(this.b,e))}toObject(){return{l:this.l,a:this.a,b:this.b}}}no.epsilon=216/24389,no.kappa=24389/27;class ro{constructor(e,t,o){this.l=e,this.c=t,this.h=o}static fromObject(e){return!e||isNaN(e.l)||isNaN(e.c)||isNaN(e.h)?null:new ro(e.l,e.c,e.h)}equalValue(e){return this.l===e.l&&this.c===e.c&&this.h===e.h}roundToPrecision(e){return new ro(Yt(this.l,e),Yt(this.c,e),Yt(this.h,e))}toObject(){return{l:this.l,c:this.c,h:this.h}}}class ao{constructor(e,t,o){this.x=e,this.y=t,this.z=o}static fromObject(e){return!e||isNaN(e.x)||isNaN(e.y)||isNaN(e.z)?null:new ao(e.x,e.y,e.z)}equalValue(e){return this.x===e.x&&this.y===e.y&&this.z===e.z}roundToPrecision(e){return new ao(Yt(this.x,e),Yt(this.y,e),Yt(this.z,e))}toObject(){return{x:this.x,y:this.y,z:this.z}}}function so(e){return.2126*e.r+.7152*e.g+.0722*e.b}function lo(e){function t(e){return e<=.03928?e/12.92:Math.pow((e+.055)/1.055,2.4)}return so(new Zt(t(e.r),t(e.g),t(e.b),1))}ao.whitePoint=new ao(.95047,1,1.08883);const co=(e,t)=>(e+.05)/(t+.05);function uo(e,t){const o=lo(e),i=lo(t);return o>i?co(o,i):co(i,o)}function po(e){const t=Math.max(e.r,e.g,e.b),o=Math.min(e.r,e.g,e.b),i=t-o;let n=0;0!==i&&(n=t===e.r?(e.g-e.b)/i%6*60:t===e.g?60*((e.b-e.r)/i+2):60*((e.r-e.g)/i+4)),n<0&&(n+=360);const r=(t+o)/2;let a=0;return 0!==i&&(a=i/(1-Math.abs(2*r-1))),new oo(n,a,r)}function ho(e,t=1){const o=(1-Math.abs(2*e.l-1))*e.s,i=o*(1-Math.abs(e.h/60%2-1)),n=e.l-o/2;let r=0,a=0,s=0;return e.h<60?(r=o,a=i,s=0):e.h<120?(r=i,a=o,s=0):e.h<180?(r=0,a=o,s=i):e.h<240?(r=0,a=i,s=o):e.h<300?(r=i,a=0,s=o):e.h<360&&(r=o,a=0,s=i),new Zt(r+n,a+n,s+n,t)}function fo(e){const t=Math.max(e.r,e.g,e.b),o=t-Math.min(e.r,e.g,e.b);let i=0;0!==o&&(i=t===e.r?(e.g-e.b)/o%6*60:t===e.g?60*((e.b-e.r)/o+2):60*((e.r-e.g)/o+4)),i<0&&(i+=360);let n=0;return 0!==t&&(n=o/t),new io(i,n,t)}function go(e){function t(e){return e<=.04045?e/12.92:Math.pow((e+.055)/1.055,2.4)}const o=t(e.r),i=t(e.g),n=t(e.b);return new ao(.4124564*o+.3575761*i+.1804375*n,.2126729*o+.7151522*i+.072175*n,.0193339*o+.119192*i+.9503041*n)}function Eo(e,t=1){function o(e){return e<=.0031308?12.92*e:1.055*Math.pow(e,1/2.4)-.055}const i=o(3.2404542*e.x-1.5371385*e.y-.4985314*e.z),n=o(-.969266*e.x+1.8760108*e.y+.041556*e.z),r=o(.0556434*e.x-.2040259*e.y+1.0572252*e.z);return new Zt(i,n,r,t)}function To(e){return function(e){function t(e){return e>no.epsilon?Math.pow(e,1/3):(no.kappa*e+16)/116}const o=t(e.x/ao.whitePoint.x),i=t(e.y/ao.whitePoint.y),n=t(e.z/ao.whitePoint.z);return new no(116*i-16,500*(o-i),200*(i-n))}(go(e))}function No(e,t=1){return Eo(function(e){const t=(e.l+16)/116,o=t+e.a/500,i=t-e.b/200,n=Math.pow(o,3),r=Math.pow(t,3),a=Math.pow(i,3);let s=0;s=n>no.epsilon?n:(116*o-16)/no.kappa;let l=0;l=e.l>no.epsilon*no.kappa?r:e.l/no.kappa;let c=0;return c=a>no.epsilon?a:(116*i-16)/no.kappa,s=ao.whitePoint.x*s,l=ao.whitePoint.y*l,c=ao.whitePoint.z*c,new ao(s,l,c)}(e),t)}function mo(e){return function(e){let t=0;(Math.abs(e.b)>.001||Math.abs(e.a)>.001)&&(t=Math.atan2(e.b,e.a)*(180/Math.PI)),t<0&&(t+=360);const o=Math.sqrt(e.a*e.a+e.b*e.b);return new ro(e.l,o,t)}(To(e))}function _o(e,t=1){return No(function(e){let t=0,o=0;return 0!==e.h&&(t=Math.cos(Ht(e.h))*e.c,o=Math.sin(Ht(e.h))*e.c),new no(e.l,t,o)}(e),t)}function Oo(e,t,o=18){const i=mo(e);let n=i.c+t*o;return n<0&&(n=0),_o(new ro(i.l,n,i.h))}function Ao(e,t){return e*t}function Io(e,t){return new Zt(Ao(e.r,t.r),Ao(e.g,t.g),Ao(e.b,t.b),1)}function bo(e,t){return Ut(e<.5?2*t*e:1-2*(1-t)*(1-e),0,1)}function So(e,t){return new Zt(bo(e.r,t.r),bo(e.g,t.g),bo(e.b,t.b),1)}var Ro,yo,vo,Co;function wo(e,t,o,i){if(isNaN(e)||e<=0)return o;if(e>=1)return i;switch(t){case vo.HSL:return ho(function(e,t,o){return isNaN(e)||e<=0?t:e>=1?o:new oo(jt(e,t.h,o.h),zt(e,t.s,o.s),zt(e,t.l,o.l))}(e,po(o),po(i)));case vo.HSV:return function(e,t=1){const o=e.s*e.v,i=o*(1-Math.abs(e.h/60%2-1)),n=e.v-o;let r=0,a=0,s=0;return e.h<60?(r=o,a=i,s=0):e.h<120?(r=i,a=o,s=0):e.h<180?(r=0,a=o,s=i):e.h<240?(r=0,a=i,s=o):e.h<300?(r=i,a=0,s=o):e.h<360&&(r=o,a=0,s=i),new Zt(r+n,a+n,s+n,t)}(function(e,t,o){return isNaN(e)||e<=0?t:e>=1?o:new io(jt(e,t.h,o.h),zt(e,t.s,o.s),zt(e,t.v,o.v))}(e,fo(o),fo(i)));case vo.XYZ:return Eo(function(e,t,o){return isNaN(e)||e<=0?t:e>=1?o:new ao(zt(e,t.x,o.x),zt(e,t.y,o.y),zt(e,t.z,o.z))}(e,go(o),go(i)));case vo.LAB:return No(function(e,t,o){return isNaN(e)||e<=0?t:e>=1?o:new no(zt(e,t.l,o.l),zt(e,t.a,o.a),zt(e,t.b,o.b))}(e,To(o),To(i)));case vo.LCH:return _o(function(e,t,o){return isNaN(e)||e<=0?t:e>=1?o:new ro(zt(e,t.l,o.l),zt(e,t.c,o.c),jt(e,t.h,o.h))}(e,mo(o),mo(i)));default:return function(e,t,o){return isNaN(e)||e<=0?t:e>=1?o:new Zt(zt(e,t.r,o.r),zt(e,t.g,o.g),zt(e,t.b,o.b),zt(e,t.a,o.a))}(e,o,i)}}(yo=Ro||(Ro={}))[yo.Burn=0]="Burn",yo[yo.Color=1]="Color",yo[yo.Darken=2]="Darken",yo[yo.Dodge=3]="Dodge",yo[yo.Lighten=4]="Lighten",yo[yo.Multiply=5]="Multiply",yo[yo.Overlay=6]="Overlay",yo[yo.Screen=7]="Screen",(Co=vo||(vo={}))[Co.RGB=0]="RGB",Co[Co.HSL=1]="HSL",Co[Co.HSV=2]="HSV",Co[Co.XYZ=3]="XYZ",Co[Co.LAB=4]="LAB",Co[Co.LCH=5]="LCH";class Mo{constructor(e){if(null==e||0===e.length)throw new Error("The stops argument must be non-empty");this.stops=this.sortColorScaleStops(e)}static createBalancedColorScale(e){if(null==e||0===e.length)throw new Error("The colors argument must be non-empty");const t=new Array(e.length);for(let o=0;o<e.length;o++)0===o?t[o]={color:e[o],position:0}:o===e.length-1?t[o]={color:e[o],position:1}:t[o]={color:e[o],position:o*(1/(e.length-1))};return new Mo(t)}getColor(e,t=vo.RGB){if(1===this.stops.length)return this.stops[0].color;if(e<=0)return this.stops[0].color;if(e>=1)return this.stops[this.stops.length-1].color;let o=0;for(let t=0;t<this.stops.length;t++)this.stops[t].position<=e&&(o=t);let i=o+1;return i>=this.stops.length&&(i=this.stops.length-1),wo((e-this.stops[o].position)*(1/(this.stops[i].position-this.stops[o].position)),t,this.stops[o].color,this.stops[i].color)}trim(e,t,o=vo.RGB){if(e<0||t>1||t<e)throw new Error("Invalid bounds");if(e===t)return new Mo([{color:this.getColor(e,o),position:0}]);const i=[];for(let o=0;o<this.stops.length;o++)this.stops[o].position>=e&&this.stops[o].position<=t&&i.push(this.stops[o]);if(0===i.length)return new Mo([{color:this.getColor(e),position:e},{color:this.getColor(t),position:t}]);i[0].position!==e&&i.unshift({color:this.getColor(e),position:e}),i[i.length-1].position!==t&&i.push({color:this.getColor(t),position:t});const n=t-e,r=new Array(i.length);for(let t=0;t<i.length;t++)r[t]={color:i[t].color,position:(i[t].position-e)/n};return new Mo(r)}findNextColor(e,t,o=!1,i=vo.RGB,n=.005,r=32){isNaN(e)||e<=0?e=0:e>=1&&(e=1);const a=this.getColor(e,i),s=o?0:1;if(uo(a,this.getColor(s,i))<=t)return s;let l=o?0:e,c=o?e:0,d=s,u=0;for(;u<=r;){d=Math.abs(c-l)/2+l;const e=uo(a,this.getColor(d,i));if(Math.abs(e-t)<=n)return d;e>t?o?l=d:c=d:o?c=d:l=d,u++}return d}clone(){const e=new Array(this.stops.length);for(let t=0;t<e.length;t++)e[t]={color:this.stops[t].color,position:this.stops[t].position};return new Mo(e)}sortColorScaleStops(e){return e.sort(((e,t)=>{const o=e.position,i=t.position;return o<i?-1:o>i?1:0}))}}class Do{constructor(e){this.config=Object.assign({},Do.defaultPaletteConfig,e),this.palette=[],this.updatePaletteColors()}updatePaletteGenerationValues(e){let t=!1;for(const o in e)this.config[o]&&(this.config[o].equalValue?this.config[o].equalValue(e[o])||(this.config[o]=e[o],t=!0):e[o]!==this.config[o]&&(this.config[o]=e[o],t=!0));return t&&this.updatePaletteColors(),t}updatePaletteColors(){const e=this.generatePaletteColorScale();for(let t=0;t<this.config.steps;t++)this.palette[t]=e.getColor(t/(this.config.steps-1),this.config.interpolationMode)}generatePaletteColorScale(){const e=po(this.config.baseColor),t=new Mo([{position:0,color:this.config.scaleColorLight},{position:.5,color:this.config.baseColor},{position:1,color:this.config.scaleColorDark}]).trim(this.config.clipLight,1-this.config.clipDark);let o=t.getColor(0),i=t.getColor(1);if(e.s>=this.config.saturationAdjustmentCutoff&&(o=Oo(o,this.config.saturationLight),i=Oo(i,this.config.saturationDark)),0!==this.config.multiplyLight){const e=Io(this.config.baseColor,o);o=wo(this.config.multiplyLight,this.config.interpolationMode,o,e)}if(0!==this.config.multiplyDark){const e=Io(this.config.baseColor,i);i=wo(this.config.multiplyDark,this.config.interpolationMode,i,e)}if(0!==this.config.overlayLight){const e=So(this.config.baseColor,o);o=wo(this.config.overlayLight,this.config.interpolationMode,o,e)}if(0!==this.config.overlayDark){const e=So(this.config.baseColor,i);i=wo(this.config.overlayDark,this.config.interpolationMode,i,e)}return this.config.baseScalePosition?this.config.baseScalePosition<=0?new Mo([{position:0,color:this.config.baseColor},{position:1,color:i.clamp()}]):this.config.baseScalePosition>=1?new Mo([{position:0,color:o.clamp()},{position:1,color:this.config.baseColor}]):new Mo([{position:0,color:o.clamp()},{position:this.config.baseScalePosition,color:this.config.baseColor},{position:1,color:i.clamp()}]):new Mo([{position:0,color:o.clamp()},{position:.5,color:this.config.baseColor},{position:1,color:i.clamp()}])}}Do.defaultPaletteConfig={baseColor:qt("#808080"),steps:11,interpolationMode:vo.RGB,scaleColorLight:new Zt(1,1,1,1),scaleColorDark:new Zt(0,0,0,1),clipLight:.185,clipDark:.16,saturationAdjustmentCutoff:.05,saturationLight:.35,saturationDark:1.25,overlayLight:0,overlayDark:.25,multiplyLight:0,multiplyDark:0,baseScalePosition:.5},Do.greyscalePaletteConfig={baseColor:qt("#808080"),steps:11,interpolationMode:vo.RGB,scaleColorLight:new Zt(1,1,1,1),scaleColorDark:new Zt(0,0,0,1),clipLight:0,clipDark:0,saturationAdjustmentCutoff:0,saturationLight:0,saturationDark:0,overlayLight:0,overlayDark:0,multiplyLight:0,multiplyDark:0,baseScalePosition:.5},Do.defaultPaletteConfig.scaleColorLight,Do.defaultPaletteConfig.scaleColorDark;class Po{constructor(e){this.palette=[],this.config=Object.assign({},Po.defaultPaletteConfig,e),this.regenPalettes()}regenPalettes(){let e=this.config.steps;(isNaN(e)||e<3)&&(e=3);const t=.14,o=new Zt(t,t,t,1),i=new Do(Object.assign(Object.assign({},Do.greyscalePaletteConfig),{baseColor:o,baseScalePosition:86/94,steps:e})).palette,n=(so(this.config.baseColor)+po(this.config.baseColor).l)/2,r=this.matchRelativeLuminanceIndex(n,i)/(e-1),a=this.matchRelativeLuminanceIndex(t,i)/(e-1),s=po(this.config.baseColor),l=ho(oo.fromObject({h:s.h,s:s.s,l:t})),c=ho(oo.fromObject({h:s.h,s:s.s,l:.06})),d=new Array(5);d[0]={position:0,color:new Zt(1,1,1,1)},d[1]={position:r,color:this.config.baseColor},d[2]={position:a,color:l},d[3]={position:.99,color:c},d[4]={position:1,color:new Zt(0,0,0,1)};const u=new Mo(d);this.palette=new Array(e);for(let t=0;t<e;t++){const o=u.getColor(t/(e-1),vo.RGB);this.palette[t]=o}}matchRelativeLuminanceIndex(e,t){let o=Number.MAX_VALUE,i=0,n=0;const r=t.length;for(;n<r;n++){const r=Math.abs(so(t[n])-e);r<o&&(o=r,i=n)}return i}}function xo(e,t){const o=e.relativeLuminance>t.relativeLuminance?e:t,i=e.relativeLuminance>t.relativeLuminance?t:e;return(o.relativeLuminance+.05)/(i.relativeLuminance+.05)}Po.defaultPaletteConfig={baseColor:qt("#808080"),steps:94};const ko=Object.freeze({create:(e,t,o)=>new Lo(e,t,o),from:e=>new Lo(e.r,e.g,e.b)});class Lo extends Zt{constructor(e,t,o){super(e,t,o,1),this.toColorString=this.toStringHexRGB,this.contrast=xo.bind(null,this),this.createCSS=this.toColorString,this.relativeLuminance=lo(this)}static fromObject(e){return new Lo(e.r,e.g,e.b)}}function Fo(e,t,o=0,i=e.length-1){if(i===o)return e[o];const n=Math.floor((i-o)/2)+o;return t(e[n])?Fo(e,t,o,n):Fo(e,t,n+1,i)}const Go=(-.1+Math.sqrt(.21))/2;function Bo(e){return function(e){return e.relativeLuminance<=Go}(e)?-1:1}const Wo=Object.freeze({create:e=>Uo.from(e)});class Uo{constructor(e,t){this.closestIndexCache=new Map,this.source=e,this.swatches=t,this.reversedSwatches=Object.freeze([...this.swatches].reverse()),this.lastIndex=this.swatches.length-1}colorContrast(e,t,o,i){void 0===o&&(o=this.closestIndexOf(e));let n=this.swatches;const r=this.lastIndex;let a=o;return void 0===i&&(i=Bo(e)),-1===i&&(n=this.reversedSwatches,a=r-a),Fo(n,(o=>xo(e,o)>=t),a,r)}get(e){return this.swatches[e]||this.swatches[Ut(e,0,this.lastIndex)]}closestIndexOf(e){if(this.closestIndexCache.has(e.relativeLuminance))return this.closestIndexCache.get(e.relativeLuminance);let t=this.swatches.indexOf(e);if(-1!==t)return this.closestIndexCache.set(e.relativeLuminance,t),t;const o=this.swatches.reduce(((t,o)=>Math.abs(o.relativeLuminance-e.relativeLuminance)<Math.abs(t.relativeLuminance-e.relativeLuminance)?o:t));return t=this.swatches.indexOf(o),this.closestIndexCache.set(e.relativeLuminance,t),t}static from(e){return new Uo(e,Object.freeze(new Po({baseColor:Zt.fromObject(e)}).palette.map((e=>{const t=qt(e.toStringHexRGB());return ko.create(t.r,t.g,t.b)}))))}}var Vo,$o;($o=Vo||(Vo={})).ltr="ltr",$o.rtl="rtl";const Ho=ko.create(1,1,1),zo=ko.create(0,0,0),jo=ko.create(.5,.5,.5),Yo=qt("#0078D4"),Zo=ko.create(Yo.r,Yo.g,Yo.b);function Ko(e){return ko.create(e,e,e)}var qo,Xo;function Qo(e,t,o,i,n,r){return Math.max(e.closestIndexOf(Ko(t))+o,i,n,r)}(Xo=qo||(qo={}))[Xo.LightMode=1]="LightMode",Xo[Xo.DarkMode=.23]="DarkMode";const{create:Jo}=Mt,ei=Jo("direction").withDefault(Vo.ltr),ti=Jo("disabled-opacity").withDefault(.3),oi=Jo("base-height-multiplier").withDefault(8),ii=Jo("base-horizontal-spacing-multiplier").withDefault(3),ni=Jo("density").withDefault(0),ri=Jo("design-unit").withDefault(4),ai=Jo("control-corner-radius").withDefault(4),si=(Jo("layer-corner-radius").withDefault(4),Jo("stroke-width").withDefault(1)),li=Jo("focus-stroke-width").withDefault(2),ci=Jo("body-font").withDefault("Segoe UI, sans-serif"),di=Jo("type-ramp-base-font-size").withDefault("14px"),ui=Jo("type-ramp-base-line-height").withDefault("20px"),pi=Jo("type-ramp-minus-1-font-size").withDefault("12px"),hi=Jo("type-ramp-minus-1-line-height").withDefault("16px"),fi=Jo("type-ramp-minus-2-font-size").withDefault("10px"),gi=Jo("type-ramp-minus-2-line-height").withDefault("14px"),Ei=Jo("type-ramp-plus-1-font-size").withDefault("16px"),Ti=Jo("type-ramp-plus-1-line-height").withDefault("22px"),Ni=Jo("type-ramp-plus-2-font-size").withDefault("20px"),mi=Jo("type-ramp-plus-2-line-height").withDefault("28px"),_i=Jo("type-ramp-plus-3-font-size").withDefault("24px"),Oi=Jo("type-ramp-plus-3-line-height").withDefault("32px"),Ai=Jo("type-ramp-plus-4-font-size").withDefault("28px"),Ii=Jo("type-ramp-plus-4-line-height").withDefault("36px"),bi=Jo("type-ramp-plus-5-font-size").withDefault("32px"),Si=Jo("type-ramp-plus-5-line-height").withDefault("40px"),Ri=Jo("type-ramp-plus-6-font-size").withDefault("40px"),yi=Jo("type-ramp-plus-6-line-height").withDefault("52px"),vi=Jo("base-layer-luminance").withDefault(qo.LightMode),Ci=Jo("accent-fill-rest-delta").withDefault(0),wi=Jo("accent-fill-hover-delta").withDefault(4),Mi=Jo("accent-fill-active-delta").withDefault(-5),Di=Jo("accent-fill-focus-delta").withDefault(0),Pi=Jo("accent-foreground-rest-delta").withDefault(0),xi=Jo("accent-foreground-hover-delta").withDefault(6),ki=Jo("accent-foreground-active-delta").withDefault(-4),Li=Jo("accent-foreground-focus-delta").withDefault(0),Fi=Jo("neutral-fill-rest-delta").withDefault(7),Gi=Jo("neutral-fill-hover-delta").withDefault(10),Bi=Jo("neutral-fill-active-delta").withDefault(5),Wi=Jo("neutral-fill-focus-delta").withDefault(0),Ui=Jo("neutral-fill-input-rest-delta").withDefault(0),Vi=Jo("neutral-fill-input-hover-delta").withDefault(0),$i=Jo("neutral-fill-input-active-delta").withDefault(0),Hi=Jo("neutral-fill-input-focus-delta").withDefault(0),zi=Jo("neutral-fill-inverse-rest-delta").withDefault(0),ji=Jo("neutral-fill-inverse-hover-delta").withDefault(-3),Yi=Jo("neutral-fill-inverse-active-delta").withDefault(7),Zi=Jo("neutral-fill-inverse-focus-delta").withDefault(0),Ki=Jo("neutral-fill-layer-rest-delta").withDefault(3),qi=Jo("neutral-fill-stealth-rest-delta").withDefault(0),Xi=Jo("neutral-fill-stealth-hover-delta").withDefault(5),Qi=Jo("neutral-fill-stealth-active-delta").withDefault(3),Ji=Jo("neutral-fill-stealth-focus-delta").withDefault(0),en=Jo("neutral-fill-strong-rest-delta").withDefault(0),tn=Jo("neutral-fill-strong-hover-delta").withDefault(8),on=Jo("neutral-fill-strong-active-delta").withDefault(-5),nn=Jo("neutral-fill-strong-focus-delta").withDefault(0),rn=Jo("neutral-stroke-rest-delta").withDefault(25),an=Jo("neutral-stroke-hover-delta").withDefault(40),sn=Jo("neutral-stroke-active-delta").withDefault(16),ln=Jo("neutral-stroke-focus-delta").withDefault(25),cn=Jo("neutral-stroke-divider-rest-delta").withDefault(8),dn=Jo("neutral-stroke-strong-hover-delta").withDefault(40),un=Jo("neutral-stroke-strong-active-delta").withDefault(16),pn=Jo("neutral-stroke-strong-focus-delta").withDefault(25),hn=Jo({name:"neutral-palette",cssCustomPropertyName:null}).withDefault(Wo.create(jo)),fn=Jo({name:"accent-palette",cssCustomPropertyName:null}).withDefault(Wo.create(Zo)),gn=Jo({name:"neutral-layer-card-container-recipe",cssCustomPropertyName:null}).withDefault({evaluate:e=>function(e,t,o){const i=Ut(e.closestIndexOf(Ko(t))-o,0,e.swatches.length-1);return e.get(i+o)}(hn.getValueFor(e),vi.getValueFor(e),Ki.getValueFor(e))}),En=(Jo("neutral-layer-card-container").withDefault((e=>gn.getValueFor(e).evaluate(e))),Jo({name:"neutral-layer-floating-recipe",cssCustomPropertyName:null}).withDefault({evaluate:e=>function(e,t,o){const i=e.closestIndexOf(Ko(t))-o;return e.get(i-o)}(hn.getValueFor(e),vi.getValueFor(e),Ki.getValueFor(e))})),Tn=(Jo("neutral-layer-floating").withDefault((e=>En.getValueFor(e).evaluate(e))),Jo({name:"neutral-layer-1-recipe",cssCustomPropertyName:null}).withDefault({evaluate:e=>function(e,t){return e.get(e.closestIndexOf(Ko(t)))}(hn.getValueFor(e),vi.getValueFor(e))})),Nn=Jo("neutral-layer-1").withDefault((e=>Tn.getValueFor(e).evaluate(e))),mn=Jo({name:"neutral-layer-2-recipe",cssCustomPropertyName:null}).withDefault({evaluate:e=>{return t=hn.getValueFor(e),o=vi.getValueFor(e),i=Ki.getValueFor(e),n=Fi.getValueFor(e),r=Gi.getValueFor(e),a=Bi.getValueFor(e),t.get(Qo(t,o,i,n,r,a));var t,o,i,n,r,a}}),_n=(Jo("neutral-layer-2").withDefault((e=>mn.getValueFor(e).evaluate(e))),Jo({name:"neutral-layer-3-recipe",cssCustomPropertyName:null}).withDefault({evaluate:e=>{return t=hn.getValueFor(e),o=vi.getValueFor(e),i=Ki.getValueFor(e),n=Fi.getValueFor(e),r=Gi.getValueFor(e),a=Bi.getValueFor(e),t.get(Qo(t,o,i,n,r,a)+i);var t,o,i,n,r,a}})),On=(Jo("neutral-layer-3").withDefault((e=>_n.getValueFor(e).evaluate(e))),Jo({name:"neutral-layer-4-recipe",cssCustomPropertyName:null}).withDefault({evaluate:e=>{return t=hn.getValueFor(e),o=vi.getValueFor(e),i=Ki.getValueFor(e),n=Fi.getValueFor(e),r=Gi.getValueFor(e),a=Bi.getValueFor(e),t.get(Qo(t,o,i,n,r,a)+2*i);var t,o,i,n,r,a}})),An=(Jo("neutral-layer-4").withDefault((e=>On.getValueFor(e).evaluate(e))),Jo("fill-color").withDefault((e=>Nn.getValueFor(e))));var In,bn;(bn=In||(In={}))[bn.normal=4.5]="normal",bn[bn.large=7]="large";const Sn=Jo({name:"accent-fill-recipe",cssCustomPropertyName:null}).withDefault({evaluate:(e,t)=>(In.normal,(e,t)=>function(e,t,o,i,n,r,a,s,l){const c=e.source,d=e.closestIndexOf(c),u=d+1*i,p=d+1*n,h=d+1*r;return{rest:e.get(d),hover:e.get(u),active:e.get(p),focus:e.get(h)}}(fn.getValueFor(e),hn.getValueFor(e),t||An.getValueFor(e),wi.getValueFor(e),Mi.getValueFor(e),Di.getValueFor(e),Fi.getValueFor(e),Gi.getValueFor(e),Bi.getValueFor(e)))(e,t)}),Rn=Jo("accent-fill-rest").withDefault((e=>Sn.getValueFor(e).evaluate(e).rest)),yn=Jo("accent-fill-hover").withDefault((e=>Sn.getValueFor(e).evaluate(e).hover)),vn=Jo("accent-fill-active").withDefault((e=>Sn.getValueFor(e).evaluate(e).active)),Cn=Jo("accent-fill-focus").withDefault((e=>Sn.getValueFor(e).evaluate(e).focus)),wn=e=>(t,o)=>function(e,t){return e.contrast(Ho)>=t?Ho:zo}(o||Rn.getValueFor(t),e),Mn=Jo({name:"foreground-on-accent-recipe",cssCustomPropertyName:null}).withDefault({evaluate:(e,t)=>wn(In.normal)(e,t)}),Dn=Jo("foreground-on-accent-rest").withDefault((e=>Mn.getValueFor(e).evaluate(e,Rn.getValueFor(e)))),Pn=Jo("foreground-on-accent-hover").withDefault((e=>Mn.getValueFor(e).evaluate(e,yn.getValueFor(e)))),xn=Jo("foreground-on-accent-active").withDefault((e=>Mn.getValueFor(e).evaluate(e,vn.getValueFor(e)))),kn=(Jo("foreground-on-accent-focus").withDefault((e=>Mn.getValueFor(e).evaluate(e,Cn.getValueFor(e)))),Jo({name:"foreground-on-accent-large-recipe",cssCustomPropertyName:null}).withDefault({evaluate:(e,t)=>wn(In.large)(e,t)})),Ln=(Jo("foreground-on-accent-rest-large").withDefault((e=>kn.getValueFor(e).evaluate(e))),Jo("foreground-on-accent-hover-large").withDefault((e=>kn.getValueFor(e).evaluate(e,yn.getValueFor(e)))),Jo("foreground-on-accent-active-large").withDefault((e=>kn.getValueFor(e).evaluate(e,vn.getValueFor(e)))),Jo("foreground-on-accent-focus-large").withDefault((e=>kn.getValueFor(e).evaluate(e,Cn.getValueFor(e)))),e=>(t,o)=>function(e,t,o,i,n,r,a){const s=e.source,l=e.closestIndexOf(s),c=Bo(t),d=l+(1===c?Math.min(i,n):Math.max(c*i,c*n)),u=e.colorContrast(t,o,d,c),p=e.closestIndexOf(u),h=p+c*Math.abs(i-n);let f,g;return(1===c?i<n:c*i>c*n)?(f=p,g=h):(f=h,g=p),{rest:e.get(f),hover:e.get(g),active:e.get(f+c*r),focus:e.get(f+c*a)}}(fn.getValueFor(t),o||An.getValueFor(t),e,Pi.getValueFor(t),xi.getValueFor(t),ki.getValueFor(t),Li.getValueFor(t))),Fn=Jo({name:"accent-foreground-recipe",cssCustomPropertyName:null}).withDefault({evaluate:(e,t)=>Ln(In.normal)(e,t)}),Gn=Jo("accent-foreground-rest").withDefault((e=>Fn.getValueFor(e).evaluate(e).rest)),Bn=Jo("accent-foreground-hover").withDefault((e=>Fn.getValueFor(e).evaluate(e).hover)),Wn=Jo("accent-foreground-active").withDefault((e=>Fn.getValueFor(e).evaluate(e).active)),Un=(Jo("accent-foreground-focus").withDefault((e=>Fn.getValueFor(e).evaluate(e).focus)),Jo({name:"neutral-fill-recipe",cssCustomPropertyName:null}).withDefault({evaluate:(e,t)=>function(e,t,o,i,n,r){const a=e.closestIndexOf(t),s=a>=Math.max(o,i,n,r)?-1:1;return{rest:e.get(a+s*o),hover:e.get(a+s*i),active:e.get(a+s*n),focus:e.get(a+s*r)}}(hn.getValueFor(e),t||An.getValueFor(e),Fi.getValueFor(e),Gi.getValueFor(e),Bi.getValueFor(e),Wi.getValueFor(e))})),Vn=Jo("neutral-fill-rest").withDefault((e=>Un.getValueFor(e).evaluate(e).rest)),$n=Jo("neutral-fill-hover").withDefault((e=>Un.getValueFor(e).evaluate(e).hover)),Hn=Jo("neutral-fill-active").withDefault((e=>Un.getValueFor(e).evaluate(e).active)),zn=(Jo("neutral-fill-focus").withDefault((e=>Un.getValueFor(e).evaluate(e).focus)),Jo({name:"neutral-fill-input-recipe",cssCustomPropertyName:null}).withDefault({evaluate:(e,t)=>function(e,t,o,i,n,r){const a=Bo(t),s=e.closestIndexOf(t);return{rest:e.get(s-a*o),hover:e.get(s-a*i),active:e.get(s-a*n),focus:e.get(s-a*r)}}(hn.getValueFor(e),t||An.getValueFor(e),Ui.getValueFor(e),Vi.getValueFor(e),$i.getValueFor(e),Hi.getValueFor(e))})),jn=(Jo("neutral-fill-input-rest").withDefault((e=>zn.getValueFor(e).evaluate(e).rest)),Jo("neutral-fill-input-hover").withDefault((e=>zn.getValueFor(e).evaluate(e).hover)),Jo("neutral-fill-input-focus").withDefault((e=>zn.getValueFor(e).evaluate(e).focus)),Jo("neutral-fill-input-active").withDefault((e=>zn.getValueFor(e).evaluate(e).active)),Jo({name:"neutral-fill-inverse-recipe",cssCustomPropertyName:null}).withDefault({evaluate:(e,t)=>function(e,t,o,i,n,r){const a=Bo(t),s=e.closestIndexOf(e.colorContrast(t,14)),l=s+a*Math.abs(o-i);let c,d;return(1===a?o<i:a*o>a*i)?(c=s,d=l):(c=l,d=s),{rest:e.get(c),hover:e.get(d),active:e.get(c+a*n),focus:e.get(c+a*r)}}(hn.getValueFor(e),t||An.getValueFor(e),zi.getValueFor(e),ji.getValueFor(e),Yi.getValueFor(e),Zi.getValueFor(e))})),Yn=(Jo("neutral-fill-inverse-rest").withDefault((e=>jn.getValueFor(e).evaluate(e).rest)),Jo("neutral-fill-inverse-hover").withDefault((e=>jn.getValueFor(e).evaluate(e).hover)),Jo("neutral-fill-inverse-active").withDefault((e=>jn.getValueFor(e).evaluate(e).active)),Jo("neutral-fill-inverse-focus").withDefault((e=>jn.getValueFor(e).evaluate(e).focus)),Jo({name:"neutral-fill-layer-recipe",cssCustomPropertyName:null}).withDefault({evaluate:(e,t)=>function(e,t,o){const i=e.closestIndexOf(t);return e.get(i-o)}(hn.getValueFor(e),t||An.getValueFor(e),Ki.getValueFor(e))})),Zn=(Jo("neutral-fill-layer-rest").withDefault((e=>Yn.getValueFor(e).evaluate(e))),Jo({name:"neutral-fill-stealth-recipe",cssCustomPropertyName:null}).withDefault({evaluate:(e,t)=>function(e,t,o,i,n,r,a,s,l,c){const d=Math.max(o,i,n,r,a,s,l,c),u=e.closestIndexOf(t),p=u>=d?-1:1;return{rest:e.get(u+p*o),hover:e.get(u+p*i),active:e.get(u+p*n),focus:e.get(u+p*r)}}(hn.getValueFor(e),t||An.getValueFor(e),qi.getValueFor(e),Xi.getValueFor(e),Qi.getValueFor(e),Ji.getValueFor(e),Fi.getValueFor(e),Gi.getValueFor(e),Bi.getValueFor(e),Wi.getValueFor(e))})),Kn=Jo("neutral-fill-stealth-rest").withDefault((e=>Zn.getValueFor(e).evaluate(e).rest)),qn=Jo("neutral-fill-stealth-hover").withDefault((e=>Zn.getValueFor(e).evaluate(e).hover)),Xn=Jo("neutral-fill-stealth-active").withDefault((e=>Zn.getValueFor(e).evaluate(e).active)),Qn=(Jo("neutral-fill-stealth-focus").withDefault((e=>Zn.getValueFor(e).evaluate(e).focus)),Jo({name:"neutral-fill-strong-recipe",cssCustomPropertyName:null}).withDefault({evaluate:(e,t)=>function(e,t,o,i,n,r){const a=Bo(t),s=e.closestIndexOf(e.colorContrast(t,4.5)),l=s+a*Math.abs(o-i);let c,d;return(1===a?o<i:a*o>a*i)?(c=s,d=l):(c=l,d=s),{rest:e.get(c),hover:e.get(d),active:e.get(c+a*n),focus:e.get(c+a*r)}}(hn.getValueFor(e),t||An.getValueFor(e),en.getValueFor(e),tn.getValueFor(e),on.getValueFor(e),nn.getValueFor(e))})),Jn=(Jo("neutral-fill-strong-rest").withDefault((e=>Qn.getValueFor(e).evaluate(e).rest)),Jo("neutral-fill-strong-hover").withDefault((e=>Qn.getValueFor(e).evaluate(e).hover)),Jo("neutral-fill-strong-active").withDefault((e=>Qn.getValueFor(e).evaluate(e).active)),Jo("neutral-fill-strong-focus").withDefault((e=>Qn.getValueFor(e).evaluate(e).focus)),Jo({name:"neutral-stroke-recipe",cssCustomPropertyName:null}).withDefault({evaluate:e=>function(e,t,o,i,n,r){const a=e.closestIndexOf(t),s=Bo(t),l=a+s*o,c=l+s*(i-o),d=l+s*(n-o),u=l+s*(r-o);return{rest:e.get(l),hover:e.get(c),active:e.get(d),focus:e.get(u)}}(hn.getValueFor(e),An.getValueFor(e),rn.getValueFor(e),an.getValueFor(e),sn.getValueFor(e),ln.getValueFor(e))})),er=Jo("neutral-stroke-rest").withDefault((e=>Jn.getValueFor(e).evaluate(e).rest)),tr=Jo("neutral-stroke-hover").withDefault((e=>Jn.getValueFor(e).evaluate(e).hover)),or=Jo("neutral-stroke-active").withDefault((e=>Jn.getValueFor(e).evaluate(e).active)),ir=(Jo("neutral-stroke-focus").withDefault((e=>Jn.getValueFor(e).evaluate(e).focus)),Jo({name:"neutral-stroke-divider-recipe",cssCustomPropertyName:null}).withDefault({evaluate:(e,t)=>function(e,t,o){return e.get(e.closestIndexOf(t)+Bo(t)*o)}(hn.getValueFor(e),t||An.getValueFor(e),cn.getValueFor(e))})),nr=Jo("neutral-stroke-divider-rest").withDefault((e=>ir.getValueFor(e).evaluate(e))),rr=Jo({name:"neutral-stroke-strong-recipe",cssCustomPropertyName:null}).withDefault({evaluate:e=>function(e,t,o,i,n,r){const a=Bo(t),s=e.colorContrast(t,3),l=e.closestIndexOf(s);return{rest:s,hover:e.get(l+a*i),active:e.get(l+a*n),focus:e.get(l+a*r)}}(hn.getValueFor(e),An.getValueFor(e),0,dn.getValueFor(e),un.getValueFor(e),pn.getValueFor(e))}),ar=(Jo("neutral-stroke-strong-rest").withDefault((e=>rr.getValueFor(e).evaluate(e).rest)),Jo("neutral-stroke-strong-hover").withDefault((e=>rr.getValueFor(e).evaluate(e).hover)),Jo("neutral-stroke-strong-active").withDefault((e=>rr.getValueFor(e).evaluate(e).active)),Jo("neutral-stroke-strong-focus").withDefault((e=>rr.getValueFor(e).evaluate(e).focus)),Jo({name:"focus-stroke-outer-recipe",cssCustomPropertyName:null}).withDefault({evaluate:e=>{return t=hn.getValueFor(e),o=An.getValueFor(e),t.colorContrast(o,3.5);var t,o}})),sr=Jo("focus-stroke-outer").withDefault((e=>ar.getValueFor(e).evaluate(e))),lr=Jo({name:"focus-stroke-inner-recipe",cssCustomPropertyName:null}).withDefault({evaluate:e=>{return t=fn.getValueFor(e),o=An.getValueFor(e),i=sr.getValueFor(e),t.colorContrast(i,3.5,t.closestIndexOf(t.source),-1*Bo(o));var t,o,i}}),cr=Jo("focus-stroke-inner").withDefault((e=>lr.getValueFor(e).evaluate(e))),dr=Jo({name:"neutral-foreground-recipe",cssCustomPropertyName:null}).withDefault({evaluate:e=>{return t=hn.getValueFor(e),o=An.getValueFor(e),t.colorContrast(o,14);var t,o}}),ur=Jo("neutral-foreground-rest").withDefault((e=>dr.getValueFor(e).evaluate(e))),pr=Jo({name:"neutral-foreground-hint-recipe",cssCustomPropertyName:null}).withDefault({evaluate:e=>{return t=hn.getValueFor(e),o=An.getValueFor(e),t.colorContrast(o,4.5);var t,o}}),hr=(Jo("neutral-foreground-hint").withDefault((e=>pr.getValueFor(e).evaluate(e))),{toView(e){var t;return null==e?null:null===(t=e)||void 0===t?void 0:t.toColorString()},fromView(e){if(null==e)return null;const t=qt(e);return t?ko.create(t.r,t.g,t.b):null}}),fr=I`
  :host {
    background-color: ${An};
    color: ${ur};
  }
`.withBehaviors(Qt(I`
      :host {
        background-color: ${eo.Canvas};
        box-shadow: 0 0 0 1px ${eo.CanvasText};
        color: ${eo.CanvasText};
      }
    `));function gr(e){return(t,o)=>{t[o+"Changed"]=function(t,o){null!=o?e.setValueFor(this,o):e.deleteValueFor(this)}}}class Er extends ut{constructor(){super(),this.noPaint=!1,y.getNotifier(this).subscribe({handleChange:this.noPaintChanged.bind(this)},"fillColor")}noPaintChanged(){this.noPaint||void 0===this.fillColor?this.$fastController.removeStyles(fr):this.$fastController.addStyles(fr)}accentBaseColorChanged(e,t){null!=t?fn.setValueFor(this,Wo.create(t)):fn.deleteValueFor(this)}neutralBaseColorChanged(e,t){null!=t?hn.setValueFor(this,Wo.create(t)):hn.deleteValueFor(this)}}Wt([he({attribute:"no-paint",mode:"boolean"})],Er.prototype,"noPaint",void 0),Wt([he({attribute:"fill-color",converter:hr}),gr(An)],Er.prototype,"fillColor",void 0),Wt([he({attribute:"accent-base-color",converter:hr,mode:"fromView"})],Er.prototype,"accentBaseColor",void 0),Wt([he({attribute:"neutral-base-color",converter:hr,mode:"fromView"})],Er.prototype,"neutralBaseColor",void 0),Wt([v,gr(hn)],Er.prototype,"neutralPalette",void 0),Wt([v,gr(fn)],Er.prototype,"accentPalette",void 0),Wt([he({converter:ue}),gr(ni)],Er.prototype,"density",void 0),Wt([he({attribute:"design-unit",converter:ue}),gr(ri)],Er.prototype,"designUnit",void 0),Wt([he({attribute:"direction"}),gr(ei)],Er.prototype,"direction",void 0),Wt([he({attribute:"base-height-multiplier",converter:ue}),gr(oi)],Er.prototype,"baseHeightMultiplier",void 0),Wt([he({attribute:"base-horizontal-spacing-multiplier",converter:ue}),gr(ii)],Er.prototype,"baseHorizontalSpacingMultiplier",void 0),Wt([he({attribute:"control-corner-radius",converter:ue}),gr(ai)],Er.prototype,"controlCornerRadius",void 0),Wt([he({attribute:"stroke-width",converter:ue}),gr(si)],Er.prototype,"strokeWidth",void 0),Wt([he({attribute:"focus-stroke-width",converter:ue}),gr(li)],Er.prototype,"focusStrokeWidth",void 0),Wt([he({attribute:"disabled-opacity",converter:ue}),gr(ti)],Er.prototype,"disabledOpacity",void 0),Wt([he({attribute:"type-ramp-minus-2-font-size"}),gr(fi)],Er.prototype,"typeRampMinus2FontSize",void 0),Wt([he({attribute:"type-ramp-minus-2-line-height"}),gr(gi)],Er.prototype,"typeRampMinus2LineHeight",void 0),Wt([he({attribute:"type-ramp-minus-1-font-size"}),gr(pi)],Er.prototype,"typeRampMinus1FontSize",void 0),Wt([he({attribute:"type-ramp-minus-1-line-height"}),gr(hi)],Er.prototype,"typeRampMinus1LineHeight",void 0),Wt([he({attribute:"type-ramp-base-font-size"}),gr(di)],Er.prototype,"typeRampBaseFontSize",void 0),Wt([he({attribute:"type-ramp-base-line-height"}),gr(ui)],Er.prototype,"typeRampBaseLineHeight",void 0),Wt([he({attribute:"type-ramp-plus-1-font-size"}),gr(Ei)],Er.prototype,"typeRampPlus1FontSize",void 0),Wt([he({attribute:"type-ramp-plus-1-line-height"}),gr(Ti)],Er.prototype,"typeRampPlus1LineHeight",void 0),Wt([he({attribute:"type-ramp-plus-2-font-size"}),gr(Ni)],Er.prototype,"typeRampPlus2FontSize",void 0),Wt([he({attribute:"type-ramp-plus-2-line-height"}),gr(mi)],Er.prototype,"typeRampPlus2LineHeight",void 0),Wt([he({attribute:"type-ramp-plus-3-font-size"}),gr(_i)],Er.prototype,"typeRampPlus3FontSize",void 0),Wt([he({attribute:"type-ramp-plus-3-line-height"}),gr(Oi)],Er.prototype,"typeRampPlus3LineHeight",void 0),Wt([he({attribute:"type-ramp-plus-4-font-size"}),gr(Ai)],Er.prototype,"typeRampPlus4FontSize",void 0),Wt([he({attribute:"type-ramp-plus-4-line-height"}),gr(Ii)],Er.prototype,"typeRampPlus4LineHeight",void 0),Wt([he({attribute:"type-ramp-plus-5-font-size"}),gr(bi)],Er.prototype,"typeRampPlus5FontSize",void 0),Wt([he({attribute:"type-ramp-plus-5-line-height"}),gr(Si)],Er.prototype,"typeRampPlus5LineHeight",void 0),Wt([he({attribute:"type-ramp-plus-6-font-size"}),gr(Ri)],Er.prototype,"typeRampPlus6FontSize",void 0),Wt([he({attribute:"type-ramp-plus-6-line-height"}),gr(yi)],Er.prototype,"typeRampPlus6LineHeight",void 0),Wt([he({attribute:"accent-fill-rest-delta",converter:ue}),gr(Ci)],Er.prototype,"accentFillRestDelta",void 0),Wt([he({attribute:"accent-fill-hover-delta",converter:ue}),gr(wi)],Er.prototype,"accentFillHoverDelta",void 0),Wt([he({attribute:"accent-fill-active-delta",converter:ue}),gr(Mi)],Er.prototype,"accentFillActiveDelta",void 0),Wt([he({attribute:"accent-fill-focus-delta",converter:ue}),gr(Di)],Er.prototype,"accentFillFocusDelta",void 0),Wt([he({attribute:"accent-foreground-rest-delta",converter:ue}),gr(Pi)],Er.prototype,"accentForegroundRestDelta",void 0),Wt([he({attribute:"accent-foreground-hover-delta",converter:ue}),gr(xi)],Er.prototype,"accentForegroundHoverDelta",void 0),Wt([he({attribute:"accent-foreground-active-delta",converter:ue}),gr(ki)],Er.prototype,"accentForegroundActiveDelta",void 0),Wt([he({attribute:"accent-foreground-focus-delta",converter:ue}),gr(Li)],Er.prototype,"accentForegroundFocusDelta",void 0),Wt([he({attribute:"neutral-fill-rest-delta",converter:ue}),gr(Fi)],Er.prototype,"neutralFillRestDelta",void 0),Wt([he({attribute:"neutral-fill-hover-delta",converter:ue}),gr(Gi)],Er.prototype,"neutralFillHoverDelta",void 0),Wt([he({attribute:"neutral-fill-active-delta",converter:ue}),gr(Bi)],Er.prototype,"neutralFillActiveDelta",void 0),Wt([he({attribute:"neutral-fill-focus-delta",converter:ue}),gr(Wi)],Er.prototype,"neutralFillFocusDelta",void 0),Wt([he({attribute:"neutral-fill-input-rest-delta",converter:ue}),gr(Ui)],Er.prototype,"neutralFillInputRestDelta",void 0),Wt([he({attribute:"neutral-fill-input-hover-delta",converter:ue}),gr(Vi)],Er.prototype,"neutralFillInputHoverDelta",void 0),Wt([he({attribute:"neutral-fill-input-active-delta",converter:ue}),gr($i)],Er.prototype,"neutralFillInputActiveDelta",void 0),Wt([he({attribute:"neutral-fill-input-focus-delta",converter:ue}),gr(Hi)],Er.prototype,"neutralFillInputFocusDelta",void 0),Wt([he({attribute:"neutral-fill-layer-rest-delta",converter:ue}),gr(Ki)],Er.prototype,"neutralFillLayerRestDelta",void 0),Wt([he({attribute:"neutral-fill-stealth-rest-delta",converter:ue}),gr(qi)],Er.prototype,"neutralFillStealthRestDelta",void 0),Wt([he({attribute:"neutral-fill-stealth-hover-delta",converter:ue}),gr(Xi)],Er.prototype,"neutralFillStealthHoverDelta",void 0),Wt([he({attribute:"neutral-fill-stealth-active-delta",converter:ue}),gr(Qi)],Er.prototype,"neutralFillStealthActiveDelta",void 0),Wt([he({attribute:"neutral-fill-stealth-focus-delta",converter:ue}),gr(Ji)],Er.prototype,"neutralFillStealthFocusDelta",void 0),Wt([he({attribute:"neutral-fill-strong-hover-delta",converter:ue}),gr(tn)],Er.prototype,"neutralFillStrongHoverDelta",void 0),Wt([he({attribute:"neutral-fill-strong-active-delta",converter:ue}),gr(on)],Er.prototype,"neutralFillStrongActiveDelta",void 0),Wt([he({attribute:"neutral-fill-strong-focus-delta",converter:ue}),gr(nn)],Er.prototype,"neutralFillStrongFocusDelta",void 0),Wt([he({attribute:"base-layer-luminance",converter:ue}),gr(vi)],Er.prototype,"baseLayerLuminance",void 0),Wt([he({attribute:"neutral-stroke-divider-rest-delta",converter:ue}),gr(cn)],Er.prototype,"neutralStrokeDividerRestDelta",void 0),Wt([he({attribute:"neutral-stroke-rest-delta",converter:ue}),gr(rn)],Er.prototype,"neutralStrokeRestDelta",void 0),Wt([he({attribute:"neutral-stroke-hover-delta",converter:ue}),gr(an)],Er.prototype,"neutralStrokeHoverDelta",void 0),Wt([he({attribute:"neutral-stroke-active-delta",converter:ue}),gr(sn)],Er.prototype,"neutralStrokeActiveDelta",void 0),Wt([he({attribute:"neutral-stroke-focus-delta",converter:ue}),gr(ln)],Er.prototype,"neutralStrokeFocusDelta",void 0);const Tr=Er.compose({baseName:"design-system-provider",template:ne` <slot></slot> `,styles:I`
    ${Jt("block")}
  `});class Nr{}Se([he({attribute:"aria-atomic"})],Nr.prototype,"ariaAtomic",void 0),Se([he({attribute:"aria-busy"})],Nr.prototype,"ariaBusy",void 0),Se([he({attribute:"aria-controls"})],Nr.prototype,"ariaControls",void 0),Se([he({attribute:"aria-current"})],Nr.prototype,"ariaCurrent",void 0),Se([he({attribute:"aria-describedby"})],Nr.prototype,"ariaDescribedby",void 0),Se([he({attribute:"aria-details"})],Nr.prototype,"ariaDetails",void 0),Se([he({attribute:"aria-disabled"})],Nr.prototype,"ariaDisabled",void 0),Se([he({attribute:"aria-errormessage"})],Nr.prototype,"ariaErrormessage",void 0),Se([he({attribute:"aria-flowto"})],Nr.prototype,"ariaFlowto",void 0),Se([he({attribute:"aria-haspopup"})],Nr.prototype,"ariaHaspopup",void 0),Se([he({attribute:"aria-hidden"})],Nr.prototype,"ariaHidden",void 0),Se([he({attribute:"aria-invalid"})],Nr.prototype,"ariaInvalid",void 0),Se([he({attribute:"aria-keyshortcuts"})],Nr.prototype,"ariaKeyshortcuts",void 0),Se([he({attribute:"aria-label"})],Nr.prototype,"ariaLabel",void 0),Se([he({attribute:"aria-labelledby"})],Nr.prototype,"ariaLabelledby",void 0),Se([he({attribute:"aria-live"})],Nr.prototype,"ariaLive",void 0),Se([he({attribute:"aria-owns"})],Nr.prototype,"ariaOwns",void 0),Se([he({attribute:"aria-relevant"})],Nr.prototype,"ariaRelevant",void 0),Se([he({attribute:"aria-roledescription"})],Nr.prototype,"ariaRoledescription",void 0);class mr{constructor(e,t){this.target=e,this.propertyName=t}bind(e){e[this.propertyName]=this.target}unbind(){}}function _r(e){return new x("fast-ref",mr,e)}class Or{handleStartContentChange(){this.startContainer.classList.toggle("start",this.start.assignedNodes().length>0)}handleEndContentChange(){this.endContainer.classList.toggle("end",this.end.assignedNodes().length>0)}}const Ar=(e,t)=>ne`
    <span
        part="end"
        ${_r("endContainer")}
        class=${e=>t.end?"end":void 0}
    >
        <slot name="end" ${_r("end")} @slotchange="${e=>e.handleEndContentChange()}">
            ${t.end||""}
        </slot>
    </span>
`,Ir=(e,t)=>ne`
    <span
        part="start"
        ${_r("startContainer")}
        class="${e=>t.start?"start":void 0}"
    >
        <slot
            name="start"
            ${_r("start")}
            @slotchange="${e=>e.handleStartContentChange()}"
        >
            ${t.start||""}
        </slot>
    </span>
`;function br(e,...t){const o=ce.locate(e);t.forEach((t=>{Object.getOwnPropertyNames(t.prototype).forEach((o=>{"constructor"!==o&&Object.defineProperty(e.prototype,o,Object.getOwnPropertyDescriptor(t.prototype,o))})),ce.locate(t).forEach((e=>o.push(e)))}))}ne`
    <span part="end" ${_r("endContainer")}>
        <slot
            name="end"
            ${_r("end")}
            @slotchange="${e=>e.handleEndContentChange()}"
        ></slot>
    </span>
`,ne`
    <span part="start" ${_r("startContainer")}>
        <slot
            name="start"
            ${_r("start")}
            @slotchange="${e=>e.handleStartContentChange()}"
        ></slot>
    </span>
`;class Sr extends ut{constructor(){super(...arguments),this.handleUnsupportedDelegatesFocus=()=>{var e;window.ShadowRoot&&!window.ShadowRoot.prototype.hasOwnProperty("delegatesFocus")&&(null===(e=this.$fastController.definition.shadowOptions)||void 0===e?void 0:e.delegatesFocus)&&(this.focus=()=>{var e;null===(e=this.control)||void 0===e||e.focus()})}}connectedCallback(){super.connectedCallback(),this.handleUnsupportedDelegatesFocus()}}Se([he],Sr.prototype,"download",void 0),Se([he],Sr.prototype,"href",void 0),Se([he],Sr.prototype,"hreflang",void 0),Se([he],Sr.prototype,"ping",void 0),Se([he],Sr.prototype,"referrerpolicy",void 0),Se([he],Sr.prototype,"rel",void 0),Se([he],Sr.prototype,"target",void 0),Se([he],Sr.prototype,"type",void 0),Se([v],Sr.prototype,"defaultSlottedContent",void 0);class Rr{}Se([he({attribute:"aria-expanded"})],Rr.prototype,"ariaExpanded",void 0),br(Rr,Nr),br(Sr,Or,Rr);class yr extends class{constructor(e,t){this.target=e,this.options=t,this.source=null}bind(e){const t=this.options.property;this.shouldUpdate=y.getAccessors(e).some((e=>e.name===t)),this.source=e,this.updateTarget(this.computeNodes()),this.shouldUpdate&&this.observe()}unbind(){this.updateTarget(r),this.source=null,this.shouldUpdate&&this.disconnect()}handleEvent(){this.updateTarget(this.computeNodes())}computeNodes(){let e=this.getNodes();return void 0!==this.options.filter&&(e=e.filter(this.options.filter)),e}updateTarget(e){this.source[this.options.property]=e}}{constructor(e,t){super(e,t)}observe(){this.target.addEventListener("slotchange",this)}disconnect(){this.target.removeEventListener("slotchange",this)}getNodes(){return this.target.assignedNodes(this.options)}}function vr(e){return"string"==typeof e&&(e={property:e}),new x("fast-slotted",yr,e)}let Cr;const wr=function(){if("boolean"==typeof Cr)return Cr;if("undefined"==typeof window||!window.document||!window.document.createElement)return Cr=!1,Cr;const e=document.createElement("style"),t=function(){const e=document.querySelector('meta[property="csp-nonce"]');return e?e.getAttribute("content"):null}();null!==t&&e.setAttribute("nonce",t),document.head.appendChild(e);try{e.sheet.insertRule("foo:focus-visible {color:inherit}",0),Cr=!0}catch(e){Cr=!1}finally{document.head.removeChild(e)}return Cr}()?"focus-visible":"focus",Mr=(function(e,...t){const{styles:o,behaviors:i}=A(e,t);return new b(o,i)})`(${oi} + ${ni}) * ${ri}`,Dr=(e,t)=>I`
    ${Jt("inline-flex")} :host {
      font-family: ${ci};
      outline: none;
      font-size: ${di};
      line-height: ${ui};
      height: calc(${Mr} * 1px);
      min-width: calc(${Mr} * 1px);
      background-color: ${Vn};
      color: ${ur};
      border-radius: calc(${ai} * 1px);
      fill: currentcolor;
      cursor: pointer;
    }

    .control {
      background: transparent;
      height: inherit;
      flex-grow: 1;
      box-sizing: border-box;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      padding: 0 calc((10 + (${ri} * 2 * ${ni})) * 1px);
      white-space: nowrap;
      outline: none;
      text-decoration: none;
      border: calc(${si} * 1px) solid transparent;
      color: inherit;
      border-radius: inherit;
      fill: inherit;
      cursor: inherit;
      font-family: inherit;
    }

    .control,
    .end,
    .start {
      font: inherit;
    }

    .control.icon-only {
      padding: 0;
      line-height: 0;
    }

    :host(:hover) {
      background-color: ${$n};
    }

    :host(:active) {
      background-color: ${Hn};
    }

    .control:${wr} {
      border: calc(${si} * 1px) solid ${sr};
      box-shadow: 0 0 0 calc((${li} - ${si}) * 1px) ${sr};
    }

    .control::-moz-focus-inner {
      border: 0;
    }

    .content {
      pointer-events: none;
    }

    .start,
    .end {
      display: flex;
      pointer-events: none;
    }

    ::slotted(svg) {
      ${""} width: 16px;
      height: 16px;
      pointer-events: none;
    }

    .start {
      margin-inline-end: 11px;
    }

    .end {
      margin-inline-start: 11px;
    }
  `.withBehaviors(Qt(I`
        :host,
        :host([appearance="neutral"]) .control {
          background-color: ${eo.ButtonFace};
          border-color: ${eo.ButtonText};
          color: ${eo.ButtonText};
          fill: currentcolor;
        }

        :host(:not([disabled][href]):hover),
        :host([appearance="neutral"]:not([disabled]):hover) .control {
          forced-color-adjust: none;
          background-color: ${eo.Highlight};
          color: ${eo.HighlightText};
        }

        .control:${wr},
        :host([appearance="outline"]) .control:${wr},
        :host([appearance="neutral"]:${wr}) .control {
          forced-color-adjust: none;
          background-color: ${eo.Highlight};
          border-color: ${eo.ButtonText};
          box-shadow: 0 0 0 calc((${li} - ${si}) * 1px) ${eo.ButtonText};
          color: ${eo.HighlightText};
        }

        .control:not([disabled]):hover,
        :host([appearance="outline"]) .control:hover {
          border-color: ${eo.ButtonText};
        }

        :host([href]) .control {
          border-color: ${eo.LinkText};
          color: ${eo.LinkText};
        }

        :host([href]) .control:hover,
        :host(.neutral[href]) .control:hover,
        :host(.outline[href]) .control:hover,
        :host([href]) .control:${wr}{
          forced-color-adjust: none;
          background: ${eo.ButtonFace};
          border-color: ${eo.LinkText};
          box-shadow: 0 0 0 1px ${eo.LinkText} inset;
          color: ${eo.LinkText};
          fill: currentcolor;
        }
    `)),Pr=I`
  :host([appearance='accent']) {
    background: ${Rn};
    color: ${Dn};
  }

  :host([appearance='accent']:hover) {
    background: ${yn};
    color: ${Pn};
  }

  :host([appearance='accent']:active) .control:active {
    background: ${vn};
    color: ${xn};
  }

  :host([appearance="accent"]) .control:${wr} {
    box-shadow: 0 0 0 calc(${li} * 1px) inset ${cr},
      0 0 0 calc((${li} - ${si}) * 1px) ${sr};
  }
`.withBehaviors(Qt(I`
      :host([appearance='accent']) .control {
        forced-color-adjust: none;
        background: ${eo.Highlight};
        color: ${eo.HighlightText};
      }

      :host([appearance='accent']) .control:hover,
      :host([appearance='accent']:active) .control:active {
        background: ${eo.HighlightText};
        border-color: ${eo.Highlight};
        color: ${eo.Highlight};
      }

      :host([appearance="accent"]) .control:${wr} {
        border-color: ${eo.ButtonText};
        box-shadow: 0 0 0 2px ${eo.HighlightText} inset;
      }

      :host([appearance='accent'][href]) .control {
        background: ${eo.LinkText};
        color: ${eo.HighlightText};
      }

      :host([appearance='accent'][href]) .control:hover {
        background: ${eo.ButtonFace};
        border-color: ${eo.LinkText};
        box-shadow: none;
        color: ${eo.LinkText};
        fill: currentcolor;
      }

      :host([appearance="accent"][href]) .control:${wr} {
        border-color: ${eo.LinkText};
        box-shadow: 0 0 0 2px ${eo.HighlightText} inset;
      }
    `)),xr=I`
  :host([appearance='hypertext']) {
    height: auto;
    font-size: inherit;
    line-height: inherit;
    background: transparent;
    min-width: 0;
  }

  :host([appearance='hypertext']) .control {
    display: inline;
    padding: 0;
    border: none;
    box-shadow: none;
    border-radius: 0;
    line-height: 1;
  }
  :host a.control:not(:link) {
    background-color: transparent;
    cursor: default;
  }
  :host([appearance='hypertext']) .control:link,
  :host([appearance='hypertext']) .control:visited {
    background: transparent;
    color: ${Gn};
    border-bottom: calc(${si} * 1px) solid ${Gn};
  }
  :host([appearance='hypertext']) .control:hover {
    border-bottom-color: ${Bn};
  }
  :host([appearance='hypertext']) .control:active {
    border-bottom-color: ${Wn};
  }
  :host([appearance="hypertext"]) .control:${wr} {
    border-bottom: calc(${li} * 1px) solid ${sr};
    margin-bottom: calc(calc(${si} - ${li}) * 1px);
  }
`.withBehaviors(Qt(I`
      :host([appearance="hypertext"]) .control:${wr} {
        color: ${eo.LinkText};
        border-bottom-color: ${eo.LinkText};
      }
    `)),kr=I`
  :host([appearance='lightweight']) {
    background: transparent;
    color: ${Gn};
  }

  :host([appearance='lightweight']) .control {
    padding: 0;
    height: initial;
    border: none;
    box-shadow: none;
    border-radius: 0;
  }

  :host([appearance='lightweight']:hover) {
    color: ${Bn};
  }

  :host([appearance='lightweight']:active) {
    color: ${Wn};
  }

  :host([appearance='lightweight']) .content {
    position: relative;
  }

  :host([appearance='lightweight']) .content::before {
    content: '';
    display: block;
    height: calc(${si} * 1px);
    position: absolute;
    top: calc(1em + 3px);
    width: 100%;
  }

  :host([appearance='lightweight']:hover) .content::before {
    background: ${Bn};
  }

  :host([appearance='lightweight']:active) .content::before {
    background: ${Wn};
  }

  :host([appearance="lightweight"]) .control:${wr} .content::before {
    background: ${ur};
    height: calc(${li} * 1px);
  }
`.withBehaviors(Qt(I`
      :host([appearance='lightweight']) {
        color: ${eo.ButtonText};
      }
      :host([appearance="lightweight"]) .control:hover,
        :host([appearance="lightweight"]) .control:${wr} {
        forced-color-adjust: none;
        background: ${eo.ButtonFace};
        color: ${eo.Highlight};
      }
      :host([appearance="lightweight"]) .control:hover .content::before,
        :host([appearance="lightweight"]) .control:${wr} .content::before {
        background: ${eo.Highlight};
      }

      :host([appearance="lightweight"][href]) .control:hover,
        :host([appearance="lightweight"][href]) .control:${wr} {
        background: ${eo.ButtonFace};
        box-shadow: none;
        color: ${eo.LinkText};
      }

      :host([appearance="lightweight"][href]) .control:hover .content::before,
        :host([appearance="lightweight"][href]) .control:${wr} .content::before {
        background: ${eo.LinkText};
      }
    `)),Lr=I`
  :host([appearance='outline']) {
    background: transparent;
    border-color: ${er};
  }

  :host([appearance='outline']:hover) {
    border-color: ${tr};
  }

  :host([appearance='outline']:active) {
    border-color: ${or};
  }

  :host([appearance='outline']) .control {
    border-color: inherit;
  }

  :host([appearance="outline"]) .control:${wr} {
    box-shadow: 0 0 0 calc((${li} - ${si}) * 1px) ${sr};
    border-color: ${sr};
  }
`.withBehaviors(Qt(I`
      :host([appearance='outline']) {
        border-color: ${eo.ButtonText};
      }
      :host([appearance='outline'][href]) {
        border-color: ${eo.LinkText};
      }
    `)),Fr=I`
  :host([appearance='stealth']) {
    background: ${Kn};
  }

  :host([appearance='stealth']:hover) {
    background: ${qn};
  }

  :host([appearance='stealth']:active) {
    background: ${Xn};
  }
`.withBehaviors(Qt(I`
      :host([appearance='stealth']),
      :host([appearance='stealth']) .control {
        forced-color-adjust: none;
        background: ${eo.ButtonFace};
        border-color: transparent;
        color: ${eo.ButtonText};
        fill: currentcolor;
      }

      :host([appearance='stealth']:hover) .control {
        background: ${eo.Highlight};
        border-color: ${eo.Highlight};
        color: ${eo.HighlightText};
        fill: currentcolor;
      }

      :host([appearance="stealth"]:${wr}) .control {
        background: ${eo.Highlight};
        box-shadow: 0 0 0 1px ${eo.Highlight};
        color: ${eo.HighlightText};
        fill: currentcolor;
      }

      :host([appearance='stealth'][href]) .control {
        color: ${eo.LinkText};
      }

      :host([appearance="stealth"]:hover[href]) .control,
        :host([appearance="stealth"]:${wr}[href]) .control {
        background: ${eo.LinkText};
        border-color: ${eo.LinkText};
        color: ${eo.HighlightText};
        fill: currentcolor;
      }

      :host([appearance="stealth"]:${wr}[href]) .control {
        box-shadow: 0 0 0 1px ${eo.LinkText};
      }
    `));class Gr{constructor(e,t,o){this.propertyName=e,this.value=t,this.styles=o}bind(e){y.getNotifier(e).subscribe(this,this.propertyName),this.handleChange(e,this.propertyName)}unbind(e){y.getNotifier(e).unsubscribe(this,this.propertyName),e.$fastController.removeStyles(this.styles)}handleChange(e,t){e[t]===this.value?e.$fastController.addStyles(this.styles):e.$fastController.removeStyles(this.styles)}}function Br(e,t){return new Gr("appearance",e,t)}class Wr extends Sr{appearanceChanged(e,t){e!==t&&(this.classList.add(t),this.classList.remove(e))}connectedCallback(){super.connectedCallback(),this.appearance||(this.appearance="neutral")}defaultSlottedContentChanged(){const e=this.defaultSlottedContent.filter((e=>e.nodeType===Node.ELEMENT_NODE));1===e.length&&e[0]instanceof SVGElement?this.control.classList.add("icon-only"):this.control.classList.remove("icon-only")}}Wt([he],Wr.prototype,"appearance",void 0);const Ur=Wr.compose({baseName:"anchor",baseClass:Sr,template:(e,t)=>ne`
    <a
        class="control"
        part="control"
        download="${e=>e.download}"
        href="${e=>e.href}"
        hreflang="${e=>e.hreflang}"
        ping="${e=>e.ping}"
        referrerpolicy="${e=>e.referrerpolicy}"
        rel="${e=>e.rel}"
        target="${e=>e.target}"
        type="${e=>e.type}"
        aria-atomic="${e=>e.ariaAtomic}"
        aria-busy="${e=>e.ariaBusy}"
        aria-controls="${e=>e.ariaControls}"
        aria-current="${e=>e.ariaCurrent}"
        aria-describedby="${e=>e.ariaDescribedby}"
        aria-details="${e=>e.ariaDetails}"
        aria-disabled="${e=>e.ariaDisabled}"
        aria-errormessage="${e=>e.ariaErrormessage}"
        aria-expanded="${e=>e.ariaExpanded}"
        aria-flowto="${e=>e.ariaFlowto}"
        aria-haspopup="${e=>e.ariaHaspopup}"
        aria-hidden="${e=>e.ariaHidden}"
        aria-invalid="${e=>e.ariaInvalid}"
        aria-keyshortcuts="${e=>e.ariaKeyshortcuts}"
        aria-label="${e=>e.ariaLabel}"
        aria-labelledby="${e=>e.ariaLabelledby}"
        aria-live="${e=>e.ariaLive}"
        aria-owns="${e=>e.ariaOwns}"
        aria-relevant="${e=>e.ariaRelevant}"
        aria-roledescription="${e=>e.ariaRoledescription}"
        ${_r("control")}
    >
        ${Ir(0,t)}
        <span class="content" part="content">
            <slot ${vr("defaultSlottedContent")}></slot>
        </span>
        ${Ar(0,t)}
    </a>
`,styles:(e,t)=>I`
    ${Dr()}
  `.withBehaviors(Br("accent",Pr),Br("hypertext",xr),Br("lightweight",kr),Br("outline",Lr),Br("stealth",Fr)),shadowOptions:{delegatesFocus:!0}});class Vr extends ut{constructor(){super(...arguments),this.role="separator",this.orientation="horizontal"}}Se([he],Vr.prototype,"role",void 0),Se([he],Vr.prototype,"orientation",void 0);const $r=Vr.compose({baseName:"divider",template:(e,t)=>ne`
    <template role="${e=>e.role}" aria-orientation="${e=>e.orientation}"></template>
`,styles:(e,t)=>I`
    ${Jt("block")} :host {
      box-sizing: content-box;
      height: 0;
      margin: calc(${ri} * 1px) 0;
      border: none;
      border-top: calc(${si} * 1px) solid ${nr};
    }
  `});var Hr,zr;(zr=Hr||(Hr={}))[zr.alt=18]="alt",zr[zr.arrowDown=40]="arrowDown",zr[zr.arrowLeft=37]="arrowLeft",zr[zr.arrowRight=39]="arrowRight",zr[zr.arrowUp=38]="arrowUp",zr[zr.back=8]="back",zr[zr.backSlash=220]="backSlash",zr[zr.break=19]="break",zr[zr.capsLock=20]="capsLock",zr[zr.closeBracket=221]="closeBracket",zr[zr.colon=186]="colon",zr[zr.colon2=59]="colon2",zr[zr.comma=188]="comma",zr[zr.ctrl=17]="ctrl",zr[zr.delete=46]="delete",zr[zr.end=35]="end",zr[zr.enter=13]="enter",zr[zr.equals=187]="equals",zr[zr.equals2=61]="equals2",zr[zr.equals3=107]="equals3",zr[zr.escape=27]="escape",zr[zr.forwardSlash=191]="forwardSlash",zr[zr.function1=112]="function1",zr[zr.function10=121]="function10",zr[zr.function11=122]="function11",zr[zr.function12=123]="function12",zr[zr.function2=113]="function2",zr[zr.function3=114]="function3",zr[zr.function4=115]="function4",zr[zr.function5=116]="function5",zr[zr.function6=117]="function6",zr[zr.function7=118]="function7",zr[zr.function8=119]="function8",zr[zr.function9=120]="function9",zr[zr.home=36]="home",zr[zr.insert=45]="insert",zr[zr.menu=93]="menu",zr[zr.minus=189]="minus",zr[zr.minus2=109]="minus2",zr[zr.numLock=144]="numLock",zr[zr.numPad0=96]="numPad0",zr[zr.numPad1=97]="numPad1",zr[zr.numPad2=98]="numPad2",zr[zr.numPad3=99]="numPad3",zr[zr.numPad4=100]="numPad4",zr[zr.numPad5=101]="numPad5",zr[zr.numPad6=102]="numPad6",zr[zr.numPad7=103]="numPad7",zr[zr.numPad8=104]="numPad8",zr[zr.numPad9=105]="numPad9",zr[zr.numPadDivide=111]="numPadDivide",zr[zr.numPadDot=110]="numPadDot",zr[zr.numPadMinus=109]="numPadMinus",zr[zr.numPadMultiply=106]="numPadMultiply",zr[zr.numPadPlus=107]="numPadPlus",zr[zr.openBracket=219]="openBracket",zr[zr.pageDown=34]="pageDown",zr[zr.pageUp=33]="pageUp",zr[zr.period=190]="period",zr[zr.print=44]="print",zr[zr.quote=222]="quote",zr[zr.scrollLock=145]="scrollLock",zr[zr.shift=16]="shift",zr[zr.space=32]="space",zr[zr.tab=9]="tab",zr[zr.tilde=192]="tilde",zr[zr.windowsLeft=91]="windowsLeft",zr[zr.windowsOpera=219]="windowsOpera",zr[zr.windowsRight=92]="windowsRight";const jr="form-associated-proxy",Yr="ElementInternals"in window&&"setFormValue"in window.ElementInternals.prototype,Zr=new WeakMap;class Kr extends ut{}class qr extends(function(e){const t=class extends e{constructor(...e){super(...e),this.dirtyValue=!1,this.disabled=!1,this.proxyEventsToBlock=["change","click"],this.proxyInitialized=!1,this.required=!1,this.initialValue=this.initialValue||"",this.elementInternals||(this.formResetCallback=this.formResetCallback.bind(this))}static get formAssociated(){return Yr}get validity(){return this.elementInternals?this.elementInternals.validity:this.proxy.validity}get form(){return this.elementInternals?this.elementInternals.form:this.proxy.form}get validationMessage(){return this.elementInternals?this.elementInternals.validationMessage:this.proxy.validationMessage}get willValidate(){return this.elementInternals?this.elementInternals.willValidate:this.proxy.willValidate}get labels(){if(this.elementInternals)return Object.freeze(Array.from(this.elementInternals.labels));if(this.proxy instanceof HTMLElement&&this.proxy.ownerDocument&&this.id){const e=this.proxy.labels,t=Array.from(this.proxy.getRootNode().querySelectorAll(`[for='${this.id}']`)),o=e?t.concat(Array.from(e)):t;return Object.freeze(o)}return r}valueChanged(e,t){this.dirtyValue=!0,this.proxy instanceof HTMLElement&&(this.proxy.value=this.value),this.currentValue=this.value,this.setFormValue(this.value),this.validate()}currentValueChanged(){this.value=this.currentValue}initialValueChanged(e,t){this.dirtyValue||(this.value=this.initialValue,this.dirtyValue=!1)}disabledChanged(e,t){this.proxy instanceof HTMLElement&&(this.proxy.disabled=this.disabled),h.queueUpdate((()=>this.classList.toggle("disabled",this.disabled)))}nameChanged(e,t){this.proxy instanceof HTMLElement&&(this.proxy.name=this.name)}requiredChanged(e,t){this.proxy instanceof HTMLElement&&(this.proxy.required=this.required),h.queueUpdate((()=>this.classList.toggle("required",this.required))),this.validate()}get elementInternals(){if(!Yr)return null;let e=Zr.get(this);return e||(e=this.attachInternals(),Zr.set(this,e)),e}connectedCallback(){super.connectedCallback(),this.addEventListener("keypress",this._keypressHandler),this.value||(this.value=this.initialValue,this.dirtyValue=!1),this.elementInternals||(this.attachProxy(),this.form&&this.form.addEventListener("reset",this.formResetCallback))}disconnectedCallback(){this.proxyEventsToBlock.forEach((e=>this.proxy.removeEventListener(e,this.stopPropagation))),!this.elementInternals&&this.form&&this.form.removeEventListener("reset",this.formResetCallback)}checkValidity(){return this.elementInternals?this.elementInternals.checkValidity():this.proxy.checkValidity()}reportValidity(){return this.elementInternals?this.elementInternals.reportValidity():this.proxy.reportValidity()}setValidity(e,t,o){this.elementInternals?this.elementInternals.setValidity(e,t,o):"string"==typeof t&&this.proxy.setCustomValidity(t)}formDisabledCallback(e){this.disabled=e}formResetCallback(){this.value=this.initialValue,this.dirtyValue=!1}attachProxy(){var e;this.proxyInitialized||(this.proxyInitialized=!0,this.proxy.style.display="none",this.proxyEventsToBlock.forEach((e=>this.proxy.addEventListener(e,this.stopPropagation))),this.proxy.disabled=this.disabled,this.proxy.required=this.required,"string"==typeof this.name&&(this.proxy.name=this.name),"string"==typeof this.value&&(this.proxy.value=this.value),this.proxy.setAttribute("slot",jr),this.proxySlot=document.createElement("slot"),this.proxySlot.setAttribute("name",jr)),null===(e=this.shadowRoot)||void 0===e||e.appendChild(this.proxySlot),this.appendChild(this.proxy)}detachProxy(){var e;this.removeChild(this.proxy),null===(e=this.shadowRoot)||void 0===e||e.removeChild(this.proxySlot)}validate(e){this.proxy instanceof HTMLElement&&this.setValidity(this.proxy.validity,this.proxy.validationMessage,e)}setFormValue(e,t){this.elementInternals&&this.elementInternals.setFormValue(e,t||e)}_keypressHandler(e){if("Enter"===e.key&&this.form instanceof HTMLFormElement){const e=this.form.querySelector("[type=submit]");null==e||e.click()}}stopPropagation(e){e.stopPropagation()}};return he({mode:"boolean"})(t.prototype,"disabled"),he({mode:"fromView",attribute:"value"})(t.prototype,"initialValue"),he({attribute:"current-value"})(t.prototype,"currentValue"),he(t.prototype,"name"),he({mode:"boolean"})(t.prototype,"required"),v(t.prototype,"value"),t}(Kr)){constructor(){super(...arguments),this.proxy=document.createElement("input")}}class Xr extends qr{constructor(){super(...arguments),this.handleClick=e=>{var t;this.disabled&&(null===(t=this.defaultSlottedContent)||void 0===t?void 0:t.length)<=1&&e.stopPropagation()},this.handleSubmission=()=>{if(!this.form)return;const e=this.proxy.isConnected;e||this.attachProxy(),"function"==typeof this.form.requestSubmit?this.form.requestSubmit(this.proxy):this.proxy.click(),e||this.detachProxy()},this.handleFormReset=()=>{var e;null===(e=this.form)||void 0===e||e.reset()},this.handleUnsupportedDelegatesFocus=()=>{var e;window.ShadowRoot&&!window.ShadowRoot.prototype.hasOwnProperty("delegatesFocus")&&(null===(e=this.$fastController.definition.shadowOptions)||void 0===e?void 0:e.delegatesFocus)&&(this.focus=()=>{this.control.focus()})}}formactionChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.formAction=this.formaction)}formenctypeChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.formEnctype=this.formenctype)}formmethodChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.formMethod=this.formmethod)}formnovalidateChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.formNoValidate=this.formnovalidate)}formtargetChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.formTarget=this.formtarget)}typeChanged(e,t){this.proxy instanceof HTMLInputElement&&(this.proxy.type=this.type),"submit"===t&&this.addEventListener("click",this.handleSubmission),"submit"===e&&this.removeEventListener("click",this.handleSubmission),"reset"===t&&this.addEventListener("click",this.handleFormReset),"reset"===e&&this.removeEventListener("click",this.handleFormReset)}validate(){super.validate(this.control)}connectedCallback(){var e;super.connectedCallback(),this.proxy.setAttribute("type",this.type),this.handleUnsupportedDelegatesFocus();const t=Array.from(null===(e=this.control)||void 0===e?void 0:e.children);t&&t.forEach((e=>{e.addEventListener("click",this.handleClick)}))}disconnectedCallback(){var e;super.disconnectedCallback();const t=Array.from(null===(e=this.control)||void 0===e?void 0:e.children);t&&t.forEach((e=>{e.removeEventListener("click",this.handleClick)}))}}Se([he({mode:"boolean"})],Xr.prototype,"autofocus",void 0),Se([he({attribute:"form"})],Xr.prototype,"formId",void 0),Se([he],Xr.prototype,"formaction",void 0),Se([he],Xr.prototype,"formenctype",void 0),Se([he],Xr.prototype,"formmethod",void 0),Se([he({mode:"boolean"})],Xr.prototype,"formnovalidate",void 0),Se([he],Xr.prototype,"formtarget",void 0),Se([he],Xr.prototype,"type",void 0),Se([v],Xr.prototype,"defaultSlottedContent",void 0);class Qr{}Se([he({attribute:"aria-expanded"})],Qr.prototype,"ariaExpanded",void 0),Se([he({attribute:"aria-pressed"})],Qr.prototype,"ariaPressed",void 0),br(Qr,Nr),br(Xr,Or,Qr);class Jr extends Xr{appearanceChanged(e,t){e!==t&&(this.classList.add(t),this.classList.remove(e))}connectedCallback(){super.connectedCallback(),this.appearance||(this.appearance="neutral")}defaultSlottedContentChanged(){const e=this.defaultSlottedContent.filter((e=>e.nodeType===Node.ELEMENT_NODE));1===e.length&&e[0]instanceof SVGElement?this.control.classList.add("icon-only"):this.control.classList.remove("icon-only")}}Wt([he],Jr.prototype,"appearance",void 0);const ea=Jr.compose({baseName:"button",baseClass:Xr,template:(e,t)=>ne`
    <button
        class="control"
        part="control"
        ?autofocus="${e=>e.autofocus}"
        ?disabled="${e=>e.disabled}"
        form="${e=>e.formId}"
        formaction="${e=>e.formaction}"
        formenctype="${e=>e.formenctype}"
        formmethod="${e=>e.formmethod}"
        formnovalidate="${e=>e.formnovalidate}"
        formtarget="${e=>e.formtarget}"
        name="${e=>e.name}"
        type="${e=>e.type}"
        value="${e=>e.value}"
        aria-atomic="${e=>e.ariaAtomic}"
        aria-busy="${e=>e.ariaBusy}"
        aria-controls="${e=>e.ariaControls}"
        aria-current="${e=>e.ariaCurrent}"
        aria-describedby="${e=>e.ariaDescribedby}"
        aria-details="${e=>e.ariaDetails}"
        aria-disabled="${e=>e.ariaDisabled}"
        aria-errormessage="${e=>e.ariaErrormessage}"
        aria-expanded="${e=>e.ariaExpanded}"
        aria-flowto="${e=>e.ariaFlowto}"
        aria-haspopup="${e=>e.ariaHaspopup}"
        aria-hidden="${e=>e.ariaHidden}"
        aria-invalid="${e=>e.ariaInvalid}"
        aria-keyshortcuts="${e=>e.ariaKeyshortcuts}"
        aria-label="${e=>e.ariaLabel}"
        aria-labelledby="${e=>e.ariaLabelledby}"
        aria-live="${e=>e.ariaLive}"
        aria-owns="${e=>e.ariaOwns}"
        aria-pressed="${e=>e.ariaPressed}"
        aria-relevant="${e=>e.ariaRelevant}"
        aria-roledescription="${e=>e.ariaRoledescription}"
        ${_r("control")}
    >
        ${Ir(0,t)}
        <span class="content" part="content">
            <slot ${vr("defaultSlottedContent")}></slot>
        </span>
        ${Ar(0,t)}
    </button>
`,styles:(e,t)=>I`
    :host([disabled]),
    :host([disabled]:hover),
    :host([disabled]:active) {
      opacity: ${ti};
      background-color: ${Vn};
      cursor: ${"not-allowed"};
    }

    ${Dr()}
  `.withBehaviors(Qt(I`
        :host([disabled]),
        :host([disabled]:hover),
        :host([disabled]:active),
        :host([disabled]) .control,
        :host([disabled]) .control:hover,
        :host([appearance='neutral'][disabled]:hover) .control {
          forced-color-adjust: none;
          background-color: ${eo.ButtonFace};
          border-color: ${eo.GrayText};
          color: ${eo.GrayText};
          opacity: 1;
        }
      `),Br("accent",I`
        :host([appearance='accent'][disabled]),
        :host([appearance='accent'][disabled]:hover),
        :host([appearance='accent'][disabled]:active) {
          background: ${Rn};
        }

        ${Pr}
      `.withBehaviors(Qt(I`
            :host([appearance='accent'][disabled]) .control,
            :host([appearance='accent'][disabled]) .control:hover {
              background: ${eo.ButtonFace};
              border-color: ${eo.GrayText};
              color: ${eo.GrayText};
            }
          `))),Br("lightweight",I`
        :host([appearance='lightweight'][disabled]:hover),
        :host([appearance='lightweight'][disabled]:active) {
          background-color: transparent;
          color: ${Gn};
        }

        :host([appearance='lightweight'][disabled]) .content::before,
        :host([appearance='lightweight'][disabled]:hover) .content::before,
        :host([appearance='lightweight'][disabled]:active) .content::before {
          background: transparent;
        }

        ${kr}
      `.withBehaviors(Qt(I`
            :host([appearance='lightweight'][disabled]) .control {
              forced-color-adjust: none;
              color: ${eo.GrayText};
            }

            :host([appearance='lightweight'][disabled]) .control:hover .content::before {
              background: none;
            }
          `))),Br("outline",I`
        :host([appearance='outline'][disabled]:hover),
        :host([appearance='outline'][disabled]:active) {
          background: transparent;
          border-color: ${er};
        }

        ${Lr}
      `.withBehaviors(Qt(I`
            :host([appearance='outline'][disabled]) .control {
              border-color: ${eo.GrayText};
            }
          `))),Br("stealth",I`
        :host([appearance='stealth'][disabled]),
        :host([appearance='stealth'][disabled]:hover),
        :host([appearance='stealth'][disabled]:active) {
          background: ${Kn};
        }

        ${Fr}
      `.withBehaviors(Qt(I`
            :host([appearance='stealth'][disabled]),
            :host([appearance='stealth'][disabled]:hover) {
              background: ${eo.ButtonFace};
            }

            :host([appearance='stealth'][disabled]) .control {
              background: ${eo.ButtonFace};
              border-color: transparent;
              color: ${eo.GrayText};
            }
          `)))),shadowOptions:{delegatesFocus:!0}}),ta={darkGreen2DarkBlue:"linear-gradient(113.02deg, rgba(17, 70, 47, 0.534) 0%, rgba(32, 74, 222, 0.6) 87.93%),                        linear-gradient(111.95deg, rgba(93, 231, 189, 0.95) -23.05%, rgba(6, 44, 120, 0.95) 118.51%)",lightGreen2LightBlue:"linear-gradient(75.54deg, #A7FBC3 9.26%, #AEE4FF 83.12%)",xLightGreeen2xLightBlue:"linear-gradient(79.5deg, rgba(235, 255, 242, 0.6) 7.26%, rgba(227, 246, 255, 0.6) 94.85%)",darkModexLightGreen2xLightBlue:"linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), linear-gradient(79.89deg, rgba(12, 243, 91, 0.1) 13.08%, rgba(26, 144, 255, 0.1) 87.46%)",green2Blue:"linear-gradient(112.65deg, #0CF35B -3.38%, #0072DC 105.35%)",orange2Yellow:"linear-gradient(236.16deg, #FE7002 6.29%, #FF6388 77.23%, #FFD98E 115.25%, #FFD98E 115.25%)",get darkRed2Yellow(){return`linear-gradient(97.07deg, rgba(114, 18, 35, 0.4) 6.18%, rgba(71, 10, 10, 0.368) 98.82%), ${this.orange2Yellow}`},blue2Green:"linear-gradient(180deg, rgba(3, 163, 214, 0.5) 0%, rgba(12, 51, 28, 0.43) 116.67%),                linear-gradient(173.61deg, #0072DC 1.09%, #0CF35B 103.87%)",darkModeDarkGreen2DarkBlue:"linear-gradient(86.44deg, #00C16E 5.86%, #07C5D9 50.32%, #46A6FF 92.97%);",white2red:"linear-gradient(236.16deg, #FE7002 6.29%, #FF6388 77.23%, #FFD98E 115.25%, #FFD98E 115.25%)",lightWhite2red:"linear-gradient(236.16deg, rgba(254, 112, 2, 0.2) 6.29%, rgba(255, 99, 136, 0.2) 77.23%, rgba(255, 217, 142, 0.2) 115.25%, rgba(255, 217, 142, 0.2) 115.25%)",xLightBlue2LightBlue:"linear-gradient(106.79deg, #EFF8FF 7.25%, #E1F1FF 92.37%)",purple2green:"linear-gradient(89.79deg, #6517CB 0.19%, #3251C4 47.92%, #499FB0 99.81%)"},oa="#1E4061",ia=(ta.darkGreen2DarkBlue,ta.orange2Yellow,ta.darkModeDarkGreen2DarkBlue,ta.lightGreen2LightBlue,ta.green2Blue,ta.darkRed2Yellow,ta.white2red,ta.lightWhite2red,ta.green2Blue,ta.lightGreen2LightBlue,ta.blue2Green,ta.green2Blue,ta.green2Blue,ta.green2Blue,ta.green2Blue,ta.darkRed2Yellow,ta.xLightBlue2LightBlue,ta.orange2Yellow,ta.xLightGreeen2xLightBlue,ta.darkModexLightGreen2xLightBlue,ta.purple2green,{whiteBackground:"#FFFFFF",darkBackground:"#4A4A4A",darkMiniWalletBackground:"#303030",darkNotificationBackground:"#303030",blackBackground:"#000000",listItemBackground:"#F7F7F7",darkModeListItemBackground:"#575757",normalText:"#344555",darkModeNormalText:"#FFFFFF",darModeHyperText:"#D1D1D1",normalTextDarkMode:"#FFFFFF",accentTextSecondary:"#0066B4",accentTextTertiary:"#057665",textSecondary:"#44596C",textSecondaryDarkMode:"#D1D1D1",accentTextSecondaryDarkMode:"#46A6FF",accentTextTertiaryDarkMode:"#00BAAE",textDisabled:"#A19F9D",textDisabledStrokeDarkMode:"#6E6E6E",grayScale300:"#ACACAC",grayScale100:"#E1E1E1",grayScale96:"#606060",emphasizeText:oa,alertCloseButtonHover:"#F2F2F2",alertCloseButtonActive:"#F7F7F7",buttonFocusBorder:"#838383",buttonText:"#0066B4",buttonTextHover:"#0060A9",buttonBackgroundHover:"#EAEAEA",darkModeButtonBackgroundHover:"#404040",buttonBackgroundDoubleHover:"#CACACA",darkButtonBackgroundDoubleHover:"#5B5B5B",darkModeButtonText:"#46A6FF",darkModeButtonTextHover:"#68AFE5",buttonTextDarkMode:"linear-gradient(86.44deg, #00C16E 5.86%, #07C5D9 50.32%, #1A90FF 92.97%)",buttonHover:oa,buttonDisabledBackground:"#E5E5E5",buttonDisabledText:"#919191",errorColor:"#A3341D",errorColorDarkMode:"#D13438",toggleColor:"#20C68A",toggleUnselectIndicator:"#44596C",toggleUnselectedBorder:"#5D6873",formFieldBackground:"rgba(255, 255, 255, 0.7)",darkModeformFieldBackground:"rgba(255, 255, 255, 0.02)",formFieldHoverBackground:"rgb(247, 247, 247)",formFieldBorder:"rgba(12, 127, 192, 0.3)",formFieldHoverBorder:"rgb(144, 144, 144)",selectOption:"#262626",darkModeSelectOption:"rgba(255, 255, 255, 0.02)",selectToggleGlyph:"#262626",selectOptionSelected:"rgba(174, 212, 232, 0.3)",darkModeSelectOptionSelected:"#244666",darkModeSelectOptionDropdownBackground:"#151D24",sectionBackground:"rgba(255, 255, 255, 0.7)",darkModeSectionBackground:"rgba(21, 29, 36, 0.8)",sectionBorder:"rgba(12, 127, 192, 0.1)",toggleUnselectedBackground:"#EFEFEF",accentText:"#0A7D80",accentTextDark:"#00BAAE",hotAccentText:"#C14821",hotAccentTextDark:"#FF9D7C",lightModeTextDisabled:"#A19F9D",normalControl:"#0078D4",normalControlHover:"#006CBE",darkModeNormalControl:"#63ADE5",iconFillColorLight:"#212121",darkModeNormalBackground:"#151d24",bottomBorderGray:"#D3D3D3",highContrastDarkMode:"#1AEBFF",highContrastLightMode:"#37006E",textDescriptionHighContrastLightModeColor:"#000000",textDescriptionHighContrastDarkModeColor:"#FFFFFF",separator:"#EFEFEF",separatorDark:"#FFFFFF",dialogText:"#262626",modalBackgroundDark:"#3B3B3B",typePrimary:"#323130",typeSecondary:"#605E5C",typeDisabled:"#A19F9D",typePrimaryDarkMode:"#FFFFFF",isReskinMVPNormalText:"#1A1A1A",criticalPrimary:"#C42B1C",criticalDarkMode:"#BC2F2F",YellowAlertPrimary:"#817400",YellowAlertIconDarkMode:"#feee66",RedAlertIconDarkMode:"#D73333",redForeground:"#BC2F32",iconBackplateBackgroud:"rgba(4, 115, 206, 0.08)",iconBackplate:"rgba(3, 106, 196, 1)",iconBackplateBackgroundDarkMode:"#4C5963",iconBackplateDarkMode:"#FFFFFF",miniWalletDividerLight:"#EFEFEF",miniWalletDividerDarkMode:"#525252",packageTrackingIconLight:"#004377",packageTrackingIconDark:"#a9d3f2",compoundBrandForegroundDark:"#479ef5",compoundBrandForegroundLight:"#0f6cbd",donationIconBackgroundDark:"#3f1011",donationIconBackgroundLight:"#fdf6f6",personalizedOfferTextColorDark:"#e37d80"}),na="REDACTED",ra=(window.chrome,(e,t)=>{const o=JSON.stringify({eventName:e,data:t});performance.mark(o)});window.cachedMojom||(window.cachedMojom={});const{loadTimeData:aa}=window,sa=aa;function la(e,t,o=!1){const i=(i,n)=>{if(!n)return sa?.getString(i);try{return sa.valueExists(i)?sa.getValue(i)!==n&&((e,t)=>sa.valueExists(e)&&sa.getValue(e)===t)(e,t)?(console.warn(`Different values for key ${i}: loadtimeData: ${sa.getValue(i)}\n                       default: ${n}`),n):sa.getValue(i):(o||i?.toLowerCase()?.includes("crypto")||console.warn(`Missing localized string: ${i}, will use ${n}`),n)}catch(e){console.error(`Error loading loadtimeData for ${i}: ${e}`)}return n};return{getString:i,getStringF:sa?.getStringF.bind(sa),getStringFWithDefaultValue:(e,t,...o)=>{const n=i(e,t);return n?sa?.substituteString(n,...o):""},getInteger:sa?.getInteger.bind(sa),getBoolean:sa?.getBoolean.bind(sa),valueExists:sa?.valueExists.bind(sa),getValue:sa?.getValue.bind(sa),overrideValues:sa?.overrideValues.bind(sa)}}const ca=la("notificationTitle","Notification"),da=e=>ca.valueExists(e)&&ca.getBoolean(e),ua=()=>da("isWalletNotificationContentClickable"),pa=()=>!ca.valueExists("EdgeWalletPageHandlerVersion")||da("isWalletHomepageNotificationEnabled"),ha=pa()?import("./edge-wallet-notification.mojom-webui.js").then((e=>e?.EdgeWalletNotificationHandler?.getRemote?.())):null,fa="object"==typeof global&&global&&global.Object===Object&&global;var ga="object"==typeof self&&self&&self.Object===Object&&self;const Ea=(fa||ga||Function("return this")()).Symbol;var Ta=Object.prototype,Na=Ta.hasOwnProperty,ma=Ta.toString,_a=Ea?Ea.toStringTag:void 0;var Oa=Object.prototype.toString;var Aa=Ea?Ea.toStringTag:void 0;const Ia=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Aa&&Aa in Object(e)?function(e){var t=Na.call(e,_a),o=e[_a];try{e[_a]=void 0;var i=!0}catch(e){}var n=ma.call(e);return i&&(t?e[_a]=o:delete e[_a]),n}(e):function(e){return Oa.call(e)}(e)};var ba;!function(e){e.HISTOGRAM_LOG="histogram-log",e.HISTOGRAM_LINEAR="histogram-linear"}(ba||(ba={}));const Sa=window?.chrome?.metricsPrivate;var Ra;!function(e){e.TOKENIZATION_MASTERCARD="TOKENIZATION_MASTERCARD",e.TOKENIZATION_VISA="TOKENIZATION_VISA",e.TOKENIZATION_NOTIFICATION_MASTERCARD="TOKENIZATION_NOTIFICATION_MASTERCARD",e.TOKENIZATION_NOTIFICATION_VISA="TOKENIZATION_NOTIFICATION_VISA"}(Ra||(Ra={}));const ya=window?.chrome?.edgeWalletPrivate;var va,Ca,wa;!function(e){e.visa="visa",e.mastercard="mastercard",e.amex="amex",e.discover="discover"}(va||(va={})),function(e){e.expired="expired",e.lastused="lastused",e.bestdeal="bestdeal",e.notsynced="notsynced",e.tokenized="tokenized",e.tokenizationEligible="tokenizationEligible",e.partialCard="partialCard"}(Ca||(Ca={})),function(e){e[e.NONE=0]="NONE",e[e.INVALID_NAME=1]="INVALID_NAME",e[e.INVALID_EXP_DATE=2]="INVALID_EXP_DATE",e[e.INVALID_CVV=4]="INVALID_CVV",e[e.INVALID_ADDRESS=8]="INVALID_ADDRESS",e[e.INVALID_NETWORK_TYPE=16]="INVALID_NETWORK_TYPE",e[e.UNKNOWN_ERROR=32]="UNKNOWN_ERROR",e[e.kMaxValue=32]="kMaxValue"}(wa||(wa={}));const Ma=e=>va[e?.toLowerCase()],Da=e=>e?.slice(-4)||"";var Pa,xa,ka,La,Fa,Ga,Ba,Wa,Ua,Va,$a;function Ha(e,t){const{error:o}=e,i=o?.message?o?.message:o?.name,n=o?.stack;ds.logErrorDetails(i?`${i}. Source: ${t}`:`no error message. Source: ${t}`,n??"no error callstack")}($a=Pa||(Pa={})).WalletNotificationStatusMiniWallet="Microsoft.Wallet.NotificationStatus.MiniWallet",$a.WalletNotificationStatusMiniWalletWithAttraction="Microsoft.Wallet.NotificationStatus.MiniWallet.WithAttraction",$a.WalletNotificationStatusMiniWalletWithoutAttraction="Microsoft.Wallet.NotificationStatus.MiniWallet.WithoutAttraction",$a.WalletNotificationStatusAllApps="Microsoft.Wallet.NotificationStatus.AllApps",$a.WalletNotificationStatusAllAppsWithAttraction="Microsoft.Wallet.NotificationStatus.AllApps.WithAttraction",$a.WalletNotificationStatusAllAppsWithoutAttraction="Microsoft.Wallet.NotificationStatus.AllApps.WithoutAttraction",$a.WalletNotificationStatusHubHome="Microsoft.Wallet.NotificationStatus.HubHome",$a.WalletNotificationStatusHubHomeWithAttraction="Microsoft.Wallet.NotificationStatus.HubHome.WithAttraction",$a.WalletNotificationStatusHubHomeWithoutAttraction="Microsoft.Wallet.NotificationStatus.HubHome.WithoutAttraction",$a.WalletNotificationStatusHubHeader="Microsoft.Wallet.NotificationStatus.HubHeader",$a.WalletNotificationStatusHubHeaderWithAttraction="Microsoft.Wallet.NotificationStatus.HubHeader.WithAttraction",$a.WalletNotificationStatusHubHeaderWithoutAttraction="Microsoft.Wallet.NotificationStatus.HubHeader.WithoutAttraction",$a.WalletNotificationFirstBuildToFirstViewDuration="Microsoft.Wallet.Notification.Duration.FirstBuildToFirstViewInSeconds",$a.WalletNotificationFirstBuildToFirstEngagementDuration="Microsoft.Wallet.Notification.Duration.FirstBuildToFirstEngagementInSeconds",$a.WalletNotificationFirstViewToFirstEngagementDuration="Microsoft.Wallet.Notification.Duration.FirstViewToFirstEngagementInSeconds",$a.WalletNotificationCardExpiredDiffDaysOfEnagement="Microsoft.Wallet.Notification.DiffDaysOfEnagement.CardExpired",$a.WalletNotificationCardExpiringDiffDaysOfEnagement="Microsoft.Wallet.Notification.DiffDaysOfEnagement.CardExpiring",$a.NotificationFeaturePromotionStatus="Microsoft.Wallet.NotificationFeaturePromotionStatus",(Va=xa||(xa={}))[Va.NOTIFICATION_PROFILE_STATE_SHOWN=0]="NOTIFICATION_PROFILE_STATE_SHOWN",Va[Va.NOTIFICATION_PROFILE_STATE_ENGAGEMENT=1]="NOTIFICATION_PROFILE_STATE_ENGAGEMENT",Va[Va.NOTIFICATION_CARD_LOADED=2]="NOTIFICATION_CARD_LOADED",Va[Va.CARD_EXPIRED_IMPRESSION=3]="CARD_EXPIRED_IMPRESSION",Va[Va.CARD_EXPIRED_ENGAGEMENT=4]="CARD_EXPIRED_ENGAGEMENT",Va[Va.CARD_EXPIRED_ENGAGEMENT_SUCCESS=5]="CARD_EXPIRED_ENGAGEMENT_SUCCESS",Va[Va.CARD_EXPIRED_ENGAGEMENT_FAILURE=6]="CARD_EXPIRED_ENGAGEMENT_FAILURE",Va[Va.CARD_EXPIRED_SNOOZE=7]="CARD_EXPIRED_SNOOZE",Va[Va.CARD_EXPIRED_DISMISS_PERMANENTLY=8]="CARD_EXPIRED_DISMISS_PERMANENTLY",Va[Va.CARD_EXPIRING_SOON_IMPRESSION=9]="CARD_EXPIRING_SOON_IMPRESSION",Va[Va.CARD_EXPIRING_SOON_ENGAGEMENT=10]="CARD_EXPIRING_SOON_ENGAGEMENT",Va[Va.CARD_EXPIRING_SOON_ENGAGEMENT_SUCCESS=11]="CARD_EXPIRING_SOON_ENGAGEMENT_SUCCESS",Va[Va.CARD_EXPIRING_SOON_ENGAGEMENT_FAILURE=12]="CARD_EXPIRING_SOON_ENGAGEMENT_FAILURE",Va[Va.CARD_EXPIRING_SOON_SNOOZE=13]="CARD_EXPIRING_SOON_SNOOZE",Va[Va.CARD_EXPIRING_SOON_DISMISS_PERMANENTLY=14]="CARD_EXPIRING_SOON_DISMISS_PERMANENTLY",Va[Va.ROAM_CARD_IMPRESSION=15]="ROAM_CARD_IMPRESSION",Va[Va.ROAM_CARD_ENGAGEMENT=16]="ROAM_CARD_ENGAGEMENT",Va[Va.ROAM_CARD_ENGAGEMENT_SUCCESS=17]="ROAM_CARD_ENGAGEMENT_SUCCESS",Va[Va.ROAM_CARD_ENGAGEMENT_FAILURE=18]="ROAM_CARD_ENGAGEMENT_FAILURE",Va[Va.ROAM_CARD_SNOOZE=19]="ROAM_CARD_SNOOZE",Va[Va.ROAM_CARD_DISMISS_PERMANENTLY=20]="ROAM_CARD_DISMISS_PERMANENTLY",Va[Va.SIGN_UP_REBATES_IMPRESSION_DEPRECATED=21]="SIGN_UP_REBATES_IMPRESSION_DEPRECATED",Va[Va.SIGN_UP_REBATES_ENGAGEMENT_DEPRECATED=22]="SIGN_UP_REBATES_ENGAGEMENT_DEPRECATED",Va[Va.SIGN_UP_REBATES_ENGAGEMENT_SUCCESS_DEPRECATED=23]="SIGN_UP_REBATES_ENGAGEMENT_SUCCESS_DEPRECATED",Va[Va.SIGN_UP_REBATES_ENGAGEMENT_FAILURE_DEPRECATED=24]="SIGN_UP_REBATES_ENGAGEMENT_FAILURE_DEPRECATED",Va[Va.SIGN_UP_REBATES_SNOOZE_DEPRECATED=25]="SIGN_UP_REBATES_SNOOZE_DEPRECATED",Va[Va.SIGN_UP_REBATES_DISMISS_PERMANENTLY_DEPRECATED=26]="SIGN_UP_REBATES_DISMISS_PERMANENTLY_DEPRECATED",Va[Va.REBATES_PAYOUT_IMPRESSION_DEPRECATED=27]="REBATES_PAYOUT_IMPRESSION_DEPRECATED",Va[Va.REBATES_PAYOUT_ENGAGEMENT_DEPRECATED=28]="REBATES_PAYOUT_ENGAGEMENT_DEPRECATED",Va[Va.REBATES_PAYOUT_ENGAGEMENT_SUCCESS_DEPRECATED=29]="REBATES_PAYOUT_ENGAGEMENT_SUCCESS_DEPRECATED",Va[Va.REBATES_PAYOUT_ENGAGEMENT_FAILURE_DEPRECATED=30]="REBATES_PAYOUT_ENGAGEMENT_FAILURE_DEPRECATED",Va[Va.REBATES_PAYOUT_SNOOZE_DEPRECATED=31]="REBATES_PAYOUT_SNOOZE_DEPRECATED",Va[Va.REBATES_PAYOUT_DISMISS_PERMANENTLY_DEPRECATED=32]="REBATES_PAYOUT_DISMISS_PERMANENTLY_DEPRECATED",Va[Va.SIGN_UP_REWARDS_IMPRESSION_DEPRECATED=33]="SIGN_UP_REWARDS_IMPRESSION_DEPRECATED",Va[Va.SIGN_UP_REWARDS_ENGAGEMENT_DEPRECATED=34]="SIGN_UP_REWARDS_ENGAGEMENT_DEPRECATED",Va[Va.SIGN_UP_REWARDS_SNOOZE_DEPRECATED=35]="SIGN_UP_REWARDS_SNOOZE_DEPRECATED",Va[Va.SIGN_UP_REWARDS_DISMISS_PERMANENTLY_DEPRECATED=36]="SIGN_UP_REWARDS_DISMISS_PERMANENTLY_DEPRECATED",Va[Va.NOTIFICATION_CARD_ENGAGEMENT=37]="NOTIFICATION_CARD_ENGAGEMENT",Va[Va.CARD_TOKENIZATION_ELIGIBLE_IMPRESSION=38]="CARD_TOKENIZATION_ELIGIBLE_IMPRESSION",Va[Va.CARD_TOKENIZATION_ELIGIBLE_ENGAGEMENT=39]="CARD_TOKENIZATION_ELIGIBLE_ENGAGEMENT",Va[Va.CARD_TOKENIZATION_ELIGIBLE_SNOOZE=40]="CARD_TOKENIZATION_ELIGIBLE_SNOOZE",Va[Va.CARD_TOKENIZATION_ELIGIBLE_DISMISS_PERMANENTLY=41]="CARD_TOKENIZATION_ELIGIBLE_DISMISS_PERMANENTLY",Va[Va.UNSUPPORTED_TYPE=42]="UNSUPPORTED_TYPE",Va[Va.PERSONALIZED_OFFERS_AVAILABLE_IMPRESSION=43]="PERSONALIZED_OFFERS_AVAILABLE_IMPRESSION",Va[Va.PERSONALIZED_OFFERS_AVAILABLE_ENGAGEMENT=44]="PERSONALIZED_OFFERS_AVAILABLE_ENGAGEMENT",Va[Va.PERSONALIZED_OFFERS_AVAILABLE_SNOOZE=45]="PERSONALIZED_OFFERS_AVAILABLE_SNOOZE",Va[Va.PERSONALIZED_OFFERS_AVAILABLE_DISMISS_PERMANENTLY=46]="PERSONALIZED_OFFERS_AVAILABLE_DISMISS_PERMANENTLY",Va[Va.WALLET_NOTIFICATION_INTRODUCTION_FRE_IMPRESSION=47]="WALLET_NOTIFICATION_INTRODUCTION_FRE_IMPRESSION",Va[Va.WALLET_NOTIFICATION_INTRODUCTION_FRE_ENGAGEMENT=48]="WALLET_NOTIFICATION_INTRODUCTION_FRE_ENGAGEMENT",Va[Va.WALLET_NOTIFICATION_INTRODUCTION_FRE_SNOOZE=49]="WALLET_NOTIFICATION_INTRODUCTION_FRE_SNOOZE",Va[Va.WALLET_NOTIFICATION_INTRODUCTION_FRE_DISMISS_PERMANENTLY=50]="WALLET_NOTIFICATION_INTRODUCTION_FRE_DISMISS_PERMANENTLY",Va[Va.PASSWORD_LEAKAGE_IMPRESSION=51]="PASSWORD_LEAKAGE_IMPRESSION",Va[Va.PASSWORD_LEAKAGE_ENGAGEMENT=52]="PASSWORD_LEAKAGE_ENGAGEMENT",Va[Va.PASSWORD_LEAKAGE_SNOOZE=53]="PASSWORD_LEAKAGE_SNOOZE",Va[Va.PASSWORD_LEAKAGE_DISMISS_PERMANENTLY=54]="PASSWORD_LEAKAGE_DISMISS_PERMANENTLY",Va[Va.SIGN_UP_CRYPTOWALLET_IMPRESSION=55]="SIGN_UP_CRYPTOWALLET_IMPRESSION",Va[Va.SIGN_UP_CRYPTOWALLET_ENGAGEMENT=56]="SIGN_UP_CRYPTOWALLET_ENGAGEMENT",Va[Va.SIGN_UP_CRYPTOWALLET_SNOOZE=57]="SIGN_UP_CRYPTOWALLET_SNOOZE",Va[Va.SIGN_UP_CRYPTOWALLET_DISMISS_PERMANENTLY=58]="SIGN_UP_CRYPTOWALLET_DISMISS_PERMANENTLY",Va[Va.GIVE_WITH_BING_IMPRESSION_DEPRECATED=59]="GIVE_WITH_BING_IMPRESSION_DEPRECATED",Va[Va.GIVE_WITH_BING_ENGAGEMENT_DEPRECATED=60]="GIVE_WITH_BING_ENGAGEMENT_DEPRECATED",Va[Va.GIVE_WITH_BING_SNOOZE_DEPRECATED=61]="GIVE_WITH_BING_SNOOZE_DEPRECATED",Va[Va.GIVE_WITH_BING_DISMISS_PERMANENTLY_DEPRECATED=62]="GIVE_WITH_BING_DISMISS_PERMANENTLY_DEPRECATED",Va[Va.NOTIFICATION_INVALIDATED=63]="NOTIFICATION_INVALIDATED",Va[Va.UPCOMING_HOTEL_RESERVATION_IMPRESSION=64]="UPCOMING_HOTEL_RESERVATION_IMPRESSION",Va[Va.UPCOMING_HOTEL_RESERVATION_ENGAGEMENT=65]="UPCOMING_HOTEL_RESERVATION_ENGAGEMENT",Va[Va.UPCOMING_HOTEL_RESERVATION_SNOOZE=66]="UPCOMING_HOTEL_RESERVATION_SNOOZE",Va[Va.UPCOMING_HOTEL_RESERVATION_DISMISS_PERMANENTLY=67]="UPCOMING_HOTEL_RESERVATION_DISMISS_PERMANENTLY",Va[Va.NOTIFICATION_CARD_ENGAGEMENT_CTA=68]="NOTIFICATION_CARD_ENGAGEMENT_CTA",Va[Va.NOTIFICATION_CARD_ENGAGEMENT_DISMISS=69]="NOTIFICATION_CARD_ENGAGEMENT_DISMISS",Va[Va.NOTIFICATION_CARD_ENGAGEMENT_SNOOZE=70]="NOTIFICATION_CARD_ENGAGEMENT_SNOOZE",Va[Va.NOTIFICATION_CARD_ENGAGEMENT_FROM_CONTENT=71]="NOTIFICATION_CARD_ENGAGEMENT_FROM_CONTENT",Va[Va.DONATION_SUMMARY_IMPRESSION=72]="DONATION_SUMMARY_IMPRESSION",Va[Va.DONATION_SUMMARY_ENGAGEMENT=73]="DONATION_SUMMARY_ENGAGEMENT",Va[Va.DONATION_SUMMARY_SNOOZE=74]="DONATION_SUMMARY_SNOOZE",Va[Va.DONATION_SUMMARY_DISMISS_PERMANENTLY=75]="DONATION_SUMMARY_DISMISS_PERMANENTLY",Va[Va.FEATURE_PROMOTION_IMPRESSION=76]="FEATURE_PROMOTION_IMPRESSION",Va[Va.FEATURE_PROMOTION_ENGAGEMENT=77]="FEATURE_PROMOTION_ENGAGEMENT",Va[Va.FEATURE_PROMOTION_SNOOZE=78]="FEATURE_PROMOTION_SNOOZE",Va[Va.FEATURE_PROMOTION_DISMISS_PERMANENTLY=79]="FEATURE_PROMOTION_DISMISS_PERMANENTLY",Va[Va.PACKAGE_TRACKING_IMPRESSION=80]="PACKAGE_TRACKING_IMPRESSION",Va[Va.PACKAGE_TRACKING_SNOOZE=81]="PACKAGE_TRACKING_SNOOZE",Va[Va.PACKAGE_TRACKING_DISMISS_PERMANENTLY=82]="PACKAGE_TRACKING_DISMISS_PERMANENTLY",Va[Va.PACKAGE_TRACKING_VIEW_ORDER_ENGAGEMENT=83]="PACKAGE_TRACKING_VIEW_ORDER_ENGAGEMENT",Va[Va.PACKAGE_TRACKING_TRACK_ORDER_ENGAGEMENT=84]="PACKAGE_TRACKING_TRACK_ORDER_ENGAGEMENT",Va[Va.REBATES_IMPRESSION=85]="REBATES_IMPRESSION",Va[Va.REBATES_ENGAGEMENT=86]="REBATES_ENGAGEMENT",Va[Va.REBATES_ENGAGEMENT_SUCCESS=87]="REBATES_ENGAGEMENT_SUCCESS",Va[Va.REBATES_ENGAGEMENT_FAILURE=88]="REBATES_ENGAGEMENT_FAILURE",Va[Va.REBATES_SNOOZE=89]="REBATES_SNOOZE",Va[Va.REBATES_DISMISS_PERMANENTLY=90]="REBATES_DISMISS_PERMANENTLY",Va[Va.ETREE_CAMPAIGN_IMPRESSION=91]="ETREE_CAMPAIGN_IMPRESSION",Va[Va.ETREE_CAMPAIGN_ENGAGEMENT=92]="ETREE_CAMPAIGN_ENGAGEMENT",Va[Va.ETREE_CAMPAIGN_SNOOZE=93]="ETREE_CAMPAIGN_SNOOZE",Va[Va.ETREE_CAMPAIGN_DISMISS_PERMANENTLY=94]="ETREE_CAMPAIGN_DISMISS_PERMANENTLY",Va[Va.PWA_PROMOTION_IMPRESSION=95]="PWA_PROMOTION_IMPRESSION",Va[Va.PWA_PROMOTION_ENGAGEMENT=96]="PWA_PROMOTION_ENGAGEMENT",Va[Va.PWA_PROMOTION_SNOOZE=97]="PWA_PROMOTION_SNOOZE",Va[Va.PWA_PROMOTION_DISMISS_PERMANENTLY=98]="PWA_PROMOTION_DISMISS_PERMANENTLY",Va[Va.ETREE_NORMAL_IMPRESSION=99]="ETREE_NORMAL_IMPRESSION",Va[Va.ETREE_NORMAL_ENGAGEMENT=100]="ETREE_NORMAL_ENGAGEMENT",Va[Va.ETREE_NORMAL_SNOOZE=101]="ETREE_NORMAL_SNOOZE",Va[Va.ETREE_NORMAL_DISMISS_PERMANENTLY=102]="ETREE_NORMAL_DISMISS_PERMANENTLY",Va[Va.ETREE_NORMAL_ENROLLED_IMPRESSION=103]="ETREE_NORMAL_ENROLLED_IMPRESSION",Va[Va.ETREE_NORMAL_ENROLLED_ENGAGEMENT=104]="ETREE_NORMAL_ENROLLED_ENGAGEMENT",Va[Va.ETREE_NORMAL_ENROLLED_SNOOZE=105]="ETREE_NORMAL_ENROLLED_SNOOZE",Va[Va.ETREE_NORMAL_ENROLLED_DISMISS_PERMANENTLY=106]="ETREE_NORMAL_ENROLLED_DISMISS_PERMANENTLY",Va[Va.ETREE_NORMAL_NONENROLLED_IMPRESSION=107]="ETREE_NORMAL_NONENROLLED_IMPRESSION",Va[Va.ETREE_NORMAL_NONENROLLED_ENGAGEMENT=108]="ETREE_NORMAL_NONENROLLED_ENGAGEMENT",Va[Va.ETREE_NORMAL_NONENROLLED_SNOOZE=109]="ETREE_NORMAL_NONENROLLED_SNOOZE",Va[Va.ETREE_NORMAL_NONENROLLED_DISMISS_PERMANENTLY=110]="ETREE_NORMAL_NONENROLLED_DISMISS_PERMANENTLY",Va[Va.DONATION_TREND_NPO_IMPRESSION=111]="DONATION_TREND_NPO_IMPRESSION",Va[Va.DONATION_TREND_NPO_ENGAGEMENT=112]="DONATION_TREND_NPO_ENGAGEMENT",Va[Va.DONATION_TREND_NPO_SNOOZE=113]="DONATION_TREND_NPO_SNOOZE",Va[Va.DONATION_TREND_NPO_DISMISS_PERMANENTLY=114]="DONATION_TREND_NPO_DISMISS_PERMANENTLY",Va[Va.MAX=115]="MAX",function(e){e[e.FEATURE_PROMOTION_1_IMPRESSION_WITH_ATTRACTION=0]="FEATURE_PROMOTION_1_IMPRESSION_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_1_ENGAGEMENT_WITH_ATTRACTION=1]="FEATURE_PROMOTION_1_ENGAGEMENT_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_1_SNOOZE_WITH_ATTRACTION=2]="FEATURE_PROMOTION_1_SNOOZE_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_1_DISMISS_PERMANENTLY_WITH_ATTRACTION=3]="FEATURE_PROMOTION_1_DISMISS_PERMANENTLY_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_1_IMPRESSION_WITHOUT_ATTRACTION=4]="FEATURE_PROMOTION_1_IMPRESSION_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_1_ENGAGEMENT_WITHOUT_ATTRACTION=5]="FEATURE_PROMOTION_1_ENGAGEMENT_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_1_SNOOZE_WITHOUT_ATTRACTION=6]="FEATURE_PROMOTION_1_SNOOZE_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_1_DISMISS_PERMANENTLY_WITHOUT_ATTRACTION=7]="FEATURE_PROMOTION_1_DISMISS_PERMANENTLY_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_2_IMPRESSION_WITH_ATTRACTION=8]="FEATURE_PROMOTION_2_IMPRESSION_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_2_ENGAGEMENT_WITH_ATTRACTION=9]="FEATURE_PROMOTION_2_ENGAGEMENT_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_2_SNOOZE_WITH_ATTRACTION=10]="FEATURE_PROMOTION_2_SNOOZE_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_2_DISMISS_PERMANENTLY_WITH_ATTRACTION=11]="FEATURE_PROMOTION_2_DISMISS_PERMANENTLY_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_2_IMPRESSION_WITHOUT_ATTRACTION=12]="FEATURE_PROMOTION_2_IMPRESSION_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_2_ENGAGEMENT_WITHOUT_ATTRACTION=13]="FEATURE_PROMOTION_2_ENGAGEMENT_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_2_SNOOZE_WITHOUT_ATTRACTION=14]="FEATURE_PROMOTION_2_SNOOZE_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_2_DISMISS_PERMANENTLY_WITHOUT_ATTRACTION=15]="FEATURE_PROMOTION_2_DISMISS_PERMANENTLY_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_3_IMPRESSION_WITH_ATTRACTION=16]="FEATURE_PROMOTION_3_IMPRESSION_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_3_ENGAGEMENT_WITH_ATTRACTION=17]="FEATURE_PROMOTION_3_ENGAGEMENT_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_3_SNOOZE_WITH_ATTRACTION=18]="FEATURE_PROMOTION_3_SNOOZE_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_3_DISMISS_PERMANENTLY_WITH_ATTRACTION=19]="FEATURE_PROMOTION_3_DISMISS_PERMANENTLY_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_3_IMPRESSION_WITHOUT_ATTRACTION=20]="FEATURE_PROMOTION_3_IMPRESSION_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_3_ENGAGEMENT_WITHOUT_ATTRACTION=21]="FEATURE_PROMOTION_3_ENGAGEMENT_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_3_SNOOZE_WITHOUT_ATTRACTION=22]="FEATURE_PROMOTION_3_SNOOZE_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_3_DISMISS_PERMANENTLY_WITHOUT_ATTRACTION=23]="FEATURE_PROMOTION_3_DISMISS_PERMANENTLY_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_4_IMPRESSION_WITH_ATTRACTION=24]="FEATURE_PROMOTION_4_IMPRESSION_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_4_ENGAGEMENT_WITH_ATTRACTION=25]="FEATURE_PROMOTION_4_ENGAGEMENT_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_4_SNOOZE_WITH_ATTRACTION=26]="FEATURE_PROMOTION_4_SNOOZE_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_4_DISMISS_PERMANENTLY_WITH_ATTRACTION=27]="FEATURE_PROMOTION_4_DISMISS_PERMANENTLY_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_4_IMPRESSION_WITHOUT_ATTRACTION=28]="FEATURE_PROMOTION_4_IMPRESSION_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_4_ENGAGEMENT_WITHOUT_ATTRACTION=29]="FEATURE_PROMOTION_4_ENGAGEMENT_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_4_SNOOZE_WITHOUT_ATTRACTION=30]="FEATURE_PROMOTION_4_SNOOZE_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_4_DISMISS_PERMANENTLY_WITHOUT_ATTRACTION=31]="FEATURE_PROMOTION_4_DISMISS_PERMANENTLY_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_5_IMPRESSION_WITH_ATTRACTION=32]="FEATURE_PROMOTION_5_IMPRESSION_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_5_ENGAGEMENT_WITH_ATTRACTION=33]="FEATURE_PROMOTION_5_ENGAGEMENT_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_5_SNOOZE_WITH_ATTRACTION=34]="FEATURE_PROMOTION_5_SNOOZE_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_5_DISMISS_PERMANENTLY_WITH_ATTRACTION=35]="FEATURE_PROMOTION_5_DISMISS_PERMANENTLY_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_5_IMPRESSION_WITHOUT_ATTRACTION=36]="FEATURE_PROMOTION_5_IMPRESSION_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_5_ENGAGEMENT_WITHOUT_ATTRACTION=37]="FEATURE_PROMOTION_5_ENGAGEMENT_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_5_SNOOZE_WITHOUT_ATTRACTION=38]="FEATURE_PROMOTION_5_SNOOZE_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_5_DISMISS_PERMANENTLY_WITHOUT_ATTRACTION=39]="FEATURE_PROMOTION_5_DISMISS_PERMANENTLY_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_6_IMPRESSION_WITH_ATTRACTION=40]="FEATURE_PROMOTION_6_IMPRESSION_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_6_ENGAGEMENT_WITH_ATTRACTION=41]="FEATURE_PROMOTION_6_ENGAGEMENT_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_6_SNOOZE_WITH_ATTRACTION=42]="FEATURE_PROMOTION_6_SNOOZE_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_6_DISMISS_PERMANENTLY_WITH_ATTRACTION=43]="FEATURE_PROMOTION_6_DISMISS_PERMANENTLY_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_6_IMPRESSION_WITHOUT_ATTRACTION=44]="FEATURE_PROMOTION_6_IMPRESSION_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_6_ENGAGEMENT_WITHOUT_ATTRACTION=45]="FEATURE_PROMOTION_6_ENGAGEMENT_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_6_SNOOZE_WITHOUT_ATTRACTION=46]="FEATURE_PROMOTION_6_SNOOZE_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_6_DISMISS_PERMANENTLY_WITHOUT_ATTRACTION=47]="FEATURE_PROMOTION_6_DISMISS_PERMANENTLY_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_7_IMPRESSION_WITH_ATTRACTION=48]="FEATURE_PROMOTION_7_IMPRESSION_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_7_ENGAGEMENT_WITH_ATTRACTION=49]="FEATURE_PROMOTION_7_ENGAGEMENT_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_7_SNOOZE_WITH_ATTRACTION=50]="FEATURE_PROMOTION_7_SNOOZE_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_7_DISMISS_PERMANENTLY_WITH_ATTRACTION=51]="FEATURE_PROMOTION_7_DISMISS_PERMANENTLY_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_7_IMPRESSION_WITHOUT_ATTRACTION=52]="FEATURE_PROMOTION_7_IMPRESSION_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_7_ENGAGEMENT_WITHOUT_ATTRACTION=53]="FEATURE_PROMOTION_7_ENGAGEMENT_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_7_SNOOZE_WITHOUT_ATTRACTION=54]="FEATURE_PROMOTION_7_SNOOZE_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_7_DISMISS_PERMANENTLY_WITHOUT_ATTRACTION=55]="FEATURE_PROMOTION_7_DISMISS_PERMANENTLY_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_8_IMPRESSION_WITH_ATTRACTION=56]="FEATURE_PROMOTION_8_IMPRESSION_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_8_ENGAGEMENT_WITH_ATTRACTION=57]="FEATURE_PROMOTION_8_ENGAGEMENT_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_8_SNOOZE_WITH_ATTRACTION=58]="FEATURE_PROMOTION_8_SNOOZE_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_8_DISMISS_PERMANENTLY_WITH_ATTRACTION=59]="FEATURE_PROMOTION_8_DISMISS_PERMANENTLY_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_8_IMPRESSION_WITHOUT_ATTRACTION=60]="FEATURE_PROMOTION_8_IMPRESSION_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_8_ENGAGEMENT_WITHOUT_ATTRACTION=61]="FEATURE_PROMOTION_8_ENGAGEMENT_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_8_SNOOZE_WITHOUT_ATTRACTION=62]="FEATURE_PROMOTION_8_SNOOZE_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_8_DISMISS_PERMANENTLY_WITHOUT_ATTRACTION=63]="FEATURE_PROMOTION_8_DISMISS_PERMANENTLY_WITHOUT_ATTRACTION",e[e.MAX=64]="MAX"}(ka||(ka={})),(Ua=La||(La={})).CardExpired="CardExpired",Ua.CardExpiring="CardExpiring",Ua.RoamCard="RoamCard",Ua.CardTokenizationEligible="CardTokenizationEligible",Ua.PasswordLeakage="PasswordLeakage",Ua.PersonalizedOffersAvailable="PersonalizedOffersAvailable",Ua.UpcomingHotelReservations="UpcomingHotelReservations",Ua.SignupCryptoWallet="SignupCryptoWallet",Ua.DonationSummary="DonationSummary",Ua.FeaturePromotion="FeaturePromotion",Ua.PackageTracking="PackageTracking",Ua.Rebates="Rebates",Ua.EtreeCampaign="EtreeCampaign",Ua.Etree="Etree",Ua.PWAPromotion="PWAPromotion",Ua.DonationTrendNpo="DonationTrendNpo",function(e){e[e.WalletHomeBell=0]="WalletHomeBell",e[e.WalletHomeOther=1]="WalletHomeOther"}(Fa||(Fa={})),(Wa=Ga||(Ga={})).MiniWallet="MiniWallet",Wa.HubHomepage="HubHomepage",Wa.HubHeader="HubHeader",function(e){e.Halloween="41F3EF79-FA45-4A06-B38C-F46B514817B2",e.Christmas="40B1A79D-3455-46F8-B20E-AF2568178AEA",e.NormalEnrolled="41F3EF79-FA45-4A06-B38C-F46B514817B2",e.NormalNonEnrolled="86F10C77-0F63-40E0-B1D8-086A3181F6B5"}(Ba||(Ba={}));const za=[xa.CARD_EXPIRED_ENGAGEMENT,xa.ROAM_CARD_ENGAGEMENT,xa.CARD_EXPIRING_SOON_ENGAGEMENT,xa.CARD_TOKENIZATION_ELIGIBLE_ENGAGEMENT,xa.WALLET_NOTIFICATION_INTRODUCTION_FRE_ENGAGEMENT,xa.SIGN_UP_CRYPTOWALLET_ENGAGEMENT,xa.PASSWORD_LEAKAGE_ENGAGEMENT,xa.PERSONALIZED_OFFERS_AVAILABLE_ENGAGEMENT,xa.UPCOMING_HOTEL_RESERVATION_ENGAGEMENT,xa.DONATION_SUMMARY_ENGAGEMENT,xa.FEATURE_PROMOTION_ENGAGEMENT,xa.REBATES_ENGAGEMENT,xa.ETREE_CAMPAIGN_ENGAGEMENT,xa.ETREE_NORMAL_ENGAGEMENT,xa.PWA_PROMOTION_ENGAGEMENT,xa.DONATION_TREND_NPO_ENGAGEMENT,xa.CARD_EXPIRED_DISMISS_PERMANENTLY,xa.ROAM_CARD_DISMISS_PERMANENTLY,xa.CARD_EXPIRING_SOON_DISMISS_PERMANENTLY,xa.CARD_TOKENIZATION_ELIGIBLE_DISMISS_PERMANENTLY,xa.WALLET_NOTIFICATION_INTRODUCTION_FRE_DISMISS_PERMANENTLY,xa.SIGN_UP_CRYPTOWALLET_DISMISS_PERMANENTLY,xa.PASSWORD_LEAKAGE_DISMISS_PERMANENTLY,xa.PERSONALIZED_OFFERS_AVAILABLE_DISMISS_PERMANENTLY,xa.UPCOMING_HOTEL_RESERVATION_DISMISS_PERMANENTLY,xa.DONATION_SUMMARY_DISMISS_PERMANENTLY,xa.FEATURE_PROMOTION_DISMISS_PERMANENTLY,xa.REBATES_DISMISS_PERMANENTLY,xa.ETREE_CAMPAIGN_DISMISS_PERMANENTLY,xa.ETREE_NORMAL_DISMISS_PERMANENTLY,xa.PWA_PROMOTION_DISMISS_PERMANENTLY,xa.DONATION_TREND_NPO_DISMISS_PERMANENTLY,xa.CARD_EXPIRED_SNOOZE,xa.ROAM_CARD_SNOOZE,xa.CARD_EXPIRING_SOON_SNOOZE,xa.CARD_TOKENIZATION_ELIGIBLE_SNOOZE,xa.WALLET_NOTIFICATION_INTRODUCTION_FRE_SNOOZE,xa.SIGN_UP_CRYPTOWALLET_SNOOZE,xa.PASSWORD_LEAKAGE_SNOOZE,xa.PERSONALIZED_OFFERS_AVAILABLE_SNOOZE,xa.UPCOMING_HOTEL_RESERVATION_SNOOZE,xa.DONATION_SUMMARY_SNOOZE,xa.FEATURE_PROMOTION_SNOOZE,xa.REBATES_SNOOZE,xa.ETREE_CAMPAIGN_SNOOZE,xa.ETREE_NORMAL_SNOOZE,xa.PWA_PROMOTION_SNOOZE,xa.DONATION_TREND_NPO_SNOOZE],ja=[xa.CARD_EXPIRED_IMPRESSION,xa.ROAM_CARD_IMPRESSION,xa.SIGN_UP_CRYPTOWALLET_IMPRESSION,xa.CARD_EXPIRING_SOON_IMPRESSION,xa.CARD_TOKENIZATION_ELIGIBLE_IMPRESSION,xa.WALLET_NOTIFICATION_INTRODUCTION_FRE_IMPRESSION,xa.PASSWORD_LEAKAGE_IMPRESSION,xa.PERSONALIZED_OFFERS_AVAILABLE_IMPRESSION,xa.UPCOMING_HOTEL_RESERVATION_IMPRESSION,xa.DONATION_SUMMARY_IMPRESSION,xa.FEATURE_PROMOTION_IMPRESSION,xa.REBATES_IMPRESSION,xa.ETREE_CAMPAIGN_IMPRESSION,xa.ETREE_NORMAL_IMPRESSION,xa.PWA_PROMOTION_IMPRESSION,xa.DONATION_TREND_NPO_IMPRESSION],Ya=new Map([[xa.CARD_EXPIRED_ENGAGEMENT,xa.NOTIFICATION_CARD_ENGAGEMENT_CTA],[xa.ROAM_CARD_ENGAGEMENT,xa.NOTIFICATION_CARD_ENGAGEMENT_CTA],[xa.CARD_EXPIRING_SOON_ENGAGEMENT,xa.NOTIFICATION_CARD_ENGAGEMENT_CTA],[xa.CARD_TOKENIZATION_ELIGIBLE_ENGAGEMENT,xa.NOTIFICATION_CARD_ENGAGEMENT_CTA],[xa.WALLET_NOTIFICATION_INTRODUCTION_FRE_ENGAGEMENT,xa.NOTIFICATION_CARD_ENGAGEMENT_CTA],[xa.SIGN_UP_CRYPTOWALLET_ENGAGEMENT,xa.NOTIFICATION_CARD_ENGAGEMENT_CTA],[xa.PASSWORD_LEAKAGE_ENGAGEMENT,xa.NOTIFICATION_CARD_ENGAGEMENT_CTA],[xa.PERSONALIZED_OFFERS_AVAILABLE_ENGAGEMENT,xa.NOTIFICATION_CARD_ENGAGEMENT_CTA],[xa.UPCOMING_HOTEL_RESERVATION_ENGAGEMENT,xa.NOTIFICATION_CARD_ENGAGEMENT_CTA],[xa.DONATION_SUMMARY_ENGAGEMENT,xa.NOTIFICATION_CARD_ENGAGEMENT_CTA],[xa.FEATURE_PROMOTION_ENGAGEMENT,xa.NOTIFICATION_CARD_ENGAGEMENT_CTA],[xa.REBATES_ENGAGEMENT,xa.NOTIFICATION_CARD_ENGAGEMENT_CTA],[xa.ETREE_CAMPAIGN_ENGAGEMENT,xa.NOTIFICATION_CARD_ENGAGEMENT_CTA],[xa.ETREE_NORMAL_ENGAGEMENT,xa.NOTIFICATION_CARD_ENGAGEMENT_CTA],[xa.PWA_PROMOTION_ENGAGEMENT,xa.NOTIFICATION_CARD_ENGAGEMENT_CTA],[xa.DONATION_TREND_NPO_ENGAGEMENT,xa.NOTIFICATION_CARD_ENGAGEMENT_CTA],[xa.CARD_EXPIRED_DISMISS_PERMANENTLY,xa.NOTIFICATION_CARD_ENGAGEMENT_DISMISS],[xa.ROAM_CARD_DISMISS_PERMANENTLY,xa.NOTIFICATION_CARD_ENGAGEMENT_DISMISS],[xa.CARD_EXPIRING_SOON_DISMISS_PERMANENTLY,xa.NOTIFICATION_CARD_ENGAGEMENT_DISMISS],[xa.CARD_TOKENIZATION_ELIGIBLE_DISMISS_PERMANENTLY,xa.NOTIFICATION_CARD_ENGAGEMENT_DISMISS],[xa.WALLET_NOTIFICATION_INTRODUCTION_FRE_DISMISS_PERMANENTLY,xa.NOTIFICATION_CARD_ENGAGEMENT_DISMISS],[xa.SIGN_UP_CRYPTOWALLET_DISMISS_PERMANENTLY,xa.NOTIFICATION_CARD_ENGAGEMENT_DISMISS],[xa.PASSWORD_LEAKAGE_DISMISS_PERMANENTLY,xa.NOTIFICATION_CARD_ENGAGEMENT_DISMISS],[xa.PERSONALIZED_OFFERS_AVAILABLE_DISMISS_PERMANENTLY,xa.NOTIFICATION_CARD_ENGAGEMENT_DISMISS],[xa.UPCOMING_HOTEL_RESERVATION_DISMISS_PERMANENTLY,xa.NOTIFICATION_CARD_ENGAGEMENT_DISMISS],[xa.DONATION_SUMMARY_DISMISS_PERMANENTLY,xa.NOTIFICATION_CARD_ENGAGEMENT_DISMISS],[xa.FEATURE_PROMOTION_DISMISS_PERMANENTLY,xa.NOTIFICATION_CARD_ENGAGEMENT_DISMISS],[xa.REBATES_DISMISS_PERMANENTLY,xa.NOTIFICATION_CARD_ENGAGEMENT_DISMISS],[xa.ETREE_CAMPAIGN_DISMISS_PERMANENTLY,xa.NOTIFICATION_CARD_ENGAGEMENT_DISMISS],[xa.ETREE_NORMAL_DISMISS_PERMANENTLY,xa.NOTIFICATION_CARD_ENGAGEMENT_DISMISS],[xa.PWA_PROMOTION_DISMISS_PERMANENTLY,xa.NOTIFICATION_CARD_ENGAGEMENT_DISMISS],[xa.DONATION_TREND_NPO_DISMISS_PERMANENTLY,xa.NOTIFICATION_CARD_ENGAGEMENT_DISMISS],[xa.CARD_EXPIRED_SNOOZE,xa.NOTIFICATION_CARD_ENGAGEMENT_SNOOZE],[xa.ROAM_CARD_SNOOZE,xa.NOTIFICATION_CARD_ENGAGEMENT_SNOOZE],[xa.CARD_EXPIRING_SOON_SNOOZE,xa.NOTIFICATION_CARD_ENGAGEMENT_SNOOZE],[xa.CARD_TOKENIZATION_ELIGIBLE_SNOOZE,xa.NOTIFICATION_CARD_ENGAGEMENT_SNOOZE],[xa.WALLET_NOTIFICATION_INTRODUCTION_FRE_SNOOZE,xa.NOTIFICATION_CARD_ENGAGEMENT_SNOOZE],[xa.SIGN_UP_CRYPTOWALLET_SNOOZE,xa.NOTIFICATION_CARD_ENGAGEMENT_SNOOZE],[xa.PASSWORD_LEAKAGE_SNOOZE,xa.NOTIFICATION_CARD_ENGAGEMENT_SNOOZE],[xa.PERSONALIZED_OFFERS_AVAILABLE_SNOOZE,xa.NOTIFICATION_CARD_ENGAGEMENT_SNOOZE],[xa.UPCOMING_HOTEL_RESERVATION_SNOOZE,xa.NOTIFICATION_CARD_ENGAGEMENT_SNOOZE],[xa.DONATION_SUMMARY_SNOOZE,xa.NOTIFICATION_CARD_ENGAGEMENT_SNOOZE],[xa.FEATURE_PROMOTION_SNOOZE,xa.NOTIFICATION_CARD_ENGAGEMENT_SNOOZE],[xa.REBATES_SNOOZE,xa.NOTIFICATION_CARD_ENGAGEMENT_SNOOZE],[xa.ETREE_CAMPAIGN_SNOOZE,xa.NOTIFICATION_CARD_ENGAGEMENT_SNOOZE],[xa.ETREE_NORMAL_SNOOZE,xa.NOTIFICATION_CARD_ENGAGEMENT_SNOOZE],[xa.PWA_PROMOTION_SNOOZE,xa.NOTIFICATION_CARD_ENGAGEMENT_SNOOZE],[xa.DONATION_TREND_NPO_SNOOZE,xa.NOTIFICATION_CARD_ENGAGEMENT_SNOOZE]]),Za="walletNotificationFunnelEnumMaxValue",Ka="walletNotificationUseNewHistograms";const qa=(e,t)=>{const o=ca.valueExists(Za)?parseInt(ca.getValue(Za),10)+1:xa.MAX;if(e>o)return void console.warn("Action enum passed in is larger than known size. Please check walletNotificationFunnelEnumMaxValue.");const i=(e=>{const t=(e=>{const t="isUserTriggeredByAttration";switch(e){case Ga.MiniWallet:return!!ca.valueExists(t)&&ca.getValue(t);case Ga.HubHeader:case Ga.HubHomepage:return!1}})(e),o=[];switch(e){case Ga.MiniWallet:o.push(t?Pa.WalletNotificationStatusMiniWalletWithAttraction:Pa.WalletNotificationStatusMiniWalletWithoutAttraction),o.push(Pa.WalletNotificationStatusMiniWallet);break;case Ga.HubHomepage:o.push(t?Pa.WalletNotificationStatusHubHomeWithAttraction:Pa.WalletNotificationStatusHubHomeWithoutAttraction),o.push(Pa.WalletNotificationStatusHubHome);break;case Ga.HubHeader:o.push(t?Pa.WalletNotificationStatusHubHeaderWithAttraction:Pa.WalletNotificationStatusHubHeaderWithoutAttraction),o.push(Pa.WalletNotificationStatusHubHeader)}return o.push(t?Pa.WalletNotificationStatusAllAppsWithAttraction:Pa.WalletNotificationStatusAllAppsWithoutAttraction),o.push(Pa.WalletNotificationStatusAllApps),o})(t);i.forEach((t=>{((e,t,o)=>{try{if(Sa.recordEnumerationValue(e,t,o),za.includes(t)){Sa.recordEnumerationValue(e,xa.NOTIFICATION_CARD_ENGAGEMENT,o);const i=Ya.get(t);i<o&&Sa.recordEnumerationValue(e,i,o)}ja.includes(t)&&Sa.recordEnumerationValue(e,xa.NOTIFICATION_CARD_LOADED,o)}catch(i){Ha(i,`MetricName : ${e}, Key: ${t}, MaxValue: ${o}`)}})(t,e,o)}))},Xa=(e,t)=>{ca.valueExists(Ka)&&ca.getValue(Ka)&&qa(e,t)},Qa=(e,t)=>{if(ya&&function(e){if(!function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}(e))return!1;var t=Ia(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}(ya?.triggerExperiment))try{if(e)switch(t){case va.mastercard:ya.triggerExperiment(Ra.TOKENIZATION_NOTIFICATION_MASTERCARD);break;case va.visa:ya.triggerExperiment(Ra.TOKENIZATION_NOTIFICATION_VISA)}}catch{}};function Ja(e,t="profileflyoutnotification"){try{const o=new URL(e);return o.searchParams.append("source",t),o.toString()}catch(e){return console.error("Error happened when appending source, redirect to default page.",e),`edge://wallet?source=${t}`}}function es(e,t=""){try{const o=new URL(e);return o.searchParams.append("type",t),o.toString()}catch(e){return console.error("Error happened when appending type, redirect to default page.",e),`edge://wallet?type=${t}`}}var ts=n(274);function os(e,t,o=!1){const i=!!e.values&&e.values.every((e=>"string"==typeof e||"number"==typeof e));let n;if(n=o||!0!==t.valueExists(e.id)?e.defaultMessage:t.getString(e.id),!e.values)return n;try{return i?n.replace(/\$(.|$|\n)/g,(function(t){return"$$"===t?"$":Number(t[1])>0?e.values?.[Number(t[1])-1]:""})):(r=n,a=e.values,function(e,t,o,i){const n=(e.match(/(\$[1-9])|(([^$]|\$([^1-9]|$))+)/g)||[]).map((t=>{if(!t.match(/^\$[1-9]$/)){if((t.match(/\$/g)||[]).length%2!=0)throw new Error(`Unescaped $ found in localized string "${e}".`);const n=t.replace(/\$\$/g,"$");return{value:o?o(n,i):n,arg:null}}return{value:(n=parseInt(t[1],10)-1,a[n]),arg:t};var n}));return ts.createElement(ts.Fragment,null,n.map(((e,t)=>ts.createElement(ts.Fragment,{key:t},e.value))))}(r,0,s,l))}catch(t){return console.error(`Error when replacing ${JSON.stringify(e)}:\r\n ${t}`),t instanceof Error&&window?.onerror?.(t.message,"_TL_Without_LoadTimeData error",1,0,t),n}var r,a,s,l}function is(e,t,o=!1){return i=>os({...e,values:i},t,o)}function ns(e){return os(e,ca)}function rs(e){return is(e,ca)}const as={msWalletNotificationCardExpired:rs({id:"msWalletNotificationCardExpired",description:"Title of wallet notification for expired payment card",defaultMessage:"Your card has expired"}),msWalletNotificationCardExpiring:rs({id:"msWalletNotificationCardExpiring",description:"Title of wallet notification for payment card that is going to expire",defaultMessage:"Your card is expiring soon"}),msWalletNotificationUpdateExpText:rs({id:"msWalletNotificationUpdateExpText",description:"Description of expiration date update, $1 is last 4 digits of the card number",defaultMessage:"Edit your card information to continue use of your card ending in $1."}),msWalletNotificationUpdateButton:rs({id:"msWalletNotificationUpdateButton",description:"Text of wallet notification update action button",defaultMessage:"Update now"}),msWalletNotificationUpdateButton1:rs({id:"msWalletNotificationUpdateButton1",description:"Text of wallet notification update action button",defaultMessage:"Update or remove card"}),msWalletNotificationUpdateButton2:rs({id:"msWalletNotificationUpdateButton2",description:"Text of wallet notification update action button",defaultMessage:"Fix it"}),msWalletNotificationSnoozeText1:rs({id:"msWalletNotificationSnoozeText1",description:"Wallet notification snooze description",defaultMessage:"Notification snoozed for $1 days"}),msWalletNotificationUpdatedCardText:rs({id:"msWalletNotificationUpdatedCardText",description:"Description of successfully updating payment card result",defaultMessage:"Card updated successfully!"}),msWalletNotificationUpdateCardFailText:rs({id:"msWalletNotificationUpdateCardFailText",description:"Description of failed payment card update result, $1 is link",defaultMessage:"Update failed. Try again in $1"}),msWalletNotificationDismissLabel:rs({id:"msWalletNotificationDismissLabel",description:"Title text for the dismiss button",defaultMessage:"Dismiss"}),msWalletNotificationSnoozeLabel:rs({id:"msWalletNotificationSnoozeLabel",description:"Title text for the snooze button",defaultMessage:"Snooze"}),msWalletNotificationCardTokenizationEligibleTitle:rs({id:"msWalletNotificationCardTokenizationEligibleTitle",description:"Title of wallet notification for card tokenization eligible",defaultMessage:"Protect your purchases with virtual cards"}),msWalletNotificationCardTokenizationEligibleDescription:rs({id:"msWalletNotificationCardTokenizationEligibleDescription",description:"Description of wallet notification for card tokenization eligible, $1 is card number(****1234)",defaultMessage:"Virtual cards are single-use payment credentials that are randomly generated each time you make an online purchase. This helps ensure your $1 is protected."}),learnMoreLinkTextShort:rs({id:"learnMoreLinkTextShort",defaultMessage:"Learn more",description:"Text of learn more link text"}),activateVirtualCardButtonText:rs({id:"activateVirtualCardButtonText",description:"Text of Activate virtual card button text",defaultMessage:"Activate virtual card"}),msWalletNotificationCardTokenizationEligibleHeader:rs({id:"msWalletNotificationCardTokenizationEligibleHeader",description:"Header of wallet notification for card tokenization eligible V2",defaultMessage:"Protect your card details"}),msWalletNotificationCardTokenizationEligibleIntroduction:rs({id:"msWalletNotificationCardTokenizationEligibleIntroduction",description:"Introduction of wallet notification for card tokenization eligible V2",defaultMessage:"Set up a virtual card to make online shopping safer and more convenient."}),msWalletNotificationCardTokenizationEligibleIntroductionForRewards:rs({id:"msWalletNotificationCardTokenizationEligibleIntroductionForRewards",description:"Introduction of wallet notification for card tokenization eligible V2 for rewards incentive",defaultMessage:"Set up a virtual card to make online shopping safer and earn 20 Microsoft Rewards points."}),msWalletNotificationCardTokenizationEligibleSetUpText:rs({id:"msWalletNotificationCardTokenizationEligibleSetUpText",description:"Text of Set up button of wallet notification for card tokenization eligible V2",defaultMessage:"Set up for $1"}),personalizedOffersCashBackDescription:rs({id:"personalizedOffersCashBackDescription",defaultMessage:"Earn $1 cash back with $2.",description:"Text of Personalized Offers available description, $1 is cash value($20), $2 is seller name(Nike),"}),personalizedOffersOfferExpiresInText:rs({id:"personalizedOffersOfferExpiresInText",description:"Text of Personalized Offers available Offer expires in text",defaultMessage:"Offer expires in"}),msWalletNotificationCryptowalletSignupTitle:rs({id:"msWalletNotificationCryptowalletSignupTitle",description:"Title of set up crypto wallet",defaultMessage:"Set up your Crypto Wallet"}),msWalletNotificationCryptowalletSignupDescription:rs({id:"msWalletNotificationCryptowalletSignupDescription",description:"Description of set up crypto wallet",defaultMessage:"Explore Web3 securely with this non-custodial wallet"}),msWalletNotificationCryptowalletJoinButton:rs({id:"msWalletNotificationCryptowalletJoinButton",description:"Text of set up crypto wallet action button",defaultMessage:"Set up"}),msWalletNotificationHotelReservationTitle:rs({id:"msWalletNotificationHotelReservationTitle",description:"The title of upcoming hotel reservation notification",defaultMessage:"Upcoming accommodation"}),msWalletNotificationHotelReservationBody:rs({id:"msWalletNotificationHotelReservationBody",description:"The body of upcoming hotel reservation notification, $1 is hotel name, $2 is checkin date, $3 is checkout date",defaultMessage:"Your booking date of $1 from $2 to $3 is upcoming."}),msWalletNotificationHotelReservationViewDetails:rs({id:"msWalletNotificationHotelReservationViewDetails",description:"The text of link to view detials in upcoming hotel reservation notification",defaultMessage:"View details"}),personalizedOffersNoThanksText:rs({id:"personalizedOffersNoThanksText",description:"Text of Personalized Offers available no thanks text",defaultMessage:"No thanks"}),msWalletNotificationDonationSummaryTitle:rs({id:"msWalletNotificationDonationSummaryTitle",description:"Title of donation summary notification",defaultMessage:"View your entire giving summary and more"}),msWalletNotificationDonationSummaryLinkText:rs({id:"msWalletNotificationDonationSummaryLinkText",description:"Text of donation summary notification link",defaultMessage:"Check it out"}),walletTitle:rs({id:"walletTitle",description:"The title of wallet page",defaultMessage:"Wallet"}),msWalletNotificationRebatesCashoutTitle:rs({id:"msWalletNotificationRebatesCashoutTitle",description:"Title of wallet notification for rebates cashout",defaultMessage:"It's time to cash out!"}),msWalletNotificationRebatesCashoutDescription:rs({id:"msWalletNotificationRebatesCashoutDescription",description:"Description of wallet notification for rebates cashout",defaultMessage:"Know more about cashback program and cashout using PayPal."}),msWalletNotificationRebatesCashoutAmountDescription:rs({id:"msWalletNotificationRebatesCashoutAmountDescription",description:"Description of wallet notification for rebates cashout. $1 is cashback amount",defaultMessage:"Congratulations you have $1 in cashback. Cashout using PayPal."}),msWalletNotificationRebatesCashoutAction:rs({id:"msWalletNotificationRebatesCashoutAction",description:"Action text of wallet notification for rebates cashout",defaultMessage:"Transfer to Paypal"}),msWalletNotificationDismissText:rs({id:"msWalletNotificationDismissText",description:"Wallet notification dismiss permanently description",defaultMessage:"You will not see this notification again"}),msWalletNotificationSnoozeText:rs({id:"msWalletNotificationSnoozeText",description:"Wallet notification snooze description",defaultMessage:"Notification snoozed for 7 days"}),msWalletNotificationDismissPermanently:rs({id:"msWalletNotificationDismissPermanently",description:"Wallet notification dismiss permanently action text",defaultMessage:"Dismiss permanently"}),msWalletNotificationPOMessage:rs({id:"msWalletNotificationPOMessage",description:"Text of Personalized Offers available description, $1 is cash value($20)",defaultMessage:"Your selected $1 Microsoft Cashback offer expires in $2"}),personalizedOffersViewOfferText:rs({id:"personalizedOffersViewOfferText",description:"Text of Personalized Offers available view offer text",defaultMessage:"View offer"}),msWalletNotificationLeakageTitle:rs({id:"msWalletNotificationLeakageTitle",description:"The title of password leakage notification",defaultMessage:"Password leakage"}),msWalletNotificationPasswordCompromiseTitle:rs({id:"msWalletNotificationPasswordCompromiseTitle",description:"The title of password leakage or compromise notification",defaultMessage:"Password compromised"}),msWalletNotificationPasswordCompromiseBody:rs({id:"msWalletNotificationPasswordCompromiseBody",description:"The content of password compromise notification, $1 is the compromised password count",defaultMessage:"$1 passwords have appeared in a data leak."}),msWalletNotificationPasswordCompromiseBodySingular:rs({id:"msWalletNotificationPasswordCompromiseBodySingular",description:"The content of password compromise notification for one password leaked",defaultMessage:"1 password has appeared in a data leak."}),msWalletNotificationOrderStatusTitle:rs({id:"msWalletNotificationOrderStatusTitle",description:"The title of your order status change notification in package tracking notification",defaultMessage:"Order status updated"}),msWalletNotificationViewDetailLink:rs({id:"msWalletNotificationViewDetailLink",description:"The text of View details link in package tracking notification",defaultMessage:"View details"}),msWalletNotificationTrackOrderTitle:rs({id:"msWalletNotificationTrackOrderTitle",description:"The title of Track your order notification in package tracking notification",defaultMessage:"Track your order"}),msWalletNotificationTrackOrderSubTitle:rs({id:"msWalletNotificationTrackOrderSubTitle",description:"The subtitle of Track your order notification in package tracking notification",defaultMessage:"Wallet helps you track your order when you buy from certain sites."}),msWalletNotificationTrackOrderLink:rs({id:"msWalletNotificationTrackOrderLink",description:"The text of Track your order link in package tracking notification",defaultMessage:"Track order"}),msWalletNotificationPOTitle:rs({id:"msWalletNotificationPOTitle",description:"The title of personalized offers notification",defaultMessage:"Offer expiring soon"}),msWalletNotificationEtreeHalloweenTitle:rs({id:"msWalletNotificationEtreeHalloweenTitle",description:"Title of Etree Halloween event notification, E-tree is term and don't need translation",defaultMessage:"Halloween event with E-tree"}),msWalletNotificationEtreeHalloweenContent:rs({id:"msWalletNotificationEtreeHalloweenContent",description:"Content of Etree Halloween event notification",defaultMessage:"Check in daily to get a special ornament and extra water drops."}),msWalletNotificationEtreeHalloweenLinkText:rs({id:"msWalletNotificationEtreeHalloweenLinkText",description:"Text of Etree Halloween event notification action link",defaultMessage:"Join the Halloween event"}),msWalletNotificationEtreeChristmasTitle:rs({id:"msWalletNotificationEtreeChristmasTitle",description:"Title of Etree Christmas event notification",defaultMessage:"Earn holiday surprises"}),msWalletNotificationEtreeChristmasContent:rs({id:"msWalletNotificationEtreeChristmasContent",description:"Content of Etree Christmas event notification",defaultMessage:"Get daily surprises and make this holiday season more magical!"}),msWalletNotificationEtreeChristmasLinkText:rs({id:"msWalletNotificationEtreeChristmasLinkText",description:"Text of Etree Christmas event notification action link",defaultMessage:"Join now"}),msWalletNotificationEtreeNonEnrolledTitleV1:rs({id:"msWalletNotificationEtreeNonEnrolledTitleV1",description:"Title of Etree notification for non-enrolled users",defaultMessage:"Plant a tree"}),msWalletNotificationEtreeNonEnrolledDescriptionV1:rs({id:"msWalletNotificationEtreeNonEnrolledDescriptionV1",description:"Description of Etree notification for non-enrolled users",defaultMessage:"Plant a virtual tree in Wallet and we’ll plant a real tree to support reforestation efforts."}),msWalletNotificationEtreeNonEnrolledLinkTextV1:rs({id:"msWalletNotificationEtreeNonEnrolledLinkTextV1",description:"Text of Etree notification action link for non-enrolled users",defaultMessage:"Start now"}),msWalletNotificationEtreeEnrolledTitleV1:rs({id:"msWalletNotificationEtreeEnrolledTitleV1",description:"Title of Etree notification for enrolled users, $1 is water drops count for next level",defaultMessage:"$1 drops to level up"}),msWalletNotificationEtreeEnrolledDescriptionV1:rs({id:"msWalletNotificationEtreeEnrolledDescriptionV1",description:"Description of Etree notification for enrolled users, , $1 is water drops count for next level",defaultMessage:"$1 drops to level up and keep growing your tree."}),msWalletNotificationEtreeEnrolledLinkTextV1:rs({id:"msWalletNotificationEtreeEnrolledLinkTextV1",description:"Text of Etree notification action link for enrolled users",defaultMessage:"Grow my tree"}),msWalletNotificationEtreeNonEnrolledTitleV2:rs({id:"msWalletNotificationEtreeNonEnrolledTitleV2",description:"Title of Etree notification for enrolled users",defaultMessage:"Let’s grow green"}),msWalletNotificationEtreeNonEnrolledDescriptionV2:rs({id:"msWalletNotificationEtreeNonEnrolledDescriptionV2",description:"Description of Etree notification for enrolled users",defaultMessage:"Collect water drops today to grow your tree."}),msWalletNotificationEtreeNonEnrolledLinkTextV2:rs({id:"msWalletNotificationEtreeNonEnrolledLinkTextV2",description:"Text of Etree notification action link for non-enrolled users",defaultMessage:"Grow my tree"}),msWalletNotificationEtreeEnrolledTitleV2:rs({id:"msWalletNotificationEtreeEnrolledTitleV2",description:"Title of Etree notification for enrolled users, E-tree is term and don't need translation",defaultMessage:"Continue the green initiative"}),msWalletNotificationEtreeEnrolledDescriptionV2:rs({id:"msWalletNotificationEtreeEnrolledDescriptionV2",description:"Description of Etree notification for enrolled users",defaultMessage:"Collect water drops today to grow your tree. More efforts, more trees."}),msWalletNotificationEtreeEnrolledLinkTextV2:rs({id:"msWalletNotificationEtreeEnrolledLinkTextV2",description:"Text of Etree notification action link for enrolled users",defaultMessage:"Grow my tree"}),msWalletNotificationDonationTrendNpoTitle:rs({id:"msWalletNotificationDonationTrendNpoTitle",description:"Title of donation notification to recommend trending non-profit organization",defaultMessage:"Make your gift go further"}),msWalletNotificationDonationTrendNpoDescription:rs({id:"msWalletNotificationDonationTrendNpoDescription",description:"Description of donation notification to recommend trending non-profit organization, $1 is NPO name",defaultMessage:"$1 is trending, your gift will make a greater impact!"}),msWalletNotificationDonationTrendNpoLink:rs({id:"msWalletNotificationDonationTrendNpoLink",description:"Text of donation notification action link to redirect to the donation page",defaultMessage:"Give now"})},ss={featurePromotionActionLinkText:rs({id:"featurePromotionActionLinkText",description:"Text of Wallet Notification Feature Promotion Action Link",defaultMessage:"Try it now"}),passwordsPromotionTitle:rs({id:"passwordsPromotionTitle",description:"Title of Wallet Notification Password Promotion",defaultMessage:"Manage your passwords in Wallet"}),passwordsDescription:rs({id:"passwordsDescription",description:"Text of Wallet Notification Password Promotion",defaultMessage:"Try the new management experience in Wallet."}),membershipsPromotionTitle:rs({id:"membershipsPromotionTitle",description:"Title of Wallet Notification Password Promotion",defaultMessage:"Manage your memberships in Wallet"}),membershipsDescription:rs({id:"membershipsDescription",description:"Text of Wallet Notification Password Promotion",defaultMessage:"Stay organized and in control."}),rewardsPromotionTitle:rs({id:"rewardsPromotionTitle",description:"Title of Wallet Notification Rewards Promotion",defaultMessage:"Add memberships to earn Microsoft Rewards"}),rewardsPromotionDescription:rs({id:"rewardsPromotionDescription",description:"Text of Wallet Notification Rewards Promotion",defaultMessage:"Earn up to 20 rewards points by adding new memberships to wallet"}),rewardsPromotionActionLinkText:rs({id:"rewardsPromotionActionLinkText",description:"Text of Wallet Notification Rewards Promotion Action Link",defaultMessage:"Add new membership"}),msWalletNotificationRoamCardHeader:rs({id:"msWalletNotificationRoamCardHeader",description:"Header of wallet notification for roam card",defaultMessage:"Save the card to Microsoft account"}),msWalletNotificationRoamCardHeaderV2:rs({id:"msWalletNotificationRoamCardHeaderV2",description:"Header of wallet notification for roam card",defaultMessage:"Pay faster and from different devices on future purchases"}),msWalletNotificationRoamCardHeaderV3:rs({id:"msWalletNotificationRoamCardHeaderV3",description:"Header of wallet notification for roam card",defaultMessage:"Pay faster from any device"}),msWalletNotificationRoamCardAction:rs({id:"msWalletNotificationRoamCardAction",description:"Action text of wallet notification for roam card",defaultMessage:"Save the card"}),msWalletNotificationRoamCardActionV2:rs({id:"msWalletNotificationRoamCardActionV2",description:"Action text of wallet notification for roam card",defaultMessage:"Save card to Microsoft account"})},ls=["getRewardsItem","clearCurrentNotification","getNotificationsUnviewedStatus","getMultipleNotifications","getCurrentNotification","onApply","onDismiss","onSnooze","isValidNotificationFromMultipleNotifications","isValidNotification"],cs=["setNotificationsUnviewedStatus","buildNotifications","clearCurrentNotification","onApply","onDismiss","onSnooze","isValidNotification","isValidNotificationFromMultipleNotifications"],ds=new class{constructor(){this.instanceBase=()=>{var e,t;return this.instanceBase_||(this.instanceBase_=pa()?(e="EdgeWalletNotificationMojomModule",t=ha,new Proxy({},{get:(o,i)=>async(...o)=>{const n=window.cachedMojom;return n[e]||(n[e]="function"==typeof t?await(t?.()):await t),n[e]?.[i]?.(...o)}})):null),this.instanceBase_},this.instanceTracingProxy_=function(e,t,o,i){const n={get:function(i,n){const r=i[n],a=new Error;return(...i)=>{const s={args:0===i.length?[]:t.includes(n)?[...i.values()]:na,status:"Success",result:na,stack:a.stack};try{const t=r.apply(this,i);return o.includes(n)&&(s.result=t),ra(e,{apiName:n,data:s}),t}catch(t){s.status="Error",ra(e,{apiName:n,data:s})}}}};return i?new Proxy(i,n):null}("WalletNotification::Mojom",ls,cs,this.instanceBase()),this.instance=()=>this.instanceTracingProxy_,this.navigateToUrl=e=>this.instance()?.navigateToUrl?.({url:e}),this.logErrorDetails=(e,t)=>{this.instance()?.logErrorDetails?.(e,t)},this.isValidNotification=e=>this.instance()?.isValidNotification?.(e),this.isValidNotificationFromMultipleNotifications=e=>this.instance()?.isValidNotificationFromMultipleNotifications?.(e),this.getCurrentNotification=()=>this.instance()?.getCurrentNotification?.(),this.getMultipleNotifications=()=>this.instance()?.getMultipleNotifications?.(),this.setNotificationsUnviewedStatus=e=>{this.instance()?.setNotificationsUnviewedStatus(e)},this.getNotificationsUnviewedStatus=()=>this.instance()?.getNotificationsUnviewedStatus?.().then((e=>e?.status)),this.buildNotifications=e=>{this.instance()?.buildNotifications(e)},this.clearCurrentNotification=()=>{this.instance()?.clearCurrentNotification?.()},this.onApply=e=>{this.instance()?.onApply?.(e)},this.onDismiss=e=>{this.instance()?.onDismiss?.(e)},this.onSnooze=e=>{this.instance()?.onSnooze?.(e)},this.getRewardsItem=()=>this.instance()?.getRewardsItem?.(),this.writeDiagnosticLog=e=>this.instance()?.writeDiagnosticLog?.(e)}};var us,ps,hs;!function(e){e.BOOLEAN="BOOLEAN",e.NUMBER="NUMBER",e.STRING="STRING",e.URL="URL",e.LIST="LIST",e.DICTIONARY="DICTIONARY"}(us||(us={})),function(e){e.DEVICE_POLICY="DEVICE_POLICY",e.USER_POLICY="USER_POLICY",e.OWNER="OWNER",e.PRIMARY_USER="PRIMARY_USER",e.EXTENSION="EXTENSION",e.PARENT="PARENT",e.CHILD_RESTRICTION="CHILD_RESTRICTION"}(ps||(ps={})),function(e){e.ENFORCED="ENFORCED",e.RECOMMENDED="RECOMMENDED",e.PARENT_SUPERVISED="PARENT_SUPERVISED"}(hs||(hs={}));const{settingsPrivate:fs}=window?.chrome,gs=()=>{const e={};return function(t,o){void 0===e[t.id]&&(t.insertCSSRules(o),e[t.id]=!0)}};function Es(e){for(var t,o=0,i=0,n=e.length;n>=4;++i,n-=4)t=1540483477*(65535&(t=255&e.charCodeAt(i)|(255&e.charCodeAt(++i))<<8|(255&e.charCodeAt(++i))<<16|(255&e.charCodeAt(++i))<<24))+(59797*(t>>>16)<<16),o=1540483477*(65535&(t^=t>>>24))+(59797*(t>>>16)<<16)^1540483477*(65535&o)+(59797*(o>>>16)<<16);switch(n){case 3:o^=(255&e.charCodeAt(i+2))<<16;case 2:o^=(255&e.charCodeAt(i+1))<<8;case 1:o=1540483477*(65535&(o^=255&e.charCodeAt(i)))+(59797*(o>>>16)<<16)}return(((o=1540483477*(65535&(o^=o>>>13))+(59797*(o>>>16)<<16))^o>>>15)>>>0).toString(36)}function Ts(e){return e.reduce((function(e,t){var o=t[0],i=t[1];return e[o]=i,e[i]=o,e}),{})}function Ns(e){return"number"==typeof e}function ms(e,t){return-1!==e.indexOf(t)}function _s(e,t,o,i){return t+(n=o,0===parseFloat(n)?n:"-"===n[0]?n.slice(1):"-"+n)+i;var n}function Os(e){return e.replace(/ +/g," ").split(" ").map((function(e){return e.trim()})).filter(Boolean).reduce((function(e,t){var o=e.list,i=e.state,n=(t.match(/\(/g)||[]).length,r=(t.match(/\)/g)||[]).length;return i.parensDepth>0?o[o.length-1]=o[o.length-1]+" "+t:o.push(t),i.parensDepth+=n-r,{list:o,state:i}}),{list:[],state:{parensDepth:0}}).list}function As(e){var t=Os(e);if(t.length<=3||t.length>4)return e;var o=t[0],i=t[1],n=t[2];return[o,t[3],n,i].join(" ")}var Is={padding:function(e){var t=e.value;return Ns(t)?t:As(t)},textShadow:function(e){return function(e){for(var t=[],o=0,i=0,n=!1;i<e.length;)n||","!==e[i]?"("===e[i]?(n=!0,i++):")"===e[i]?(n=!1,i++):i++:(t.push(e.substring(o,i).trim()),o=++i);return o!=i&&t.push(e.substring(o,i+1)),t}(e.value).map((function(e){return e.replace(/(^|\s)(-*)([.|\d]+)/,(function(e,t,o,i){return"0"===i?e:t+(""===o?"-":"")+i}))})).join(",")},borderColor:function(e){return As(e.value)},borderRadius:function(e){var t=e.value;if(Ns(t))return t;if(ms(t,"/")){var o=t.split("/"),i=o[0],n=o[1];return Is.borderRadius({value:i.trim()})+" / "+Is.borderRadius({value:n.trim()})}var r=Os(t);switch(r.length){case 2:return r.reverse().join(" ");case 4:var a=r[0],s=r[1],l=r[2];return[s,a,r[3],l].join(" ");default:return t}},background:function(e){var t=e.value,o=e.valuesToConvert,i=e.isRtl,n=e.bgImgDirectionRegex,r=e.bgPosDirectionRegex;if(Ns(t))return t;var a=t.replace(/(url\(.*?\))|(rgba?\(.*?\))|(hsl\(.*?\))|(#[a-fA-F0-9]+)|((^| )(\D)+( |$))/g,"").trim();return t=t.replace(a,Is.backgroundPosition({value:a,valuesToConvert:o,isRtl:i,bgPosDirectionRegex:r})),Is.backgroundImage({value:t,valuesToConvert:o,bgImgDirectionRegex:n})},backgroundImage:function(e){var t=e.value,o=e.valuesToConvert,i=e.bgImgDirectionRegex;return ms(t,"url(")||ms(t,"linear-gradient(")?t.replace(i,(function(e,t,i){return e.replace(i,o[i])})):t},backgroundPosition:function(e){var t=e.value,o=e.valuesToConvert,i=e.isRtl,n=e.bgPosDirectionRegex;return t.replace(i?/^((-|\d|\.)+%)/:null,(function(e,t){return function(e){var t=e.indexOf(".");if(-1===t)e=100-parseFloat(e)+"%";else{var o=e.length-t-2;e=(e=100-parseFloat(e)).toFixed(o)+"%"}return e}(t)})).replace(n,(function(e){return o[e]}))},backgroundPositionX:function(e){var t=e.value,o=e.valuesToConvert,i=e.isRtl,n=e.bgPosDirectionRegex;return Ns(t)?t:Is.backgroundPosition({value:t,valuesToConvert:o,isRtl:i,bgPosDirectionRegex:n})},transition:function(e){var t=e.value,o=e.propertiesToConvert;return t.split(/,\s*/g).map((function(e){var t=e.split(" ");return t[0]=o[t[0]]||t[0],t.join(" ")})).join(", ")},transitionProperty:function(e){var t=e.value,o=e.propertiesToConvert;return t.split(/,\s*/g).map((function(e){return o[e]||e})).join(", ")},transform:function(e){var t=e.value,o="(?:(?:(?:\\[0-9a-f]{1,6})(?:\\r\\n|\\s)?)|\\\\[^\\r\\n\\f0-9a-f])",i="((?:-?(?:[0-9]*\\.[0-9]+|[0-9]+)(?:\\s*(?:em|ex|px|cm|mm|in|pt|pc|deg|rad|grad|ms|s|hz|khz|%)|-?(?:[_a-z]|[^\\u0020-\\u007e]|"+o+")(?:[_a-z0-9-]|[^\\u0020-\\u007e]|"+o+")*)?)|(?:inherit|auto))",n=new RegExp("(translateX\\s*\\(\\s*)"+i+"(\\s*\\))","gi"),r=new RegExp("(translate\\s*\\(\\s*)"+i+"((?:\\s*,\\s*"+i+"){0,1}\\s*\\))","gi"),a=new RegExp("(translate3d\\s*\\(\\s*)"+i+"((?:\\s*,\\s*"+i+"){0,2}\\s*\\))","gi"),s=new RegExp("(rotate[ZY]?\\s*\\(\\s*)"+i+"(\\s*\\))","gi");return t.replace(n,_s).replace(r,_s).replace(a,_s).replace(s,_s)}};Is.objectPosition=Is.backgroundPosition,Is.margin=Is.padding,Is.borderWidth=Is.padding,Is.boxShadow=Is.textShadow,Is.webkitBoxShadow=Is.boxShadow,Is.mozBoxShadow=Is.boxShadow,Is.WebkitBoxShadow=Is.boxShadow,Is.MozBoxShadow=Is.boxShadow,Is.borderStyle=Is.borderColor,Is.webkitTransform=Is.transform,Is.mozTransform=Is.transform,Is.WebkitTransform=Is.transform,Is.MozTransform=Is.transform,Is.transformOrigin=Is.backgroundPosition,Is.webkitTransformOrigin=Is.transformOrigin,Is.mozTransformOrigin=Is.transformOrigin,Is.WebkitTransformOrigin=Is.transformOrigin,Is.MozTransformOrigin=Is.transformOrigin,Is.webkitTransition=Is.transition,Is.mozTransition=Is.transition,Is.WebkitTransition=Is.transition,Is.MozTransition=Is.transition,Is.webkitTransitionProperty=Is.transitionProperty,Is.mozTransitionProperty=Is.transitionProperty,Is.WebkitTransitionProperty=Is.transitionProperty,Is.MozTransitionProperty=Is.transitionProperty,Is["text-shadow"]=Is.textShadow,Is["border-color"]=Is.borderColor,Is["border-radius"]=Is.borderRadius,Is["background-image"]=Is.backgroundImage,Is["background-position"]=Is.backgroundPosition,Is["background-position-x"]=Is.backgroundPositionX,Is["object-position"]=Is.objectPosition,Is["border-width"]=Is.padding,Is["box-shadow"]=Is.textShadow,Is["-webkit-box-shadow"]=Is.textShadow,Is["-moz-box-shadow"]=Is.textShadow,Is["border-style"]=Is.borderColor,Is["-webkit-transform"]=Is.transform,Is["-moz-transform"]=Is.transform,Is["transform-origin"]=Is.transformOrigin,Is["-webkit-transform-origin"]=Is.transformOrigin,Is["-moz-transform-origin"]=Is.transformOrigin,Is["-webkit-transition"]=Is.transition,Is["-moz-transition"]=Is.transition,Is["transition-property"]=Is.transitionProperty,Is["-webkit-transition-property"]=Is.transitionProperty,Is["-moz-transition-property"]=Is.transitionProperty;var bs=Ts([["paddingLeft","paddingRight"],["marginLeft","marginRight"],["left","right"],["borderLeft","borderRight"],["borderLeftColor","borderRightColor"],["borderLeftStyle","borderRightStyle"],["borderLeftWidth","borderRightWidth"],["borderTopLeftRadius","borderTopRightRadius"],["borderBottomLeftRadius","borderBottomRightRadius"],["padding-left","padding-right"],["margin-left","margin-right"],["border-left","border-right"],["border-left-color","border-right-color"],["border-left-style","border-right-style"],["border-left-width","border-right-width"],["border-top-left-radius","border-top-right-radius"],["border-bottom-left-radius","border-bottom-right-radius"]]),Ss=["content"],Rs=Ts([["ltr","rtl"],["left","right"],["w-resize","e-resize"],["sw-resize","se-resize"],["nw-resize","ne-resize"]]),ys=new RegExp("(^|\\W|_)((ltr)|(rtl)|(left)|(right))(\\W|_|$)","g"),vs=new RegExp("(left)|(right)");function Cs(e){return Object.keys(e).reduce((function(t,o){var i=e[o];if("string"==typeof i&&(i=i.trim()),ms(Ss,o))return t[o]=i,t;var n=ws(o,i),r=n.key,a=n.value;return t[r]=a,t}),Array.isArray(e)?[]:{})}function ws(e,t){var o,i=/\/\*\s?@noflip\s?\*\//.test(t),n=i?e:bs[o=e]||o,r=i?t:function(e,t){if(!function(e){return!("boolean"==typeof e||function(e){return null==e}(e))}(t))return t;if((o=t)&&"object"==typeof o)return Cs(t);var o,i,n=Ns(t),r=function(e){return"function"==typeof e}(t),a=n||r?t:t.replace(/ !important.*?$/,""),s=!n&&a.length!==t.length,l=Is[e];return i=l?l({value:a,valuesToConvert:Rs,propertiesToConvert:bs,isRtl:!0,bgImgDirectionRegex:ys,bgPosDirectionRegex:vs}):Rs[a]||a,s?i+" !important":i}(n,t);return{key:n,value:r}}const Ms="undefined"==typeof window?n.g:window,Ds="@griffel/";function Ps(e,t){return Ms[Symbol.for(Ds+e)]||(Ms[Symbol.for(Ds+e)]=t),Ms[Symbol.for(Ds+e)]}const xs=Ps("DEFINITION_LOOKUP_TABLE",{}),ks="___",Ls=ks.length+7,Fs={all:1,animation:1,background:1,backgroundPosition:1,border:1,borderBlock:1,borderBlockEnd:1,borderBlockStart:1,borderBottom:1,borderColor:1,borderImage:1,borderInline:1,borderInlineEnd:1,borderInlineStart:1,borderLeft:1,borderRadius:1,borderRight:1,borderStyle:1,borderTop:1,borderWidth:1,caret:1,columns:1,columnRule:1,containIntrinsicSize:1,container:1,flex:1,flexFlow:1,font:1,gap:1,grid:1,gridArea:1,gridColumn:1,gridRow:1,gridTemplate:1,inset:1,insetBlock:1,insetInline:1,lineClamp:1,listStyle:1,margin:1,marginBlock:1,marginInline:1,mask:1,maskBorder:1,motion:1,offset:1,outline:1,overflow:1,overscrollBehavior:1,padding:1,paddingBlock:1,paddingInline:1,placeItems:1,placeContent:1,placeSelf:1,scrollMargin:1,scrollMarginBlock:1,scrollMarginInline:1,scrollPadding:1,scrollPaddingBlock:1,scrollPaddingInline:1,scrollSnapMargin:1,scrollTimeline:1,textDecoration:1,textEmphasis:1,transition:1},Gs=/[A-Z]/g,Bs=/^ms-/,Ws={};function Us(e){return"-"+e.toLowerCase()}function Vs(e){if(Object.prototype.hasOwnProperty.call(Ws,e))return Ws[e];if("--"===e.substr(0,2))return e;const t=e.replace(Gs,Us);return Ws[e]=Bs.test(t)?"-"+t:t}function $s(e){return"&"===e.charAt(0)?e.slice(1):e}var Hs="-moz-",zs="-webkit-",js="comm",Ys="rule",Zs="decl",Ks="@layer",qs=Math.abs,Xs=String.fromCharCode,Qs=Object.assign;function Js(e){return e.trim()}function el(e,t){return(e=t.exec(e))?e[0]:e}function tl(e,t,o){return e.replace(t,o)}function ol(e,t){return e.indexOf(t)}function il(e,t){return 0|e.charCodeAt(t)}function nl(e,t,o){return e.slice(t,o)}function rl(e){return e.length}function al(e){return e.length}function sl(e,t){return t.push(e),e}function ll(e,t){for(var o="",i=0;i<e.length;i++)o+=t(e[i],i,e,t)||"";return o}function cl(e,t,o,i){switch(e.type){case Ks:if(e.children.length)break;case"@import":case Zs:return e.return=e.return||e.value;case js:return"";case"@keyframes":return e.return=e.value+"{"+ll(e.children,i)+"}";case Ys:if(!rl(e.value=e.props.join(",")))return""}return rl(o=ll(e.children,i))?e.return=e.value+"{"+o+"}":""}var dl=1,ul=1,pl=0,hl=0,fl=0,gl="";function El(e,t,o,i,n,r,a,s){return{value:e,root:t,parent:o,type:i,props:n,children:r,line:dl,column:ul,length:a,return:"",siblings:s}}function Tl(e,t){return Qs(El("",null,null,"",null,null,0,e.siblings),e,{length:-e.length},t)}function Nl(){return fl=hl>0?il(gl,--hl):0,ul--,10===fl&&(ul=1,dl--),fl}function ml(){return fl=hl<pl?il(gl,hl++):0,ul++,10===fl&&(ul=1,dl++),fl}function _l(){return il(gl,hl)}function Ol(){return hl}function Al(e,t){return nl(gl,e,t)}function Il(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function bl(e){return dl=ul=1,pl=rl(gl=e),hl=0,[]}function Sl(e){return gl="",e}function Rl(e){return Js(Al(hl-1,Cl(91===e?e+2:40===e?e+1:e)))}function yl(e){for(;(fl=_l())&&fl<33;)ml();return Il(e)>2||Il(fl)>3?"":" "}function vl(e,t){for(;--t&&ml()&&!(fl<48||fl>102||fl>57&&fl<65||fl>70&&fl<97););return Al(e,Ol()+(t<6&&32==_l()&&32==ml()))}function Cl(e){for(;ml();)switch(fl){case e:return hl;case 34:case 39:34!==e&&39!==e&&Cl(fl);break;case 40:41===e&&Cl(e);break;case 92:ml()}return hl}function wl(e,t){for(;ml()&&e+fl!==57&&(e+fl!==84||47!==_l()););return"/*"+Al(t,hl-1)+"*"+Xs(47===e?e:ml())}function Ml(e){for(;!Il(_l());)ml();return Al(e,hl)}function Dl(e){return Sl(Pl("",null,null,null,[""],e=bl(e),0,[0],e))}function Pl(e,t,o,i,n,r,a,s,l){for(var c=0,d=0,u=a,p=0,h=0,f=0,g=1,E=1,T=1,N=0,m="",_=n,O=r,A=i,I=m;E;)switch(f=N,N=ml()){case 40:if(108!=f&&58==il(I,u-1)){-1!=ol(I+=tl(Rl(N),"&","&\f"),"&\f")&&(T=-1);break}case 34:case 39:case 91:I+=Rl(N);break;case 9:case 10:case 13:case 32:I+=yl(f);break;case 92:I+=vl(Ol()-1,7);continue;case 47:switch(_l()){case 42:case 47:sl(kl(wl(ml(),Ol()),t,o,l),l);break;default:I+="/"}break;case 123*g:s[c++]=rl(I)*T;case 125*g:case 59:case 0:switch(N){case 0:case 125:E=0;case 59+d:-1==T&&(I=tl(I,/\f/g,"")),h>0&&rl(I)-u&&sl(h>32?Ll(I+";",i,o,u-1,l):Ll(tl(I," ","")+";",i,o,u-2,l),l);break;case 59:I+=";";default:if(sl(A=xl(I,t,o,c,d,n,s,m,_=[],O=[],u,r),r),123===N)if(0===d)Pl(I,t,A,A,_,r,u,s,O);else switch(99===p&&110===il(I,3)?100:p){case 100:case 108:case 109:case 115:Pl(e,A,A,i&&sl(xl(e,A,A,0,0,n,s,m,n,_=[],u,O),O),n,O,u,s,i?_:O);break;default:Pl(I,A,A,A,[""],O,0,s,O)}}c=d=h=0,g=T=1,m=I="",u=a;break;case 58:u=1+rl(I),h=f;default:if(g<1)if(123==N)--g;else if(125==N&&0==g++&&125==Nl())continue;switch(I+=Xs(N),N*g){case 38:T=d>0?1:(I+="\f",-1);break;case 44:s[c++]=(rl(I)-1)*T,T=1;break;case 64:45===_l()&&(I+=Rl(ml())),p=_l(),d=u=rl(m=I+=Ml(Ol())),N++;break;case 45:45===f&&2==rl(I)&&(g=0)}}return r}function xl(e,t,o,i,n,r,a,s,l,c,d,u){for(var p=n-1,h=0===n?r:[""],f=al(h),g=0,E=0,T=0;g<i;++g)for(var N=0,m=nl(e,p+1,p=qs(E=a[g])),_=e;N<f;++N)(_=Js(E>0?h[N]+" "+m:tl(m,/&\f/g,h[N])))&&(l[T++]=_);return El(e,t,o,0===n?Ys:s,l,c,d,u)}function kl(e,t,o,i){return El(e,t,o,js,Xs(fl),nl(e,2,-2),0,i)}function Ll(e,t,o,i,n){return El(e,t,o,Zs,nl(e,0,i),nl(e,i+1,-1),i,n)}function Fl(e){var t=al(e);return function(o,i,n,r){for(var a="",s=0;s<t;s++)a+=e[s](o,i,n,r)||"";return a}}function Gl(e){return function(t){t.root||(t=t.return)&&e(t)}}const Bl=e=>{if(e.type===Ys){if("string"==typeof e.props)return;e.props=e.props.map((e=>-1===e.indexOf(":global(")?e:function(e){return Sl(function(e){for(;ml();)switch(Il(fl)){case 0:sl(Ml(hl-1),e);break;case 2:sl(Rl(fl),e);break;default:sl(Xs(fl),e)}return e}(bl(e)))}(e).reduce(((e,t,o,i)=>{if(""===t)return e;if(":"===t&&"global"===i[o+1]){const t=i[o+2].slice(1,-1)+" ";return e.unshift(t),i[o+1]="",i[o+2]="",e}return e.push(t),e}),[]).join("")))}};function Wl(e,t,o){switch(function(e,t){return 45^il(e,0)?(((t<<2^il(e,0))<<2^il(e,1))<<2^il(e,2))<<2^il(e,3):0}(e,t)){case 5103:return"-webkit-print-"+e+e;case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:return zs+e+e;case 4789:return Hs+e+e;case 5349:case 4246:case 6968:return zs+e+Hs+e+"-ms-"+e+e;case 6187:if(!el(e,/grab/))return tl(tl(tl(e,/(zoom-|grab)/,"-webkit-$1"),/(image-set)/,"-webkit-$1"),e,"")+e;case 5495:case 3959:return tl(e,/(image-set\([^]*)/,"-webkit-$1$`$1");case 4095:case 3583:case 4068:case 2532:return tl(e,/(.+)-inline(.+)/,"-webkit-$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(rl(e)-1-t>6)switch(il(e,t+1)){case 102:if(108===il(e,t+3))return tl(e,/(.+:)(.+)-([^]+)/,"$1-webkit-$2-$3$1-moz-"+(108==il(e,t+3)?"$3":"$2-$3"))+e;case 115:return~ol(e,"stretch")?Wl(tl(e,"stretch","fill-available"),t)+e:e}}return e}function Ul(e,t,o,i){if(e.length>-1&&!e.return)switch(e.type){case Zs:return void(e.return=Wl(e.value,e.length));case Ys:if(e.length)return function(e,t){return e.map(t).join("")}(e.props,(function(t){switch(el(t,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return ll([Tl(e,{props:[tl(t,/:(read-\w+)/,":-moz-$1")]})],i);case"::placeholder":return ll([Tl(e,{props:[tl(t,/:(plac\w+)/,":-webkit-input-$1")]}),Tl(e,{props:[tl(t,/:(plac\w+)/,":-moz-$1")]}),Tl(e,{props:[tl(t,/:(plac\w+)/,"-ms-input-$1")]})],i)}return""}))}}const Vl=e=>{(function(e){switch(e.type){case"@container":case"@media":case"@supports":case Ks:return!0}return!1})(e)&&Array.isArray(e.children)&&e.children.sort(((e,t)=>e.props[0]>t.props[0]?1:-1))};const $l=/,( *[^ &])/g;function Hl(e,t,o){let i=t;return o.length>0&&(i=o.reduceRight(((e,t)=>{return`${o=t,"&"+$s(o.replace($l,",&$1"))} { ${e} }`;var o}),t)),`${e}{${i}}`}function zl(e){const{className:t,media:o,layer:i,selectors:n,support:r,property:a,rtlClassName:s,rtlProperty:l,rtlValue:c,value:d,container:u}=e;let p=Hl(`.${t}`,Array.isArray(d)?`${d.map((e=>`${Vs(a)}: ${e}`)).join(";")};`:`${Vs(a)}: ${d};`,n);return l&&s&&(p+=Hl(`.${s}`,Array.isArray(c)?`${c.map((e=>`${Vs(l)}: ${e}`)).join(";")};`:`${Vs(l)}: ${c};`,n)),o&&(p=`@media ${o} { ${p} }`),i&&(p=`@layer ${i} { ${p} }`),r&&(p=`@supports ${r} { ${p} }`),u&&(p=`@container ${u} { ${p} }`),function(e,t){const o=[];return ll(Dl(e),Fl([Bl,Vl,Ul,cl,Gl((e=>o.push(e)))])),o}(p)}function jl(e){let t="";for(const o in e){const i=e[o];"string"!=typeof i&&"number"!=typeof i||(t+=Vs(o)+":"+i+";")}return t}function Yl(e){let t="";for(const o in e)t+=`${o}{${jl(e[o])}}`;return t}function Zl(e,t){const o=[];return ll(Dl(`@keyframes ${e} {${t}}`),Fl([cl,Ul,Gl((e=>o.push(e)))])),o}function Kl(e,t){return 0===e.length?t:`${e} and ${t}`}function ql(e){return"@media"===e.substr(0,6)}function Xl(e){return"@layer"===e.substr(0,6)}const Ql=/^(:|\[|>|&)/;function Jl(e){return Ql.test(e)}function ec(e){return"@supports"===e.substr(0,9)}function tc(e){return"@container"===e.substring(0,10)}const oc={"us-w":"w","us-v":"i",nk:"l",si:"v",cu:"f",ve:"h",ti:"a"};function ic(e,t,o,i,n){if(o)return"m";if(t||i)return"t";if(n)return"c";if(e.length>0){const t=e[0].trim();if(58===t.charCodeAt(0))return oc[t.slice(4,8)]||oc[t.slice(3,5)]||"d"}return"d"}function nc({container:e,media:t,layer:o,property:i,selector:n,support:r,value:a}){return"f"+Es(n+e+t+o+r+i+a.trim())}function rc(e,t,o,i,n){const r=Es(e+t+o+i+n),a=r.charCodeAt(0);return a>=48&&a<=57?String.fromCharCode(a+17)+r.slice(1):r}function ac(e){return e.replace(/>\s+/g,">")}function sc(e,t){const o=JSON.stringify(t,null,2),i=["@griffel/react: A rule was not resolved to CSS properly. Please check your `makeStyles` or `makeResetStyles` calls for following:"," ".repeat(2)+"makeStyles({"," ".repeat(4)+"[slot]: {"," ".repeat(6)+`"${e}": ${o.split("\n").map(((e,t)=>" ".repeat(0===t?0:6)+e)).join("\n")}`," ".repeat(4)+"}"," ".repeat(2)+"})",""];-1===e.indexOf("&")?(i.push("It looks that you're are using a nested selector, but it is missing an ampersand placeholder where the generated class name should be injected."),i.push(`Try to update a property to include it i.e "${e}" => "&${e}".`)):(i.push(""),i.push("If it's not obvious what triggers a problem, please report an issue at https://github.com/microsoft/griffel/issues")),i.join("\n")}function lc(e,t){[`@griffel/react: You are using unsupported shorthand CSS property "${e}". Please check your "makeStyles" calls, there *should not* be following:`," ".repeat(2)+"makeStyles({"," ".repeat(4)+`[slot]: { ${e}: "${t}" }`," ".repeat(2)+"})","","Learn why CSS shorthands are not supported: https://aka.ms/griffel-css-shorthands"].join("\n")}var cc=Object.defineProperty,dc=Object.getOwnPropertySymbols,uc=Object.prototype.hasOwnProperty,pc=Object.prototype.propertyIsEnumerable,hc=(e,t,o)=>t in e?cc(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o,fc=(e,t)=>{for(var o in t||(t={}))uc.call(t,o)&&hc(e,o,t[o]);if(dc)for(var o of dc(t))pc.call(t,o)&&hc(e,o,t[o]);return e};function gc(e,t,o,i){e[t]=i?[o,i]:o}function Ec(e,t){return t?[e,t]:e}function Tc(e,t,o,i,n){var r;let a;"m"===t&&n&&(a={m:n}),null!==(r=e[t])&&void 0!==r||(e[t]=[]),o&&e[t].push(Ec(o,a)),i&&e[t].push(Ec(i,a))}function Nc(e,t=[],o="",i="",n="",r="",a={},s={},l){for(const d in e){if(Fs.hasOwnProperty(d)){lc(d,e[d]);continue}const u=e[d];if(null!=u)if("string"==typeof u||"number"==typeof u){const e=ac(t.join("")),c=rc(e,r,o,n,d),p=nc({container:r,media:o,layer:i,value:u.toString(),support:n,selector:e,property:d}),h=l&&{key:d,value:l}||ws(d,u),f=h.key!==d||h.value!==u,g=f?nc({container:r,value:h.value.toString(),property:h.key,selector:e,media:o,layer:i,support:n}):void 0,E=f?{rtlClassName:g,rtlProperty:h.key,rtlValue:h.value}:void 0,T=ic(t,i,o,n,r),[N,m]=zl(fc({className:p,media:o,layer:i,selectors:t,property:d,support:n,container:r,value:u},E));gc(a,c,p,g),Tc(s,T,N,m,o)}else if("animationName"===d){const e=Array.isArray(u)?u:[u],l=[],c=[];for(const t of e){const e=Yl(t),i=Yl(Cs(t)),n="f"+Es(e);let r;const a=Zl(n,e);let d=[];e===i?r=n:(r="f"+Es(i),d=Zl(r,i));for(let e=0;e<a.length;e++)Tc(s,"k",a[e],d[e],o);l.push(n),c.push(r)}Nc({animationName:l.join(", ")},t,o,i,n,r,a,s,c.join(", "))}else if(Array.isArray(u)){if(0===u.length)continue;const e=ac(t.join("")),l=rc(e,r,o,n,d),c=nc({container:r,media:o,layer:i,value:u.map((e=>(null!=e?e:"").toString())).join(";"),support:n,selector:e,property:d}),p=u.map((e=>ws(d,e)));if(p.some((e=>e.key!==p[0].key)))continue;const h=p[0].key!==d||p.some(((e,t)=>e.value!==u[t])),f=h?nc({container:r,value:p.map((e=>{var t;return(null!==(t=null==e?void 0:e.value)&&void 0!==t?t:"").toString()})).join(";"),property:p[0].key,selector:e,layer:i,media:o,support:n}):void 0,g=h?{rtlClassName:f,rtlProperty:p[0].key,rtlValue:p.map((e=>e.value))}:void 0,E=ic(t,i,o,n,r),[T,N]=zl(fc({className:c,media:o,layer:i,selectors:t,property:d,support:n,container:r,value:u},g));gc(a,l,c,f),Tc(s,E,T,N,o)}else if(null!=(c=u)&&"object"==typeof c&&!1===Array.isArray(c))if(Jl(d))Nc(u,t.concat($s(d)),o,i,n,r,a,s);else if(ql(d)){const e=Kl(o,d.slice(6).trim());Nc(u,t,e,i,n,r,a,s)}else if(Xl(d)){const e=(i?`${i}.`:"")+d.slice(6).trim();Nc(u,t,o,e,n,r,a,s)}else if(ec(d)){const e=Kl(n,d.slice(9).trim());Nc(u,t,o,i,e,r,a,s)}else if(tc(d)){const e=d.slice(10).trim();Nc(u,t,o,i,n,e,a,s)}else sc(d,u)}var c;return[a,s]}function mc(e,t,o=[]){return ks+function(e){const t=e.length;if(7===t)return e;for(let o=t;o<7;o++)e+="0";return e}(Es(e+t))}function _c(e,t){let o="";for(const i in e){const n=e[i];if(n){const e=Array.isArray(n);o+="rtl"===t?(e?n[1]:n)+" ":(e?n[0]:n)+" "}}return o.slice(0,-1)}function Oc(e,t){const o={};for(const i in e){const n=_c(e[i],t);if(""===n){o[i]="";continue}const r=mc(n,t),a=r+" "+n;xs[r]=[e[i],t],o[i]=a}return o}const Ac=ts.useInsertionEffect?ts.useInsertionEffect:void 0,Ic=()=>{const e={};return function(t,o){Ac&&"undefined"!=typeof window&&window.document&&window.document.createElement?Ac((()=>{t.insertCSSRules(o)}),[t,o]):void 0===e[t.id]&&(t.insertCSSRules(o),e[t.id]=!0)}};var bc=Object.defineProperty,Sc=Object.getOwnPropertySymbols,Rc=Object.prototype.hasOwnProperty,yc=Object.prototype.propertyIsEnumerable,vc=(e,t,o)=>t in e?bc(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o,Cc=(e,t)=>{for(var o in t||(t={}))Rc.call(t,o)&&vc(e,o,t[o]);if(Sc)for(var o of Sc(t))yc.call(t,o)&&vc(e,o,t[o]);return e};const wc=["r","d","l","v","w","f","i","h","a","s","k","t","m","c"].reduce(((e,t,o)=>(e[t]=o,e)),{});function Mc(e,t,o,i,n={}){const r="m"===e,a=r?e+n.m:e;if(!i.stylesheets[a]){const s=t&&t.createElement("style"),l=function(e,t,o){const i=[];if(o["data-make-styles-bucket"]=t,e)for(const t in o)e.setAttribute(t,o[t]);return{elementAttributes:o,insertRule:function(t){return(null==e?void 0:e.sheet)?e.sheet.insertRule(t,e.sheet.cssRules.length):i.push(t)},element:e,bucketName:t,cssRules:()=>(null==e?void 0:e.sheet)?Array.from(e.sheet.cssRules).map((e=>e.cssText)):i}}(s,e,Cc(Cc({},i.styleElementAttributes),r&&{media:n.m}));i.stylesheets[a]=l,t&&s&&t.head.insertBefore(s,function(e,t,o,i,n){const r=wc[o];let a=e=>r-wc[e.getAttribute("data-make-styles-bucket")],s=e.head.querySelectorAll("[data-make-styles-bucket]");if("m"===o&&n){const t=e.head.querySelectorAll(`[data-make-styles-bucket="${o}"]`);t.length&&(s=t,a=e=>i.compareMediaQueries(n.m,e.media))}const l=s.length;let c=l-1;for(;c>=0;){const e=s.item(c);if(a(e)>0)return e.nextSibling;c--}return l>0?s.item(0):t?t.nextSibling:null}(t,o,e,i,n))}return i.stylesheets[a]}function Dc(e,t){try{e.insertRule(t)}catch(e){}}let Pc=0;const xc=(e,t)=>e<t?-1:e>t?1:0;function kc(e=("undefined"==typeof document?void 0:document),t={}){const{unstable_filterCSSRule:o,insertionPoint:i,styleElementAttributes:n,compareMediaQueries:r=xc}=t,a={insertionCache:{},stylesheets:{},styleElementAttributes:Object.freeze(n),compareMediaQueries:r,id:"d"+Pc++,insertCSSRules(t){for(const r in t){const s=t[r];for(let t=0,l=s.length;t<l;t++){const[l,c]=(n=s[t],Array.isArray(n)?n:[n]),d=Mc(r,e,i||null,a,c);a.insertionCache[l]||(a.insertionCache[l]=r,o?o(l)&&Dc(d,l):Dc(d,l))}}var n}};return a}const Lc=ts.createContext(kc());function Fc(){return ts.useContext(Lc)}const Gc=ts.createContext("ltr");function Bc(){return ts.useContext(Gc)}function Wc(e){const t=function(e,t=gs){const o=t();let i=null,n=null,r=null,a=null;return function(t){const{dir:s,renderer:l}=t;null===i&&([i,n]=function(e){const t={},o={};for(const i in e){const n=e[i],[r,a]=Nc(n);t[i]=r,Object.keys(a).forEach((e=>{o[e]=(o[e]||[]).concat(a[e])}))}return[t,o]}(e));const c="ltr"===s;return c?null===r&&(r=Oc(i,s)):null===a&&(a=Oc(i,s)),o(l,n),c?r:a}}(e,Ic);return function(){const e=Bc(),o=Fc();return t({dir:e,renderer:o})}}var Uc;(Uc||(Uc={})).isTokenizationEnrollRewardsEnabled="isTokenizationEnrollRewardsEnabled";const Vc={showContent:!0,setAppState:()=>{},appName:Ga.MiniWallet},$c=(ts.createContext(Vc),Wc({container:{}}),["Top","Right","Bottom","Left"]);function Hc(e,t,...o){const[i,n=i,r=i,a=n]=o,s=[i,n,r,a],l={};for(let o=0;o<s.length;o+=1)(s[o]||0===s[o])&&(l[e+$c[o]+t]=s[o]);return l}function zc(...e){return Hc("border","Width",...e)}function jc(...e){return Hc("border","Style",...e)}function Yc(...e){return Hc("border","Color",...e)}const Zc=["none","hidden","dotted","dashed","solid","double","groove","ridge","inset","outset"];function Kc(e){return Zc.includes(e)}var qc=Object.defineProperty,Xc=Object.getOwnPropertySymbols,Qc=Object.prototype.hasOwnProperty,Jc=Object.prototype.propertyIsEnumerable,ed=(e,t,o)=>t in e?qc(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o,td=(e,t)=>{for(var o in t||(t={}))Qc.call(t,o)&&ed(e,o,t[o]);if(Xc)for(var o of Xc(t))Jc.call(t,o)&&ed(e,o,t[o]);return e},od=Object.defineProperty,id=Object.getOwnPropertySymbols,nd=Object.prototype.hasOwnProperty,rd=Object.prototype.propertyIsEnumerable,ad=(e,t,o)=>t in e?od(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o,sd=(e,t)=>{for(var o in t||(t={}))nd.call(t,o)&&ad(e,o,t[o]);if(id)for(var o of id(t))rd.call(t,o)&&ad(e,o,t[o]);return e},ld=Object.defineProperty,cd=Object.getOwnPropertySymbols,dd=Object.prototype.hasOwnProperty,ud=Object.prototype.propertyIsEnumerable,pd=(e,t,o)=>t in e?ld(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o,hd=(e,t)=>{for(var o in t||(t={}))dd.call(t,o)&&pd(e,o,t[o]);if(cd)for(var o of cd(t))ud.call(t,o)&&pd(e,o,t[o]);return e},fd=Object.defineProperty,gd=Object.getOwnPropertySymbols,Ed=Object.prototype.hasOwnProperty,Td=Object.prototype.propertyIsEnumerable,Nd=(e,t,o)=>t in e?fd(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o,md=(e,t)=>{for(var o in t||(t={}))Ed.call(t,o)&&Nd(e,o,t[o]);if(gd)for(var o of gd(t))Td.call(t,o)&&Nd(e,o,t[o]);return e},_d=Object.defineProperty,Od=Object.getOwnPropertySymbols,Ad=Object.prototype.hasOwnProperty,Id=Object.prototype.propertyIsEnumerable,bd=(e,t,o)=>t in e?_d(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o,Sd=(e,t)=>{for(var o in t||(t={}))Ad.call(t,o)&&bd(e,o,t[o]);if(Od)for(var o of Od(t))Id.call(t,o)&&bd(e,o,t[o]);return e};const Rd=e=>"number"==typeof e&&!Number.isNaN(e),yd=e=>"auto"===e,vd=["content","fit-content","max-content","min-content"],Cd=e=>vd.some((t=>e===t))||(e=>"string"==typeof e&&/(\d+(\w+|%))/.test(e))(e),wd=/var\(.*\)/gi,Md=/^[a-zA-Z0-9\-_\\#;]+$/,Dd=/^-moz-initial$|^auto$|^initial$|^inherit$|^revert$|^unset$|^span \d+$|\d.*/;function Pd(e){return void 0!==e&&"string"==typeof e&&Md.test(e)&&!Dd.test(e)}var xd=Object.defineProperty,kd=Object.getOwnPropertySymbols,Ld=Object.prototype.hasOwnProperty,Fd=Object.prototype.propertyIsEnumerable,Gd=(e,t,o)=>t in e?xd(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o,Bd=(e,t)=>{for(var o in t||(t={}))Ld.call(t,o)&&Gd(e,o,t[o]);if(kd)for(var o of kd(t))Fd.call(t,o)&&Gd(e,o,t[o]);return e};const Wd=["-moz-initial","inherit","initial","revert","unset"];var Ud=Object.defineProperty,Vd=Object.getOwnPropertySymbols,$d=Object.prototype.hasOwnProperty,Hd=Object.prototype.propertyIsEnumerable,zd=(e,t,o)=>t in e?Ud(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o,jd=(e,t)=>{for(var o in t||(t={}))$d.call(t,o)&&zd(e,o,t[o]);if(Vd)for(var o of Vd(t))Hd.call(t,o)&&zd(e,o,t[o]);return e};const Yd=["dashed","dotted","double","solid","wavy"],Zd={border:function(...e){return Kc(e[0])?td(td(td({},jc(e[0])),e[1]&&zc(e[1])),e[2]&&Yc(e[2])):td(td(td({},zc(e[0])),e[1]&&jc(e[1])),e[2]&&Yc(e[2]))},borderLeft:function(...e){return Kc(e[0])?sd(sd({borderLeftStyle:e[0]},e[1]&&{borderLeftWidth:e[1]}),e[2]&&{borderLeftColor:e[2]}):sd(sd({borderLeftWidth:e[0]},e[1]&&{borderLeftStyle:e[1]}),e[2]&&{borderLeftColor:e[2]})},borderBottom:function(...e){return Kc(e[0])?hd(hd({borderBottomStyle:e[0]},e[1]&&{borderBottomWidth:e[1]}),e[2]&&{borderBottomColor:e[2]}):hd(hd({borderBottomWidth:e[0]},e[1]&&{borderBottomStyle:e[1]}),e[2]&&{borderBottomColor:e[2]})},borderRight:function(...e){return Kc(e[0])?md(md({borderRightStyle:e[0]},e[1]&&{borderRightWidth:e[1]}),e[2]&&{borderRightColor:e[2]}):md(md({borderRightWidth:e[0]},e[1]&&{borderRightStyle:e[1]}),e[2]&&{borderRightColor:e[2]})},borderTop:function(...e){return Kc(e[0])?Sd(Sd({borderTopStyle:e[0]},e[1]&&{borderTopWidth:e[1]}),e[2]&&{borderTopColor:e[2]}):Sd(Sd({borderTopWidth:e[0]},e[1]&&{borderTopStyle:e[1]}),e[2]&&{borderTopColor:e[2]})},borderColor:Yc,borderStyle:jc,borderRadius:function(e,t=e,o=e,i=t){return{borderBottomRightRadius:o,borderBottomLeftRadius:i,borderTopRightRadius:t,borderTopLeftRadius:e}},borderWidth:zc,flex:function(...e){const t=1===e.length,o=2===e.length,i=3===e.length;if(t){const[t]=e;if("initial"===t)return{flexGrow:0,flexShrink:1,flexBasis:"auto"};if(yd(t))return{flexGrow:1,flexShrink:1,flexBasis:"auto"};if("none"===t)return{flexGrow:0,flexShrink:0,flexBasis:"auto"};if(Rd(t))return{flexGrow:t,flexShrink:1,flexBasis:0};if(Cd(t))return{flexGrow:1,flexShrink:1,flexBasis:t}}if(o){const[t,o]=e;if(Rd(o))return{flexGrow:t,flexShrink:o,flexBasis:0};if(Cd(o))return{flexGrow:t,flexShrink:1,flexBasis:o}}if(i){const[t,o,i]=e;if(Rd(t)&&Rd(o)&&(yd(i)||Cd(i)))return{flexGrow:t,flexShrink:o,flexBasis:i}}return{}},gap:function(e,t=e){return{columnGap:e,rowGap:t}},gridArea:function(...e){if(e.some((e=>!function(e){return void 0===e||"number"==typeof e||"string"==typeof e&&!wd.test(e)}(e))))return{};const t=void 0!==e[0]?e[0]:"auto",o=void 0!==e[1]?e[1]:Pd(t)?t:"auto";return{gridRowStart:t,gridColumnStart:o,gridRowEnd:void 0!==e[2]?e[2]:Pd(t)?t:"auto",gridColumnEnd:void 0!==e[3]?e[3]:Pd(o)?o:"auto"}},margin:function(...e){return Hc("margin","",...e)},marginBlock:function(e,t=e){return{marginBlockStart:e,marginBlockEnd:t}},marginInline:function(e,t=e){return{marginInlineStart:e,marginInlineEnd:t}},padding:function(...e){return Hc("padding","",...e)},paddingBlock:function(e,t=e){return{paddingBlockStart:e,paddingBlockEnd:t}},paddingInline:function(e,t=e){return{paddingInlineStart:e,paddingInlineEnd:t}},overflow:function(e,t=e){return{overflowX:e,overflowY:t}},inset:function(...e){const[t,o=t,i=t,n=o]=e;return{top:t,right:o,bottom:i,left:n}},outline:function(e,t,o){return Bd(Bd({outlineWidth:e},t&&{outlineStyle:t}),o&&{outlineColor:o})},transition:function(...e){return function(e){return 1===e.length&&Wd.includes(e[0])}(e)?{transitionDelay:e[0],transitionDuration:e[0],transitionProperty:e[0],transitionTimingFunction:e[0]}:(t=e,1===t.length&&Array.isArray(t[0])?t[0]:[t]).reduce(((e,[t,o="0s",i="0s",n="ease"],r)=>(0===r?(e.transitionProperty=t,e.transitionDuration=o,e.transitionDelay=i,e.transitionTimingFunction=n):(e.transitionProperty+=`, ${t}`,e.transitionDuration+=`, ${o}`,e.transitionDelay+=`, ${i}`,e.transitionTimingFunction+=`, ${n}`),e)),{});var t},textDecoration:function(e,...t){if(0===t.length)return function(e){return Yd.includes(e)}(e)?{textDecorationStyle:e}:{textDecorationLine:e};const[o,i,n]=t;return jd(jd(jd({textDecorationLine:e},o&&{textDecorationStyle:o}),i&&{textDecorationColor:i}),n&&{textDecorationThickness:n})}},Kd="@media (prefers-color-scheme: dark)",qd=e=>`@media only screen and (min-width: ${e}px)`,Xd=(qd(1),qd(375),qd(480),qd(640),qd(1036),qd(1367),qd(1920),{paddingInlineStart:"16px",paddingInlineEnd:"16px",fontSize:"12px",lineHeight:"16px",userSelect:"none"}),Qd=Zd.borderColor(ia.errorColor),Jd={...Qd,"&:hover:enabled":{...Qd},"&:active:enabled":{...Qd},"&:focus:enabled":{...Qd}},eu=ua()?{[Kd]:{backgroundColor:ia.darkNotificationBackground,":hover,:focus":{backgroundColor:ia.darkModeButtonBackgroundHover}},"@media screen and (-ms-high-contrast: active)":{":hover,:focus":{"forced-color-adjust":"none",backgroundColor:"Highlight"}},":hover,:focus":{backgroundColor:ia.buttonBackgroundHover},":hover .CTA":{"text-decoration":"underline"},":focus .CTA":{"text-decoration":"underline"},":focus-visible":{outlineWidth:"0px"},marginBlockEnd:"1px"}:{},tu={container:{width:"100%",backgroundColor:ia.whiteBackground,[Kd]:{backgroundColor:ia.darkNotificationBackground},userSelect:"none",textAlign:"start",...eu},header:{paddingBlockStart:"16px",paddingBlockEnd:"16px",paddingInlineStart:"18px",width:"252px",fontWeight:"600",wordBreak:"break-word"},image:{width:"100%"},text:{...Xd,paddingBlockStart:"12px",paddingBlockEnd:"12px"},textCompact:{...Xd},action:{paddingInlineStart:"16px",paddingBlockEnd:"16px",width:"fit-content"},actionButton:{fontWeight:"400"},agreement:{paddingInlineStart:"16px",paddingInlineEnd:"16px",fontSize:"10px",lineHeight:"13px",paddingBlockEnd:"16px"},agreementLarge:{...Xd,paddingBlockEnd:"16px"},agreementSmall:{...Xd,paddingBlockEnd:"12px"},link:{},linkLarge:{},inlineError:{color:ia.errorColor,fontSize:"12px",lineHeight:"16px",fontWeight:"600"}},ou="Enter",iu={width:"126px",textAlign:"start",display:"grid",gridTemplateColumns:"88px 12px"};var nu,ru,au,su,lu,cu,du,uu,pu,hu,fu;Wc({...tu,icon:{display:"inline-block",paddingInlineStart:"18px",paddingBlockStart:"14px",color:ia.normalControl},header:{...tu.header,paddingBlockStart:"12px",paddingBlockEnd:"0px",width:"220px"},title:{fontWeight:"600",lineHeight:"20px",fontSize:"14px"},text:{...tu.text,fontSize:"12px",paddingInlineStart:"18px",paddingBlockStart:"8px",paddingBlockEnd:"0px",lineHeight:"16px",width:"220px"},expDate:{paddingBlockEnd:"12px",paddingInlineStart:"16px",width:"fit-content"},expDateSelector:{width:"126px",paddingInlineEnd:"12px",display:"inline-block"},label:{paddingBlockEnd:"4px",display:"inline-block"},menuButton:iu,menuPopover:{minWidth:"116px",height:"64px",overflowY:"scroll"},errorMenuButton:{...Jd,...iu},iconContainer:{display:"inline-block",verticalAlign:"top"},contentContainer:{display:"inline-block"},action:{...tu.action,width:"fit-content",height:"20px",paddingInlineStart:"18px",paddingBlockStart:"8px",paddingBlockEnd:"8px"},actionButton:{display:"inline-block",...tu.actionButton,fontSize:"12px"}}),function(e){e.ClientErrors="Microsoft.Wallet.ClientError",e.TokenizationEnrollActions="Microsoft.Wallet.Tokenization.MainFunnel"}(nu||(nu={})),function(e){e[e.ExpressCheckout=0]="ExpressCheckout",e[e.WalletHub=1]="WalletHub",e[e.BNPL=2]="BNPL",e[e.MAX=3]="MAX"}(ru||(ru={})),(fu=au||(au={})).Enroll="Enroll",fu.Autofill="Autofill",fu.EnrollInline="EnrollInline",fu.AutofillInline="AutofillInline",(hu=su||(su={})).DropdownList="DropdownList",hu.Settings="Settings",hu.EditCard="EditCard",hu.WalletCheckout="WalletCheckout",hu.PaymentList="PaymentList",hu.ProfileNotification="ProfileNotification",function(e){e.Local="Local",e.Server="Server",e.Partial="Partial",e.Unknown="Unknown"}(lu||(lu={})),function(e){e.VISA="Visa",e.MASTERCARD="MasterCard",e.UNKNOWN="Unknown"}(cu||(cu={})),(pu=du||(du={}))[pu.CARD_ELIGIBLE=0]="CARD_ELIGIBLE",pu[pu.CARD_TOKENIZED=1]="CARD_TOKENIZED",pu[pu.USE_TOKENIZED_CARD_TOGGLE_ON=2]="USE_TOKENIZED_CARD_TOGGLE_ON",pu[pu.USE_TOKENIZED_CARD_TOGGLE_OFF=3]="USE_TOKENIZED_CARD_TOGGLE_OFF",pu[pu.SELECT_TOKENIZED_CARD_AUTOFILL=4]="SELECT_TOKENIZED_CARD_AUTOFILL",pu[pu.SELECT_ORIGINAL_CARD_AUTOFILL=5]="SELECT_ORIGINAL_CARD_AUTOFILL",pu[pu.CLICK_ENROLL_TOGGLE=6]="CLICK_ENROLL_TOGGLE",pu[pu.OPEN_ENROLL_DIALOG=7]="OPEN_ENROLL_DIALOG",pu[pu.CLCIK_ENROALL_DIALOG_LEARN_MORE_LINK=8]="CLCIK_ENROALL_DIALOG_LEARN_MORE_LINK",pu[pu.CLICK_ENROLL_NOW=9]="CLICK_ENROLL_NOW",pu[pu.CLOSE_ENROLL_DIALOG=10]="CLOSE_ENROLL_DIALOG",pu[pu.OPEN_CONTACTING_BANK_DIALOG=11]="OPEN_CONTACTING_BANK_DIALOG",pu[pu.CLOSE_CONTACTING_BANK_DIALOG=12]="CLOSE_CONTACTING_BANK_DIALOG",pu[pu.REQUEST_TOKEN_STARTED=13]="REQUEST_TOKEN_STARTED",pu[pu.REQUEST_TOKEN_GET_HBI_FAILED=14]="REQUEST_TOKEN_GET_HBI_FAILED",pu[pu.REQUEST_TOKEN_SUCCESS=15]="REQUEST_TOKEN_SUCCESS",pu[pu.DEVICE_BINDING_REQUIRED=16]="DEVICE_BINDING_REQUIRED",pu[pu.DEVICE_BINDING_NOT_REQUIRED=17]="DEVICE_BINDING_NOT_REQUIRED",pu[pu.VERIFICATION_SUCCESS=18]="VERIFICATION_SUCCESS",pu[pu.DEVICE_ENROLL_REQUIRED=19]="DEVICE_ENROLL_REQUIRED",pu[pu.DEVICE_ENROLL_NOT_REQUIRED=20]="DEVICE_ENROLL_NOT_REQUIRED",pu[pu.DEVICE_ENROLL_STARTED=21]="DEVICE_ENROLL_STARTED",pu[pu.DEVICE_ENROLL_SUCCESS=22]="DEVICE_ENROLL_SUCCESS",pu[pu.REQUEST_DEVICE_BINGDING_STARTED=23]="REQUEST_DEVICE_BINGDING_STARTED",pu[pu.REQUEST_DEVICE_BINGDING_GET_HBI_FAILED=24]="REQUEST_DEVICE_BINGDING_GET_HBI_FAILED",pu[pu.REQUEST_DEVICE_BINGDING_SUCCESS=25]="REQUEST_DEVICE_BINGDING_SUCCESS",pu[pu.CHALLENGE_APPROVED=26]="CHALLENGE_APPROVED",pu[pu.CHALLENGE_REQUIRED=27]="CHALLENGE_REQUIRED",pu[pu.GET_CRENDETAILS_STARTED=28]="GET_CRENDETAILS_STARTED",pu[pu.GET_CRENDETAILS_GET_HBI_FAILED=29]="GET_CRENDETAILS_GET_HBI_FAILED",pu[pu.GET_CRENDETAILS_SUCCESS=30]="GET_CRENDETAILS_SUCCESS",pu[pu.DECRYPT_TOKEN_SUCCESS=31]="DECRYPT_TOKEN_SUCCESS",pu[pu.GET_TOKEN_METADATA_SUCCESS=32]="GET_TOKEN_METADATA_SUCCESS",pu[pu.FETCH_AUTOFILL_DATA_SUCCESS=33]="FETCH_AUTOFILL_DATA_SUCCESS",pu[pu.OPEN_OTP_DIALOG=34]="OPEN_OTP_DIALOG",pu[pu.CLOSE_OTP_DIALOG=35]="CLOSE_OTP_DIALOG",pu[pu.CHANGE_OTP_METHOD=36]="CHANGE_OTP_METHOD",pu[pu.GET_OTP_SUCCESS=37]="GET_OTP_SUCCESS",pu[pu.CLICK_VERIFY_OTP_BUTTON=38]="CLICK_VERIFY_OTP_BUTTON",pu[pu.OPEN_VERIFIED_SUCCESS_DIALOG=39]="OPEN_VERIFIED_SUCCESS_DIALOG",pu[pu.CLOSE_VERIFIED_SUCCESS_DIALOG=40]="CLOSE_VERIFIED_SUCCESS_DIALOG",pu[pu.OPEN_GENERATING_CARD_DETAILS_DIALOG=41]="OPEN_GENERATING_CARD_DETAILS_DIALOG",pu[pu.CLOSE_GENERATING_CARD_DETAILS_DIALOG_FROM_UI=42]="CLOSE_GENERATING_CARD_DETAILS_DIALOG_FROM_UI",pu[pu.CLOSE_GENERATING_CARD_DETAILS_DIALOG_FROM_NATIVE=43]="CLOSE_GENERATING_CARD_DETAILS_DIALOG_FROM_NATIVE",pu[pu.AUTOFILL_SUCCESS=44]="AUTOFILL_SUCCESS",pu[pu.SHOW_VIRTUAL_CARD_READY_FLYOUT=45]="SHOW_VIRTUAL_CARD_READY_FLYOUT",pu[pu.OPEN_ERROR_DIALOG=46]="OPEN_ERROR_DIALOG",pu[pu.GET_LOCAL_META_DATA_SUCCESS=47]="GET_LOCAL_META_DATA_SUCCESS",pu[pu.CLICK_ELIGIBLE_CARD=48]="CLICK_ELIGIBLE_CARD",pu[pu.CLICK_ELIGIBLE_CARD_TAG=49]="CLICK_ELIGIBLE_CARD_TAG",pu[pu.SAVE_ELIGIBLE_CARD_WITH_TOKENIZATION_CONSENT=50]="SAVE_ELIGIBLE_CARD_WITH_TOKENIZATION_CONSENT",pu[pu.TOKEN_WILL_BE_SUBMITTED=51]="TOKEN_WILL_BE_SUBMITTED",pu[pu.TOKEN_SUBMITTED_FOR_TRANSACTION=52]="TOKEN_SUBMITTED_FOR_TRANSACTION",pu[pu.CARD_ELIGIBLE_SHOWN_ONCE=53]="CARD_ELIGIBLE_SHOWN_ONCE",pu[pu.CARD_TOKENIZED_SHOWN_ONCE=54]="CARD_TOKENIZED_SHOWN_ONCE",pu[pu.OPEN_VERIFY_CVV_DIALOG=55]="OPEN_VERIFY_CVV_DIALOG",pu[pu.CLICK_VERIFY_CVV_BUTTON=56]="CLICK_VERIFY_CVV_BUTTON",pu[pu.CLOSE_VERIFY_CVV_DIALOG=57]="CLOSE_VERIFY_CVV_DIALOG",pu[pu.CLICK_ERROR_DIALOG_TRY_AGAIN_BUTTON=58]="CLICK_ERROR_DIALOG_TRY_AGAIN_BUTTON",pu[pu.OPEN_EDIT_CARD_PAGE=59]="OPEN_EDIT_CARD_PAGE",pu[pu.MAX=59]="MAX",function(e){e.impression="impression",e.engagement="engagement",e.snooze="snooze",e.dismiss="dismiss"}(uu||(uu={}));const gu={[La.CardExpiring]:{[uu.impression]:xa.CARD_EXPIRING_SOON_IMPRESSION,[uu.engagement]:xa.CARD_EXPIRING_SOON_ENGAGEMENT,[uu.snooze]:xa.CARD_EXPIRING_SOON_SNOOZE,[uu.dismiss]:xa.CARD_EXPIRING_SOON_DISMISS_PERMANENTLY},[La.CardExpired]:{[uu.impression]:xa.CARD_EXPIRED_IMPRESSION,[uu.engagement]:xa.CARD_EXPIRED_ENGAGEMENT,[uu.snooze]:xa.CARD_EXPIRED_SNOOZE,[uu.dismiss]:xa.CARD_EXPIRED_DISMISS_PERMANENTLY},[La.CardTokenizationEligible]:{[uu.impression]:xa.CARD_TOKENIZATION_ELIGIBLE_IMPRESSION,[uu.engagement]:xa.CARD_TOKENIZATION_ELIGIBLE_ENGAGEMENT,[uu.snooze]:xa.CARD_TOKENIZATION_ELIGIBLE_SNOOZE,[uu.dismiss]:xa.CARD_TOKENIZATION_ELIGIBLE_DISMISS_PERMANENTLY},[La.SignupCryptoWallet]:{[uu.impression]:xa.SIGN_UP_CRYPTOWALLET_IMPRESSION,[uu.engagement]:xa.SIGN_UP_CRYPTOWALLET_ENGAGEMENT,[uu.snooze]:xa.SIGN_UP_CRYPTOWALLET_SNOOZE,[uu.dismiss]:xa.SIGN_UP_CRYPTOWALLET_DISMISS_PERMANENTLY},[La.PasswordLeakage]:{[uu.impression]:xa.PASSWORD_LEAKAGE_IMPRESSION,[uu.engagement]:xa.PASSWORD_LEAKAGE_ENGAGEMENT,[uu.snooze]:xa.PASSWORD_LEAKAGE_SNOOZE,[uu.dismiss]:xa.PASSWORD_LEAKAGE_DISMISS_PERMANENTLY},[La.UpcomingHotelReservations]:{[uu.impression]:xa.UPCOMING_HOTEL_RESERVATION_IMPRESSION,[uu.engagement]:xa.UPCOMING_HOTEL_RESERVATION_ENGAGEMENT,[uu.snooze]:xa.UPCOMING_HOTEL_RESERVATION_SNOOZE,[uu.dismiss]:xa.UPCOMING_HOTEL_RESERVATION_DISMISS_PERMANENTLY},[La.PersonalizedOffersAvailable]:{[uu.impression]:xa.PERSONALIZED_OFFERS_AVAILABLE_IMPRESSION,[uu.engagement]:xa.PERSONALIZED_OFFERS_AVAILABLE_ENGAGEMENT,[uu.snooze]:xa.PERSONALIZED_OFFERS_AVAILABLE_SNOOZE,[uu.dismiss]:xa.PERSONALIZED_OFFERS_AVAILABLE_DISMISS_PERMANENTLY},[La.RoamCard]:{[uu.impression]:xa.ROAM_CARD_IMPRESSION,[uu.engagement]:xa.ROAM_CARD_ENGAGEMENT,[uu.snooze]:xa.ROAM_CARD_SNOOZE,[uu.dismiss]:xa.ROAM_CARD_DISMISS_PERMANENTLY},[La.DonationSummary]:{[uu.impression]:xa.DONATION_SUMMARY_IMPRESSION,[uu.engagement]:xa.DONATION_SUMMARY_ENGAGEMENT,[uu.snooze]:xa.DONATION_SUMMARY_SNOOZE,[uu.dismiss]:xa.DONATION_SUMMARY_DISMISS_PERMANENTLY},[La.PackageTracking]:{[uu.impression]:xa.PACKAGE_TRACKING_IMPRESSION,[uu.engagement]:xa.MAX,[uu.snooze]:xa.PACKAGE_TRACKING_SNOOZE,[uu.dismiss]:xa.PACKAGE_TRACKING_DISMISS_PERMANENTLY},[La.Rebates]:{[uu.impression]:xa.REBATES_IMPRESSION,[uu.engagement]:xa.REBATES_ENGAGEMENT,[uu.snooze]:xa.REBATES_SNOOZE,[uu.dismiss]:xa.REBATES_DISMISS_PERMANENTLY},[La.PWAPromotion]:{[uu.impression]:xa.PWA_PROMOTION_IMPRESSION,[uu.engagement]:xa.PWA_PROMOTION_ENGAGEMENT,[uu.snooze]:xa.PWA_PROMOTION_SNOOZE,[uu.dismiss]:xa.PWA_PROMOTION_DISMISS_PERMANENTLY},[La.DonationTrendNpo]:{[uu.impression]:xa.DONATION_TREND_NPO_IMPRESSION,[uu.engagement]:xa.DONATION_TREND_NPO_ENGAGEMENT,[uu.snooze]:xa.DONATION_TREND_NPO_SNOOZE,[uu.dismiss]:xa.DONATION_TREND_NPO_DISMISS_PERMANENTLY}};Wc({...tu,header:{...tu.header,display:"inline-block",paddingInlineStart:"16px",paddingBlockStart:"12px",paddingBlockEnd:"0px",width:"195px",fontWeight:"600",lineHeight:"20px",fontSize:"14px"},text:{...tu.text,paddingInlineStart:"16px",paddingBlockStart:"8px",paddingBlockEnd:"0px",lineHeight:"16px",width:"206px",fontSize:"12px"},action:{...tu.action,fontSize:"12px",width:"fit-content",paddingInlineStart:"16px",paddingBlockStart:"8px",paddingBlockEnd:"8px"},actionButton:{display:"inline-block",...tu.actionButton,fontSize:"12px"},iconContainer:{display:"inline-block",verticalAlign:"top"},icon:{paddingInlineStart:"20px",paddingBlockStart:"12px"},contentContainer:{display:"inline-block"}}),su.ProfileNotification;const Eu=(Wc({container:{...tu.container,paddingBlockEnd:"10px"},header:{paddingBlockStart:"10px",paddingBlockEnd:"8px",width:"252px",fontWeight:"600",wordBreak:"break-word",display:"flex",paddingInlineStart:"0px"},text:{...tu.text,paddingInlineStart:"62px",paddingInlineEnd:"36px",paddingBlockStart:"0px",paddingBlockEnd:"8px"},linkLarge:{...tu.linkLarge,fontSize:"12px",paddingInlineStart:"62px !important"},icon:{color:"var(--colorPaletteDarkOrangeForeground1)",[Kd]:{color:ia.RedAlertIconDarkMode},paddingInlineStart:"24px"}}),Wc({...tu,container__header_container:{display:"flex",paddingBlockStart:"12px",paddingInlineStart:"16px",paddingInlineEnd:"12px"},imageContainer:{width:"36px"},image:{paddingBlockStart:"5px",display:"block",maxWidth:"36px",maxHeight:"36px",width:"auto",height:"auto",objectFit:"contain"},container__header_text:{maxWidth:"198px",paddingInlineStart:"12px"},expirationTitle:{...tu.header,maxWidth:"182px",maxHeight:"50px",paddingInlineStart:"0px",paddingBlockStart:"0px",paddingBlockEnd:"0px",fontSize:"16px",lineHeight:"22px",overflowY:"hidden",textOverflow:"ellipsis",display:"-webkit-box","-webkit-line-clamp":"2","-webkit-box-orient":"vertical"},expirationContainer:{display:"flex",flexDirection:"row",fontSize:"12px",fontWeight:"400",lineHeight:"16px"},expiresInText:{overflowY:"hidden",textOverflow:"ellipsis",maxWidth:"198px",paddingInlineEnd:"6px"},countDownTimerText:{display:"inline-block",color:"var(--colorPaletteRedForeground1)"},action:{...tu.link,display:"flex",flexDirection:"row",justifyContent:"space-between",paddingBlockStart:"8px",width:"unset",paddingBlockEnd:"8px"},actionButton:{display:"inline-block",...tu.actionButton,fontSize:"12px",overflowY:"hidden",textOverflow:"ellipsis",maxWidth:"198px",minWidth:"132px",lineHeight:"16px",color:"#036AC4"},header:{...tu.header,paddingBlockStart:"0px",paddingInlineStart:"0px",paddingBlockEnd:"0px",width:"200px"},title:{fontWeight:"600",lineHeight:"20px",fontSize:"14px"}}),Wc({...tu,header:{...tu.header,paddingInlineStart:"0px",display:"inline-block",paddingBlockStart:"12px",paddingBlockEnd:"0px",width:"195px",fontWeight:"600",lineHeight:"20px",fontSize:"14px"},textContainer:{...tu.text,paddingInlineStart:"0px",paddingBlockStart:"8px",paddingBlockEnd:"0px",display:"flex",columnGap:"8px",alignItems:"center",width:"206px"},text:{lineHeight:"18px",fontSize:"14px"},action:{...tu.action,paddingInlineStart:"0px",fontSize:"12px",width:"fit-content",paddingBlockStart:"8px",paddingBlockEnd:"8px"},actionButton:{display:"inline-block",...tu.actionButton,fontSize:"12px"},iconContainer:{display:"inline-block",verticalAlign:"top"},icon:{paddingInlineStart:"24px",paddingBlockStart:"14px",[Kd]:{fill:"white","& path":{fill:"white"}}},contentContainer:{paddingInlineStart:"18px",display:"inline-block",boxSizing:"border-box"},cardLogo:{display:"block",width:"32px",height:"auto"}}),Wc({container:{...tu.container,paddingBlockEnd:"8px"},header:{display:"flex",marginInlineStart:"24px",paddingBlockStart:"12px",paddingBlockEnd:"0px",width:"240px"},icon:{color:"var(--colorCompoundBrandForeground1)",lineHeight:"20px"},headerText:{width:"220px",fontWeight:"600",wordBreak:"break-word",paddingInlineStart:"18px",lineHeight:"20px"},text:{fontSize:"12px",lineHeight:"16px",marginInlineStart:"62px",marginInlineEnd:"16px",paddingBlockStart:"8px",paddingBlockEnd:"0px",width:"200px"},action:{paddingBlockStart:"8px",marginInlineStart:"62px",width:"fit-content"},actionLink:{fontSize:"12px",lineHeight:"16px"}}),Wc({container:{...tu.container,paddingBlockEnd:"8px"},header:{display:"flex",marginInlineStart:"16px",marginInlineEnd:"40px",paddingBlockStart:"12px"},icon:{width:"20px",height:"20px",...Zd.paddingBlock("8px"),...Zd.paddingInline("8px"),backgroundColor:"var(--colorPaletteRedBackground1)",color:"var(--colorPaletteRedBackground3)",...Zd.borderRadius("50%")},text:{fontWeight:"600",wordBreak:"break-word",marginInlineStart:"12px",lineHeight:"20px"},action:{height:"20px",marginBlockStart:"16px",paddingBlockStart:"8px",paddingBlockEnd:"4px",marginInlineStart:"64px",width:"fit-content"},actionButton:{...tu.actionButton,fontSize:"12px",lineHeight:"20px"}}),Wc({container:{...tu.container,display:"flex"},iconContainer:{paddingBlockStart:"8px",paddingInlineStart:"16px",paddingInlineEnd:"12px",flexShrink:0,flexGrow:0},icon:{lineHeight:"36px",width:"36px",height:"36px"},contentContainer:{display:"flex",flexDirection:"column",marginInlineEnd:"44px"},header:{fontWeight:"600",wordBreak:"break-word",lineHeight:"20px",paddingBlockStart:"8px",paddingBlockEnd:"8px"},description:{fontSize:"12px",lineHeight:"16px"},action:{height:"20px",paddingBlockStart:"8px",paddingBlockEnd:"12px"},actionButton:{...tu.actionButton,fontSize:"12px",lineHeight:"20px"}}),Symbol("fui.slotRenderFunction")),Tu=Symbol("fui.slotElementType");var Nu=Object.defineProperty,mu=Object.defineProperties,_u=Object.getOwnPropertyDescriptors,Ou=Object.getOwnPropertySymbols,Au=Object.prototype.hasOwnProperty,Iu=Object.prototype.propertyIsEnumerable,bu=(e,t,o)=>t in e?Nu(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o,Su=(e,t)=>{for(var o in t||(t={}))Au.call(t,o)&&bu(e,o,t[o]);if(Ou)for(var o of Ou(t))Iu.call(t,o)&&bu(e,o,t[o]);return e};function Ru(e,t){const{defaultProps:o,elementType:i}=t,n=function(e){return"string"==typeof e||"number"==typeof e||Array.isArray(e)||ts.isValidElement(e)?{children:e}:e}(e),r=(a=Su(Su({},o),n),mu(a,_u({[Tu]:i})));var a;return n&&"function"==typeof n.children&&(r[Eu]=n.children,r.children=null==o?void 0:o.children),r}const yu=(...e)=>{const t={};for(const o of e){const e=Array.isArray(o)?o:Object.keys(o);for(const o of e)t[o]=1}return t},vu=yu(["onAuxClick","onAnimationEnd","onAnimationStart","onCopy","onCut","onPaste","onCompositionEnd","onCompositionStart","onCompositionUpdate","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onInput","onSubmit","onLoad","onError","onKeyDown","onKeyDownCapture","onKeyPress","onKeyUp","onAbort","onCanPlay","onCanPlayThrough","onDurationChange","onEmptied","onEncrypted","onEnded","onLoadedData","onLoadedMetadata","onLoadStart","onPause","onPlay","onPlaying","onProgress","onRateChange","onSeeked","onSeeking","onStalled","onSuspend","onTimeUpdate","onVolumeChange","onWaiting","onClick","onClickCapture","onContextMenu","onDoubleClick","onDrag","onDragEnd","onDragEnter","onDragExit","onDragLeave","onDragOver","onDragStart","onDrop","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseOut","onMouseOver","onMouseUp","onMouseUpCapture","onSelect","onTouchCancel","onTouchEnd","onTouchMove","onTouchStart","onScroll","onWheel","onPointerCancel","onPointerDown","onPointerEnter","onPointerLeave","onPointerMove","onPointerOut","onPointerOver","onPointerUp","onGotPointerCapture","onLostPointerCapture"]),Cu=yu(["accessKey","children","className","contentEditable","dir","draggable","hidden","htmlFor","id","lang","ref","role","style","tabIndex","title","translate","spellCheck","name"]),wu=yu(["itemID","itemProp","itemRef","itemScope","itemType"]),Mu=yu(Cu,vu,wu),Du=yu(Mu,["form"]),Pu=yu(Mu,["height","loop","muted","preload","src","width"]),xu=yu(Pu,["poster"]),ku=yu(Mu,["start"]),Lu=yu(Mu,["value"]),Fu=yu(Mu,["download","href","hrefLang","media","rel","target","type"]),Gu=yu(Mu,["dateTime"]),Bu=yu(Mu,["autoFocus","disabled","form","formAction","formEncType","formMethod","formNoValidate","formTarget","type","value"]),Wu={label:Du,audio:Pu,video:xu,ol:ku,li:Lu,a:Fu,button:Bu,input:yu(Bu,["accept","alt","autoCapitalize","autoComplete","checked","dirname","form","height","inputMode","list","max","maxLength","min","multiple","pattern","placeholder","readOnly","required","src","step","size","type","value","width"]),textarea:yu(Bu,["autoCapitalize","cols","dirname","form","maxLength","placeholder","readOnly","required","rows","wrap"]),select:yu(Bu,["form","multiple","required"]),option:yu(Mu,["selected","value"]),table:yu(Mu,["cellPadding","cellSpacing"]),tr:Mu,th:yu(Mu,["colSpan","rowSpan","scope"]),td:yu(Mu,["colSpan","headers","rowSpan","scope"]),colGroup:yu(Mu,["span"]),col:yu(Mu,["span"]),fieldset:yu(Mu,["disabled","form"]),form:yu(Mu,["acceptCharset","action","encType","encType","method","noValidate","target"]),iframe:yu(Mu,["allow","allowFullScreen","allowPaymentRequest","allowTransparency","csp","height","importance","referrerPolicy","sandbox","src","srcDoc","width"]),img:yu(Mu,["alt","crossOrigin","height","src","srcSet","useMap","width"]),time:Gu,dialog:yu(Mu,["open","onCancel","onClose"])};const Uu=(e,t,o)=>{var i;return function(e,t,o){const i=e&&Wu[e]||Mu;return i.as=1,function(e,t,o){const i=Array.isArray(t),n={},r=Object.keys(e);for(const a of r)!(!i&&t[a]||i&&t.indexOf(a)>=0||0===a.indexOf("data-")||0===a.indexOf("aria-"))||o&&-1!==(null==o?void 0:o.indexOf(a))||(n[a]=e[a]);return n}(t,i,o)}(null!==(i=t.as)&&void 0!==i?i:e,t,o)};var Vu=Object.defineProperty,$u=Object.getOwnPropertySymbols,Hu=Object.prototype.hasOwnProperty,zu=Object.prototype.propertyIsEnumerable,ju=(e,t,o)=>t in e?Vu(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o,Yu=(e,t)=>{for(var o in t||(t={}))Hu.call(t,o)&&ju(e,o,t[o]);if($u)for(var o of $u(t))zu.call(t,o)&&ju(e,o,t[o]);return e};function Zu(e){return Boolean(null==e?void 0:e.hasOwnProperty(Tu))}var Ku=Object.defineProperty,qu=Object.defineProperties,Xu=Object.getOwnPropertyDescriptors,Qu=Object.getOwnPropertySymbols,Ju=Object.prototype.hasOwnProperty,ep=Object.prototype.propertyIsEnumerable,tp=(e,t,o)=>t in e?Ku(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o;function op(e,t){return function(o,i,n,r,a){return Zu(i)?t(function(e,t){return o=((e,t)=>{for(var o in t||(t={}))Ju.call(t,o)&&tp(e,o,t[o]);if(Qu)for(var o of Qu(t))ep.call(t,o)&&tp(e,o,t[o]);return e})({},t),qu(o,Xu({[Tu]:e}));var o}(o,i),null,n,r,a):Zu(o)?t(o,i,n,r,a):e(o,i,n,r,a)}}n(5732);var ip=Object.getOwnPropertySymbols,np=Object.prototype.hasOwnProperty,rp=Object.prototype.propertyIsEnumerable,ap=e=>"symbol"==typeof e?e:e+"";function sp(e){var t,o;const i=e,{as:n,[t=Tu]:r,[o=Eu]:a}=i,s=((e,t)=>{var o={};for(var i in e)np.call(e,i)&&t.indexOf(i)<0&&(o[i]=e[i]);if(null!=e&&ip)for(var i of ip(e))t.indexOf(i)<0&&rp.call(e,i)&&(o[i]=e[i]);return o})(i,["as",ap(t),ap(o)]),l=s,c="string"==typeof r&&null!=n?n:r;return"string"!=typeof c&&n&&(l.as=n),{elementType:c,props:l,renderFunction:a}}var lp=n(2668);const cp=n.t(lp,2);var dp=Object.defineProperty,up=Object.getOwnPropertySymbols,pp=Object.prototype.hasOwnProperty,hp=Object.prototype.propertyIsEnumerable,fp=(e,t,o)=>t in e?dp(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o,gp=(e,t)=>{for(var o in t||(t={}))pp.call(t,o)&&fp(e,o,t[o]);if(up)for(var o of up(t))hp.call(t,o)&&fp(e,o,t[o]);return e},Ep=Object.defineProperty,Tp=Object.defineProperties,Np=Object.getOwnPropertyDescriptors,mp=Object.getOwnPropertySymbols,_p=Object.prototype.hasOwnProperty,Op=Object.prototype.propertyIsEnumerable,Ap=(e,t,o)=>t in e?Ep(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o,Ip=(e,t)=>{for(var o in t||(t={}))_p.call(t,o)&&Ap(e,o,t[o]);if(mp)for(var o of mp(t))Op.call(t,o)&&Ap(e,o,t[o]);return e};const bp=op(cp.jsx,((e,t,o)=>{const{elementType:i,renderFunction:n,props:r}=sp(e),a=gp(gp({},r),t);return n?cp.jsx(ts.Fragment,{children:n(i,a)},o):cp.jsx(i,a,o)}));function Sp(e,t){const o=function(e,t,o=gs){const i=o();let n=null,r=null;return function(o){const{dir:a,renderer:s}=o,l="ltr"===a;return l?null===n&&(n=Oc(e,a)):null===r&&(r=Oc(e,a)),i(s,t),l?n:r}}(e,t,Ic);return function(){const e=Bc(),t=Fc();return o({dir:e,renderer:t})}}op(cp.jsxs,((e,t,o)=>{const{elementType:i,renderFunction:n,props:r}=sp(e),a=Ip(Ip({},r),t);return n?cp.jsx(ts.Fragment,{children:n(i,(s=Ip({},a),l={children:cp.jsxs(ts.Fragment,{children:a.children},void 0)},Tp(s,Np(l))))},o):cp.jsxs(i,a,o);var s,l}));const Rp={};function yp(){let e=null,t="",o="";const i=new Array(arguments.length);for(let e=0;e<arguments.length;e++){const n=arguments[e];if("string"==typeof n&&""!==n){const r=n.indexOf(ks);if(-1===r)t+=n+" ";else{const a=n.substr(r,Ls);r>0&&(t+=n.slice(0,r)),o+=a,i[e]=a}}}if(""===o)return t.slice(0,-1);const n=Rp[o];if(void 0!==n)return t+n;const r=[];for(let t=0;t<arguments.length;t++){const o=i[t];if(o){const t=xs[o];t&&(r.push(t[0]),e=t[1])}}const a=Object.assign.apply(Object,[{}].concat(r));let s=_c(a,e);const l=mc(s,e,i);return s=l+" "+s,Rp[o]=s,xs[l]=[a,e],t+s}const vp=Sp({root:{Bahqtrf:"fk6fouc",Be2twd7:"fkhj508",Bg96gwp:"f1i3iumi",Bhrd7zp:"figsok6",fsow6f:"fpgzoln",mc9l5x:"f1w7gpdv",Huce71:"f6juhto",B68tc82:"f1mtd64y",Bmxbyg5:"f1y7q3j9",ygn44y:"f2jf649"},nowrap:{Huce71:"fz5stix",B68tc82:"f1p9o1ba",Bmxbyg5:"f1sil6mw"},truncate:{ygn44y:"f1cmbuwj"},block:{mc9l5x:"ftgm304"},italic:{B80ckks:"f1j4dglz"},underline:{w71qe1:"f13mvf36"},strikethrough:{w71qe1:"fv5q2k7"},strikethroughUnderline:{w71qe1:"f1drk4o6"},base100:{Be2twd7:"f13mqy1h",Bg96gwp:"fcpl73t"},base200:{Be2twd7:"fy9rknc",Bg96gwp:"fwrc4pm"},base400:{Be2twd7:"fod5ikn",Bg96gwp:"faaz57k"},base500:{Be2twd7:"f1pp30po",Bg96gwp:"f106mvju"},base600:{Be2twd7:"f1x0m3f5",Bg96gwp:"fb86gi6"},hero700:{Be2twd7:"fojgt09",Bg96gwp:"fcen8rp"},hero800:{Be2twd7:"fccw675",Bg96gwp:"f1ebx5kk"},hero900:{Be2twd7:"f15afnhw",Bg96gwp:"fr3w3wp"},hero1000:{Be2twd7:"fpyltcb",Bg96gwp:"f1ivgwrt"},monospace:{Bahqtrf:"f1fedwem"},numeric:{Bahqtrf:"f1uq0ln5"},weightMedium:{Bhrd7zp:"fdj6btp"},weightSemibold:{Bhrd7zp:"fl43uef"},weightBold:{Bhrd7zp:"flh3ekv"},alignCenter:{fsow6f:"f17mccla"},alignEnd:{fsow6f:"f12ymhq5"},alignJustify:{fsow6f:"f1j59e10"}},{d:[".fk6fouc{font-family:var(--fontFamilyBase);}",".fkhj508{font-size:var(--fontSizeBase300);}",".f1i3iumi{line-height:var(--lineHeightBase300);}",".figsok6{font-weight:var(--fontWeightRegular);}",".fpgzoln{text-align:start;}",".f1w7gpdv{display:inline;}",".f6juhto{white-space:normal;}",".f1mtd64y{overflow-x:visible;}",".f1y7q3j9{overflow-y:visible;}",".f2jf649{text-overflow:clip;}",".fz5stix{white-space:nowrap;}",".f1p9o1ba{overflow-x:hidden;}",".f1sil6mw{overflow-y:hidden;}",".f1cmbuwj{text-overflow:ellipsis;}",".ftgm304{display:block;}",".f1j4dglz{font-style:italic;}",".f13mvf36{text-decoration-line:underline;}",".fv5q2k7{text-decoration-line:line-through;}",".f1drk4o6{text-decoration-line:line-through underline;}",".f13mqy1h{font-size:var(--fontSizeBase100);}",".fcpl73t{line-height:var(--lineHeightBase100);}",".fy9rknc{font-size:var(--fontSizeBase200);}",".fwrc4pm{line-height:var(--lineHeightBase200);}",".fod5ikn{font-size:var(--fontSizeBase400);}",".faaz57k{line-height:var(--lineHeightBase400);}",".f1pp30po{font-size:var(--fontSizeBase500);}",".f106mvju{line-height:var(--lineHeightBase500);}",".f1x0m3f5{font-size:var(--fontSizeBase600);}",".fb86gi6{line-height:var(--lineHeightBase600);}",".fojgt09{font-size:var(--fontSizeHero700);}",".fcen8rp{line-height:var(--lineHeightHero700);}",".fccw675{font-size:var(--fontSizeHero800);}",".f1ebx5kk{line-height:var(--lineHeightHero800);}",".f15afnhw{font-size:var(--fontSizeHero900);}",".fr3w3wp{line-height:var(--lineHeightHero900);}",".fpyltcb{font-size:var(--fontSizeHero1000);}",".f1ivgwrt{line-height:var(--lineHeightHero1000);}",".f1fedwem{font-family:var(--fontFamilyMonospace);}",".f1uq0ln5{font-family:var(--fontFamilyNumeric);}",".fdj6btp{font-weight:var(--fontWeightMedium);}",".fl43uef{font-weight:var(--fontWeightSemibold);}",".flh3ekv{font-weight:var(--fontWeightBold);}",".f17mccla{text-align:center;}",".f12ymhq5{text-align:end;}",".f1j59e10{text-align:justify;}"]}),Cp=ts.createContext(void 0),wp=()=>{},Mp=(Cp.Provider,ts.forwardRef(((e,t)=>{const o=((e,t)=>{const{wrap:o,truncate:i,block:n,italic:r,underline:a,strikethrough:s,size:l,font:c,weight:d,align:u}=e;return{align:null!=u?u:"start",block:null!=n&&n,font:null!=c?c:"base",italic:null!=r&&r,size:null!=l?l:300,strikethrough:null!=s&&s,truncate:null!=i&&i,underline:null!=a&&a,weight:null!=d?d:"regular",wrap:null==o||o,components:{root:"span"},root:Ru(Uu("span",Yu({ref:t},e)),{elementType:"span"})}})(e,t);var i,n;return(e=>{const t=vp();e.root.className=yp("fui-Text",t.root,!1===e.wrap&&t.nowrap,e.truncate&&t.truncate,e.block&&t.block,e.italic&&t.italic,e.underline&&t.underline,e.strikethrough&&t.strikethrough,e.underline&&e.strikethrough&&t.strikethroughUnderline,100===e.size&&t.base100,200===e.size&&t.base200,400===e.size&&t.base400,500===e.size&&t.base500,600===e.size&&t.base600,700===e.size&&t.hero700,800===e.size&&t.hero800,900===e.size&&t.hero900,1e3===e.size&&t.hero1000,"monospace"===e.font&&t.monospace,"numeric"===e.font&&t.numeric,"medium"===e.weight&&t.weightMedium,"semibold"===e.weight&&t.weightSemibold,"bold"===e.weight&&t.weightBold,"center"===e.align&&t.alignCenter,"end"===e.align&&t.alignEnd,"justify"===e.align&&t.alignJustify,e.root.className)})(o),("useTextStyles_unstable",null!==(n=null===(i=ts.useContext(Cp))||void 0===i?void 0:i.useTextStyles_unstable)&&void 0!==n?n:wp)(o),(e=>bp(e.root,{}))(o)})));Mp.displayName="Text";const Dp=ts.createContext(void 0);Dp.Provider;var Pp=Object.defineProperty,xp=Object.defineProperties,kp=Object.getOwnPropertyDescriptors,Lp=Object.getOwnPropertySymbols,Fp=Object.prototype.hasOwnProperty,Gp=Object.prototype.propertyIsEnumerable,Bp=(e,t,o)=>t in e?Pp(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o,Wp=(e,t)=>{for(var o in t||(t={}))Fp.call(t,o)&&Bp(e,o,t[o]);if(Lp)for(var o of Lp(t))Gp.call(t,o)&&Bp(e,o,t[o]);return e};const Up=Sp({focusIndicator:{Bttzg6e:"fhgqx19",B3uz8dt:"f1olyrje",B6ihwck:"f1p93eir",g9k6zt:"f1nev41a"},root:{B486eqv:"f2hkw1w",De3pzq:"f3rmtva",B7ck84d:"f1ewtqcl",sj55zd:"fyind8e",Bceei9c:"f1k6fduh",mc9l5x:"f1w7gpdv",Bahqtrf:"fk6fouc",Be2twd7:"fkhj508",Bhrd7zp:"figsok6",B6of3ja:"f1hu3pq6",t21cq0:["f11qmguv","f1tyq0we"],jrapky:"f19f4twv",Frg6f3:["f1tyq0we","f11qmguv"],z8tnut:"f1g0x7ka",z189sj:["fhxju0i","f1cnd47f"],Byoj8tv:"f1qch9an",uwmqm3:["f1cnd47f","fhxju0i"],B68tc82:"fqv5qza",Bmxbyg5:"f1vmzxwi",fsow6f:["f1o700av","fes3tcz"],w71qe1:"f1iuv45f",Bkioxbp:"f1cmlufx",ygn44y:"f9n3di6",famaaq:"f1ids18y",Bde5pd6:"f1tx3yz7",Bi91k9c:"f1deo86v",i089h6:"f1eh06m1",lj723h:"f1iescvh"},button:{icvyot:"f1ern45e",vrafjx:["f1n71otn","f1deefiw"],oivjwe:"f1h8hb77",wvpqe5:["f1deefiw","f1n71otn"]},href:{Be2twd7:"fjoy568"},subtle:{sj55zd:"fkfq4zb",Bde5pd6:"f1tx3yz7",Bi91k9c:"fnwyq0v",i089h6:"f1eh06m1",lj723h:"flvvhsy"},inline:{w71qe1:"f13mvf36"},disabled:{w71qe1:"f1iuv45f",sj55zd:"f1s2aq7o",Bceei9c:"fdrzuqr",Bde5pd6:"fbnuktb",Bi91k9c:"fvgxktp",i089h6:"fljg2da",lj723h:"f19wldhg"},inverted:{sj55zd:"f1qz2gb0",Bi91k9c:"f1mlt8il",lj723h:"f1hsd4st"}},{d:[".fhgqx19[data-fui-focus-visible]{text-decoration-color:var(--colorStrokeFocus2);}",".f1olyrje[data-fui-focus-visible]{text-decoration-line:underline;}",".f1p93eir[data-fui-focus-visible]{text-decoration-style:double;}",".f1nev41a[data-fui-focus-visible]{outline-style:none;}",".f3rmtva{background-color:transparent;}",".f1ewtqcl{box-sizing:border-box;}",".fyind8e{color:var(--colorBrandForegroundLink);}",".f1k6fduh{cursor:pointer;}",".f1w7gpdv{display:inline;}",".fk6fouc{font-family:var(--fontFamilyBase);}",".fkhj508{font-size:var(--fontSizeBase300);}",".figsok6{font-weight:var(--fontWeightRegular);}",".f1hu3pq6{margin-top:0;}",".f11qmguv{margin-right:0;}",".f1tyq0we{margin-left:0;}",".f19f4twv{margin-bottom:0;}",".f1g0x7ka{padding-top:0;}",".fhxju0i{padding-right:0;}",".f1cnd47f{padding-left:0;}",".f1qch9an{padding-bottom:0;}",".fqv5qza{overflow-x:inherit;}",".f1vmzxwi{overflow-y:inherit;}",".f1o700av{text-align:left;}",".fes3tcz{text-align:right;}",".f1iuv45f{text-decoration-line:none;}",".f1cmlufx{text-decoration-thickness:var(--strokeWidthThin);}",".f9n3di6{text-overflow:inherit;}",".f1ids18y{-webkit-user-select:text;-moz-user-select:text;-ms-user-select:text;user-select:text;}",".f1ern45e{border-top-style:none;}",".f1n71otn{border-right-style:none;}",".f1deefiw{border-left-style:none;}",".f1h8hb77{border-bottom-style:none;}",".fjoy568{font-size:inherit;}",".fkfq4zb{color:var(--colorNeutralForeground2);}",".f13mvf36{text-decoration-line:underline;}",".f1s2aq7o{color:var(--colorNeutralForegroundDisabled);}",".fdrzuqr{cursor:not-allowed;}",".f1qz2gb0{color:var(--colorBrandForegroundInverted);}"],i:[".f2hkw1w:focus-visible{outline-style:none;}"],h:[".f1tx3yz7:hover{text-decoration-line:underline;}",".f1deo86v:hover{color:var(--colorBrandForegroundLinkHover);}",".fnwyq0v:hover{color:var(--colorNeutralForeground2Hover);}",".fbnuktb:hover{text-decoration-line:none;}",".fvgxktp:hover{color:var(--colorNeutralForegroundDisabled);}",".f1mlt8il:hover{color:var(--colorBrandForegroundInvertedHover);}"],a:[".f1eh06m1:active{text-decoration-line:underline;}",".f1iescvh:active{color:var(--colorBrandForegroundLinkPressed);}",".flvvhsy:active{color:var(--colorNeutralForeground2Pressed);}",".fljg2da:active{text-decoration-line:none;}",".f19wldhg:active{color:var(--colorNeutralForegroundDisabled);}",".f1hsd4st:active{color:var(--colorBrandForegroundInvertedPressed);}"]}),Vp=ts.forwardRef(((e,t)=>{const o=((e,t)=>{const o=ts.useContext(Dp),{appearance:i="default",disabled:n=!1,disabledFocusable:r=!1,inline:a=!1}=e,s=e.as||(e.href?"a":"button"),l=(c=Wp({role:"span"===s?"button":void 0,type:"button"===s?"button":void 0},e),xp(c,kp({as:s})));var c;const d={appearance:i,disabled:n,disabledFocusable:r,inline:a,components:{root:s},root:Ru(Uu(s,Wp({ref:t},l)),{elementType:s}),backgroundAppearance:o};return(e=>{const{disabled:t,disabledFocusable:o}=e,{onClick:i,onKeyDown:n,role:r,tabIndex:a}=e.root;"a"===e.root.as&&(e.root.href=t?void 0:e.root.href,(t||o)&&(e.root.role=r||"link")),"a"!==e.root.as&&"span"!==e.root.as||(e.root.tabIndex=null!=a?a:t&&!o?void 0:0),e.root.onClick=e=>{t||o?e.preventDefault():null==i||i(e)},e.root.onKeyDown=e=>{!t&&!o||e.key!==ou&&" "!==e.key?null==n||n(e):(e.preventDefault(),e.stopPropagation())},e.disabled=t||o,e.root["aria-disabled"]=t||o||void 0,"button"===e.root.as&&(e.root.disabled=t&&!o)})(d),d})(e,t);return(e=>{const t=Up(),{appearance:o,disabled:i,inline:n,root:r,backgroundAppearance:a}=e;e.root.className=yp("fui-Link",t.root,t.focusIndicator,"a"===r.as&&r.href&&t.href,"button"===r.as&&t.button,"subtle"===o&&t.subtle,"inverted"===a&&t.inverted,n&&t.inline,i&&t.disabled,e.root.className)})(o),(e=>bp(e.root,{}))(o)}));Vp.displayName="Link";Wc({container:{...tu.container},header:{display:"flex",marginInlineStart:"22px",paddingBlockStart:"12px",paddingBlockEnd:"0px",width:"240px"},headerText:{width:"220px",fontWeight:"600",wordBreak:"break-word",marginInlineStart:"22px",lineHeight:"20px"},action:{...Zd.paddingBlock("8px"),marginInlineStart:"62px",width:"fit-content"},actionLink:{fontSize:"12px",lineHeight:"16px"}}),Array.isArray;var $p=Ea?Ea.prototype:void 0;$p&&$p.toString;Wc({icon:{lineHeight:"20px",width:"20px",height:"19px",paddingBlockStart:"4px"}});(e=>{if(ka.MAX/8<1)throw new Error("Invalid index 1, index should not be larger than "+ka.MAX/8);ka.FEATURE_PROMOTION_1_IMPRESSION_WITH_ATTRACTION,ka.FEATURE_PROMOTION_1_ENGAGEMENT_WITH_ATTRACTION,ka.FEATURE_PROMOTION_1_SNOOZE_WITH_ATTRACTION,ka.FEATURE_PROMOTION_1_DISMISS_PERMANENTLY_WITH_ATTRACTION,ka.FEATURE_PROMOTION_1_IMPRESSION_WITHOUT_ATTRACTION,ka.FEATURE_PROMOTION_1_ENGAGEMENT_WITHOUT_ATTRACTION,ka.FEATURE_PROMOTION_1_SNOOZE_WITHOUT_ATTRACTION,ka.FEATURE_PROMOTION_1_DISMISS_PERMANENTLY_WITHOUT_ATTRACTION})(),ss.rewardsPromotionTitle(),Wc({container:{...tu.container,paddingBlockEnd:"4px",marginInlineEnd:"40px",display:"flex"},iconContainer:{paddingInlineStart:"14px",paddingInlineEnd:"12px",paddingBlockStart:"10px",flexShrink:0,flexGrow:0},icon:{width:"36px",height:"30px"},contentContainer:{display:"flex",flexDirection:"column",marginInlineEnd:"40px"},header:{paddingBlockStart:"12px",paddingBlockEnd:"0px"},headerText:{fontWeight:"600",lineHeight:"20px",fontSize:"14px"},description:{fontSize:"12px",lineHeight:"16px",paddingBlockStart:"8px",paddingBlockEnd:"8px"},action:{paddingBlockEnd:"4px"},actionButton:{...tu.actionButton,fontSize:"12px",lineHeight:"16px"}}),Wc({container:{...tu.container,display:"flex"},iconContainer:{paddingBlockStart:"8px",paddingInlineStart:"24px",paddingInlineEnd:"20px",flexShrink:0,flexGrow:0},icon:{lineHeight:"20px",width:"20px",height:"20px"},contentContainer:{display:"flex",flexDirection:"column",marginInlineEnd:"44px"},header:{fontWeight:"600",wordBreak:"break-word",lineHeight:"20px",paddingBlockStart:"8px",paddingBlockEnd:"8px"},description:{fontSize:"12px",lineHeight:"16px"},action:{height:"20px",paddingBlockStart:"8px",paddingBlockEnd:"12px"},actionButton:{...tu.actionButton,fontSize:"12px",lineHeight:"20px"}}),Wc({container:{...tu.container,display:"flex"},iconContainer:{paddingBlockStart:"8px",paddingInlineStart:"20px",paddingInlineEnd:"16px",flexShrink:0,flexGrow:0},iconBackground:{backgroundColor:"var(--colorPaletteGreenBackground1)",display:"flex",alignItems:"center",justifyContent:"center",height:"28px",width:"28px",...Zd.borderRadius("200px")},icon:{lineHeight:"20px",width:"20px",height:"20px",color:"var(--colorPaletteGreenForeground1)"},contentContainer:{display:"flex",flexDirection:"column",marginInlineEnd:"44px"},header:{fontWeight:"600",wordBreak:"break-word",lineHeight:"20px",paddingBlockStart:"8px",paddingBlockEnd:"8px"},description:{fontSize:"12px",lineHeight:"16px"},action:{minHeight:"20px",paddingBlockStart:"8px",paddingBlockEnd:"12px"},actionButton:{...tu.actionButton,fontSize:"12px",lineHeight:"20px"}}),Wc({container:{...tu.container,display:"flex"},iconContainer:{paddingBlockStart:"8px",paddingInlineStart:"16px",paddingInlineEnd:"12px",flexShrink:0,flexGrow:0},icon:{lineHeight:"36px",width:"36px",height:"36px"},contentContainer:{height:"108px",display:"flex",flexDirection:"column",justifyContent:"space-between",marginInlineEnd:"44px"},header:{fontWeight:"600",wordBreak:"break-word",lineHeight:"20px",paddingBlockStart:"8px"},description:{fontSize:"12px",lineHeight:"16px",display:"-webkit-box",WebkitBoxOrient:"vertical",WebkitLineClamp:3,color:"var(--colorNeutralForeground3)",...Zd.overflow("hidden")},action:{height:"16px",paddingBlockEnd:"12px"},actionButton:{...tu.actionButton,fontSize:"12px",lineHeight:"16px",textDecorationLine:"none"}}),La.CardExpired,xa.CARD_EXPIRED_SNOOZE,La.CardExpiring,xa.CARD_EXPIRING_SOON_SNOOZE,La.RoamCard,xa.ROAM_CARD_SNOOZE,La.SignupCryptoWallet,xa.SIGN_UP_CRYPTOWALLET_SNOOZE,La.CardTokenizationEligible,xa.CARD_TOKENIZATION_ELIGIBLE_SNOOZE,La.PasswordLeakage,xa.PASSWORD_LEAKAGE_SNOOZE,La.PersonalizedOffersAvailable,xa.PERSONALIZED_OFFERS_AVAILABLE_SNOOZE,La.UpcomingHotelReservations,xa.UPCOMING_HOTEL_RESERVATION_SNOOZE,La.DonationSummary,xa.DONATION_SUMMARY_SNOOZE,La.FeaturePromotion,xa.FEATURE_PROMOTION_SNOOZE,La.PackageTracking,xa.PACKAGE_TRACKING_SNOOZE,La.Rebates,xa.REBATES_SNOOZE,La.EtreeCampaign,xa.ETREE_CAMPAIGN_SNOOZE,La.Etree,xa.ETREE_NORMAL_SNOOZE,La.PWAPromotion,xa.PWA_PROMOTION_SNOOZE,La.DonationTrendNpo,xa.DONATION_TREND_NPO_SNOOZE,La.CardExpired,xa.CARD_EXPIRED_DISMISS_PERMANENTLY,La.CardExpiring,xa.CARD_EXPIRING_SOON_DISMISS_PERMANENTLY,La.RoamCard,xa.ROAM_CARD_DISMISS_PERMANENTLY,La.SignupCryptoWallet,xa.SIGN_UP_CRYPTOWALLET_DISMISS_PERMANENTLY,La.CardTokenizationEligible,xa.CARD_TOKENIZATION_ELIGIBLE_DISMISS_PERMANENTLY,La.PasswordLeakage,xa.PASSWORD_LEAKAGE_DISMISS_PERMANENTLY,La.PersonalizedOffersAvailable,xa.PERSONALIZED_OFFERS_AVAILABLE_DISMISS_PERMANENTLY,La.UpcomingHotelReservations,xa.UPCOMING_HOTEL_RESERVATION_DISMISS_PERMANENTLY,La.DonationSummary,xa.DONATION_SUMMARY_DISMISS_PERMANENTLY,La.FeaturePromotion,xa.FEATURE_PROMOTION_DISMISS_PERMANENTLY,La.PackageTracking,xa.PACKAGE_TRACKING_DISMISS_PERMANENTLY,La.Rebates,xa.REBATES_DISMISS_PERMANENTLY,La.EtreeCampaign,xa.ETREE_CAMPAIGN_DISMISS_PERMANENTLY,La.Etree,xa.ETREE_NORMAL_DISMISS_PERMANENTLY,La.PWAPromotion,xa.PWA_PROMOTION_DISMISS_PERMANENTLY,La.DonationTrendNpo,xa.DONATION_TREND_NPO_DISMISS_PERMANENTLY,Wc({dismiss:{zIndex:"1",position:"absolute",height:"20px",top:"16px",minWidth:"20px",width:"20px",right:"8px",...Zd.borderWidth("0"),...Zd.borderRadius("0"),...Zd.padding("0"),backgroundColor:"transparent","&:focus-visible":{...Zd.borderColor("var(--colorTransparentStroke)"),...Zd.borderRadius("var(--borderRadiusMedium)"),outlineColor:"var(--colorTransparentStroke)",outlineStyle:"solid",outlineWidth:"var(--strokeWidthThick)",boxShadow:"var(--shadow4),0 0 0 2px var(--colorStrokeFocus2)",zIndex:"1"}},topV2:{top:"11px"}}),Wc({container:{width:"100%",backgroundColor:ia.whiteBackground,[Kd]:{backgroundColor:ia.darkNotificationBackground},userSelect:"none",textAlign:"start",paddingBlockStart:"36px",paddingBlockEnd:"28px"},content:{textAlign:"center"},icon:{paddingInlineEnd:"8px",verticalAlign:"bottom"},text:{display:"inline-block"},link:{textAlign:"center",paddingBlockStart:"8px"}});const Hp={width:"126px",textAlign:"start",display:"grid",gridTemplateColumns:"88px 12px"};Wc({...tu,icon:{display:"inline-block",paddingInlineStart:"24px",paddingBlockStart:"14px"},errorIcon:{color:ia.criticalPrimary,[Kd]:{color:ia.RedAlertIconDarkMode}},warningIcon:{color:ia.YellowAlertPrimary,[Kd]:{color:ia.YellowAlertIconDarkMode}},header:{...tu.header,paddingBlockStart:"12px",paddingBlockEnd:"0px",width:"200px"},title:{fontWeight:"600",lineHeight:"20px",fontSize:"14px"},text:{...tu.text,fontSize:"12px",paddingInlineStart:"18px",paddingBlockStart:"8px",paddingBlockEnd:"0px",lineHeight:"16px",width:"200px"},expDate:{paddingBlockEnd:"12px",paddingInlineStart:"16px",width:"fit-content"},expDateSelector:{width:"126px",paddingInlineEnd:"12px",display:"inline-block"},label:{paddingBlockEnd:"4px",display:"inline-block"},menuButton:Hp,menuPopover:{minWidth:"116px",height:"64px",overflowY:"scroll"},errorMenuButton:{...Jd,...Hp},iconContainer:{display:"inline-block",verticalAlign:"top"},contentContainer:{display:"inline-block"},action:{...tu.action,width:"fit-content",paddingInlineStart:"18px",paddingBlockStart:"8px",paddingBlockEnd:"8px"},actionButton:{display:"inline-block",...tu.actionButton,fontSize:"12px"}}),Wc({container:{...tu.container,display:"flex",flexDirection:"column",...Zd.paddingBlock("8px"),...Zd.gap("8px")},header:{...tu.header,display:"flex",paddingInlineStart:"0px",...Zd.paddingBlock("0px")},text:{...tu.text,paddingInlineStart:"62px",paddingInlineEnd:"36px",...Zd.paddingBlock("0px !important")},linkLarge:{...tu.linkLarge,fontSize:"12px",...Zd.paddingBlock("0px"),paddingInlineStart:"62px !important"},icon:{width:"62px",color:"var(--colorPaletteBlueForeground2)"},title:{width:"195px",fontWeight:"var(--fontWeightSemibold)"}}),ts.Component,Wc({notificationDivider:{...Zd.borderTop("1px","solid","var(--colorNeutralStroke2)")}});const zp=function(){};var jp=function(e,t,o,i){var n,r=arguments.length,a=r<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,o):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,o,i);else for(var s=e.length-1;s>=0;s--)(n=e[s])&&(a=(r<3?n(a):r>3?n(t,o,a):n(t,o))||a);return r>3&&a&&Object.defineProperty(t,o,a),a};const Yp=I`
  .dismissNotificationStyle {
    padding: 10px 14px;
    display: grid;
    grid-template-rows: auto auto;
    grid-template-areas:
      'icon'
      'content';
  }

  .dismissText {
    font-size: 12px;
    line-height: 16px;
  }

  .actionLink {
    font-weight: 400;
    color: #115ea3;
    text-decoration: none;
  }

  @media (prefers-color-scheme: dark) {
    .actionLink {
      color: #479ef5;
    }
  }

  .actionLink:hover,
  :focus {
    text-decoration: underline;
  }

  .dismiss {
    color: inherit;
    background-color: transparent;
    border: none;
    justify-self: center;
    align-self: center;
    padding: 4px;
  }

  .dismiss:hover,
  :focus {
    background-color: ${ia.buttonBackgroundDoubleHover};
    border-radius: 4px;
  }

  @media (prefers-color-scheme: dark) {
    .dismiss:hover,
    :focus {
      background-color: ${ia.darkButtonBackgroundDoubleHover};
    }
  }
`,Zp=ne`
  <svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
    <path
      fill="currentColor"
      d="M5 11.5V8.05a5 5 0 016.36-4.87.5.5 0 10.27-.96A6 6 0 004 8v3.4l-.92 2.22A1 1 0 004 15h3.5a2.5 2.5 0 005 0H16a1 1 0 00.92-1.38L16 11.4V10a.5.5 0 00-1 0v1.5c0 .07.01.13.04.2L16 14H4l.96-2.3a.5.5 0 00.04-.2zM8.5 15h3a1.5 1.5 0 01-3 0zM14 2h3.5c.38 0 .6.4.45.71l-.04.08L14.96 7h2.54a.5.5 0 01.09 1H14a.5.5 0 01-.45-.71l.04-.08L16.54 3H14a.5.5 0 01-.09-1H14zM9.5 6H12c.4 0 .62.43.43.75l-.04.07L10.57 9H12a.5.5 0 01.1 1H9.5a.5.5 0 01-.43-.75l.05-.07L10.93 7H9.5a.5.5 0 01-.09-1h.1z"
    />
  </svg>
`,Kp=ne`
  <svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
    <path
      fill="currentColor"
      d="M4 7.57c.04-.82.24-1.59.58-2.28L2.15 2.85a.5.5 0 11.7-.7l15 15a.5.5 0 01-.7.7L14.3 15h-1.8v.17a2.5 2.5 0 01-5 0V15H4a1 1 0 01-.26-.03l-.13-.04a1 1 0 01-.6-1.05l.02-.13.05-.13L4 11.4V7.57zM13.3 14L5.34 6.05a4.6 4.6 0 00-.32 1.33L5 7.6V11.5l-.04.2L4 14h9.3zm-1.8 1h-3v.14a1.5 1.5 0 001.36 1.34l.14.01c.78 0 1.42-.6 1.5-1.36V15zm3.54-3.32l.87 2.1.86.85c.15-.17.23-.4.23-.64v-.13l-.02-.08a1 1 0 00-.06-.17L16 11.4V7.58l-.02-.22A5.92 5.92 0 0010 2a6.1 6.1 0 00-4.21 1.66l.7.71A5.1 5.1 0 0110 3a4.9 4.9 0 015 4.6l.01.21v3.69l.04.2z"
    />
  </svg>
`,qp=ne`
  <div class="dismissNotificationStyle" role="group">
    <div style="grid-area:icon; text-align: right;">
      <button class="dismiss" @click=${e=>e.onCloseClick()}>
        <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
          <path
            fill="currentColor"
            d="M2.59 2.72l.06-.07a.5.5 0 01.63-.06l.07.06L8 7.29l4.65-4.64a.5.5 0 01.7.7L8.71 8l4.64 4.65c.18.17.2.44.06.63l-.06.07a.5.5 0 01-.63.06l-.07-.06L8 8.71l-4.65 4.64a.5.5 0 01-.7-.7L7.29 8 2.65 3.35a.5.5 0 01-.06-.63l.06-.07-.06.07z"
          />
        </svg>
      </button>
    </div>
    <div style="grid-area:content; text-align: center; margin-bottom: 20px;">
      <div>
        ${e=>e.dismissedPermanently?Kp:Zp}
        <label style="vertical-align: top; padding-left: 4px;">
          ${e=>e.dismissedPermanently?as.msWalletNotificationDismissText():as.msWalletNotificationSnoozeText()}
        </label>
      </div>
      ${le((e=>!e.dismissedPermanently),ne`
          <div>
            <a href="#" class="actionLink" @click=${e=>e.onDismissClick()} data-test-id="close-permanently-link">
              ${as.msWalletNotificationDismissPermanently()}
            </a>
          </div>
        `)}
    </div>
  </div>
`;let Xp=class extends Ie{constructor(){super(),this._dismissedPermanently=!1,this.notificationType=this.getAttribute("notificationType"),this.dataKey=this.getAttribute("dataKey"),this.appName=this.getAttribute("appName")}get dismissedPermanently(){return y.track(this,"dismissedPermanently"),this._dismissedPermanently}set dismissedPermanently(e){this._dismissedPermanently=e,y.notify(this,"dismissedPermanently")}onDismissClick(){var e;this.dismissedPermanently=!0,e=this.dataKey,ds.onDismiss(e),Xa(gu[this.notificationType][uu.dismiss],this.appName)}onCloseClick(){this.shadowRoot.querySelector(".dismissNotificationStyle").remove()}};jp([he],Xp.prototype,"notificationType",void 0),jp([he],Xp.prototype,"dataKey",void 0),jp([he],Xp.prototype,"appName",void 0),Xp=jp([be({name:"dismiss-notification",template:qp,styles:Yp})],Xp);var Qp=function(e,t,o,i){var n,r=arguments.length,a=r<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,o):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,o,i);else for(var s=e.length-1;s>=0;s--)(n=e[s])&&(a=(r<3?n(a):r>3?n(t,o,a):n(t,o))||a);return r>3&&a&&Object.defineProperty(t,o,a),a};const Jp=I`
  .notificationRowStyle {
    padding: 10px 14px;
    display: grid;
    grid-template-columns: 36px 198px 16px;
    grid-template-rows: auto auto;
    grid-template-areas:
      'icon title dismiss'
      'icon content dismiss';
    column-gap: 10px;
  }

  .notificationRowStyle:hover,
  .notificationRowStyle:focus {
    background-color: ${ia.buttonBackgroundHover};
    outline: 0;
    text-decoration: none !important;
  }

  @media (prefers-color-scheme: dark) {
    .notificationRowStyle:hover,
    :focus {
      background-color: ${ia.darkModeButtonBackgroundHover};
      outline: 0;
    }
  }

  @media screen and (-ms-high-contrast: active) {
    .notificationRowStyle:hover,
    :focus {
      background-color: highlight;
    }
  }

  .noitificationIcon {
    grid-area: icon;
    display: inline-grid;
    padding-top: 2px;
    justify-content: center;
  }

  .errorIcon {
    fill: ${ia.criticalPrimary};
  }

  @media (prefers-color-scheme: dark) {
    .errorIcon {
      fill: ${ia.RedAlertIconDarkMode};
    }
  }

  .warningIcon {
    fill: ${ia.YellowAlertPrimary};
  }

  @media (prefers-color-scheme: dark) {
    .warningIcon {
      fill: ${ia.YellowAlertIconDarkMode};
    }
  }

  .blueIcon {
    fill: ${ia.compoundBrandForegroundLight};
  }

  @media (prefers-color-scheme: dark) {
    .blueIcon {
      fill: ${ia.compoundBrandForegroundDark};
    }
  }

  .notificationTitle {
    font-weight: 600;
    line-height: 20px;
    font-size: 14px;
    color: inherit;
  }

  .notificationText {
    font-size: 12px;
    line-height: 16px;
    padding-top: 8px;
    padding-bottom: 8px;
  }

  .actionLink {
    font-size: 12px;
    font-weight: 400;
    color: #115ea3;
    text-decoration: none;
  }

  @media (prefers-color-scheme: dark) {
    .actionLink {
      color: #479ef5;
    }
  }

  .actionLink:hover,
  .actionLink:focus {
    outline: 0;
    text-decoration: underline !important;
  }

  .dismiss {
    color: inherit;
    background-color: transparent;
    border: none;
    justify-self: center;
    align-self: center;
    padding: 4px;
  }

  .dismiss:focus {
    outline: 2px solid #80868b !important;
    border-radius: 4px;
  }

  .dismiss:hover {
    background-color: ${ia.buttonBackgroundDoubleHover};
    border-radius: 4px;
    outline: 0;
  }

  @media (prefers-color-scheme: dark) {
    .dismiss:focus {
      outline: 2px solid background-color: ${ia.darkButtonBackgroundDoubleHover} !important;
    }
    .dismiss:hover {
      background-color: ${ia.darkButtonBackgroundDoubleHover};
    }
  }
`,eh=ne`
  ${le((e=>!e.dismiss),ne`
      <div
        class="notificationRowStyle"
        tabindex="0"
        @click=${(e,t)=>e.onActionAreaClick(t.event)}
        @keydown=${(e,t)=>e.onActionAreaKeyDown(t.event)}
      >
        <div class="${e=>(e.iconClassName||" ")+" noitificationIcon"}">
          <slot name="icon"></slot>
        </div>
        <div style="grid-area: title">
          <label class="notificationTitle">${e=>e.headerTitle}</label>
        </div>
        <div style="grid-area: content">
          <slot name="content">
            <div class="notificationText" id="content">${e=>e.content}</div>
          </slot>
          <div>
            <a class="actionLink" @click=${(e,t)=>e.onActionLinkClick(t.event)} href="#">${e=>e.actionText}</a>
          </div>
        </div>
        <div style="grid-area:dismiss; text-align: right;">
          <button
            class="dismiss"
            title="${as.msWalletNotificationSnoozeLabel()}"
            aria-label="${as.msWalletNotificationSnoozeLabel()}"
            @click=${(e,t)=>e.onDismissClick(t.event)}
          >
            <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
              <path
                fill="currentColor"
                d="M2.59 2.72l.06-.07a.5.5 0 01.63-.06l.07.06L8 7.29l4.65-4.64a.5.5 0 01.7.7L8.71 8l4.64 4.65c.18.17.2.44.06.63l-.06.07a.5.5 0 01-.63.06l-.07-.06L8 8.71l-4.65 4.64a.5.5 0 01-.7-.7L7.29 8 2.65 3.35a.5.5 0 01-.06-.63l.06-.07-.06.07z"
              />
            </svg>
          </button>
        </div>
      </div>
    `)}
  ${le((e=>e.dismiss),ne`
      <dismiss-notification
        notificationType="${e=>e.notificationType}"
        dataKey="${e=>e.dataKey}"
      ></dismiss-notification>
    `)}
`;let th=class extends Ie{get dismiss(){return y.track(this,"dismiss"),this._dismiss}set dismiss(e){this._dismiss=e,y.notify(this,"dismiss")}constructor(){super(),this.onActionLinkClickExtended=zp,this.notificationType=this.getAttribute("notificationType"),this.dataKey=this.getAttribute("dataKey"),this.headerTitle=this.getAttribute("headerTitle"),this.content=this.getAttribute("content"),this.actionUrl=this.getAttribute("actionUrl"),this.actionText=this.getAttribute("actionText"),this.appName=this.getAttribute("appName"),function(e,...t){["onActionLinkClickExtended"].forEach((t=>{let o=e[t];delete e[t],Object.defineProperty(e,t,{get:()=>(y.track(e,t),o),set(i){const n=e[t];o=i,y.notify(e,t);const r=e[t+"Changed"];r&&r.call(e,i,n)}})}))}(this)}connectedCallback(){super.connectedCallback(),Xa(gu[this.notificationType][uu.impression],this.appName)}onActionAreaClick(e){Xa(xa.NOTIFICATION_CARD_ENGAGEMENT_FROM_CONTENT,this.appName),this.onActionLinkClick(e)}onActionAreaKeyDown(e){return e.key!==ou&&" "!==e.key||this.onActionAreaClick(e),!0}onActionLinkClick(e){var t;Xa(gu[this.notificationType][uu.engagement],this.appName),t=this.dataKey,ds.onApply(t),this.actionUrl&&ds.navigateToUrl(this.actionUrl),this.onActionLinkClickExtended(),e.stopPropagation()}onDismissClick(e){var t;Xa(gu[this.notificationType][uu.snooze],this.appName),t=this.dataKey,ds.onSnooze(t),this.dismiss=!0,e.stopPropagation()}};var oh,ih;Qp([he],th.prototype,"iconClassName",void 0),Qp([he],th.prototype,"notificationType",void 0),Qp([he],th.prototype,"dataKey",void 0),Qp([he],th.prototype,"headerTitle",void 0),Qp([he],th.prototype,"content",void 0),Qp([he],th.prototype,"actionUrl",void 0),Qp([he],th.prototype,"actionText",void 0),Qp([he],th.prototype,"appName",void 0),th=Qp([be({name:"base-notification",template:eh,styles:Jp})],th),(ih=oh||(oh={})).Sync="edge://settings/profiles/sync",ih.RoamingSync="edge://settings/profiles/sync#Roaming",ih.Payments="edge://settings/payments",ih.WalletHub="edge://wallet",ih.walletPaymentMethodsURL="edge://wallet/paymentMethods",ih.walletPasswordsURL="edge://wallet/passwords",ih.walletPersonalInfoURL="edge://wallet/personalInfo",ih.walletEtreeURL="edge://wallet/#popup=etree",ih.EnrollCardToMSALearnMore="https://aka.ms/EdgeSaveCardFAQ",ih.UseVirtualCardLearnMore="https://aka.ms/EdgeVirtualCardFAQ",ih.WalletSettings="edge://wallet/settings",ih.microsoftRewardsDashboardURL="https://go.microsoft.com/fwlink/?linkid=2222743",ih.microsoftRewardsGiveDashboardURL="https://go.microsoft.com/fwlink/?linkid=2147443",ih.microsoftRewardsRedeemURL="https://rewards.microsoft.com/redeem/?form=edgepredeem",ih.microsoftRebatesPayoutURL="https://www.bing.com/rebates/payouts?pay=1",ih.microsoftRebatesDealsURL="https://www.bing.com/rebates",ih.PCToMobileLandingPage="https://aka.ms/emw";const nh=ne`
  <svg slot="icon" fill="inherit" width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M10 2a8 8 0 110 16 8 8 0 010-16zm0 10.5a.75.75 0 100 ********* 0 000-1.5zM10 6a.5.5 0 00-.5.41v4.68a.5.5 0 001 0V6.41A.5.5 0 0010 6z"
    />
  </svg>
`,rh=ne`
  <svg slot="icon" fill="inherit" width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M2.62235 18.5C2.40213 18.5 2.1932 18.4576 1.99556 18.3729C1.80358 18.2882 1.63135 18.1753 1.47889 18.0341C1.33207 17.8873 1.21349 17.7179 1.12315 17.5259C1.03844 17.3283 0.996094 17.1194 0.996094 16.8992C0.996094 16.6394 1.05256 16.3909 1.1655 16.1538L7.54347 3.39783C7.68464 3.11549 7.8851 2.89527 8.14485 2.73716C8.4046 2.57905 8.68976 2.5 9.00033 2.5C9.3109 2.5 9.59606 2.57905 9.85581 2.73716C10.1156 2.89527 10.316 3.11549 10.4572 3.39783L16.8352 16.1538C16.9481 16.3909 17.0046 16.6394 17.0046 16.8992C17.0046 17.1194 16.9594 17.3283 16.869 17.5259C16.7843 17.7179 16.6658 17.8873 16.5133 18.0341C16.3665 18.1753 16.1943 18.2882 15.9966 18.3729C15.8046 18.4576 15.5985 18.5 15.3783 18.5H2.62235ZM9.54241 12.6726V8.33589C9.54241 8.18908 9.48877 8.06203 9.38148 7.95474C9.27419 7.84745 9.14714 7.79381 9.00033 7.79381C8.85351 7.79381 8.72646 7.84745 8.61917 7.95474C8.51189 8.06203 8.45824 8.18908 8.45824 8.33589V12.6726C8.45824 12.825 8.50906 12.9549 8.6107 13.0622C8.71799 13.1638 8.84787 13.2147 9.00033 13.2147C9.15279 13.2147 9.27984 13.1638 9.38148 13.0622C9.48877 12.9549 9.54241 12.825 9.54241 12.6726ZM9.81346 14.8409C9.81346 14.7336 9.79087 14.632 9.7457 14.536C9.70617 14.4344 9.64688 14.3468 9.56782 14.2734C9.49442 14.1944 9.40689 14.1351 9.30525 14.0956C9.20926 14.0504 9.10762 14.0278 9.00033 14.0278C8.88739 14.0278 8.78011 14.0504 8.67846 14.0956C8.58247 14.1407 8.49777 14.2 8.42436 14.2734C8.35095 14.3468 8.29166 14.4344 8.24649 14.536C8.20696 14.6376 8.1872 14.7421 8.1872 14.8494C8.1872 14.9623 8.20696 15.0696 8.24649 15.1713C8.29166 15.2672 8.35095 15.3519 8.42436 15.4254C8.50342 15.4931 8.59094 15.5496 8.68693 15.5948C8.78293 15.6343 8.88739 15.654 9.00033 15.654C9.2262 15.654 9.41819 15.575 9.57629 15.4169C9.7344 15.2588 9.81346 15.0668 9.81346 14.8409Z"
    />
  </svg>
`;var ah,sh,lh,ch;!function(e){e[e.PROGRAMMATIC=0]="PROGRAMMATIC",e[e.USER_ACTION=1]="USER_ACTION"}(ah||(ah={})),(ch=sh||(sh={})).Tokenization="Tokenization",ch.RoamCard="RoamCard",ch.ErrorHandling="ErrorHandling",ch.Unknown="Unknown",function(e){e.isWalletBNPLAffirmAdaptiveCheckoutEnabled="isWalletBNPLAffirmAdaptiveCheckoutEnabled",e.isTokenizationEnrollRewardsEnabled="isTokenizationEnrollRewardsEnabled",e.isWalletTokenizedAutofillEnabled="isWalletTokenizedAutofillEnabled",e.creditCardUploadEnabled="creditCardUploadEnabled",e.creditCardGlobalizationEnabled="creditCardGlobalizationEnabled",e.creditCardBetterStateTranslationEnabled="creditCardBetterStateTranslationEnabled",e.creditCardSyncCardEnabled="creditCardSyncCardEnabled",e.cardUploadErrorHandling="cardUploadErrorHandling",e.isCardRoamingLocalSaveCvvEnabled="isCardRoamingLocalSaveCvvV2Enabled",e.isCardRoamingAlwaysVerifyEnabled="isCardRoamingAlwaysVerifyEnabled",e.isCreditCardSilentUploadEnabled="creditCardSilentUpload",e.isCreditCardAutoSaveEnabled="creditCardAutoSaveEnabled",e.isWalletPartialCardEnabled="isWalletPartialCardEnabled",e.isWalletHubUXReskinEnabled="isWalletHubUXReskinEnabled",e.isWalletHubLayoutRefreshEnabled="isWalletHubLayoutRefreshEnabled",e.isWalletTokenizationCardMetadataEnabled="isWalletTokenizationCardMetadataEnabled",e.isBNPLFeedbackEnabled="isBNPLFeedbackEnabled",e.isCardRoamingSupportCrossRegionV2Enabled="isCardRoamingSupportCrossRegionV2Enabled",e.isCardRoamingSupportCrossRegionV3Enabled="isCardRoamingSupportCrossRegionV3Enabled"}(lh||(lh={}));const dh=la("walletTitle","Wallet"),uh=(e,t,o,i,n=au.Enroll)=>{if(r=lh.isWalletTokenizedAutofillEnabled,!dh.valueExists(r)||!dh.getBoolean(r))return;var r;if(!(e&&t&&n&&o))return;const a=dh.valueExists("tokenizationFunnelEnumMaxValue")?parseInt(dh.getValue("tokenizationFunnelEnumMaxValue"),10)+1:-1;if(null!==i&&i>=0&&i<=a&&!isNaN(a)){const r=n===au.Autofill?`${nu.TokenizationEnrollActions}.${n}.${e}.${o}`:`${nu.TokenizationEnrollActions}.${n}.${e}.${o}.${t}`;Sa.recordEnumerationValue(r,i,a)}};function ph(e,t){const o=e.data,i=!0===e.previewData?.isLocal||!0===o?.metadata?.isLocal,n=e.previewData?.guid??o?.guid,r=as.msWalletNotificationCardTokenizationEligibleHeader(),a=as.msWalletNotificationCardTokenizationEligibleIntroduction(),s=((e,t)=>es(Ja(`edge://wallet${((e,t)=>e?`/paymentMethods/cardDetails/edit#paymentInstrumentId=${t}&from=${su.ProfileNotification}&usage=${sh.Tokenization}`:`/paymentMethods#paymentInstrumentId=${t}&from=${su.ProfileNotification}&usage=${sh.Tokenization}`)(e,t)}`),"CardTokenizationEligible"))(i,n),l=function(e){const t=`${(e=>e.cardType??e.network)(e)} ****${Da(e?.cardNumber)}`;return as.msWalletNotificationCardTokenizationEligibleSetUpText([t])}(e.previewData);var c;return uh(su.ProfileNotification,(e=>e?(e=>!0===e.metadata?.isLocal)(e)?lu.Local:(e=>!0===e?.metadata?.isPartialCard)(e)?lu.Partial:(e=>!1===e?.metadata?.isLocal)(e)?lu.Server:lu.Unknown:lu.Unknown)(o),(c=o?.network,cu[c?.toUpperCase()]??cu.UNKNOWN),du.CARD_ELIGIBLE,au.Enroll),ne`<base-notification
    notificationType="${e.type}"
    dataKey="${e.dataKey}"
    headerTitle="${r}"
    content="${a}"
    actionUrl="${s}"
    actionText="${l}"
    appName="${t}"
  >
    <svg slot="icon" width="28" height="28" viewBox="0 0 28 28" fill="none">
      <rect width="28" height="28" rx="14" fill="#FDE300"></rect>
      <path
        fill-rule="evenodd"
        clip-path="evenodd"
        d="M6.53325 9.13302C6.23875 9.13302 6 9.37177 6 9.66627V13.3991C6 16.9549 8.10284 19.5675 12.2036 21.183C12.3292 21.2325 12.4689 21.2325 12.5945 21.183C13.2001 20.9444 13.7622 20.6841 14.2803 20.4022C13.5496 19.5225 13.1103 18.3922 13.1103 17.1593C13.1103 14.3545 15.3841 12.0807 18.1889 12.0807C18.3951 12.0807 18.5984 12.093 18.7981 12.1169V9.66627C18.7981 9.37177 18.5594 9.13302 18.2649 9.13302C16.3712 9.13302 14.5264 8.4622 12.719 7.10665C12.5294 6.96445 12.2687 6.96445 12.0791 7.10665C10.2717 8.4622 8.42695 9.13302 6.53325 9.13302Z"
        fill="black"
      ></path>
      <path
        fill-rule="evenodd"
        clip-path="evenodd"
        d="M22.2494 17.1589C22.2494 14.9154 20.4307 13.0967 18.1872 13.0967C15.9437 13.0967 14.125 14.9154 14.125 17.1589C14.125 19.4024 15.9437 21.2211 18.1872 21.2211C20.4307 21.2211 22.2494 19.4024 22.2494 17.1589ZM21.0296 17.1583C21.0296 16.7657 20.7113 16.4474 20.3186 16.4474L16.0531 16.4474C15.6605 16.4474 15.3422 16.7657 15.3422 17.1583C15.3422 17.5509 15.6605 17.8692 16.0531 17.8692L20.3186 17.8692C20.7113 17.8692 21.0296 17.5509 21.0296 17.1583Z"
        fill="black"
      ></path>
    </svg>
  </base-notification>`}var hh=function(e,t,o,i){var n,r=arguments.length,a=r<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,o):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,o,i);else for(var s=e.length-1;s>=0;s--)(n=e[s])&&(a=(r<3?n(a):r>3?n(t,o,a):n(t,o))||a);return r>3&&a&&Object.defineProperty(t,o,a),a};const fh=I`
  .image {
    max-width: 36px;
    max-height: 36px;
    width: auto;
    height: auto;
    object-fit: contain;
  }

  .cashbackShape {
    height: 20px;
    display: inline-block;
    padding: 0px;
    position: relative;
    background: ${"#3267FA"};
    border-radius: 4px;
    margin-inline-end: 10px;
    margin-block-start: 2px;
    color: ${ia.whiteBackground};
  }

  .notificationText {
    font-size: 12px;
    padding-bottom: 8px;
  }

  .cashbackShape::before {
    top: 10%;
    right: -6px;
    width: 16px;
    height: 16px;
    content: '';
    position: absolute;
    transform: rotate(-45deg);
    background: inherit;
    border-radius: 4px;
  }

  .cashbackPadding {
    padding-inline-end: 5px;
    padding-inline-start: 5px;
    padding-block-start: 1px;
    position: relative;
  }

  .expireText {
    display: inline-block;
    color: ${ia.redForeground};
  }

  @media (prefers-color-scheme: dark) {
    .expireText {
      color: ${ia.personalizedOfferTextColorDark};
    }
  }
`,gh=ne`
  <base-notification
    notificationType="${e=>e.notificationType}"
    dataKey="${e=>e.dataKey}"
    content="${e=>e.content}"
    actionUrl="${e=>e.actionUrl}"
    actionText="${e=>e.actionText}"
    appName="${e=>e.appName}"
  >
    <img slot="icon" class="image" src="${e=>`edge://image?${e.imageUrl}`}" />
    <div slot="content" class="notificationText">
      ${e=>ne`${as.msWalletNotificationPOMessage([`<div class="cashbackShape">\n            <div class="cashbackPadding">\n              <span>$</span>\n              <span>${e.rebateValue}</span>\n            </div>\n          </div>`,`<div class="expireText">${e.expireStr}</div>`])} `}
    </div>
  </base-notification>
`;let Eh=class extends Ie{padNumber(e){return e<10?`0${e}`:`${e}`}connectedCallback(){super.connectedCallback(),this.notificationType=this.getAttribute("notificationType");const e=Date.now()/1e3,t=this.expiration-e;if(t>0){const e=Math.floor(t/3600),o=Math.floor((t-3600*e)/60);this.expireStr=`${this.padNumber(e)}h:${this.padNumber(o)}m`}else this.expireStr="00h:00m"}};hh([v],Eh.prototype,"dismiss",void 0),hh([he],Eh.prototype,"notificationType",void 0),hh([he],Eh.prototype,"dataKey",void 0),hh([he],Eh.prototype,"actionUrl",void 0),hh([he],Eh.prototype,"actionText",void 0),hh([he],Eh.prototype,"expiration",void 0),hh([he],Eh.prototype,"rebateValue",void 0),hh([he],Eh.prototype,"imageUrl",void 0),hh([he],Eh.prototype,"appName",void 0),hh([v],Eh.prototype,"expireStr",void 0),Eh=hh([be({name:"personalized-offers-notification",template:gh,styles:fh})],Eh);const Th=I`
  .icon-background {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: ${ia.donationIconBackgroundLight};
    padding-block-start: 10px;
    padding-block-end: 6px;
    padding-inline-start: 5px;
    padding-inline-end: 6px;
  }

  .icon-container {
    padding-inline-start: 3px;
    padding-block-start: 3px;
  }

  @media (prefers-color-scheme: dark) {
    .icon-background {
      background-color: ${ia.donationIconBackgroundDark};
    }
  }
`,Nh=ne`
  <span class="icon-background">
    <svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" class="icon-container">
      <path
        d="M10 6.5V2H5.5C4.67 2 4 2.67 4 3.5V9c.7.03 1.4.25 2 .66a3.85 3.85 0 014.88 5.91L8.45 18h6.05c.83 0 1.5-.67 1.5-1.5V8h-4.5A1.5 1.5 0 0110 6.5zm1 0V2.25L15.75 7H11.5a.5.5 0 01-.5-.5zm-4.86 4.33a2.85 2.85 0 114.03 4.04l-3.82 3.81a.5.5 0 01-.7 0l-3.82-3.81a2.85 2.85 0 114.03-4.04l.14.14.14-.14zm4.03 4.04l-.36-.36z"
      />
    </svg>
  </span>
`;let mh=class extends Ie{};var _h,Oh,Ah;mh=function(e,t,o,i){var n,r=arguments.length,a=r<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,o):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,o,i);else for(var s=e.length-1;s>=0;s--)(n=e[s])&&(a=(r<3?n(a):r>3?n(t,o,a):n(t,o))||a);return r>3&&a&&Object.defineProperty(t,o,a),a}([be({name:"donation-icon",template:Nh,styles:Th})],mh),(Ah=_h||(_h={})).AddCreditCardPref="autofill.credit_card_enabled",Ah.TokenizationNumRewardsUsed="autofill.tokenization_number_rewards_used",Ah.MSPayCreditCardEnabled="autofill.mspay_credit_card_enabled",Ah.AutofillOfferToSyncCardEnabled="autofill.offer_to_sync_card_enabled",Ah.ProfileEnabled="autofill.profile_enabled",Ah.AutofillInfoEnabled="autofill.autostuff_enabled",Ah.SaveCreditCardCvvLocally="autofill.save_credit_card_cvv_locally",Ah.RoamingCardSilentlyEnabled="autofill.roaming_card_silently_enabled",Ah.MultiFactorAuthToFill="edge_wallet.multi_factor_auth_to_fill",Ah.MembershipsAutofillEnabled="autofill.memberships_enabled",Ah.PCToMobileSectionDismissed="edge_wallet.pc_to_mobile_section_dismissed",Ah.EnabledFeedbackPref="edge.feedback_allowed",Ah.ShowRewards="edge_rewards.show",Ah.EdgeWalletBNPLEnabled="edge_wallet_bnpl_enabled",Ah.ShowWalletIntroPref="edge_wallet.show_wallet_intro_section",Ah.WalletCheckoutEnabled="edge_wallet.wallet_checkout_enabled",Ah.ShoppingAssistantPref="edge_shopping_assistant_enabled",Ah.ShowOffersIntroPref="edge_wallet.show_wallet_offers_intro",Ah.ShowNewInTickets="edge_wallet.show_new_in_tickets",Ah.ShowTickets="edge_wallet.show_wallet_tickets",Ah.EdgeTippingAssistantEnabled="edge_tipping_assistant_enabled",Ah.ShoppingPackageTrackingUserConsent="shopping.package_tracking_user_consented",Ah.CredentialsEnableService="credentials_enable_service",Ah.CredentialsEnableAutofillPasswords="credentials_enable_autofill_passwords",Ah.CredentialsEnableBreachDetection="credentials_enable_breachdetection",Ah.PasswordBreachScanned="profile.password_breach_scanned",Ah.PasswordBreachLastScannedTime="profile.password_breach_last_scanned_time",Ah.PasswordBreachScanTriggeredCount="profile.password_breach_scan_triggered_count",Ah.PasswordBreachScanTriggeredPasswordCount="profile.password_breach_scan_triggered_password_count",Ah.PasswordRestrictLength="profile.edge_password_restrict_length",Ah.CredentialsEnablePasswordGeneration="credentials_enable_passwordgeneration",Ah.CredentialsEnablePasswordReveal="credentials_enable_passwordreveal",Ah.MasterPasswordAuthCriteria="profile.master_password_auth_criteria",Ah.MasterPasswordAuthFrequency="profile.master_password_auth_frequency",Ah.CredentailsEnableAutoSave="credentials_enable_autosave",Ah.CustomPrimaryPasswordHash="profile.custom_primary_password_hash",Ah.FeaturePromotionDict="edge_wallet.notification.featurePromotion.promotionDict",Ah.SettingsMigration="edge_wallet.use_as_default_t2",Ah.SettingsMigrationDismissed="edge_wallet.use_as_default_t2_dismissed",Ah.ShowWalletETree="edge_wallet.show_wallet_e_tree",Ah.WalletETreeEnabled="edge_wallet.wallet_etree_enabled",Ah.EtreeNotificationLastShow="edge_wallet.etree_notification_last_show",Ah.IsSignedInProfile="edge_wallet.isSignedInProfile",Ah.HomeFreDismissed="edge_wallet.home.fre.dismissed",Ah.HomeFrePaymentsStepCompleted="edge_wallet.home.fre.payments_step_completed",Ah.HomeFreMembershipsStepCompleted="edge_wallet.home.fre.memberships_step_completed",Ah.HomeFrePasswordsStepCompleted="edge_wallet.home.fre.passwords_step_completed",Ah.HomeFreRewardsStepCompleted="edge_wallet.home.fre.rewards_step_completed",Ah.HomeFreSavingsStepCompleted="edge_wallet.home.fre.savings_step_completed",Ah.HomeFreSigninStepCompleted="edge_wallet.home.fre.signin_step_completed",Ah.HomeFreAutosaveStepCompleted="edge_wallet.home.fre.autosave_step_completed",Ah.CryptoWalletUserEnabled="edge_wallet.crypto_wallet_user_enabled",Ah.CryptoUserFREState="edge_wallet.crypto_user_fre_state",Ah.CryptoWalletSuspended="edge_wallet.a0b1b27987ba331be1a8f2cd0d74c615c3d35ec3f298ad69506bbe21d5cb71d7",Ah.PWAPromotionDialogShownTimes="edge_wallet.pwa_promotion.dialog_shown_times",Ah.PWAPromotionDialogNextTime="edge_wallet.pwa_promotion.dialog_next_time",Ah.PWAPromotionRewardsLastReportTime="edge_wallet.pwa_promotion.rewards_last_report_time",Ah.PWAPromotionNotificationCompleted="edge_wallet.notification.pwa_promotion_completed",function(e){e.history="import_dialog_history",e.favorites="import_dialog_bookmarks",e.passwords="import_dialog_saved_passwords",e.search="import_dialog_search_engine",e.autofillFormData="import_dialog_autofill_form_data",e.payments="import_dialog_payment_info",e.cookies="import_dialog_cookies",e.homePage="import_dialog_homepage",e.settings="import_dialog_browser_settings",e.openTabs="import_dialog_open_tabs",e.extensions="import_dialog_browser_extensions"}(Oh||(Oh={}));var Ih,bh=function(e,t,o,i){var n,r=arguments.length,a=r<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,o):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,o,i);else for(var s=e.length-1;s>=0;s--)(n=e[s])&&(a=(r<3?n(a):r>3?n(t,o,a):n(t,o))||a);return r>3&&a&&Object.defineProperty(t,o,a),a};!function(e){e.TrackOrder="TrackOrder",e.ViewOrder="ViewOrder"}(Ih||(Ih={}));const Sh=I`
  .packageTrackingIcon {
    fill: ${ia.packageTrackingIconLight};
  }

  @media (prefers-color-scheme: dark) {
    .packageTrackingIcon {
      fill: ${ia.packageTrackingIconDark};
    }
  }
`,Rh=ne`<base-notification
  notificationType="${e=>e.notificationType}"
  dataKey="${e=>e.dataKey}"
  headerTitle="${e=>e.headerTitle}"
  content="${e=>e.content}"
  actionUrl="${e=>e.actionUrl}"
  actionText="${e=>e.actionText}"
  appName="${e=>e.appName}"
  :onActionLinkClickExtended=${e=>e.onActionLinkClickExtended}
>
  <svg
    slot="icon"
    class="packageTrackingIcon"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M1.75 4.5a.75.75 0 0 0-.75.75v6c0 1.24 1 2.25 2.25 2.25h5c1.24 0 2.25-1 2.25-2.25v-6a.75.75 0 0 0-.75-.75H9.5v-1a2.5 2.5 0 0 0-3.75-2.17A2.5 2.5 0 0 0 2 3.5v1h-.25ZM7 4.5v-1c0-.35-.07-.68-.2-.98A1 1 0 0 1 8 3.5v1H7Zm-1.5-1v1h-2v-1a1 1 0 0 1 2 0ZM2 17.25v-3c.38.16.8.25 1.25.25h.25v2.75c0 .39.3.7.67.75a3 3 0 0 1 5.66 0h2.34A3 3 0 0 1 15 16V5.25a.75.75 0 0 0-.75-.75h-2.92a1.76 1.76 0 0 0-.83-.83V3.5c0-.17-.01-.34-.04-.5h3.79c1.24 0 2.25 1 2.25 2.25V6h1.55c.87 0 1.66.5 2.03 1.29l1.7 3.58c.15.3.22.63.22.97v5.41c0 1.24-1 2.25-2.25 2.25h-1.8a3 3 0 0 1-5.9 0h-2.1a3 3 0 0 1-5.91-.01A2.25 2.25 0 0 1 2 17.25Zm15.83.75h1.92c.41 0 .75-.34.75-.75V17h-1.25a.75.75 0 0 1 0-1.5h1.25v-3h-4v3.9c.61.36 1.09.92 1.33 1.6ZM16.5 7.5V11h3.68l-1.45-3.07a.75.75 0 0 0-.68-.43H16.5ZM7 20.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Zm9.5-1.5a1.5 1.5 0 1 0-3 0 1.5 1.5 0 0 0 3 0ZM3.5 3.5a1 1 0 0 1 .08-.4Z"
    ></path>
  </svg>
</base-notification>`;let yh=class extends Ie{constructor(){super(...arguments),this.onActionLinkClickExtended=()=>{var e;this.subType===Ih.ViewOrder?Xa(xa.PACKAGE_TRACKING_VIEW_ORDER_ENGAGEMENT,this.appName):this.subType===Ih.TrackOrder&&(Xa(xa.PACKAGE_TRACKING_TRACK_ORDER_ENGAGEMENT,this.appName),e=_h.ShoppingPackageTrackingUserConsent,!0,fs.setPref(e,true))}}};function vh(e){return is(e,dh)}bh([he],yh.prototype,"notificationType",void 0),bh([he],yh.prototype,"dataKey",void 0),bh([he],yh.prototype,"headerTitle",void 0),bh([he],yh.prototype,"content",void 0),bh([he],yh.prototype,"actionUrl",void 0),bh([he],yh.prototype,"actionText",void 0),bh([he],yh.prototype,"subType",void 0),bh([he],yh.prototype,"appName",void 0),yh=bh([be({name:"package-tracking",styles:Sh,template:Rh})],yh);const Ch={cancel:vh({id:"cancel",defaultMessage:"Cancel",description:"Text of the cancel button"}),dismiss:vh({id:"dismiss",defaultMessage:"Dismiss",description:"Text of the dismiss button"}),learnMore:vh({id:"learnMore",defaultMessage:"Learn more",description:"Text of the learn more button"}),close:vh({id:"close",defaultMessage:"Close",description:"Text of the Close button"}),textOfCreateButton:vh({id:"textOfCreateButton",description:"Text for create button",defaultMessage:"Create"}),textOfChangeButton:vh({id:"textOfChangeButton",description:"Text for change button",defaultMessage:"Change"}),tokenizeCardEnterCode:vh({id:"tokenizeCardEnterCode",defaultMessage:"Enter code",description:"Label for enter verification code field"}),tokenizeCardVerify:vh({id:"tokenizeCardVerify",defaultMessage:"Verify",description:"Label for the verify button"}),tokenizeCardChallengeMethodAPPToAPP:vh({id:"tokenizeCardChallengeMethodAPPToAPP",defaultMessage:"Mobile banking app",description:"Simple description for the verify via banking app option"}),tokenizeCardChallengeMethodCustomerService:vh({id:"tokenizeCardChallengeMethodCustomerService",defaultMessage:"Bank customer service",description:"Simple description for the verify via customer service option"}),tokenizeCardChallengeMethodEmail:vh({id:"tokenizeCardChallengeMethodEmail",defaultMessage:"Email",description:"Simple description for the verify via email option"}),tokenizeCardChallengeMethodOnlineBanking:vh({id:"tokenizeCardChallengeMethodOnlineBanking",defaultMessage:"Bank account",description:"Simple description for the verify via online banking option"}),tokenizeCardChallengeMethodOutboundCall:vh({id:"tokenizeCardChallengeMethodOutboundCall",defaultMessage:"Phone call",description:"Simple description for the verify via phone call option"}),tokenizeCardChallengeMethodSMS:vh({id:"tokenizeCardChallengeMethodSMS",defaultMessage:"Text message",description:"Simple description for the verify via SMS option"}),tokenizeCardSelectMethodDescription:vh({id:"tokenizeCardSelectMethodDescription",defaultMessage:"Choose one of the following verification methods:",description:"Description for choose a new method"}),tokenizeCardBack:vh({id:"tokenizeCardBack",defaultMessage:"Back",description:"Label for the Back button"}),tokenizeCardNext:vh({id:"tokenizeCardNext",defaultMessage:"Next",description:"Label for the Next button"}),tokenizeCardEnterCodeStepTitle:vh({id:"tokenizeCardEnterCodeStepTitle",defaultMessage:"Let's get you verified",description:"Title for the enter verification code step"}),tokenizeCardSelectMethodStepTitle:vh({id:"tokenizeCardSelectMethodStepTitle",defaultMessage:"Let's get you verified",description:"Title for the select method code step"}),tokenizeCardErrorStepTitle:vh({id:"tokenizeCardErrorStepTitle",defaultMessage:"Something went wrong",description:"Title for the error step"}),tokenizeCardLoadingTitle:vh({id:"tokenizeCardLoadingTitle",defaultMessage:"Contacting your bank…",description:"Title for the loading step"}),tokenizeCardEnterCvvStepDescription:vh({id:"tokenizeCardEnterCvvStepDescription",defaultMessage:"Your bank requires you to verify the CVV security code associated with your $1 ending in $2.",description:"Description shows in CVV verification dialog"}),tokenizeCardCvvInvalidError:vh({id:"tokenizeCardCvvInvalidError",defaultMessage:"Please verify your security code and try again.",description:"Error message shows when cvv is invalid"}),tokenizeCardGeneralError:vh({id:"tokenizeCardGeneralError",defaultMessage:"Your card issuer is having issues at the moment, please try again later.",description:"Error message for the generate card error"}),tokenizationEnroll:vh({id:"tokenizationEnroll",description:"Label for the confirmation button in verification dialog",defaultMessage:"Enroll"}),tokenizationEnrollConfirmDialogTitle:vh({id:"tokenizationEnrollConfirmDialogTitle",defaultMessage:"Pay safely with a virtual card",description:"Title for the enroll confirmation dialog"}),tokenizationEnrollConfirmDialogTitleForRewards:vh({id:"tokenizationEnrollConfirmDialogTitleForRewards",defaultMessage:"Set up a virtual card and earn 20 Microsoft Rewards points",description:"Title for the enroll confirmation dialog for rewards incentive"}),tokenizationEnrollConfirmModalDescription:vh({id:"tokenizationEnrollConfirmModalDescription",description:"Description for the enroll confirm dialog",defaultMessage:"A virtual card hides your card details from merchants when you shop online. If any of them experiences a data breach, your details remain protected. $1"}),tokenizationEnrollConfirmFooterDescription:vh({id:"tokenizationEnrollConfirmFooterDescription",description:"Description for the enroll confirm footer",defaultMessage:"Your card issuer may send a verification code or request the card's security code to verify it's you."}),tokenizationTerms:vh({id:"tokenizationTerms",defaultMessage:"By continuing, you agree to the $1.",description:"Terms description shows in tokenization dialog"}),tokenizeCardFetchCodeError:vh({id:"tokenizeCardFetchCodeError",defaultMessage:"Your card issuer had an issue sending the code. Request another code or use a different verification method.",description:"Error message shows when sending verification code failed"}),tokenizeCardFetchCodeErrorCustomerService:vh({id:"tokenizeCardFetchCodeErrorCustomerService",defaultMessage:"Your card issuer had an issue generating the code. Please try a different verification method or try again later.",description:"Error message shows when issuer cannot generate verification code"}),manageYourPaymentMethodsLinkText:vh({id:"manageYourPaymentMethodsLinkText",defaultMessage:"Manage your payment methods",description:"the link text for wallet page"}),walletDrawerLabelShowCardNumber:vh({id:"walletDrawerLabelShowCardNumber",defaultMessage:"Show card number",description:"The label for the button to show card number"}),walletDrawerLabelCopyCardNumber:vh({id:"walletDrawerLabelCopyCardNumber",defaultMessage:"Copy card number",description:"The label for the button to copy card number"}),walletDrawerLabelShowExpirationDate:vh({id:"walletDrawerLabelShowExpirationDate",defaultMessage:"Copy expiration date",description:"The label for the button to copy expiration date"}),walletDrawerLabelShowCvc:vh({id:"walletDrawerLabelShowCvc",defaultMessage:"Show CVC",description:"The label for the button to show CVC"}),walletDrawerLabelCopyCvc:vh({id:"walletDrawerLabelCopyCvc",defaultMessage:"Copy CVC",description:"The label for the button to copy CVC"}),walletDrawerLabelCopyNameAddress:vh({id:"walletDrawerLabelCopyNameAddress",defaultMessage:"Copy name and address",description:"The label for the button to copy name and address"}),walletFeedbackThankYouText:vh({id:"walletFeedbackThankYouText",defaultMessage:"Thank you for your feedback. It will help us improve Wallet.",description:"Than you text after users provide feedback"}),walletMicrofeedbackPrompt:vh({id:"walletMicrofeedbackPrompt",defaultMessage:"Satisfied with Wallet?",description:"Text to ask users to provide feedback"}),feedbackOptionAutofillDontWork:vh({id:"feedbackOptionAutofillDontWork",defaultMessage:"Autofill did not work",description:"The string used as the first dislike option for wallet BNPL feedback."}),feedbackOptionTookTooMuchTime:vh({id:"feedbackOptionTookTooMuchTime",defaultMessage:"Took too much time",description:"The string used as the second dislike option for wallet BNPL feedback."}),feedbackOptionOthers:vh({id:"feedbackOptionOthers",defaultMessage:"Other",description:"The string used as the other dislike option for wallet feedback."}),walletDrawerLinkErrorMessage:vh({id:"walletDrawerLinkErrorMessage",defaultMessage:"We are unable to link your Microsoft account to $1. Please try again later.",description:"Text of failed link account notification in Wallet EC BNPL"}),walletDrawerPayWithVirtualCard:vh({id:"walletDrawerPayWithVirtualCard",defaultMessage:"Pay with your $1 $2 card",description:"The description to pay with your virtual card",examples:[{name:"INSTALLMENT_NAME",value:"ZIP"},{name:"CARD_BRAND",value:"Visa"}]}),walletDrawerSystemErrorHeader:vh({id:"walletDrawerSystemErrorHeader",defaultMessage:"Uh oh",description:"Text of system failure view title"}),walletDrawerAutoFillMessage:vh({id:"walletDrawerAutoFillMessage",defaultMessage:"We've automatically filled your card information from $1.",description:"The message when autofill succeed"}),walletDrawerAutoFillMessageWithBillingAddress:vh({id:"walletDrawerAutoFillMessageWithBillingAddress",defaultMessage:"Your payment details from $1 have been filled in. If not, click the card and address details below to copy.",description:"The message when autofill succeed which has billing address"}),walletDrawerAutoFillMessageWithoutBillingAddress:vh({id:"walletDrawerAutoFillMessageWithoutBillingAddress",defaultMessage:"Your payment details from $1 have been filled in. If not, click the card details below to copy. To complete this purchase, please fill in your home billing address.",description:"The message when autofill succeed which does not have billing address"}),walletDrawerAutoFillErrorMessage:vh({id:"walletDrawerAutoFillErrorMessage",defaultMessage:"We are unable to autofill your card information from $1. Please manually enter your $1 card information during checkout.",description:"The error message when autofill fails"}),commonSubtitle:vh({id:"commonSubtitle",defaultMessage:"See if you qualify to pay over time",description:"The common sub title of the total amount dialog"}),msPayAddCardAgreement:vh({id:"msPayAddCardAgreement",defaultMessage:"By continuing, you agree to the $1, $2, and $3 regarding how your data is handled.",description:"Privacy statement at the save card action when the card is to be saved by uploading it to Microsoft Payments and also saved locally, $1, $2 and $3 are links"}),paymentServiceTermsLinkText:vh({id:"paymentServiceTermsLinkText",defaultMessage:"Payment Service Terms",description:"Text for Edge terms of servcie link for the Autofill save card prompt when the card is to be saved by uploading it to Microsoft Payments and also saved locally. The prompt can be either a bubble or an infobar."}),previousButtonAriaLabel:vh({id:"previousButtonAriaLabel",defaultMessage:"Go to previous page",description:"label for the previous button in the pagination mechanism. Clicking this button will take the user back to the previous page in the pagination."}),nextButtonAriaLabel:vh({id:"nextButtonAriaLabel",defaultMessage:"Go to next page",description:"label for the next button in the pagination mechanism. Clicking this button will take the user to the next page in the pagination."}),paginationButtonAriaLabel:vh({id:"paginationButtonAriaLabel",defaultMessage:"Go to page $1",description:"label for the pagination button. Clicking this button will take the user to the specific page in the pagination."}),aboutTermsOfUseLinkText:vh({id:"aboutTermsOfUseLinkText",description:"The label of the link used to direct a user to the terms of use webpage.",defaultMessage:"Microsoft Edge Terms of Use"}),aboutPrivacyStatementLinkText:vh({id:"aboutPrivacyStatementLinkText",description:"The label of the link used to direct a user to the privacy statement webpage",defaultMessage:"Microsoft Privacy Statement"}),walletCommonAccountCount:vh({id:"walletCommonAccountCount",description:"For each group indicating the count of accounts, $1 is the count of accounts",defaultMessage:"$1 accounts"}),walletSeeDetailsAriaLabel:vh({id:"walletSeeDetailsAriaLabel",description:"Aria-label for buttons that go to detail page",defaultMessage:"see details"}),walletPWAPromoTitle:vh({id:"walletPWAPromoTitle",description:"Title of promo Wallet PWA (progressive web app) installation",defaultMessage:"Add a shortcut to Wallet"}),walletPWAPromoContent:vh({id:"walletPWAPromoContent",description:"Content of promo Wallet PWA (progressive web app) installation, $1 is the number of reward points",defaultMessage:"Install now and earn $1 Microsoft Rewards points.",examples:[{name:"NUMBER_OF_POINTS",value:"30"}]}),walletPWACommonPromoContent:vh({id:"walletPWACommonPromoContent",description:"Content of promo Wallet PWA (progressive web app) installation for all type od users",defaultMessage:"Install now for faster access to Wallet."}),walletPWAAddShortcut:vh({id:"walletPWAAddShortcut",description:"Wallet PWA (progressive web app) installation button text",defaultMessage:"Add shortcut"}),walletNotInterestedText:vh({id:"walletNotInterestedText",description:"Text for not interested action/feedback, especially for promotion",defaultMessage:"Not interested"}),feedbackLike:vh({id:"feedbackLike",description:"Like button for mini feedback",defaultMessage:"Like"}),feedbackDislike:vh({id:"feedbackDislike",description:"Dislike button for mini feedback",defaultMessage:"Dislike"})};function wh(e,t){switch(e.type){case La.CardExpired:case La.CardExpiring:return function(e,t){const{guid:o,network:i,cardNumber:n}={guid:e.previewData?.guid,network:e.previewData?.network,cardNumber:e.previewData?.cardNumber},r=e.type==La.CardExpired,a=r?as.msWalletNotificationCardExpired():as.msWalletNotificationCardExpiring(),s=function(e,t){const o=new URL(`${oh.walletPaymentMethodsURL}/cardDetails/edit`);return e?.length>0&&(o.hash=`#paymentInstrumentId=${e}`),es(Ja(o.toString()),t?"CardExpired":"CardExpiring")}(o,r),l=as.msWalletNotificationUpdateButton1(),c=function(e,t){return Ma(e)?ca.valueExists("msWalletNotificationUpdateExpTextV2")?ca.getStringF("msWalletNotificationUpdateExpTextV2",e,`****${Da(t)}`):as.msWalletNotificationUpdateExpText([`****${Da(t)}`]):ns({id:"msWalletNotificationUpdateExpTextV2MissingIssuer",description:"Update your card ending in $1 to continue using it.",defaultMessage:"Update your card ending in $1 to continue using it.",values:[`****${Da(t)}`]})}(i,n);return ne`<base-notification
    iconClassName="${r?"errorIcon":"warningIcon"}"
    notificationType="${e.type}"
    dataKey="${e.dataKey}"
    headerTitle="${a}"
    content="${c}"
    actionUrl="${s}"
    actionText="${l}"
    appName="${t}"
  >
    ${r?nh:rh}
  </base-notification>`}(e,t);case La.SignupCryptoWallet:return function(e,t){const o=as.msWalletNotificationCryptowalletSignupTitle(),i=as.msWalletNotificationCryptowalletSignupDescription(),n=es(Ja("edge://wallet/crypto/onboard?from=notification"),"CryptoSignup"),r=as.msWalletNotificationCryptowalletJoinButton();return ne`<base-notification
    notificationType="${e.type}"
    dataKey="${e.dataKey}"
    headerTitle="${o}"
    content="${i}"
    actionUrl="${n}"
    actionText="${r}"
    appName="${t}"
  >
    <svg
      slot="icon"
      fill="${ia.normalControl}"
      width="20"
      height="20"
      viewBox="0 0 20 20"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14.16 1.03c.**********.31.63l-.5 1.5a.5.5 0 01-.94-.32l.5-1.5a.5.5 0 01.63-.31zm3.7 1.82a.5.5 0 10-.71-.7l-2 2a.5.5 0 00.7.7l2-2zm-10.25.3a2 2 0 013.26-.52l6.58 6.98a2 2 0 01-.64 3.2l-3.94 1.74a3.5 3.5 0 01-6.34 2.8l-1.2.52a1.5 1.5 0 01-1.67-.3l-1.22-1.22a1.5 1.5 0 01-.3-1.7l5.47-11.5zm-.16 13.78a2.5 2.5 0 004.5-1.97l-4.5 1.97zM17 6a.5.5 0 000 1h1.5a.5.5 0 100-1H17z"
      />
    </svg>
  </base-notification>`}(e,t);case La.CardTokenizationEligible:return ph(e,t);case La.PasswordLeakage:return function(e,t){const{LeakedPasswordCount:o}=e.previewData,i=as.msWalletNotificationPasswordCompromiseTitle(),n=1==o?as.msWalletNotificationPasswordCompromiseBodySingular():as.msWalletNotificationPasswordCompromiseBody([o]),r=new URL(aa.getString("msWalletPageURL")+"/passwords/");r.hash="#filter=Leaked";const a=es(Ja(r.toString()),"PasswordLeakage"),s=ns({id:"msWalletNotificationFixLeakage",description:"The text of view details link in password leakage notification",defaultMessage:"View details"});return ne`<base-notification
    iconClassName="errorIcon"
    notificationType="${e.type}"
    dataKey="${e.dataKey}"
    headerTitle="${i}"
    content="${n}"
    actionUrl="${a}"
    actionText="${s}"
    appName="${t}"
  >
    <svg slot="icon" fill="inherit" width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M10 2a8 8 0 110 16 8 8 0 010-16zm0 10.5a.75.75 0 100 ********* 0 000-1.5zM10 6a.5.5 0 00-.5.41v4.68a.5.5 0 001 0V6.41A.5.5 0 0010 6z"
      />
    </svg>
  </base-notification>`}(e,t);case La.UpcomingHotelReservations:return function(e,t){const{HotelName:o,CheckinDate:i,CheckoutDate:n}=e.previewData,r=new Date(i).toLocaleDateString(void 0,{month:"short",day:"numeric"}),a=new Date(n).toLocaleDateString(void 0,{month:"short",day:"numeric"}),s=as.msWalletNotificationHotelReservationTitle(),l=as.msWalletNotificationHotelReservationBody([o,r,a]),c=new URL(aa.getString("msWalletPageURL")+"/tickets/");c.hash="#tab=travelnotification";const d=es(Ja(c.toString()),"HotelReservation"),u=as.msWalletNotificationHotelReservationViewDetails();return ne`<base-notification
    iconClassName="blueIcon"
    notificationType="${e.type}"
    dataKey="${e.dataKey}"
    headerTitle="${s}"
    content="${l}"
    actionUrl="${d}"
    actionText="${u}"
    appName="${t}"
  >
    <svg slot="icon" width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M18 10a8 8 0 10-16 0 8 8 0 0016 0zM9.5 8.91a.5.5 0 011 0V13.6a.5.5 0 01-1 0V8.9zm-.25-2.16a.75.75 0 111.5 0 .75.75 0 01-1.5 0z"
      />
    </svg>
  </base-notification>`}(e,t);case La.PersonalizedOffersAvailable:return function(e,t){const o=e.previewData?.contentUrl,i=e.previewData?.contentImageUrl,n=e.previewData?.contentRebateValue,r=e.previewData?.expireEpochTime,a=as.personalizedOffersViewOfferText();return ne` <personalized-offers-notification
    notificationType="${e.type}"
    dataKey="${e.dataKey}"
    actionUrl="${o}"
    actionText="${a}"
    expiration="${r}"
    rebateValue="${n}"
    imageUrl="${i}"
    appName="${t}"
  >
  </personalized-offers-notification>`}(e,t);case La.RoamCard:return function(e,t){const o=e.previewData?.guid,i=e.previewData?.cardNumber,n=ss.msWalletNotificationRoamCardHeaderV3(),r=es(Ja(`edge://wallet/paymentMethods/cardDetails/edit#paymentInstrumentId=${o}&usage=${sh.RoamCard}`),"CardRoaming"),a=ss.msWalletNotificationRoamCardActionV2(),s=`•••• •••• •••• ${Da(i)}`;return ne`<base-notification
    notificationType="${e.type}"
    dataKey="${e.dataKey}"
    headerTitle="${n}"
    content="${s}"
    actionUrl="${r}"
    actionText="${a}"
    appName="${t}"
  >
    <svg slot="icon" width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M4.5 4C3.11929 4 2 5.11929 2 6.5V13.5C2 14.8807 3.11929 16 4.5 16H15.5C16.8807 16 18 14.8807 18 13.5V6.5C18 5.11929 16.8807 4 15.5 4H4.5ZM3 6.5C3 5.67157 3.67157 5 4.5 5H15.5C16.3284 5 17 5.67157 17 6.5V13.5C17 14.3284 16.3284 15 15.5 15H4.5C3.67157 15 3 14.3284 3 13.5V6.5ZM4.5 6C4.22386 6 4 6.22386 4 6.5C4 6.77614 4.22386 7 4.5 7H9.5C9.77614 7 10 6.77614 10 6.5C10 6.22386 9.77614 6 9.5 6H4.5ZM4.5 8C4.22386 8 4 8.22386 4 8.5C4 8.77614 4.22386 9 4.5 9H12.5C12.7761 9 13 8.77614 13 8.5C13 8.22386 12.7761 8 12.5 8H4.5ZM5 11C4.44772 11 4 11.4477 4 12V13C4 13.5523 4.44772 14 5 14H9C9.55228 14 10 13.5523 10 13V12C10 11.4477 9.55228 11 9 11H5Z"
        fill="#115EA3"
      />
    </svg>
  </base-notification>`}(e,t);case La.DonationSummary:return function(e,t){const o=as.msWalletNotificationDonationSummaryTitle(),i=new URL(ca.getString("msWalletPageURL")+"/donation/");i.hash="#tab=overviewfromnotification";const n=es(Ja(i.toString()),"DonationSummary"),r=as.msWalletNotificationDonationSummaryLinkText();return ne`<base-notification
    iconClassName="errorIcon"
    notificationType="${e.type}"
    dataKey="${e.dataKey}"
    headerTitle="${o}"
    actionUrl="${n}"
    actionText="${r}"
    appName="${t}"
  >
    <donation-icon slot="icon" />
  </base-notification>`}(e,t);case La.PackageTracking:return function(e,t){const{IsShoppingPackageTrackingUserConsent:o}=e.previewData,i=o?Ih.ViewOrder:Ih.TrackOrder,n=i===Ih.ViewOrder?as.msWalletNotificationOrderStatusTitle():as.msWalletNotificationTrackOrderTitle(),r=i===Ih.ViewOrder?"":as.msWalletNotificationTrackOrderSubTitle(),a=es(Ja(new URL(ca.getString("msWalletPageURL")+"/orders/").toString()),"PackageTracking"),s=i===Ih.ViewOrder?as.msWalletNotificationViewDetailLink():as.msWalletNotificationTrackOrderLink();return ne`<package-tracking
    notificationType="${e.type}"
    dataKey="${e.dataKey}"
    headerTitle="${n}"
    content="${r}"
    actionUrl="${a}"
    actionText="${s}"
    appName="${t}"
    subType="${i}"
  >
    <svg slot="icon" width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M10 6.5V2H5.5C4.67 2 4 2.67 4 3.5V9c.7.03 1.4.25 2 .66a3.85 3.85 0 014.88 5.91L8.45 18h6.05c.83 0 1.5-.67 1.5-1.5V8h-4.5A1.5 1.5 0 0110 6.5zm1 0V2.25L15.75 7H11.5a.5.5 0 01-.5-.5zm-4.86 4.33a2.85 2.85 0 114.03 4.04l-3.82 3.81a.5.5 0 01-.7 0l-3.82-3.81a2.85 2.85 0 114.03-4.04l.14.14.14-.14zm4.03 4.04l-.36-.36z"
      />
    </svg>
  </package-tracking>`}(e,t);case La.Rebates:return function(e,t){const o=as.msWalletNotificationRebatesCashoutTitle(),{balance:i=0}=e.previewData,n=5*Math.floor(i/5),r=i<5?as.msWalletNotificationRebatesCashoutDescription():as.msWalletNotificationRebatesCashoutAmountDescription([`$${n}`]),a=oh.microsoftRebatesPayoutURL,s=as.msWalletNotificationRebatesCashoutAction();return ne`<base-notification
    notificationType="${e.type}"
    dataKey="${e.dataKey}"
    headerTitle="${o}"
    content="${r}"
    actionUrl="${a}"
    actionText="${s}"
    appName="${t}"
  >
    <img
      slot="icon"
      height="30"
      width="36"
      src="data:image/png;base64,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"
    />
  </base-notification>`}(e,t);case La.PWAPromotion:return function(e,t){const o=Ch.walletPWAPromoTitle(),i=Ch.walletPWACommonPromoContent(),n=es(Ja(new URL(aa.getString("msWalletPageURL")).toString()),"PWAPromotion"),r=Ch.walletPWAAddShortcut();return ne`<base-notification
    notificationType="${e.type}"
    dataKey="${e.dataKey}"
    headerTitle="${o}"
    content="${i}"
    actionUrl="${n}"
    actionText="${r}"
    appName="${t}"
  >
    <svg slot="icon" width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="28" height="28" rx="14" fill="#F1FAF1" />
      <path
        d="M16.8712 7.01503C17.1391 7.08197 17.302 7.35342 17.2351 7.62132L16.9852 8.6214C16.9183 8.8893 16.6468 9.05222 16.3789 8.98528C16.111 8.91834 15.9481 8.64689 16.015 8.37899L16.2649 7.37891C16.3319 7.11101 16.6033 6.94809 16.8712 7.01503ZM19.8536 8.14659C20.0488 8.34186 20.0488 8.65844 19.8536 8.8537L18.8536 9.8537C18.6583 10.049 18.3417 10.049 18.1464 9.8537C17.9512 9.65844 17.9512 9.34186 18.1464 9.14659L19.1464 8.14659C19.3417 7.95133 19.6583 7.95133 19.8536 8.14659ZM12.2836 19.0325C12.7966 19.886 13.8774 20.2425 14.8102 19.8271C15.7399 19.4132 16.1982 18.3773 15.9133 17.4277L12.2836 19.0325ZM11.3642 19.439L10.3655 19.8806C9.83756 20.1141 9.21978 19.9996 8.81088 19.5919L8.41013 19.1923C8.00252 18.7858 7.88618 18.1711 8.11785 17.644L11.9891 8.83553C12.3847 7.9354 13.5625 7.71525 14.258 8.40875L19.5897 13.725C20.2853 14.4186 20.0653 15.5919 19.1678 15.9888L16.8328 17.0212C17.3372 18.4773 16.6508 20.1023 15.217 20.7407C13.7784 21.3812 12.1051 20.798 11.3642 19.439ZM20.5 11.0001H19.5C19.2239 11.0001 19 11.224 19 11.5001C19 11.7763 19.2239 12.0001 19.5 12.0001H20.5C20.7761 12.0001 21 11.7763 21 11.5001C21 11.224 20.7761 11.0001 20.5 11.0001Z"
        fill="#0E700E"
      />
    </svg>
  </base-notification>`}(e,t);case La.DonationTrendNpo:return function(e,t){const o=e.data?.causeName,i=as.msWalletNotificationDonationTrendNpoTitle(),n=as.msWalletNotificationDonationTrendNpoDescription([o]),r=new URL(aa.getString("msWalletPageURL")+"/donation/");r.hash=o?`#tab=explore&npo=${o}`:"#tab=explore";const a=es(Ja(r.toString()),"PWAPromotion"),s=as.msWalletNotificationDonationTrendNpoLink();return ne`<base-notification
    notificationType="${e.type}"
    dataKey="${e.dataKey}"
    headerTitle="${i}"
    content="${n}"
    actionUrl="${a}"
    actionText="${s}"
    appName="${t}"
  >
    <img
      slot="icon"
      height="36"
      width="36"
      src="data:image/png;base64,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"
    />
  </base-notification>`}(e,t);default:return Xa(xa.UNSUPPORTED_TYPE,t),null}}const Mh=I``,Dh=ne`
  ${le((e=>e.notification),ne`<div>${e=>wh(e.notification,e.appName)}</div> `)}
`;let Ph=class extends Ie{get notification(){return y.track(this,"notification"),this._notification}set notification(e){this._notification=e,y.notify(this,"notification")}get appName(){return y.track(this,"appName"),this._appName}set appName(e){this._appName=e,y.notify(this,"appName")}};Ph=function(e,t,o,i){var n,r=arguments.length,a=r<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,o):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,o,i);else for(var s=e.length-1;s>=0;s--)(n=e[s])&&(a=(r<3?n(a):r>3?n(t,o,a):n(t,o))||a);return r>3&&a&&Object.defineProperty(t,o,a),a}([be({name:"notification-container",template:Dh,styles:Mh})],Ph);const xh=new Set(["children","localName","ref","style","className"]),kh=Object.freeze(Object.create(null));function Lh(e,t){if(!t.name){const o=Te.forType(e);if(!o)throw new Error("React wrappers must wrap a FASTElement or be configured with a name.");t.name=o.name}return t.name}function Fh(e){return e.events||(e.events={})}function Gh(e,t,o){return!xh.has(o)||(console.warn(`${Lh(e,t)} contains property ${o} which is a React reserved property. It will be used by React and not set on the element.`),!1)}(function(e,t){let o=[];return{wrap:function(t,i={}){t instanceof ht&&(o.push(t),t=t.type);class n extends e.Component{constructor(){super(...arguments),this._element=null}_updateElement(e){const t=this._element;if(null===t)return;const o=this.props,n=e||kh,r=Fh(i);for(const e in this._elementProps){const i=o[e],a=r[e];if(void 0===a)t[e]=i;else{const o=n[e];if(i===o)continue;void 0!==o&&t.removeEventListener(a,o),void 0!==i&&t.addEventListener(a,i)}}}componentDidMount(){this._updateElement()}componentDidUpdate(e){this._updateElement(e)}render(){const o=this.props.__forwardedRef;void 0!==this._ref&&this._userRef===o||(this._ref=e=>{null===this._element&&(this._element=e),null!==o&&function(e,t){"function"==typeof e?e(t):e.current=t}(o,e),this._userRef=o});const n={ref:this._ref},r=this._elementProps={},a=function(e,t){if(!t.keys)if(t.properties)t.keys=new Set(t.properties.concat(Object.keys(Fh(t))));else{const o=new Set(Object.keys(Fh(t))),i=y.getAccessors(e.prototype);if(i.length>0)for(const n of i)Gh(e,t,n.name)&&o.add(n.name);else for(const i in e.prototype)!(i in HTMLElement.prototype)&&Gh(e,t,i)&&o.add(i);t.keys=o}return t.keys}(t,i),s=this.props;for(const e in s){const t=s[e];a.has(e)?r[e]=t:n["className"===e?"class":e]=t}return e.createElement(Lh(t,i),n)}}return e.forwardRef(((t,o)=>e.createElement(n,Object.assign(Object.assign({},t),{__forwardedRef:o}),null==t?void 0:t.children)))},registry:{register(e,...t){o.forEach((o=>o.register(e,...t))),o=[]}}}})(ts).wrap(Ph,{properties:["notification","appName"]});const Bh=e=>{e.preventDefault()};Ft.getOrCreate(undefined).withPrefix("fluent").register(Tr(),Ur(),$r(),ea());const Wh=I`
  .root {
    width: 298px;
    background-color: ${ia.whiteBackground};
    color: var(--neutral-foreground-rest);
  }

  .root fluent-divider {
    margin: 0px;
  }

  @media (prefers-color-scheme: dark) {
    .root {
      background-color: ${ia.darkMiniWalletBackground};
    }

    .root fluent-divider {
      border-color: ${ia.miniWalletDividerDarkMode};
    }
  }
`,Uh=ne`
  <fluent-design-system-provider use-defaults>
    <div class="root">
      <fluent-divider></fluent-divider>
      ${le((e=>e.notification),ne`<notification-container
          :notification=${e=>e.notification}
          :appName=${e=>e.appName}
        ></notification-container>`)}
    </div>
  </fluent-design-system-provider>
`;let Vh=class extends Ie{constructor(){super(),window.addEventListener("error",(function(e){e.error&&Ha(e,"notification-fast")})),this.notification=function(){let e=null;return ca.valueExists("notification_data")&&(e=(e=>{const t=e.type;switch(t){case La.CardExpiring:case La.CardExpired:case La.RoamCard:case La.CardTokenizationEligible:{const o=JSON.parse(e.data);return Qa(!1===o.isLocal,Ma(o.network)),{type:t,dataKey:e.key,data:null,previewData:o}}case La.PackageTracking:case La.Rebates:case La.PasswordLeakage:case La.UpcomingHotelReservations:case La.FeaturePromotion:case La.DonationSummary:return{type:t,dataKey:e.key,previewData:JSON.parse(e.data)};case La.PersonalizedOffersAvailable:return{type:t,dataKey:e.key,data:null,previewData:JSON.parse(e.data)};case La.SignupCryptoWallet:case La.EtreeCampaign:case La.PWAPromotion:return{type:t,dataKey:e.key};case La.Etree:case La.DonationTrendNpo:return{type:t,dataKey:e.key,data:e?.data?JSON.parse(e?.data):null}}})(ca.getValue("notification_data"))),e}(),this.appName=Ga.MiniWallet}connectedCallback(){super.connectedCallback();const e=window.matchMedia("(prefers-color-scheme: dark)"),t=e=>{const t=e.matches?qo.DarkMode:qo.LightMode,o=this.shadowRoot.querySelector("fluent-design-system-provider");vi.setValueFor(o,t)};t(e),e.addEventListener("change",t),document.addEventListener("contextmenu",Bh)}disconnectedCallback(){window.removeEventListener("error",(e=>Ha(e,"hub"))),document.removeEventListener("contextmenu",Bh)}};Vh=function(e,t,o,i){var n,r=arguments.length,a=r<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,o):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,o,i);else for(var s=e.length-1;s>=0;s--)(n=e[s])&&(a=(r<3?n(a):r>3?n(t,o,a):n(t,o))||a);return r>3&&a&&Object.defineProperty(t,o,a),a}([be({name:"notification-fast",template:Uh,styles:Wh})],Vh)})()})();