import __vite__cjsImport0_react_jsxDevRuntime from "/node_modules/.vite/deps/react_jsx-dev-runtime.js?v=10e87200"; const jsxDEV = __vite__cjsImport0_react_jsxDevRuntime["jsxDEV"];
import __vite__cjsImport1_react from "/node_modules/.vite/deps/react.js?v=10e87200"; const useState = __vite__cjsImport1_react["useState"];
import Header from "/src/components/Header.tsx";
import AuthModal from "/src/components/AuthModal.tsx";
import ClientForm from "/src/components/ClientForm.tsx";
import ClientList from "/src/components/ClientList.tsx";
import Statistics from "/src/components/Statistics.tsx";
import { Card, CardHeader, CardTitle, CardContent } from "/src/components/ui/card.tsx";
import { Shield, Users, Award } from "/node_modules/.vite/deps/lucide-react.js?v=10e87200";
const Index = () => {
  const [user, setUser] = useState(null);
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [clients, setClients] = useState([]);
  const handleLogin = () => {
    setIsAuthModalOpen(true);
  };
  const handleLogout = () => {
    setUser(null);
    setClients([]);
  };
  const handleAuthenticate = (userData) => {
    setUser(userData);
    console.log("Utilisateur connecté:", userData);
  };
  const handleAddClient = (clientData) => {
    const newClient = {
      ...clientData,
      id: Date.now().toString(),
      dateCreation: (/* @__PURE__ */ new Date()).toISOString()
    };
    setClients((prev) => [...prev, newClient]);
    console.log("Nouveau client ajouté:", newClient);
  };
  if (!user) {
    return /* @__PURE__ */ jsxDEV("div", { className: "min-h-screen bg-gradient-to-br from-tunisietelecom-lightgray to-white", children: [
      /* @__PURE__ */ jsxDEV(
        Header,
        {
          isAuthenticated: false,
          onLogin: handleLogin,
          onLogout: handleLogout,
          onProfile: () => {
          }
        },
        void 0,
        false,
        {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
          lineNumber: 61,
          columnNumber: 9
        },
        this
      ),
      /* @__PURE__ */ jsxDEV("main", { className: "container mx-auto px-4 py-8", children: [
        /* @__PURE__ */ jsxDEV("div", { className: "text-center mb-16 animate-fade-in", children: [
          /* @__PURE__ */ jsxDEV("div", { className: "w-32 h-24 flex items-center justify-center mx-auto mb-6", children: /* @__PURE__ */ jsxDEV(
            "img",
            {
              src: "/assets/tunisie-telecom-logo.svg",
              alt: "Tunisie Telecom Logo",
              className: "h-24 w-auto object-contain"
            },
            void 0,
            false,
            {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
              lineNumber: 72,
              columnNumber: 15
            },
            this
          ) }, void 0, false, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
            lineNumber: 71,
            columnNumber: 13
          }, this),
          /* @__PURE__ */ jsxDEV("h1", { className: "text-4xl md:text-6xl font-bold text-tunisietelecom-darkgray mb-6", children: "Tunisie Telecom" }, void 0, false, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
            lineNumber: 78,
            columnNumber: 13
          }, this),
          /* @__PURE__ */ jsxDEV("h2", { className: "text-2xl md:text-3xl font-light text-gray-600 mb-8", children: "Système de Gestion des Scores Clients" }, void 0, false, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
            lineNumber: 81,
            columnNumber: 13
          }, this),
          /* @__PURE__ */ jsxDEV("p", { className: "text-lg text-gray-600 max-w-2xl mx-auto mb-8", children: "Plateforme moderne pour suivre et gérer les scores de performance de vos clients. Créez votre compte pour commencer à enregistrer et analyser les données de vos clients." }, void 0, false, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
            lineNumber: 84,
            columnNumber: 13
          }, this),
          /* @__PURE__ */ jsxDEV(
            "button",
            {
              onClick: handleLogin,
              className: "bg-tunisietelecom-blue hover:bg-tunisietelecom-darkblue text-white px-8 py-4 rounded-lg text-lg font-semibold shadow-lg transition-all duration-200 hover:shadow-xl transform hover:-translate-y-1",
              children: "Commencer maintenant"
            },
            void 0,
            false,
            {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
              lineNumber: 88,
              columnNumber: 13
            },
            this
          )
        ] }, void 0, true, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
          lineNumber: 70,
          columnNumber: 11
        }, this),
        /* @__PURE__ */ jsxDEV("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-8 mb-16", children: [
          /* @__PURE__ */ jsxDEV(Card, { className: "border-tunisietelecom-blue/20 shadow-lg hover:shadow-xl transition-shadow duration-200", children: [
            /* @__PURE__ */ jsxDEV(CardHeader, { className: "text-center", children: [
              /* @__PURE__ */ jsxDEV("div", { className: "w-16 h-16 bg-tunisietelecom-blue rounded-full flex items-center justify-center mx-auto mb-4", children: /* @__PURE__ */ jsxDEV(Users, { className: "text-white", size: 32 }, void 0, false, {
                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
                lineNumber: 101,
                columnNumber: 19
              }, this) }, void 0, false, {
                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
                lineNumber: 100,
                columnNumber: 17
              }, this),
              /* @__PURE__ */ jsxDEV(CardTitle, { className: "text-tunisietelecom-darkgray", children: "Gestion des Clients" }, void 0, false, {
                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
                lineNumber: 103,
                columnNumber: 17
              }, this)
            ] }, void 0, true, {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
              lineNumber: 99,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDEV(CardContent, { className: "text-center", children: /* @__PURE__ */ jsxDEV("p", { className: "text-gray-600", children: "Ajoutez et gérez facilement les informations de vos clients avec leurs scores de performance." }, void 0, false, {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
              lineNumber: 106,
              columnNumber: 17
            }, this) }, void 0, false, {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
              lineNumber: 105,
              columnNumber: 15
            }, this)
          ] }, void 0, true, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
            lineNumber: 98,
            columnNumber: 13
          }, this),
          /* @__PURE__ */ jsxDEV(Card, { className: "border-tunisietelecom-blue/20 shadow-lg hover:shadow-xl transition-shadow duration-200", children: [
            /* @__PURE__ */ jsxDEV(CardHeader, { className: "text-center", children: [
              /* @__PURE__ */ jsxDEV("div", { className: "w-16 h-16 bg-tunisietelecom-darkblue rounded-full flex items-center justify-center mx-auto mb-4", children: /* @__PURE__ */ jsxDEV(Award, { className: "text-white", size: 32 }, void 0, false, {
                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
                lineNumber: 115,
                columnNumber: 19
              }, this) }, void 0, false, {
                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
                lineNumber: 114,
                columnNumber: 17
              }, this),
              /* @__PURE__ */ jsxDEV(CardTitle, { className: "text-tunisietelecom-darkgray", children: "Suivi des Scores" }, void 0, false, {
                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
                lineNumber: 117,
                columnNumber: 17
              }, this)
            ] }, void 0, true, {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
              lineNumber: 113,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDEV(CardContent, { className: "text-center", children: /* @__PURE__ */ jsxDEV("p", { className: "text-gray-600", children: "Surveillez les performances de vos clients avec un système de notation de 0 à 100." }, void 0, false, {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
              lineNumber: 120,
              columnNumber: 17
            }, this) }, void 0, false, {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
              lineNumber: 119,
              columnNumber: 15
            }, this)
          ] }, void 0, true, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
            lineNumber: 112,
            columnNumber: 13
          }, this),
          /* @__PURE__ */ jsxDEV(Card, { className: "border-tunisietelecom-blue/20 shadow-lg hover:shadow-xl transition-shadow duration-200", children: [
            /* @__PURE__ */ jsxDEV(CardHeader, { className: "text-center", children: [
              /* @__PURE__ */ jsxDEV("div", { className: "w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4", children: /* @__PURE__ */ jsxDEV(Shield, { className: "text-white", size: 32 }, void 0, false, {
                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
                lineNumber: 129,
                columnNumber: 19
              }, this) }, void 0, false, {
                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
                lineNumber: 128,
                columnNumber: 17
              }, this),
              /* @__PURE__ */ jsxDEV(CardTitle, { className: "text-tunisietelecom-darkgray", children: "Sécurisé" }, void 0, false, {
                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
                lineNumber: 131,
                columnNumber: 17
              }, this)
            ] }, void 0, true, {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
              lineNumber: 127,
              columnNumber: 15
            }, this),
            /* @__PURE__ */ jsxDEV(CardContent, { className: "text-center", children: /* @__PURE__ */ jsxDEV("p", { className: "text-gray-600", children: "Plateforme sécurisée avec authentification pour protéger vos données clients." }, void 0, false, {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
              lineNumber: 134,
              columnNumber: 17
            }, this) }, void 0, false, {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
              lineNumber: 133,
              columnNumber: 15
            }, this)
          ] }, void 0, true, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
            lineNumber: 126,
            columnNumber: 13
          }, this)
        ] }, void 0, true, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
          lineNumber: 97,
          columnNumber: 11
        }, this),
        /* @__PURE__ */ jsxDEV("div", { className: "text-center bg-white rounded-xl shadow-lg p-8 border border-tunisietelecom-blue/20", children: [
          /* @__PURE__ */ jsxDEV("h3", { className: "text-2xl font-bold text-tunisietelecom-darkgray mb-4", children: "Prêt à commencer ?" }, void 0, false, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
            lineNumber: 143,
            columnNumber: 13
          }, this),
          /* @__PURE__ */ jsxDEV("p", { className: "text-gray-600 mb-6", children: "Créez votre compte dès maintenant et commencez à gérer vos clients efficacement." }, void 0, false, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
            lineNumber: 146,
            columnNumber: 13
          }, this),
          /* @__PURE__ */ jsxDEV(
            "button",
            {
              onClick: handleLogin,
              className: "bg-tunisietelecom-blue hover:bg-tunisietelecom-darkblue text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200",
              children: "Créer un compte gratuit"
            },
            void 0,
            false,
            {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
              lineNumber: 149,
              columnNumber: 13
            },
            this
          )
        ] }, void 0, true, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
          lineNumber: 142,
          columnNumber: 11
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
        lineNumber: 68,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDEV(
        AuthModal,
        {
          isOpen: isAuthModalOpen,
          onClose: () => setIsAuthModalOpen(false),
          onAuthenticate: handleAuthenticate
        },
        void 0,
        false,
        {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
          lineNumber: 158,
          columnNumber: 9
        },
        this
      )
    ] }, void 0, true, {
      fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
      lineNumber: 60,
      columnNumber: 7
    }, this);
  }
  return /* @__PURE__ */ jsxDEV("div", { className: "min-h-screen bg-gradient-to-br from-tunisietelecom-lightgray to-white", children: [
    /* @__PURE__ */ jsxDEV(
      Header,
      {
        isAuthenticated: true,
        onLogin: handleLogin,
        onLogout: handleLogout,
        onProfile: () => console.log("Profil clicked"),
        userName: user.name
      },
      void 0,
      false,
      {
        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
        lineNumber: 170,
        columnNumber: 7
      },
      this
    ),
    /* @__PURE__ */ jsxDEV("main", { className: "container mx-auto px-4 py-8", children: [
      /* @__PURE__ */ jsxDEV("div", { className: "mb-8 animate-fade-in", children: [
        /* @__PURE__ */ jsxDEV("h1", { className: "text-3xl font-bold text-tunisietelecom-darkgray mb-2", children: "Dashboard - Gestion des Clients" }, void 0, false, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
          lineNumber: 180,
          columnNumber: 11
        }, this),
        /* @__PURE__ */ jsxDEV("p", { className: "text-gray-600", children: [
          "Bienvenue ",
          user.name,
          ", gérez vos clients et suivez leurs scores de performance."
        ] }, void 0, true, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
          lineNumber: 183,
          columnNumber: 11
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
        lineNumber: 179,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDEV(Statistics, { clients }, void 0, false, {
        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
        lineNumber: 188,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDEV("div", { className: "grid grid-cols-1 xl:grid-cols-2 gap-8", children: [
        /* @__PURE__ */ jsxDEV("div", { className: "animate-slide-in", children: /* @__PURE__ */ jsxDEV(ClientForm, { onAddClient: handleAddClient }, void 0, false, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
          lineNumber: 192,
          columnNumber: 13
        }, this) }, void 0, false, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
          lineNumber: 191,
          columnNumber: 11
        }, this),
        /* @__PURE__ */ jsxDEV("div", { className: "animate-fade-in", children: /* @__PURE__ */ jsxDEV(ClientList, { clients }, void 0, false, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
          lineNumber: 196,
          columnNumber: 13
        }, this) }, void 0, false, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
          lineNumber: 195,
          columnNumber: 11
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
        lineNumber: 190,
        columnNumber: 9
      }, this)
    ] }, void 0, true, {
      fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
      lineNumber: 178,
      columnNumber: 7
    }, this)
  ] }, void 0, true, {
    fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Index.tsx",
    lineNumber: 169,
    columnNumber: 5
  }, this);
};
export default Index;

//# sourceMappingURL=data:application/json;base64,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