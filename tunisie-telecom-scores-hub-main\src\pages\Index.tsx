
import { useState } from 'react';
import Header from '@/components/Header';
import AuthModal from '@/components/AuthModal';
import ClientForm from '@/components/ClientForm';
import ClientList from '@/components/ClientList';
import Statistics from '@/components/Statistics';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Phone, Shield, Users, Award } from 'lucide-react';

interface User {
  name: string;
  email: string;
}

interface Client {
  id: string;
  nom: string;
  prenom: string;
  score: number;
  email: string;
  telephone: string;
  adresse: string;
  notes: string;
  dateCreation: string;
}

const Index = () => {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [clients, setClients] = useState<Client[]>([]);

  const handleLogin = () => {
    setIsAuthModalOpen(true);
  };

  const handleLogout = () => {
    setUser(null);
    setClients([]);
  };

  const handleAuthenticate = (userData: User) => {
    setUser(userData);
    console.log('Utilisateur connecté:', userData);
  };

  const handleAddClient = (clientData: Omit<Client, 'id' | 'dateCreation'>) => {
    const newClient: Client = {
      ...clientData,
      id: Date.now().toString(),
      dateCreation: new Date().toISOString()
    };
    setClients(prev => [...prev, newClient]);
    console.log('Nouveau client ajouté:', newClient);
  };

  // Page d'accueil pour les utilisateurs non connectés
  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-tunisietelecom-lightgray to-white">
        <Header
          isAuthenticated={false}
          onLogin={handleLogin}
          onLogout={handleLogout}
          onProfile={() => {}}
        />
        
        <main className="container mx-auto px-4 py-8">
          {/* Hero Section */}
          <div className="text-center mb-16 animate-fade-in">
            <div className="w-32 h-24 flex items-center justify-center mx-auto mb-6">
              <img
                src="/assets/tunisie-telecom-logo.svg"
                alt="Tunisie Telecom Logo"
                className="h-24 w-auto object-contain"
              />
            </div>
            <h1 className="text-4xl md:text-6xl font-bold text-tunisietelecom-darkgray mb-6">
              Tunisie Telecom
            </h1>
            <h2 className="text-2xl md:text-3xl font-light text-gray-600 mb-8">
              Système de Gestion des Scores Clients
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-8">
              Plateforme moderne pour suivre et gérer les scores de performance de vos clients. 
              Créez votre compte pour commencer à enregistrer et analyser les données de vos clients.
            </p>
            <button
              onClick={handleLogin}
              className="bg-tunisietelecom-blue hover:bg-tunisietelecom-darkblue text-white px-8 py-4 rounded-lg text-lg font-semibold shadow-lg transition-all duration-200 hover:shadow-xl transform hover:-translate-y-1"
            >
              Commencer maintenant
            </button>
          </div>

          {/* Fonctionnalités */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            <Card className="border-tunisietelecom-blue/20 shadow-lg hover:shadow-xl transition-shadow duration-200">
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-tunisietelecom-blue rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="text-white" size={32} />
                </div>
                <CardTitle className="text-tunisietelecom-darkgray">Gestion des Clients</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-gray-600">
                  Ajoutez et gérez facilement les informations de vos clients avec leurs scores de performance.
                </p>
              </CardContent>
            </Card>

            <Card className="border-tunisietelecom-blue/20 shadow-lg hover:shadow-xl transition-shadow duration-200">
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-tunisietelecom-darkblue rounded-full flex items-center justify-center mx-auto mb-4">
                  <Award className="text-white" size={32} />
                </div>
                <CardTitle className="text-tunisietelecom-darkgray">Suivi des Scores</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-gray-600">
                  Surveillez les performances de vos clients avec un système de notation de 0 à 100.
                </p>
              </CardContent>
            </Card>

            <Card className="border-tunisietelecom-blue/20 shadow-lg hover:shadow-xl transition-shadow duration-200">
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Shield className="text-white" size={32} />
                </div>
                <CardTitle className="text-tunisietelecom-darkgray">Sécurisé</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-gray-600">
                  Plateforme sécurisée avec authentification pour protéger vos données clients.
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Call to Action */}
          <div className="text-center bg-white rounded-xl shadow-lg p-8 border border-tunisietelecom-blue/20">
            <h3 className="text-2xl font-bold text-tunisietelecom-darkgray mb-4">
              Prêt à commencer ?
            </h3>
            <p className="text-gray-600 mb-6">
              Créez votre compte dès maintenant et commencez à gérer vos clients efficacement.
            </p>
            <button
              onClick={handleLogin}
              className="bg-tunisietelecom-blue hover:bg-tunisietelecom-darkblue text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200"
            >
              Créer un compte gratuit
            </button>
          </div>
        </main>

        <AuthModal
          isOpen={isAuthModalOpen}
          onClose={() => setIsAuthModalOpen(false)}
          onAuthenticate={handleAuthenticate}
        />
      </div>
    );
  }

  // Dashboard pour les utilisateurs connectés
  return (
    <div className="min-h-screen bg-gradient-to-br from-tunisietelecom-lightgray to-white">
      <Header
        isAuthenticated={true}
        onLogin={handleLogin}
        onLogout={handleLogout}
        onProfile={() => console.log('Profil clicked')}
        userName={user.name}
      />
      
      <main className="container mx-auto px-4 py-8">
        <div className="mb-8 animate-fade-in">
          <h1 className="text-3xl font-bold text-tunisietelecom-darkgray mb-2">
            Dashboard - Gestion des Clients
          </h1>
          <p className="text-gray-600">
            Bienvenue {user.name}, gérez vos clients et suivez leurs scores de performance.
          </p>
        </div>

        <Statistics clients={clients} />

        <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
          <div className="animate-slide-in">
            <ClientForm onAddClient={handleAddClient} />
          </div>
          
          <div className="animate-fade-in">
            <ClientList clients={clients} />
          </div>
        </div>
      </main>
    </div>
  );
};

export default Index;
