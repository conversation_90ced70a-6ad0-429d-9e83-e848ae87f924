import __vite__cjsImport0_react_jsxDevRuntime from "/node_modules/.vite/deps/react_jsx-dev-runtime.js?v=10e87200"; const jsxDEV = __vite__cjsImport0_react_jsxDevRuntime["jsxDEV"];
import __vite__cjsImport1_react from "/node_modules/.vite/deps/react.js?v=10e87200"; const useState = __vite__cjsImport1_react["useState"];
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "/src/components/ui/dialog.tsx";
import { Button } from "/src/components/ui/button.tsx";
import { Input } from "/src/components/ui/input.tsx";
import { Label } from "/src/components/ui/label.tsx";
import { Card, CardHeader, CardTitle, CardContent } from "/src/components/ui/card.tsx";
import { UserPlus, LogIn } from "/node_modules/.vite/deps/lucide-react.js?v=10e87200";
const AuthModal = ({ isOpen, onClose, onAuthenticate }) => {
  const [isLogin, setIsLogin] = useState(true);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
    confirmPassword: ""
  });
  const handleSubmit = (e) => {
    e.preventDefault();
    if (!isLogin && formData.password !== formData.confirmPassword) {
      alert("Les mots de passe ne correspondent pas");
      return;
    }
    if (!formData.email || !formData.password) {
      alert("Veuillez remplir tous les champs requis");
      return;
    }
    if (!isLogin && !formData.name) {
      alert("Veuillez entrer votre nom");
      return;
    }
    onAuthenticate({
      name: formData.name || formData.email.split("@")[0],
      email: formData.email
    });
    onClose();
    setFormData({ name: "", email: "", password: "", confirmPassword: "" });
  };
  const toggleAuthMode = () => {
    setIsLogin(!isLogin);
    setFormData({ name: "", email: "", password: "", confirmPassword: "" });
  };
  return /* @__PURE__ */ jsxDEV(Dialog, { open: isOpen, onOpenChange: onClose, children: /* @__PURE__ */ jsxDEV(DialogContent, { className: "sm:max-w-md", children: [
    /* @__PURE__ */ jsxDEV(DialogHeader, { children: /* @__PURE__ */ jsxDEV(DialogTitle, { className: "text-2xl font-bold text-center text-tunisietelecom-darkgray", children: isLogin ? "Connexion" : "Inscription" }, void 0, false, {
      fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
      lineNumber: 62,
      columnNumber: 11
    }, this) }, void 0, false, {
      fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
      lineNumber: 61,
      columnNumber: 9
    }, this),
    /* @__PURE__ */ jsxDEV(Card, { className: "border-0 shadow-none", children: [
      /* @__PURE__ */ jsxDEV(CardHeader, { className: "pb-4", children: /* @__PURE__ */ jsxDEV(CardTitle, { className: "text-center text-tunisietelecom-blue flex items-center justify-center gap-2", children: [
        isLogin ? /* @__PURE__ */ jsxDEV(LogIn, { size: 20 }, void 0, false, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
          lineNumber: 70,
          columnNumber: 26
        }, this) : /* @__PURE__ */ jsxDEV(UserPlus, { size: 20 }, void 0, false, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
          lineNumber: 70,
          columnNumber: 48
        }, this),
        isLogin ? "Se connecter" : "Créer un compte"
      ] }, void 0, true, {
        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
        lineNumber: 69,
        columnNumber: 13
      }, this) }, void 0, false, {
        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
        lineNumber: 68,
        columnNumber: 11
      }, this),
      /* @__PURE__ */ jsxDEV(CardContent, { children: /* @__PURE__ */ jsxDEV("form", { onSubmit: handleSubmit, className: "space-y-4", children: [
        !isLogin && /* @__PURE__ */ jsxDEV("div", { className: "space-y-2", children: [
          /* @__PURE__ */ jsxDEV(Label, { htmlFor: "name", children: "Nom complet" }, void 0, false, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
            lineNumber: 79,
            columnNumber: 19
          }, this),
          /* @__PURE__ */ jsxDEV(
            Input,
            {
              id: "name",
              type: "text",
              placeholder: "Votre nom complet",
              value: formData.name,
              onChange: (e) => setFormData({ ...formData, name: e.target.value }),
              className: "border-gray-300 focus:border-tunisietelecom-blue"
            },
            void 0,
            false,
            {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
              lineNumber: 80,
              columnNumber: 19
            },
            this
          )
        ] }, void 0, true, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
          lineNumber: 78,
          columnNumber: 17
        }, this),
        /* @__PURE__ */ jsxDEV("div", { className: "space-y-2", children: [
          /* @__PURE__ */ jsxDEV(Label, { htmlFor: "email", children: "Email" }, void 0, false, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
            lineNumber: 92,
            columnNumber: 17
          }, this),
          /* @__PURE__ */ jsxDEV(
            Input,
            {
              id: "email",
              type: "email",
              placeholder: "<EMAIL>",
              value: formData.email,
              onChange: (e) => setFormData({ ...formData, email: e.target.value }),
              className: "border-gray-300 focus:border-tunisietelecom-blue",
              required: true
            },
            void 0,
            false,
            {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
              lineNumber: 93,
              columnNumber: 17
            },
            this
          )
        ] }, void 0, true, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
          lineNumber: 91,
          columnNumber: 15
        }, this),
        /* @__PURE__ */ jsxDEV("div", { className: "space-y-2", children: [
          /* @__PURE__ */ jsxDEV(Label, { htmlFor: "password", children: "Mot de passe" }, void 0, false, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
            lineNumber: 105,
            columnNumber: 17
          }, this),
          /* @__PURE__ */ jsxDEV(
            Input,
            {
              id: "password",
              type: "password",
              placeholder: "Votre mot de passe",
              value: formData.password,
              onChange: (e) => setFormData({ ...formData, password: e.target.value }),
              className: "border-gray-300 focus:border-tunisietelecom-blue",
              required: true
            },
            void 0,
            false,
            {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
              lineNumber: 106,
              columnNumber: 17
            },
            this
          )
        ] }, void 0, true, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
          lineNumber: 104,
          columnNumber: 15
        }, this),
        !isLogin && /* @__PURE__ */ jsxDEV("div", { className: "space-y-2", children: [
          /* @__PURE__ */ jsxDEV(Label, { htmlFor: "confirmPassword", children: "Confirmer le mot de passe" }, void 0, false, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
            lineNumber: 119,
            columnNumber: 19
          }, this),
          /* @__PURE__ */ jsxDEV(
            Input,
            {
              id: "confirmPassword",
              type: "password",
              placeholder: "Confirmez votre mot de passe",
              value: formData.confirmPassword,
              onChange: (e) => setFormData({ ...formData, confirmPassword: e.target.value }),
              className: "border-gray-300 focus:border-tunisietelecom-blue"
            },
            void 0,
            false,
            {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
              lineNumber: 120,
              columnNumber: 19
            },
            this
          )
        ] }, void 0, true, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
          lineNumber: 118,
          columnNumber: 17
        }, this),
        /* @__PURE__ */ jsxDEV(
          Button,
          {
            type: "submit",
            className: "w-full bg-tunisietelecom-blue hover:bg-tunisietelecom-darkblue text-white py-3",
            children: isLogin ? "Se connecter" : "Créer le compte"
          },
          void 0,
          false,
          {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
            lineNumber: 131,
            columnNumber: 15
          },
          this
        ),
        /* @__PURE__ */ jsxDEV("div", { className: "text-center", children: /* @__PURE__ */ jsxDEV(
          "button",
          {
            type: "button",
            onClick: toggleAuthMode,
            className: "text-tunisietelecom-blue hover:underline text-sm",
            children: isLogin ? "Pas de compte ? Créer un compte" : "Déjà un compte ? Se connecter"
          },
          void 0,
          false,
          {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
            lineNumber: 139,
            columnNumber: 17
          },
          this
        ) }, void 0, false, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
          lineNumber: 138,
          columnNumber: 15
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
        lineNumber: 76,
        columnNumber: 13
      }, this) }, void 0, false, {
        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
        lineNumber: 75,
        columnNumber: 11
      }, this)
    ] }, void 0, true, {
      fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
      lineNumber: 67,
      columnNumber: 9
    }, this)
  ] }, void 0, true, {
    fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
    lineNumber: 60,
    columnNumber: 7
  }, this) }, void 0, false, {
    fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/AuthModal.tsx",
    lineNumber: 59,
    columnNumber: 5
  }, this);
};
export default AuthModal;

//# sourceMappingURL=data:application/json;base64,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