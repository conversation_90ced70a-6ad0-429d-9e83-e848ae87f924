import __vite__cjsImport0_react_jsxDevRuntime from "/node_modules/.vite/deps/react_jsx-dev-runtime.js?v=10e87200"; const jsxDEV = __vite__cjsImport0_react_jsxDevRuntime["jsxDEV"];
import __vite__cjsImport1_react from "/node_modules/.vite/deps/react.js?v=10e87200"; const useState = __vite__cjsImport1_react["useState"];
import { useNavigate } from "/node_modules/.vite/deps/react-router-dom.js?v=10e87200";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "/src/components/ui/card.tsx";
import { Button } from "/src/components/ui/button.tsx";
import { Input } from "/src/components/ui/input.tsx";
import { Label } from "/src/components/ui/label.tsx";
import { Search, User, Phone, CreditCard } from "/node_modules/.vite/deps/lucide-react.js?v=10e87200";
import Header from "/src/components/Header.tsx";
const ClientSearch = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    nom: "",
    prenom: "",
    telephone: "",
    numeroClient: ""
  });
  const [isLoading, setIsLoading] = useState(false);
  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value
    }));
  };
  const handleSearch = async () => {
    if (!formData.nom || !formData.prenom) {
      alert("Veuillez renseigner au moins le nom et prénom du client");
      return;
    }
    setIsLoading(true);
    setTimeout(() => {
      navigate("/dashboard", {
        state: {
          clientData: formData
        }
      });
      setIsLoading(false);
    }, 1e3);
  };
  const isFormValid = formData.nom.trim() && formData.prenom.trim();
  return /* @__PURE__ */ jsxDEV("div", { className: "min-h-screen bg-gray-50", children: [
    /* @__PURE__ */ jsxDEV(
      Header,
      {
        isAuthenticated: true,
        onLogin: () => {
        },
        onLogout: () => {
        },
        onProfile: () => {
        },
        userName: "Utilisateur"
      },
      void 0,
      false,
      {
        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
        lineNumber: 59,
        columnNumber: 7
      },
      this
    ),
    /* @__PURE__ */ jsxDEV("div", { className: "container mx-auto px-4 py-8", children: /* @__PURE__ */ jsxDEV("div", { className: "max-w-2xl mx-auto", children: [
      /* @__PURE__ */ jsxDEV("div", { className: "text-center mb-8", children: [
        /* @__PURE__ */ jsxDEV("h1", { className: "text-3xl font-bold text-tunisietelecom-darkgray mb-2", children: "Recherche Client" }, void 0, false, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
          lineNumber: 70,
          columnNumber: 13
        }, this),
        /* @__PURE__ */ jsxDEV("p", { className: "text-gray-600", children: "Saisissez les informations du client pour consulter son score" }, void 0, false, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
          lineNumber: 73,
          columnNumber: 13
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
        lineNumber: 69,
        columnNumber: 11
      }, this),
      /* @__PURE__ */ jsxDEV(Card, { className: "shadow-lg", children: [
        /* @__PURE__ */ jsxDEV(CardHeader, { className: "bg-tunisietelecom-blue text-white", children: [
          /* @__PURE__ */ jsxDEV(CardTitle, { className: "flex items-center gap-2", children: [
            /* @__PURE__ */ jsxDEV(Search, { size: 24 }, void 0, false, {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
              lineNumber: 81,
              columnNumber: 17
            }, this),
            "Informations Client"
          ] }, void 0, true, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
            lineNumber: 80,
            columnNumber: 15
          }, this),
          /* @__PURE__ */ jsxDEV(CardDescription, { className: "text-blue-100", children: "Remplissez les champs ci-dessous pour rechercher le client" }, void 0, false, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
            lineNumber: 84,
            columnNumber: 15
          }, this)
        ] }, void 0, true, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
          lineNumber: 79,
          columnNumber: 13
        }, this),
        /* @__PURE__ */ jsxDEV(CardContent, { className: "p-6 space-y-6", children: [
          /* @__PURE__ */ jsxDEV("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-4", children: [
            /* @__PURE__ */ jsxDEV("div", { className: "space-y-2", children: [
              /* @__PURE__ */ jsxDEV(Label, { htmlFor: "nom", className: "flex items-center gap-2", children: [
                /* @__PURE__ */ jsxDEV(User, { size: 16 }, void 0, false, {
                  fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
                  lineNumber: 93,
                  columnNumber: 21
                }, this),
                "Nom *"
              ] }, void 0, true, {
                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
                lineNumber: 92,
                columnNumber: 19
              }, this),
              /* @__PURE__ */ jsxDEV(
                Input,
                {
                  id: "nom",
                  placeholder: "Nom de famille",
                  value: formData.nom,
                  onChange: (e) => handleInputChange("nom", e.target.value),
                  className: "border-gray-300 focus:border-tunisietelecom-blue"
                },
                void 0,
                false,
                {
                  fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
                  lineNumber: 96,
                  columnNumber: 19
                },
                this
              )
            ] }, void 0, true, {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
              lineNumber: 91,
              columnNumber: 17
            }, this),
            /* @__PURE__ */ jsxDEV("div", { className: "space-y-2", children: [
              /* @__PURE__ */ jsxDEV(Label, { htmlFor: "prenom", className: "flex items-center gap-2", children: [
                /* @__PURE__ */ jsxDEV(User, { size: 16 }, void 0, false, {
                  fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
                  lineNumber: 107,
                  columnNumber: 21
                }, this),
                "Prénom *"
              ] }, void 0, true, {
                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
                lineNumber: 106,
                columnNumber: 19
              }, this),
              /* @__PURE__ */ jsxDEV(
                Input,
                {
                  id: "prenom",
                  placeholder: "Prénom",
                  value: formData.prenom,
                  onChange: (e) => handleInputChange("prenom", e.target.value),
                  className: "border-gray-300 focus:border-tunisietelecom-blue"
                },
                void 0,
                false,
                {
                  fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
                  lineNumber: 110,
                  columnNumber: 19
                },
                this
              )
            ] }, void 0, true, {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
              lineNumber: 105,
              columnNumber: 17
            }, this)
          ] }, void 0, true, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
            lineNumber: 90,
            columnNumber: 15
          }, this),
          /* @__PURE__ */ jsxDEV("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-4", children: [
            /* @__PURE__ */ jsxDEV("div", { className: "space-y-2", children: [
              /* @__PURE__ */ jsxDEV(Label, { htmlFor: "telephone", className: "flex items-center gap-2", children: [
                /* @__PURE__ */ jsxDEV(Phone, { size: 16 }, void 0, false, {
                  fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
                  lineNumber: 123,
                  columnNumber: 21
                }, this),
                "Numéro de téléphone"
              ] }, void 0, true, {
                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
                lineNumber: 122,
                columnNumber: 19
              }, this),
              /* @__PURE__ */ jsxDEV(
                Input,
                {
                  id: "telephone",
                  placeholder: "Ex: +216 XX XXX XXX",
                  value: formData.telephone,
                  onChange: (e) => handleInputChange("telephone", e.target.value),
                  className: "border-gray-300 focus:border-tunisietelecom-blue"
                },
                void 0,
                false,
                {
                  fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
                  lineNumber: 126,
                  columnNumber: 19
                },
                this
              )
            ] }, void 0, true, {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
              lineNumber: 121,
              columnNumber: 17
            }, this),
            /* @__PURE__ */ jsxDEV("div", { className: "space-y-2", children: [
              /* @__PURE__ */ jsxDEV(Label, { htmlFor: "numeroClient", className: "flex items-center gap-2", children: [
                /* @__PURE__ */ jsxDEV(CreditCard, { size: 16 }, void 0, false, {
                  fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
                  lineNumber: 137,
                  columnNumber: 21
                }, this),
                "Numéro client"
              ] }, void 0, true, {
                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
                lineNumber: 136,
                columnNumber: 19
              }, this),
              /* @__PURE__ */ jsxDEV(
                Input,
                {
                  id: "numeroClient",
                  placeholder: "Ex: TT123456789",
                  value: formData.numeroClient,
                  onChange: (e) => handleInputChange("numeroClient", e.target.value),
                  className: "border-gray-300 focus:border-tunisietelecom-blue"
                },
                void 0,
                false,
                {
                  fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
                  lineNumber: 140,
                  columnNumber: 19
                },
                this
              )
            ] }, void 0, true, {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
              lineNumber: 135,
              columnNumber: 17
            }, this)
          ] }, void 0, true, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
            lineNumber: 120,
            columnNumber: 15
          }, this),
          /* @__PURE__ */ jsxDEV("div", { className: "pt-4", children: /* @__PURE__ */ jsxDEV(
            Button,
            {
              onClick: handleSearch,
              disabled: !isFormValid || isLoading,
              className: "w-full bg-tunisietelecom-blue hover:bg-tunisietelecom-darkblue text-white py-3",
              children: isLoading ? /* @__PURE__ */ jsxDEV("div", { className: "flex items-center gap-2", children: [
                /* @__PURE__ */ jsxDEV("div", { className: "animate-spin rounded-full h-4 w-4 border-b-2 border-white" }, void 0, false, {
                  fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
                  lineNumber: 158,
                  columnNumber: 23
                }, this),
                "Recherche en cours..."
              ] }, void 0, true, {
                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
                lineNumber: 157,
                columnNumber: 21
              }, this) : /* @__PURE__ */ jsxDEV("div", { className: "flex items-center gap-2", children: [
                /* @__PURE__ */ jsxDEV(Search, { size: 20 }, void 0, false, {
                  fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
                  lineNumber: 163,
                  columnNumber: 23
                }, this),
                "Rechercher et voir le score"
              ] }, void 0, true, {
                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
                lineNumber: 162,
                columnNumber: 21
              }, this)
            },
            void 0,
            false,
            {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
              lineNumber: 151,
              columnNumber: 17
            },
            this
          ) }, void 0, false, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
            lineNumber: 150,
            columnNumber: 15
          }, this),
          /* @__PURE__ */ jsxDEV("div", { className: "text-sm text-gray-500 text-center", children: "* Champs obligatoires" }, void 0, false, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
            lineNumber: 170,
            columnNumber: 15
          }, this)
        ] }, void 0, true, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
          lineNumber: 89,
          columnNumber: 13
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
        lineNumber: 78,
        columnNumber: 11
      }, this)
    ] }, void 0, true, {
      fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
      lineNumber: 68,
      columnNumber: 9
    }, this) }, void 0, false, {
      fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
      lineNumber: 67,
      columnNumber: 7
    }, this)
  ] }, void 0, true, {
    fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/ClientSearch.tsx",
    lineNumber: 58,
    columnNumber: 5
  }, this);
};
export default ClientSearch;

//# sourceMappingURL=data:application/json;base64,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