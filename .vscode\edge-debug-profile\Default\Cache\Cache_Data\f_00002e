import __vite__cjsImport0_react_jsxDevRuntime from "/node_modules/.vite/deps/react_jsx-dev-runtime.js?v=10e87200"; const jsxDEV = __vite__cjsImport0_react_jsxDevRuntime["jsxDEV"];
import __vite__cjsImport1_react from "/node_modules/.vite/deps/react.js?v=10e87200"; const useState = __vite__cjsImport1_react["useState"];
import { Card, CardHeader, CardTitle, CardContent } from "/src/components/ui/card.tsx";
import { Button } from "/src/components/ui/button.tsx";
import { Input } from "/src/components/ui/input.tsx";
import { Label } from "/src/components/ui/label.tsx";
import { Textarea } from "/src/components/ui/textarea.tsx";
import { UserPlus, Save } from "/node_modules/.vite/deps/lucide-react.js?v=10e87200";
const ClientForm = ({ onAddClient }) => {
  const [formData, setFormData] = useState({
    nom: "",
    prenom: "",
    score: "",
    email: "",
    telephone: "",
    adresse: "",
    notes: ""
  });
  const handleSubmit = (e) => {
    e.preventDefault();
    if (!formData.nom || !formData.prenom || !formData.score) {
      alert("Veuillez remplir au moins le nom, prénom et score");
      return;
    }
    const score = parseInt(formData.score);
    if (isNaN(score) || score < 0 || score > 100) {
      alert("Le score doit être un nombre entre 0 et 100");
      return;
    }
    onAddClient({
      nom: formData.nom,
      prenom: formData.prenom,
      score,
      email: formData.email,
      telephone: formData.telephone,
      adresse: formData.adresse,
      notes: formData.notes
    });
    setFormData({
      nom: "",
      prenom: "",
      score: "",
      email: "",
      telephone: "",
      adresse: "",
      notes: ""
    });
  };
  return /* @__PURE__ */ jsxDEV(Card, { className: "border-tunisietelecom-blue/20 shadow-lg", children: [
    /* @__PURE__ */ jsxDEV(CardHeader, { className: "bg-gradient-to-r from-tunisietelecom-blue to-tunisietelecom-darkblue text-white", children: /* @__PURE__ */ jsxDEV(CardTitle, { className: "flex items-center gap-2", children: [
      /* @__PURE__ */ jsxDEV(UserPlus, { size: 20 }, void 0, false, {
        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientForm.tsx",
        lineNumber: 77,
        columnNumber: 11
      }, this),
      "Ajouter un nouveau client"
    ] }, void 0, true, {
      fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientForm.tsx",
      lineNumber: 76,
      columnNumber: 9
    }, this) }, void 0, false, {
      fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientForm.tsx",
      lineNumber: 75,
      columnNumber: 7
    }, this),
    /* @__PURE__ */ jsxDEV(CardContent, { className: "p-6", children: /* @__PURE__ */ jsxDEV("form", { onSubmit: handleSubmit, className: "space-y-4", children: [
      /* @__PURE__ */ jsxDEV("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-4", children: [
        /* @__PURE__ */ jsxDEV("div", { className: "space-y-2", children: [
          /* @__PURE__ */ jsxDEV(Label, { htmlFor: "nom", children: "Nom *" }, void 0, false, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientForm.tsx",
            lineNumber: 86,
            columnNumber: 15
          }, this),
          /* @__PURE__ */ jsxDEV(
            Input,
            {
              id: "nom",
              type: "text",
              placeholder: "Nom du client",
              value: formData.nom,
              onChange: (e) => setFormData({ ...formData, nom: e.target.value }),
              className: "border-gray-300 focus:border-tunisietelecom-blue",
              required: true
            },
            void 0,
            false,
            {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientForm.tsx",
              lineNumber: 87,
              columnNumber: 15
            },
            this
          )
        ] }, void 0, true, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientForm.tsx",
          lineNumber: 85,
          columnNumber: 13
        }, this),
        /* @__PURE__ */ jsxDEV("div", { className: "space-y-2", children: [
          /* @__PURE__ */ jsxDEV(Label, { htmlFor: "prenom", children: "Prénom *" }, void 0, false, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientForm.tsx",
            lineNumber: 99,
            columnNumber: 15
          }, this),
          /* @__PURE__ */ jsxDEV(
            Input,
            {
              id: "prenom",
              type: "text",
              placeholder: "Prénom du client",
              value: formData.prenom,
              onChange: (e) => setFormData({ ...formData, prenom: e.target.value }),
              className: "border-gray-300 focus:border-tunisietelecom-blue",
              required: true
            },
            void 0,
            false,
            {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientForm.tsx",
              lineNumber: 100,
              columnNumber: 15
            },
            this
          )
        ] }, void 0, true, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientForm.tsx",
          lineNumber: 98,
          columnNumber: 13
        }, this),
        /* @__PURE__ */ jsxDEV("div", { className: "space-y-2", children: [
          /* @__PURE__ */ jsxDEV(Label, { htmlFor: "score", children: "Score (0-100) *" }, void 0, false, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientForm.tsx",
            lineNumber: 112,
            columnNumber: 15
          }, this),
          /* @__PURE__ */ jsxDEV(
            Input,
            {
              id: "score",
              type: "number",
              min: "0",
              max: "100",
              placeholder: "Score du client",
              value: formData.score,
              onChange: (e) => setFormData({ ...formData, score: e.target.value }),
              className: "border-gray-300 focus:border-tunisietelecom-blue",
              required: true
            },
            void 0,
            false,
            {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientForm.tsx",
              lineNumber: 113,
              columnNumber: 15
            },
            this
          )
        ] }, void 0, true, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientForm.tsx",
          lineNumber: 111,
          columnNumber: 13
        }, this),
        /* @__PURE__ */ jsxDEV("div", { className: "space-y-2", children: [
          /* @__PURE__ */ jsxDEV(Label, { htmlFor: "email", children: "Email" }, void 0, false, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientForm.tsx",
            lineNumber: 127,
            columnNumber: 15
          }, this),
          /* @__PURE__ */ jsxDEV(
            Input,
            {
              id: "email",
              type: "email",
              placeholder: "<EMAIL>",
              value: formData.email,
              onChange: (e) => setFormData({ ...formData, email: e.target.value }),
              className: "border-gray-300 focus:border-tunisietelecom-blue"
            },
            void 0,
            false,
            {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientForm.tsx",
              lineNumber: 128,
              columnNumber: 15
            },
            this
          )
        ] }, void 0, true, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientForm.tsx",
          lineNumber: 126,
          columnNumber: 13
        }, this),
        /* @__PURE__ */ jsxDEV("div", { className: "space-y-2", children: [
          /* @__PURE__ */ jsxDEV(Label, { htmlFor: "telephone", children: "Téléphone" }, void 0, false, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientForm.tsx",
            lineNumber: 139,
            columnNumber: 15
          }, this),
          /* @__PURE__ */ jsxDEV(
            Input,
            {
              id: "telephone",
              type: "tel",
              placeholder: "+216 XX XXX XXX",
              value: formData.telephone,
              onChange: (e) => setFormData({ ...formData, telephone: e.target.value }),
              className: "border-gray-300 focus:border-tunisietelecom-blue"
            },
            void 0,
            false,
            {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientForm.tsx",
              lineNumber: 140,
              columnNumber: 15
            },
            this
          )
        ] }, void 0, true, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientForm.tsx",
          lineNumber: 138,
          columnNumber: 13
        }, this),
        /* @__PURE__ */ jsxDEV("div", { className: "space-y-2", children: [
          /* @__PURE__ */ jsxDEV(Label, { htmlFor: "adresse", children: "Adresse" }, void 0, false, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientForm.tsx",
            lineNumber: 151,
            columnNumber: 15
          }, this),
          /* @__PURE__ */ jsxDEV(
            Input,
            {
              id: "adresse",
              type: "text",
              placeholder: "Adresse du client",
              value: formData.adresse,
              onChange: (e) => setFormData({ ...formData, adresse: e.target.value }),
              className: "border-gray-300 focus:border-tunisietelecom-blue"
            },
            void 0,
            false,
            {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientForm.tsx",
              lineNumber: 152,
              columnNumber: 15
            },
            this
          )
        ] }, void 0, true, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientForm.tsx",
          lineNumber: 150,
          columnNumber: 13
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientForm.tsx",
        lineNumber: 84,
        columnNumber: 11
      }, this),
      /* @__PURE__ */ jsxDEV("div", { className: "space-y-2", children: [
        /* @__PURE__ */ jsxDEV(Label, { htmlFor: "notes", children: "Notes additionnelles" }, void 0, false, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientForm.tsx",
          lineNumber: 164,
          columnNumber: 13
        }, this),
        /* @__PURE__ */ jsxDEV(
          Textarea,
          {
            id: "notes",
            placeholder: "Notes ou commentaires sur le client...",
            value: formData.notes,
            onChange: (e) => setFormData({ ...formData, notes: e.target.value }),
            className: "border-gray-300 focus:border-tunisietelecom-blue min-h-[80px]"
          },
          void 0,
          false,
          {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientForm.tsx",
            lineNumber: 165,
            columnNumber: 13
          },
          this
        )
      ] }, void 0, true, {
        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientForm.tsx",
        lineNumber: 163,
        columnNumber: 11
      }, this),
      /* @__PURE__ */ jsxDEV(
        Button,
        {
          type: "submit",
          className: "w-full bg-tunisietelecom-blue hover:bg-tunisietelecom-darkblue text-white py-3",
          children: [
            /* @__PURE__ */ jsxDEV(Save, { size: 16, className: "mr-2" }, void 0, false, {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientForm.tsx",
              lineNumber: 178,
              columnNumber: 13
            }, this),
            "Ajouter le client"
          ]
        },
        void 0,
        true,
        {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientForm.tsx",
          lineNumber: 174,
          columnNumber: 11
        },
        this
      )
    ] }, void 0, true, {
      fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientForm.tsx",
      lineNumber: 83,
      columnNumber: 9
    }, this) }, void 0, false, {
      fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientForm.tsx",
      lineNumber: 82,
      columnNumber: 7
    }, this)
  ] }, void 0, true, {
    fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/components/ClientForm.tsx",
    lineNumber: 74,
    columnNumber: 5
  }, this);
};
export default ClientForm;

//# sourceMappingURL=data:application/json;base64,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