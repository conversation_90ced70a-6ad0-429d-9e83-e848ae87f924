// Script d'authentification automatique pour le débogage
// Ce script sera injecté automatiquement pour gérer l'authentification HTTP

(function() {
    'use strict';
    
    // Fonction pour gérer l'authentification HTTP Basic
    function handleHttpAuth() {
        // Vérifier si nous sommes sur une page d'authentification
        if (document.title.includes('Authentication Required') || 
            document.body.textContent.includes('401') ||
            document.body.textContent.includes('Unauthorized')) {
            
            console.log('Page d\'authentification détectée, tentative d\'authentification automatique...');
            
            // Essayer de remplir automatiquement les champs d'authentification
            const usernameField = document.querySelector('input[type="text"], input[name="username"], input[id="username"]');
            const passwordField = document.querySelector('input[type="password"], input[name="password"], input[id="password"]');
            const submitButton = document.querySelector('input[type="submit"], button[type="submit"], button');
            
            if (usernameField && passwordField) {
                usernameField.value = 'admin';
                passwordField.value = 'admin';
                
                if (submitButton) {
                    submitButton.click();
                }
            }
        }
    }
    
    // Fonction pour gérer l'authentification via XMLHttpRequest
    function setupXHRAuth() {
        const originalOpen = XMLHttpRequest.prototype.open;
        XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
            if (url.includes('localhost:8080') && !user) {
                return originalOpen.call(this, method, url, async, 'admin', 'admin');
            }
            return originalOpen.call(this, method, url, async, user, password);
        };
    }
    
    // Fonction pour gérer l'authentification via fetch
    function setupFetchAuth() {
        const originalFetch = window.fetch;
        window.fetch = function(input, init) {
            if (typeof input === 'string' && input.includes('localhost:8080')) {
                init = init || {};
                init.headers = init.headers || {};
                if (!init.headers['Authorization']) {
                    init.headers['Authorization'] = 'Basic ' + btoa('admin:admin');
                }
            }
            return originalFetch.call(this, input, init);
        };
    }
    
    // Initialiser les gestionnaires d'authentification
    setupXHRAuth();
    setupFetchAuth();
    
    // Vérifier l'authentification au chargement de la page
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', handleHttpAuth);
    } else {
        handleHttpAuth();
    }
    
    // Observer les changements de DOM pour détecter les nouvelles pages d'authentification
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                handleHttpAuth();
            }
        });
    });
    
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
    
    console.log('Script d\'authentification automatique chargé');
})();
