# 🚀 Démarrage Simple - Tunisie Telecom Scores Hub

## ⚡ Méthode la plus simple (Recommandée)

### 1. **Démarrage manuel** (100% fiable)

1. **Ouvrir le terminal VS Code** (Ctrl + `)
2. **Copier-coller cette commande** :
   ```bash
   cd tunisie-telecom-scores-hub-main && npm run dev
   ```
3. **Attendre le message** : "Local: http://localhost:5173/"
4. **Ouvrir le navigateur** : http://localhost:5173/

### 2. **Avec débogage VS Code**

1. **Démarrer manuellement** (étape 1 ci-dessus)
2. **Appuyer sur F5** dans VS Code
3. **Choisir** : "🚀 Démarrer l'application (Recommandé)"

## 🔧 Si ça ne marche pas

### **Problème : "npm run dev" ne fonctionne pas**

**Solution** :
```bash
cd tunisie-telecom-scores-hub-main
npm install
npm run dev
```

### **Problème : Port 5173 occupé**

**Solution** :
```bash
cd tunisie-telecom-scores-hub-main
npx vite --port 3001
```
Puis ouvrir : http://localhost:3001/

### **Problème : Erreurs de dépendances**

**Solution** :
```bash
cd tunisie-telecom-scores-hub-main
rm -rf node_modules package-lock.json
npm install
npm run dev
```

## 🎯 Test de l'application

Une fois démarrée, vous devriez voir :

1. **Page d'accueil** avec le logo Tunisie Telecom
2. **Formulaire** avec les champs :
   - Nom* (obligatoire)
   - Prénom* (obligatoire)
   - Téléphone (optionnel)
   - Numéro client (optionnel)
3. **Bouton** "Rechercher et voir le score"

### **Test rapide** :
1. Saisir : Nom = "Dupont", Prénom = "Jean"
2. Cliquer "Rechercher et voir le score"
3. Voir le dashboard avec le score calculé

## 📱 Fonctionnalités

- ✅ **Page de saisie client** - Formulaire moderne
- ✅ **Calcul de score** - Simulation avec facteurs
- ✅ **Dashboard interactif** - Affichage détaillé
- ✅ **Logo Tunisie Telecom** - Branding officiel
- ✅ **Navigation fluide** - Retour à la recherche

## 🆘 Aide rapide

### **Commandes utiles** :
```bash
# Vérifier Node.js
node --version

# Vérifier npm
npm --version

# Nettoyer et réinstaller
npm cache clean --force

# Démarrer avec un autre port
npx vite --port 4000
```

### **URLs importantes** :
- **Application** : http://localhost:5173/
- **Port alternatif** : http://localhost:3001/
- **Avec IP** : http://127.0.0.1:5173/

---

**🎉 L'application est prête ! Nettoyage Lovable AI terminé avec succès !**
