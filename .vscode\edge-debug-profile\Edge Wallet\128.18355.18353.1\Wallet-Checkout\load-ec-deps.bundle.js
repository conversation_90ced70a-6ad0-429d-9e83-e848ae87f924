(()=>{"use strict";const t=t=>{const e=t??"";switch(e){case"en-au":case"en-AU":return"en-GB";case"zh-TW":return"zh-Hant";case"zh-CN":return"zh-Hans"}const r=e.split("-")?.[0];switch(r){case"pt":return"pt-PT";case"zh":return"zh-Hans";default:return`${r}`}},e=Array.isArray,r=function(t,e){for(var r=-1,n=null==t?0:t.length,o=Array(n);++r<n;)o[r]=e(t[r],r,t);return o},n=function(t,e){return t===e||t!=t&&e!=e},o=function(t,e){for(var r=t.length;r--;)if(n(t[r][0],e))return r;return-1};var a=Array.prototype.splice;function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=function(){this.__data__=[],this.size=0},u.prototype.delete=function(t){var e=this.__data__,r=o(e,t);return!(r<0||(r==e.length-1?e.pop():a.call(e,r,1),--this.size,0))},u.prototype.get=function(t){var e=this.__data__,r=o(e,t);return r<0?void 0:e[r][1]},u.prototype.has=function(t){return o(this.__data__,t)>-1},u.prototype.set=function(t,e){var r=this.__data__,n=o(r,t);return n<0?(++this.size,r.push([t,e])):r[n][1]=e,this};const i=u,c="object"==typeof global&&global&&global.Object===Object&&global;var s="object"==typeof self&&self&&self.Object===Object&&self;const f=c||s||Function("return this")(),l=f.Symbol;var p=Object.prototype,h=p.hasOwnProperty,v=p.toString,y=l?l.toStringTag:void 0;var b=Object.prototype.toString;var _=l?l.toStringTag:void 0;const d=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":_&&_ in Object(t)?function(t){var e=h.call(t,y),r=t[y];try{t[y]=void 0;var n=!0}catch(t){}var o=v.call(t);return n&&(e?t[y]=r:delete t[y]),o}(t):function(t){return b.call(t)}(t)},j=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)},g=function(t){if(!j(t))return!1;var e=d(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e},w=f["__core-js_shared__"];var O,m=(O=/[^.]+$/.exec(w&&w.keys&&w.keys.IE_PROTO||""))?"Symbol(src)_1."+O:"";var z=Function.prototype.toString;const A=function(t){if(null!=t){try{return z.call(t)}catch(t){}try{return t+""}catch(t){}}return""};var P=/^\[object .+?Constructor\]$/,S=Function.prototype,x=Object.prototype,$=S.toString,E=x.hasOwnProperty,T=RegExp("^"+$.call(E).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");const k=function(t){return!(!j(t)||(e=t,m&&m in e))&&(g(t)?T:P).test(A(t));var e},D=function(t,e){var r=function(t,e){return null==t?void 0:t[e]}(t,e);return k(r)?r:void 0},F=D(f,"Map"),B=D(Object,"create");var C=Object.prototype.hasOwnProperty;var U=Object.prototype.hasOwnProperty;function I(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}I.prototype.clear=function(){this.__data__=B?B(null):{},this.size=0},I.prototype.delete=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e},I.prototype.get=function(t){var e=this.__data__;if(B){var r=e[t];return"__lodash_hash_undefined__"===r?void 0:r}return C.call(e,t)?e[t]:void 0},I.prototype.has=function(t){var e=this.__data__;return B?void 0!==e[t]:U.call(e,t)},I.prototype.set=function(t,e){var r=this.__data__;return this.size+=this.has(t)?0:1,r[t]=B&&void 0===e?"__lodash_hash_undefined__":e,this};const M=I,V=function(t,e){var r,n,o=t.__data__;return("string"==(n=typeof(r=e))||"number"==n||"symbol"==n||"boolean"==n?"__proto__"!==r:null===r)?o["string"==typeof e?"string":"hash"]:o.map};function L(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}L.prototype.clear=function(){this.size=0,this.__data__={hash:new M,map:new(F||i),string:new M}},L.prototype.delete=function(t){var e=V(this,t).delete(t);return this.size-=e?1:0,e},L.prototype.get=function(t){return V(this,t).get(t)},L.prototype.has=function(t){return V(this,t).has(t)},L.prototype.set=function(t,e){var r=V(this,t),n=r.size;return r.set(t,e),this.size+=r.size==n?0:1,this};const N=L;function R(t){var e=this.__data__=new i(t);this.size=e.size}R.prototype.clear=function(){this.__data__=new i,this.size=0},R.prototype.delete=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r},R.prototype.get=function(t){return this.__data__.get(t)},R.prototype.has=function(t){return this.__data__.has(t)},R.prototype.set=function(t,e){var r=this.__data__;if(r instanceof i){var n=r.__data__;if(!F||n.length<199)return n.push([t,e]),this.size=++r.size,this;r=this.__data__=new N(n)}return r.set(t,e),this.size=r.size,this};const W=R;function H(t){var e=-1,r=null==t?0:t.length;for(this.__data__=new N;++e<r;)this.add(t[e])}H.prototype.add=H.prototype.push=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this},H.prototype.has=function(t){return this.__data__.has(t)};const q=H,G=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(e(t[r],r,t))return!0;return!1},X=function(t,e,r,n,o,a){var u=1&r,i=t.length,c=e.length;if(i!=c&&!(u&&c>i))return!1;var s=a.get(t),f=a.get(e);if(s&&f)return s==e&&f==t;var l=-1,p=!0,h=2&r?new q:void 0;for(a.set(t,e),a.set(e,t);++l<i;){var v=t[l],y=e[l];if(n)var b=u?n(y,v,l,e,t,a):n(v,y,l,t,e,a);if(void 0!==b){if(b)continue;p=!1;break}if(h){if(!G(e,(function(t,e){if(u=e,!h.has(u)&&(v===t||o(v,t,r,n,a)))return h.push(e);var u}))){p=!1;break}}else if(v!==y&&!o(v,y,r,n,a)){p=!1;break}}return a.delete(t),a.delete(e),p},J=f.Uint8Array,K=function(t){var e=-1,r=Array(t.size);return t.forEach((function(t,n){r[++e]=[n,t]})),r},Q=function(t){var e=-1,r=Array(t.size);return t.forEach((function(t){r[++e]=t})),r};var Y=l?l.prototype:void 0,Z=Y?Y.valueOf:void 0;const tt=function(t,e){for(var r=-1,n=e.length,o=t.length;++r<n;)t[o+r]=e[r];return t},et=function(t,r,n){var o=r(t);return e(t)?o:tt(o,n(t))},rt=function(){return[]};var nt=Object.prototype.propertyIsEnumerable,ot=Object.getOwnPropertySymbols;const at=ot?function(t){return null==t?[]:(t=Object(t),function(t,e){for(var r=-1,n=null==t?0:t.length,o=0,a=[];++r<n;){var u=t[r];e(u,r,t)&&(a[o++]=u)}return a}(ot(t),(function(e){return nt.call(t,e)})))}:rt,ut=function(t){return null!=t&&"object"==typeof t},it=function(t){return ut(t)&&"[object Arguments]"==d(t)};var ct=Object.prototype,st=ct.hasOwnProperty,ft=ct.propertyIsEnumerable;const lt=it(function(){return arguments}())?it:function(t){return ut(t)&&st.call(t,"callee")&&!ft.call(t,"callee")};var pt="object"==typeof exports&&exports&&!exports.nodeType&&exports,ht=pt&&"object"==typeof module&&module&&!module.nodeType&&module,vt=ht&&ht.exports===pt?f.Buffer:void 0;const yt=(vt?vt.isBuffer:void 0)||function(){return!1};var bt=/^(?:0|[1-9]\d*)$/;const _t=function(t,e){var r=typeof t;return!!(e=null==e?9007199254740991:e)&&("number"==r||"symbol"!=r&&bt.test(t))&&t>-1&&t%1==0&&t<e},dt=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991};var jt={};jt["[object Float32Array]"]=jt["[object Float64Array]"]=jt["[object Int8Array]"]=jt["[object Int16Array]"]=jt["[object Int32Array]"]=jt["[object Uint8Array]"]=jt["[object Uint8ClampedArray]"]=jt["[object Uint16Array]"]=jt["[object Uint32Array]"]=!0,jt["[object Arguments]"]=jt["[object Array]"]=jt["[object ArrayBuffer]"]=jt["[object Boolean]"]=jt["[object DataView]"]=jt["[object Date]"]=jt["[object Error]"]=jt["[object Function]"]=jt["[object Map]"]=jt["[object Number]"]=jt["[object Object]"]=jt["[object RegExp]"]=jt["[object Set]"]=jt["[object String]"]=jt["[object WeakMap]"]=!1;var gt="object"==typeof exports&&exports&&!exports.nodeType&&exports,wt=gt&&"object"==typeof module&&module&&!module.nodeType&&module,Ot=wt&&wt.exports===gt&&c.process,mt=function(){try{return wt&&wt.require&&wt.require("util").types||Ot&&Ot.binding&&Ot.binding("util")}catch(t){}}(),zt=mt&&mt.isTypedArray;const At=zt?(Pt=zt,function(t){return Pt(t)}):function(t){return ut(t)&&dt(t.length)&&!!jt[d(t)]};var Pt,St=Object.prototype.hasOwnProperty;const xt=function(t,r){var n=e(t),o=!n&&lt(t),a=!n&&!o&&yt(t),u=!n&&!o&&!a&&At(t),i=n||o||a||u,c=i?function(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}(t.length,String):[],s=c.length;for(var f in t)!r&&!St.call(t,f)||i&&("length"==f||a&&("offset"==f||"parent"==f)||u&&("buffer"==f||"byteLength"==f||"byteOffset"==f)||_t(f,s))||c.push(f);return c};var $t=Object.prototype;const Et=function(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||$t)},Tt=function(t,e){return function(r){return t(e(r))}},kt=Tt(Object.keys,Object);var Dt=Object.prototype.hasOwnProperty;const Ft=function(t){return null!=t&&dt(t.length)&&!g(t)},Bt=function(t){return Ft(t)?xt(t):function(t){if(!Et(t))return kt(t);var e=[];for(var r in Object(t))Dt.call(t,r)&&"constructor"!=r&&e.push(r);return e}(t)},Ct=function(t){return et(t,Bt,at)};var Ut=Object.prototype.hasOwnProperty;const It=D(f,"DataView"),Mt=D(f,"Promise"),Vt=D(f,"Set"),Lt=D(f,"WeakMap");var Nt="[object Map]",Rt="[object Promise]",Wt="[object Set]",Ht="[object WeakMap]",qt="[object DataView]",Gt=A(It),Xt=A(F),Jt=A(Mt),Kt=A(Vt),Qt=A(Lt),Yt=d;(It&&Yt(new It(new ArrayBuffer(1)))!=qt||F&&Yt(new F)!=Nt||Mt&&Yt(Mt.resolve())!=Rt||Vt&&Yt(new Vt)!=Wt||Lt&&Yt(new Lt)!=Ht)&&(Yt=function(t){var e=d(t),r="[object Object]"==e?t.constructor:void 0,n=r?A(r):"";if(n)switch(n){case Gt:return qt;case Xt:return Nt;case Jt:return Rt;case Kt:return Wt;case Qt:return Ht}return e});const Zt=Yt;var te="[object Arguments]",ee="[object Array]",re="[object Object]",ne=Object.prototype.hasOwnProperty;const oe=function(t,r,o,a,u,i){var c=e(t),s=e(r),f=c?ee:Zt(t),l=s?ee:Zt(r),p=(f=f==te?re:f)==re,h=(l=l==te?re:l)==re,v=f==l;if(v&&yt(t)){if(!yt(r))return!1;c=!0,p=!1}if(v&&!p)return i||(i=new W),c||At(t)?X(t,r,o,a,u,i):function(t,e,r,o,a,u,i){switch(r){case"[object DataView]":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case"[object ArrayBuffer]":return!(t.byteLength!=e.byteLength||!u(new J(t),new J(e)));case"[object Boolean]":case"[object Date]":case"[object Number]":return n(+t,+e);case"[object Error]":return t.name==e.name&&t.message==e.message;case"[object RegExp]":case"[object String]":return t==e+"";case"[object Map]":var c=K;case"[object Set]":var s=1&o;if(c||(c=Q),t.size!=e.size&&!s)return!1;var f=i.get(t);if(f)return f==e;o|=2,i.set(t,e);var l=X(c(t),c(e),o,a,u,i);return i.delete(t),l;case"[object Symbol]":if(Z)return Z.call(t)==Z.call(e)}return!1}(t,r,f,o,a,u,i);if(!(1&o)){var y=p&&ne.call(t,"__wrapped__"),b=h&&ne.call(r,"__wrapped__");if(y||b){var _=y?t.value():t,d=b?r.value():r;return i||(i=new W),u(_,d,o,a,i)}}return!!v&&(i||(i=new W),function(t,e,r,n,o,a){var u=1&r,i=Ct(t),c=i.length;if(c!=Ct(e).length&&!u)return!1;for(var s=c;s--;){var f=i[s];if(!(u?f in e:Ut.call(e,f)))return!1}var l=a.get(t),p=a.get(e);if(l&&p)return l==e&&p==t;var h=!0;a.set(t,e),a.set(e,t);for(var v=u;++s<c;){var y=t[f=i[s]],b=e[f];if(n)var _=u?n(b,y,f,e,t,a):n(y,b,f,t,e,a);if(!(void 0===_?y===b||o(y,b,r,n,a):_)){h=!1;break}v||(v="constructor"==f)}if(h&&!v){var d=t.constructor,j=e.constructor;d==j||!("constructor"in t)||!("constructor"in e)||"function"==typeof d&&d instanceof d&&"function"==typeof j&&j instanceof j||(h=!1)}return a.delete(t),a.delete(e),h}(t,r,o,a,u,i))},ae=function t(e,r,n,o,a){return e===r||(null==e||null==r||!ut(e)&&!ut(r)?e!=e&&r!=r:oe(e,r,n,o,t,a))},ue=function(t){return t==t&&!j(t)},ie=function(t,e){return function(r){return null!=r&&r[t]===e&&(void 0!==e||t in Object(r))}},ce=function(t){var e=function(t){for(var e=Bt(t),r=e.length;r--;){var n=e[r],o=t[n];e[r]=[n,o,ue(o)]}return e}(t);return 1==e.length&&e[0][2]?ie(e[0][0],e[0][1]):function(r){return r===t||function(t,e,r,n){var o=r.length,a=o,u=!n;if(null==t)return!a;for(t=Object(t);o--;){var i=r[o];if(u&&i[2]?i[1]!==t[i[0]]:!(i[0]in t))return!1}for(;++o<a;){var c=(i=r[o])[0],s=t[c],f=i[1];if(u&&i[2]){if(void 0===s&&!(c in t))return!1}else{var l=new W;if(n)var p=n(s,f,c,t,e,l);if(!(void 0===p?ae(f,s,3,n,l):p))return!1}}return!0}(r,t,e)}},se=function(t){return"symbol"==typeof t||ut(t)&&"[object Symbol]"==d(t)};var fe=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,le=/^\w*$/;const pe=function(t,r){if(e(t))return!1;var n=typeof t;return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=t&&!se(t))||le.test(t)||!fe.test(t)||null!=r&&t in Object(r)};function he(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw new TypeError("Expected a function");var r=function(){var n=arguments,o=e?e.apply(this,n):n[0],a=r.cache;if(a.has(o))return a.get(o);var u=t.apply(this,n);return r.cache=a.set(o,u)||a,u};return r.cache=new(he.Cache||N),r}he.Cache=N;var ve=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,ye=/\\(\\)?/g;const be=(_e=he((function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(ve,(function(t,r,n,o){e.push(n?o.replace(ye,"$1"):r||t)})),e}),(function(t){return 500===de.size&&de.clear(),t})),de=_e.cache,_e);var _e,de,je=l?l.prototype:void 0,ge=je?je.toString:void 0;const we=function t(n){if("string"==typeof n)return n;if(e(n))return r(n,t)+"";if(se(n))return ge?ge.call(n):"";var o=n+"";return"0"==o&&1/n==-1/0?"-0":o},Oe=function(t){return null==t?"":we(t)},me=function(t,r){return e(t)?t:pe(t,r)?[t]:be(Oe(t))},ze=function(t){if("string"==typeof t||se(t))return t;var e=t+"";return"0"==e&&1/t==-1/0?"-0":e},Ae=function(t,e){for(var r=0,n=(e=me(e,t)).length;null!=t&&r<n;)t=t[ze(e[r++])];return r&&r==n?t:void 0},Pe=function(t,e){return null!=t&&e in Object(t)},Se=function(t,r){return null!=t&&function(t,r,n){for(var o=-1,a=(r=me(r,t)).length,u=!1;++o<a;){var i=ze(r[o]);if(!(u=null!=t&&n(t,i)))break;t=t[i]}return u||++o!=a?u:!!(a=null==t?0:t.length)&&dt(a)&&_t(i,a)&&(e(t)||lt(t))}(t,r,Pe)},xe=function(t,e){return pe(t)&&ue(e)?ie(ze(t),e):function(r){var n=function(t,e,r){var n=null==t?void 0:Ae(t,e);return void 0===n?r:n}(r,t);return void 0===n&&n===e?Se(r,t):ae(e,n,3)}},$e=function(t){return t},Ee=function(t){return pe(t)?(e=ze(t),function(t){return null==t?void 0:t[e]}):function(t){return function(e){return Ae(e,t)}}(t);var e},Te=function(){try{var t=D(Object,"defineProperty");return t({},"",{}),t}catch(t){}}();var ke=Object.prototype.hasOwnProperty;const De=function(t,e,r){var o=t[e];ke.call(t,e)&&n(o,r)&&(void 0!==r||e in t)||function(t,e,r){"__proto__"==e&&Te?Te(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}(t,e,r)},Fe=function(t,e,r,n){if(!j(t))return t;for(var o=-1,a=(e=me(e,t)).length,u=a-1,i=t;null!=i&&++o<a;){var c=ze(e[o]),s=r;if("__proto__"===c||"constructor"===c||"prototype"===c)return t;if(o!=u){var f=i[c];void 0===(s=n?n(f,c,i):void 0)&&(s=j(f)?f:_t(e[o+1])?[]:{})}De(i,c,s),i=i[c]}return t},Be=Tt(Object.getPrototypeOf,Object),Ce=Object.getOwnPropertySymbols?function(t){for(var e=[];t;)tt(e,at(t)),t=Be(t);return e}:rt;var Ue=Object.prototype.hasOwnProperty;const Ie=function(t){if(!j(t))return function(t){var e=[];if(null!=t)for(var r in Object(t))e.push(r);return e}(t);var e=Et(t),r=[];for(var n in t)("constructor"!=n||!e&&Ue.call(t,n))&&r.push(n);return r},Me=function(t){return Ft(t)?xt(t,!0):Ie(t)},Ve=function(t,n){if(null==t)return{};var o,a=r(function(t){return et(t,Me,Ce)}(t),(function(t){return[t]}));return n="function"==typeof(o=n)?o:null==o?$e:"object"==typeof o?e(o)?xe(o[0],o[1]):ce(o):Ee(o),function(t,e,r){for(var n=-1,o=e.length,a={};++n<o;){var u=e[n],i=Ae(t,u);r(i,u)&&Fe(a,me(u,t),i)}return a}(t,a,(function(t,e){return n(t,e[0])}))};window.loadXpayECI18n=function(){const r=t=>import(`./json/i18n-ec/${t}/strings.json`,{with:{type:"json"}}),n=t=>import(`./json/i18n-shared-components/${t}/strings.json`,{with:{type:"json"}}),o=e=>r(e).catch((()=>r(t(e)))).catch((()=>{Promise.resolve({})})),a=e=>n(e).catch((()=>n(t(e)))).catch((()=>{Promise.resolve({})})),u=e=>n(e).catch((()=>(t=>import(`./json/i18n-e-tree-shared/${t}/strings.json`,{with:{type:"json"}}))(t(e)))).catch((()=>{Promise.resolve({})}));return async(t=null)=>{const r=window.loadTimeData?.data_?.locale||navigator.language;return"en-US"===(n=r)||"en-us"===n||"en"===n?Promise.resolve():Promise.all([o(r),a(r),u(r)]).then((r=>{const n=r[0],o=r[1],a=r[2];let u;if(!u){const{loadTimeData:t}=window;u=t}n&&n.default&&u.overrideValues(e(t)?Ve(n.default,((e,r)=>-1!==t.indexOf(r))):n.default),o&&o.default&&u.overrideValues(e(t)?Ve(o.default,((e,r)=>-1!==t.indexOf(r))):o.default),a&&a.default&&u.overrideValues(e(t)?Ve(a.default,((e,r)=>-1!==t.indexOf(r))):a.default)}));var n}}(),window.loadECData=t=>import(`./json/wallet/wallet-checkout/${t}.json`,{with:{type:"json"}})})();