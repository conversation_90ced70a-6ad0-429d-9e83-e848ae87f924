{"addNewAddressTitle": "Add a new address", "addNewCardDisabledExtensionTooltipTitleContent": "A browser extension has disabled this option. Please disable and try again.", "addNewCardDisabledPolicyTooltipTitleContent": "Adding a new card is disabled due to a policy applied by your organisation.", "addNewCardDisabledTooltipTitle": "Why can't I add a new card?", "addNewCardDisabledTooltipTitleContent": "To add a new card, update your preferences in $1.", "availableCash": "Available cash", "backToWorkflow": "Back to $1", "basicCreditCardAddError": "There was an error adding the credit or debit card. Please check your card information and try again.", "basicCreditCardSyncError": "Fail to save the card to your Microsoft account. Please check your card information and try again.", "billingInformation": "Billing information", "card": "Card", "cardAvailablity": "Card availability", "cardDetailsTitle": "Card details", "cardInformation": "Card information", "cardSeciryty": "Card security", "cashbackButtonTextExploreDeals": "Explore deals", "cashbackGetPaidButtonName": "Get paid through Microsoft Cashback", "closeQRCodeSection": "Close QR code section", "confirmDownloadCryptoWalletModalButton": "Enable", "confirmDownloadCryptoWalletModalDescription": "Our self-custody Crypto Wallet allows you to effortlessly navigate the Web3 and crypto space with peace of mind.", "confirmDownloadCryptoWalletModalTitle": "Enable Crypto Wallet to Edge?", "confirmKeepCryptoWalletModalDescriptionSuspension": "Are you sure you want to remove your crypto wallet? This action cannot be undone. Removing your wallet will permanently delete all associated data, including any stored cryptocurrencies and transaction history. Please confirm your decision to proceed or cancel this action.", "confirmKeepCryptoWalletModalTitleSuspension": "Do you want to keep Crypto Wallet ?", "confirmUninstallCryptoWalletModalButton": "Confirm", "confirmUninstallCryptoWalletModalDescription": "If you already have Crypto Wallet, this action will delete locally saved content such as your dApp connections, address book, and watchlist. Your assets and transactions will remain unaffected. Additionally, we’ll remove the Crypto Wallet from your browser, so you'll need to enable and add it again after restarting the browser. Remember to have your recovery phrase ready in case you need to restore access to your Crypto Wallet.", "confirmUninstallCryptoWalletModalTitle": "Are you sure you want to procceed?", "creditCardAddCardLabel": "Add card", "creditCardAddNewAddress": "Add new address", "creditCardBillingAddressExample": "Example: $1", "creditCardCountryTooltip": "You can't edit this info as this feature is available only in the selected country/region.", "creditCardCvvModalDescription": "To save your card to your Microsoft account, please input your CVV security code to validate card details. CVV is only used for validation and is never stored.", "creditCardCvvModalTitle": "Let’s get you verified", "creditCardEditPartialCardWithUnSupportedRegionTooltip": "Saving to Microsoft account is not currently available for cards in the region you selected. The card will be saved to this device only.", "creditCardErrorHandlingInvalidPostalCodeError": "Incorrectly formatted postcode. Example: $1", "creditCardErrorHandlingInvalidStateError": "Incorrectly formatted state", "creditCardErrorHandlingRequiredError": "This field is required", "creditCardFieldBillingAddress": "Billing address", "creditCardFieldCVV": "CVV", "creditCardFieldCVVLabelTooltipLine1": "If provided, CVV will be securely stored on this device to make autofill quicker and easier.", "creditCardFieldCVVLabelTooltipLine2": "If you save your card to your Microsoft account, it will be used to authenticate your card but is not saved by Microsoft.", "creditCardFieldCardHolderName": "Name on card", "creditCardFieldCountry": "Country/Region", "creditCardFieldExpiration": "Expiration", "creditCardFieldExpirationDate": "Expiration date", "creditCardMultiFactorAuthToFillToggleDescription": "When this is turned on, you’ll need to verify your identity before Microsoft Edge enters your card details.", "creditCardMultiFactorAuthToFillToggleDescription2": "When enabled, you will be asked to verify your identity before Microsoft Edge enters your card details.", "creditCardMultiFactorAuthToFillToggleTitle": "Require multi-factor authentication to fill", "creditCardMultiFactorAuthToFillToggleTitle2": "Always verify when using autofill", "creditCardSaveCvvLocallyToggleDescription": "When enabled, Microsoft Edge will store your security code (CVV, CVC) on this device to make check out quicker.", "creditCardSaveCvvLocallyToggleTitle": "Save card security codes on this device", "creditCardSelectAddress": "Select an address", "creditCardSyncSuccessTokenizationNotEligible": "$1 saved to your Microsoft account however a virtual card number is not currently supported", "creditCardTokenizationSuccess": "All set! Your card is now protected by Edge.", "cryptoWallet": "Crypto Wallet", "cryptoWalletFeedbackDislikeConnectivity": "Need more dApp support", "cryptoWalletFeedbackDislikeExp": "Wallet experience isn’t safe", "cryptoWalletFeedbackDislikeProductComplex": "Crypto Wallet is too complex", "cryptoWalletFeedbackDislikeSupport": "Need more support", "cryptoWalletFeedbackDislikeTransactionComplex": "Token transactions are too complex", "cryptoWalletFeedbackLikeConnectivity": "dApp support and connectivity was easy to set up", "cryptoWalletFeedbackLikeSecurity": "Security features make me feel safe", "cryptoWalletFeedbackLikeSetup": "Setup was smooth", "cryptoWalletFeedbackLikeSupport": "Learning and support was helpful", "cryptoWalletFeedbackLikeTransaction": "Token transactions are easy", "cryptoWalletFeedbackOtherOption": "Other", "cryptoWalletFeedbackThankYouText": "Thank you for your feedback. It will help us improve Crypto Wallet.", "cryptoWalletMicrofeedbackPrompt": "Satisfied with Crypto Wallet?", "cryptoWalletNotEligibleDescription": "Why is Crypto Wallet disabled in Edge?", "cryptoWalletNotEligibleReason": "Why is Crypto Wallet disabled in Edge? It may be disabled due to incompatible system requirements, such as using a Mac rather than Windows device, or not having Trusted Platform Module 2.0 enabled. $1.", "cryptoWalletRewardBanner": "Enable Crypto Wallet to earn more Microsoft Rewards Points.", "cryptoWalletSettings": "Crypto Wallet settings", "cryptoWalletSettingsMergedDescription": "Fine-tune and manage your Crypto Wallet settings and preferences.", "deleteCard": "Delete card", "deleteCardFromMSATitle": "Delete this card from your Microsoft account", "deleteCardFromMSAUnknownErrorMessage": "To delete this card from your Wallet, you need to remove it from your Microsoft account.", "deleteCardFromWalletHub": "Are you sure that you want to delete this card from your Wallet? This action cannot be undone.", "deleteCardWithSubscription": "To delete this card from your Wallet, you need to update its payment method in your Microsoft account.", "deleteLocalCardDescription": "Wallet makes browsing easier by supporting card autofill during checkout. Your card is encrypted and saved only on this device - Microsoft doesn’t have access to your card details. Deleting the card will remove it from autofill options.", "deletePaymentMethodTitle": "Delete payment method", "deleteServerCardDescription": "Wallet makes browsing easier by supporting card autofill during checkout. Your card is encrypted and saved to your Microsoft account. Deleting the card will remove it from autofill options and from your Microsoft purchases.", "deviceIneligibleDescription1": "Why can’t I use Wallet for crypto assets?", "deviceIneligibleDescription2": "Your Windows PC needs to have Trusted Platform Module 2.0 (TPM2.0) or Windows Core Isolation turned. If you don’t have these settings available, your device might not support these features.", "digitalIWalletFeedbackDislikeMembershipApprear": "I don't want my memberships to appear in the wallet", "digitalIWalletFeedbackDislikeMembershipFeatures": "I would like to save more types of memberships in the wallet", "digitalIWalletFeedbackDislikeOther": "Other", "digitalIWalletFeedbackDislikeTicketApprear": "I don't want my tickets to appear in the wallet", "digitalIWalletFeedbackDislikeTicketFeatures": "I would like to save more tickets in the Wallet", "digitalIdActionContinueEditing": "Continue editing", "digitalIdActionDelete": "Delete", "digitalIdActionDiscardChanges": "Discard changes", "digitalIdActionEdit": "Edit", "digitalIdActionMoreOption": "More", "digitalIdActions": "Actions", "digitalIdClose": "Close", "digitalIdEnterprisePasswordLabel": "Shared by your organisation", "digitalIdFinishSetup": "Finish setting up Wallet", "digitalIdMembershipAddMembership": "Add membership", "digitalIdMembershipAddMembershipCancel": "Add membership cancelled.", "digitalIdMembershipAddMembershipFail": "Add membership failed.", "digitalIdMembershipAddMembershipPage": "Add membership page", "digitalIdMembershipAddMembershipSuccess": "Add membership successfully.", "digitalIdMembershipCardCopyIconAriaLabel": "Copy card number of $1", "digitalIdMembershipCardListPageTitle": "Memberships page, $1 cards", "digitalIdMembershipCardNumberCopySuccess": "Copy membership number successfully!", "digitalIdMembershipEditMembershipCancel": "Edit membership cancelled.", "digitalIdMembershipEditMembershipFail": "Edit membership failed.", "digitalIdMembershipEditMembershipPage": "Edit membership page", "digitalIdMembershipEditMembershipSuccess": "Edit membership successfully.", "digitalIdMembershipEmptySubtitle": "Add your memberships to maximise savings and efficiency when booking and shopping online.", "digitalIdMembershipEmptyTitle": "No memberships yet", "digitalIdMembershipRemoveMembershipSuccess": "Membership has been removed.", "digitalIdMemberships": "Memberships", "digitalIdMembershipsAddMembership": "Add membership", "digitalIdMembershipsEditMembership": "Edit membership", "digitalIdMembershipsPromotionMessage": "Earn $1 Microsoft Rewards points when you add a membership before June 30th.", "digitalIdMembershipsRemoveDescription": "Are you sure you want to remove this membership from your Wallet?", "digitalIdMembershipsRemoveMembership": "Remove", "digitalIdPasswordAccountColumnHeader": "Account", "digitalIdPasswordAddPasswordFail": "Add password failed.", "digitalIdPasswordAddPasswordSuccess": "Password has been added successfully.", "digitalIdPasswordBreachAlertIgnored": "Password breach alert is ignored", "digitalIdPasswordBreachAlertShown": "Password breach alert is shown", "digitalIdPasswordCategory": "Category", "digitalIdPasswordCategoryBusiness": "Business", "digitalIdPasswordCategoryEmail": "Email", "digitalIdPasswordCategoryEntertainment": "Entertainment", "digitalIdPasswordCategoryFinance": "Finance", "digitalIdPasswordCategoryGames": "Games", "digitalIdPasswordCategoryNews": "News", "digitalIdPasswordCategoryOther": "Other", "digitalIdPasswordCategoryShopping": "Shopping", "digitalIdPasswordCategorySocialMedia": "Social Media", "digitalIdPasswordCategorySortAriaLabel": "Password website category sort", "digitalIdPasswordCategorySports": "Sports", "digitalIdPasswordCategoryTech": "Tech", "digitalIdPasswordCategoryTravel": "Travel", "digitalIdPasswordCategoryUtilities": "Utilities", "digitalIdPasswordCheckButton": "Check now", "digitalIdPasswordCheckPageTitle": "Checked passwords for $1 sites and apps", "digitalIdPasswordCheckSubpageTitle": "Password check", "digitalIdPasswordCompromisedAlertDescription": "Your account may have been compromised in a data breach. We recommend you reset your password now.", "digitalIdPasswordCompromisedAlertTitle": "Reset this password to secure your account", "digitalIdPasswordCopyPasswordFail": "Copy password failed.", "digitalIdPasswordDeclinedSitesSubpageTitle": "Declined sites and apps", "digitalIdPasswordDeletePasskey": "Delete passkey", "digitalIdPasswordDeletePasskeyConfirmationText": "Are you sure you want to delete this passkey?", "digitalIdPasswordDeletePassword": "Delete password", "digitalIdPasswordDeletePasswordConfirmationText": "This password will be removed directly.", "digitalIdPasswordDeletePasswordConfirmationTitle": "Delete password?", "digitalIdPasswordDeletePasswordFail": "Delete password failed.", "digitalIdPasswordDeletePasswordSuccess": "Password has been deleted successfully.", "digitalIdPasswordDetailsChangePasswordButton": "Change password on website", "digitalIdPasswordDetailsCopyPasswordMessage": "Password copied", "digitalIdPasswordDetailsCopyPasswordToClipboardMessage": "Password copied to clipboard", "digitalIdPasswordDetailsCopyUsernameAction": "Copy username", "digitalIdPasswordDetailsCopyUsernameMessage": "<PERSON><PERSON><PERSON> copied", "digitalIdPasswordDetailsGoToSiteAction": "Go to site", "digitalIdPasswordDetailsNoNotePlaceholder": "No note added", "digitalIdPasswordDetailsNotesFieldLabel": "Notes", "digitalIdPasswordDetailsNotesFieldPlaceholder": "Add note", "digitalIdPasswordDetailsSiteFieldLabel": "Site", "digitalIdPasswordDiscardChangeText": "Your changes are not saved and you’ll lose them.", "digitalIdPasswordDiscardChangeTitle": "Discard changes?", "digitalIdPasswordEditPasswordFail": "Edit password failed.", "digitalIdPasswordEditPasswordReminder": "Edit your saved password in wallet so it matches your new password on $1", "digitalIdPasswordEditPasswordSuccess": "Password has been updated successfully.", "digitalIdPasswordEmptyStateDescription": "Improve your browsing and autofill experience when you add or import your passwords.", "digitalIdPasswordEmptyStateTitle": "Add or import passwords to your Wallet", "digitalIdPasswordEnterpriseTooltip": "Your organisation has disabled the ability to view or copy this shared password", "digitalIdPasswordExportPasswordFail": "Export password failed.", "digitalIdPasswordExportPasswordSuccess": "Password has been exported successfully.", "digitalIdPasswordFeedbackCannotFind": "Can’t find my passwords", "digitalIdPasswordFeedbackDislikeAutofillExperience": "I don’t like the password autofill experience", "digitalIdPasswordFeedbackDislikeDisplay": "I don’t like the way passwords are displayed", "digitalIdPasswordFeedbackImporting": "I have trouble importing passwords", "digitalIdPasswordFeedbackNotAutosaved": "Password not autosaved", "digitalIdPasswordFeedbackNotSync": "Passwords not syncing across devices", "digitalIdPasswordFeedbackTroubleEditing": "I have trouble editing a password", "digitalIdPasswordFeedbackTroubleFinding": "I have trouble finding a password", "digitalIdPasswordFeedbackTroubleViewing": "I have trouble viewing my password", "digitalIdPasswordGuidanceStep": "Step $1", "digitalIdPasswordHealthStateIgnored": "Password health state is ignored", "digitalIdPasswordHealthStateShown": "Password health state is shown", "digitalIdPasswordHowThisWorks": "How this works", "digitalIdPasswordIgnoreWarningMenu": "Ignore warning", "digitalIdPasswordIgnoredPassword": "Password is ignored", "digitalIdPasswordIgnoredWarningsListTitle": "Ignored warnings", "digitalIdPasswordImportConflictDialogCancel": "Cancel import", "digitalIdPasswordImportConflictDialogDescription": "To continue importing, choose the passwords that you would like to import from below to replace the existing one.", "digitalIdPasswordImportConflictDialogReplace": "Replace", "digitalIdPasswordImportConflictDialogSkip": "<PERSON><PERSON>", "digitalIdPasswordImportConflictDialogTitle": "$1 conflicting passwords found", "digitalIdPasswordImportConflictSelectPassword": "Select password for $1", "digitalIdPasswordImportConflictSingleDialogTitle": "1 conflicting password found", "digitalIdPasswordImportConflictSuccessDescription": "For your security, we recommend deleting your password file now. Once deleted, it cannot be recovered by anyone.", "digitalIdPasswordImportConflictSuccessTitle": "$1 passwords imported successfully!", "digitalIdPasswordImportConflictSuccessWithFileDelete": "Delete $1 from your device.", "digitalIdPasswordImportFailedDescriptionConflictError": "We found $1 conflicting passwords. Resolve conflict to import other passwords.", "digitalIdPasswordImportFailedDescriptionCountError": "Import up to $1 passwords at once. Divide your file to import them all.", "digitalIdPasswordImportFailedDescriptionFormatError": "Ensure $1 is formatted correctly.", "digitalIdPasswordImportFailedDescriptionGeneral": "Something went wrong. Please try again.", "digitalIdPasswordImportFailedDescriptionSizeError": "Make sure your file is under $1. Divide file to import passwords.", "digitalIdPasswordImportFailedTitle": "Couldn't import passwords", "digitalIdPasswordImportFromFileHint": "$1 a passwords CSV file from a password manager", "digitalIdPasswordImportFromFileHintLink": "Learn how to export", "digitalIdPasswordImportOptionFromFile": "Passwords CSV file", "digitalIdPasswordImportPasswordChooseFileButton": "Choose file", "digitalIdPasswordImportSingleConflictSuccessTitle": "1 password imported successfully!", "digitalIdPasswordImportSuccessDescriptionNoAccountPlural": "Great! We've imported $1 passwords into Microsoft Password Manager on this device.", "digitalIdPasswordImportSuccessDescriptionNoAccountSingle": "Great! We've imported 1 password into Microsoft Password Manager on this device.", "digitalIdPasswordImportSuccessTitle": "Import successfully", "digitalIdPasswordInvalidURL": "Invalid URL", "digitalIdPasswordLastCheckInMinutes": "Last checked $1 minutes ago", "digitalIdPasswordLastCheckJustNow": "Just now", "digitalIdPasswordLastCheckMoreDays": "Last checked $1 days ago", "digitalIdPasswordLastCheckMoreHours": "Last checked $1 hours ago", "digitalIdPasswordLastCheckOneDay": "Last checked 1 day ago", "digitalIdPasswordLastCheckOneHour": "Last checked 1 hour ago", "digitalIdPasswordLastScanAllBreached": "No more passwords left to scan", "digitalIdPasswordLastScanExceededDaily": "Please check again after 24 hours", "digitalIdPasswordLastScanLessThanOneHour": "Last scan was less than an hour ago", "digitalIdPasswordLastScanMoreDays": "Last scan was $1 days ago", "digitalIdPasswordLastScanMoreHours": "Last scan was $1 hours ago", "digitalIdPasswordLastScanNoPasswords": "No saved passwords currently", "digitalIdPasswordLastScanOneDay": "Last scan was 1 day ago", "digitalIdPasswordLastScanOneHour": "Last scan was 1 hour ago", "digitalIdPasswordLeakedListDescription": "To secure your accounts, change these passwords now.", "digitalIdPasswordLeakedListTitle": "These passwords were found in a data breach", "digitalIdPasswordLeakedState": "Password leaked", "digitalIdPasswordLeakedTab": "Leaked", "digitalIdPasswordListChangePasswordButton": "Change password", "digitalIdPasswordListHeader": "$1 sites and apps", "digitalIdPasswordListOpenWebsiteButton": "Open website", "digitalIdPasswordManagePasskeys": "Manage passkeys", "digitalIdPasswordMonitorWorkflowFourthStep": "Change the passwords above to help secure your accounts.", "digitalIdPasswordNeverSavedPasswordTitle": "$1 never saved passwords", "digitalIdPasswordNoLeakedPasswordsDescription": "You'll get an alert if a password is leaked", "digitalIdPasswordNoLeakedPasswordsTitle": "No leaked passwords", "digitalIdPasswordNoReusedPasswordsDescription": "You aren't reusing any passwords", "digitalIdPasswordNoReusedPasswordsTitle": "Your passwords are unique", "digitalIdPasswordNoWeakPasswordsDescription": "Your passwords appear hard to guess", "digitalIdPasswordNoWeakPasswordsTitle": "You're using strong passwords", "digitalIdPasswordNoteColumnHeader": "Note", "digitalIdPasswordPasskey": "Passkey", "digitalIdPasswordPasskeyDescription": "These passkeys are saved to this device, they aren't saved to your Microsoft account.", "digitalIdPasswordPasskeyForWebsite": "You have a passkey for $1", "digitalIdPasswordPasskeyFuncDescription": "A passkey offers more secure sign-in options like your fingerprint, face scan, or screen lock to bypass traditional passwords.", "digitalIdPasswordPasskeySearchResult": "$1 accounts found for $2", "digitalIdPasswordPasskeys": "Passkeys", "digitalIdPasswordPasswordColumnHeader": "Password", "digitalIdPasswordPasswordWeakAndReusedPlural": "This password is weak and reused on $1 other websites", "digitalIdPasswordPasswordWeakAndReusedSingular": "This password is weak and reused on another website", "digitalIdPasswordRemovePasswordException": "Remove site from this list", "digitalIdPasswordResetPasswordOnWebsite": "Reset password on website", "digitalIdPasswordRestoreWarningMenu": "Restore warning", "digitalIdPasswordRestoredPassword": "Password is restored", "digitalIdPasswordReusedListDescription": "If someone discovers a reused password, it can be used to access your other accounts.", "digitalIdPasswordReusedListSubtitle": "$1 accounts using same password", "digitalIdPasswordReusedListTitle": "Use unique passwords for every site or app", "digitalIdPasswordReusedTab": "Reused", "digitalIdPasswordSaveHintNotSync": "Selecting Save only updates this password in Microsoft Edge. Make sure to update this password directly on $1.", "digitalIdPasswordSaveHintSynced": "Selecting Save only updates this password within your Microsoft account. Make sure to update this password directly on $1.", "digitalIdPasswordScanDelayed": "<PERSON><PERSON> taking longer. We will alert you once completed.", "digitalIdPasswordScanFirstRun": "No scan done yet", "digitalIdPasswordScanInProgress": "Scan in progress ($1 of $2)", "digitalIdPasswordScanNow": "Scan now", "digitalIdPasswordScanNowDisabledTooltip": "Enable leaked password scan in $1", "digitalIdPasswordScanNowErrorMessage": "<PERSON><PERSON> failed. Please try again later", "digitalIdPasswordScanResultCompleted": "Scan complete!", "digitalIdPasswordScanResultCouldNotScanCount": "$1 passwords", "digitalIdPasswordScanResultCouldNotScanText": "Couldn't scan", "digitalIdPasswordScanResultFailed": "<PERSON>an failed", "digitalIdPasswordScanResultLeakedPasswords": "$1 new leaked passwords", "digitalIdPasswordScanResultScannedPasswords": "$1 of $2 passwords scanned", "digitalIdPasswordScanResultStopped": "<PERSON><PERSON> stopped", "digitalIdPasswordScanResultTooltip": "When a password entry is missing a username or password, has been scanned in the last 24 hrs. or is already known to be leaked, then it is not scanned again", "digitalIdPasswordScanResultTryLater": "Please try again later.", "digitalIdPasswordScanRunning": "<PERSON><PERSON> already running in the background", "digitalIdPasswordSearchPasskeysPlaceholder": "Search passkeys", "digitalIdPasswordSearchResult": "$1 sites and apps found for $2", "digitalIdPasswordSecurityCheckButton": "Check", "digitalIdPasswordSecurityCheckCompleteTitle": "Password security check complete", "digitalIdPasswordSecurityCheckCompleteWithLeaked": "Your account is at risk. Change your passwords to strengthen your security.", "digitalIdPasswordSecurityCheckCompleteWithSecure": "Success! We've checked your saved passwords, and they are secure.", "digitalIdPasswordSecurityCheckCompleteWithWarning": "Boost account security by updating these at-risk passwords.", "digitalIdPasswordSecurityCheckDescription": "Review your saved passwords to strengthen your account security", "digitalIdPasswordSecurityCheckErrorTitle": "Password security check was unsuccessful. Please try again.", "digitalIdPasswordSecurityCheckInProgressBannerDetails": "$1 of $2", "digitalIdPasswordSecurityCheckInProgressBannerTitle": "Checking passwords", "digitalIdPasswordSecurityCheckInProgressDetails": "Checking $1/$2 passwords", "digitalIdPasswordSecurityCheckInProgressTitle": "Password security check in progress", "digitalIdPasswordSecurityCheckLeakedDescription": "To secure your account, change these passwords now", "digitalIdPasswordSecurityCheckThrottledGuidePlural": "You can check again in $1 hours.", "digitalIdPasswordSecurityCheckThrottledGuideSingular": "You can check again in 1 hour.", "digitalIdPasswordSecurityCheckThrottledTitle": "Password security check is up to date", "digitalIdPasswordSecurityCheckTitle": "Password security check", "digitalIdPasswordSeeDetailsActionAria": "see details", "digitalIdPasswordSelectCategory": "Select category", "digitalIdPasswordSerchWebsitePlaceholder": "Search websites", "digitalIdPasswordSiteAccounts": "$1 accounts", "digitalIdPasswordSiteBlockErrorMessage": "Your organisation does not allow saving passwords for this site.", "digitalIdPasswordStopScanning": "Stop scanning", "digitalIdPasswordThisPasswordIsCompromised": "This password is compromised", "digitalIdPasswordThisPasswordIsReusedPlural": "This password is reused on $1 other websites", "digitalIdPasswordThisPasswordIsReusedSingular": "This password is reused on another website", "digitalIdPasswordThisPasswordIsWeak": "This password is weak", "digitalIdPasswordUnscannedDialogDescription": "We couldn't scan the passwords below. You can try again by selecting \"<PERSON>an now\"", "digitalIdPasswordUnscannedDialogTitle": "$1 passwords couldn't be scanned", "digitalIdPasswordViewCheckDetailsButton": "View leakage details", "digitalIdPasswordWeakListDescription": "Weak passwords are easier to guess. Keep your accounts more secure with a stronger password.", "digitalIdPasswordWeakListTitle": "Create stronger passwords", "digitalIdPasswordWeakTab": "Weak", "digitalIdPasswordsExportMenuTooltip": "This function is disabled for managed browsers.", "digitalIdPasswordsExportMenuTooltipForGroupPolicy": "This function is disabled by your organisation.", "digitalIdPersonalInfoAddEmail": "Add email", "digitalIdPersonalInfoAddName": "Add name", "digitalIdPersonalInfoDeleteMessageSyncOff": "This personal info will be directly deleted from Edge.", "digitalIdPersonalInfoDeleteMessageSyncOn": "This personal info will be directly deleted from your Microsoft Account.", "digitalIdPersonalInfoDeleteMultiple": "Delete $1 personal info", "digitalIdPersonalInfoDeleteSingle": "Delete personal info", "digitalIdPersonalInfoDeleteUndoSuccessMessage": "Deleted address is retrieved.", "digitalIdPersonalInfoDeleteUndoretrievedMessage": "Deleted personal info is retrieved", "digitalIdPersonalInfoDeleted": "Personal info deleted", "digitalIdPersonalInfoEdit": "Edit personal info", "digitalIdPersonalInfoExportOption": "Export personal info", "digitalIdPersonalInfoFeedbackDislikeAutofillExperience": "The autofill experience is not good", "digitalIdPersonalInfoFeedbackNotAutosaved": "Personal info not autosaved", "digitalIdPersonalInfoFeedbackNotSync": "Personal info not syncing across devices", "digitalIdPersonalInfoFeedbackTroubleFinding": "I have trouble finding my personal info", "digitalIdPersonalInfoFeedbackTroubleUpdating": "I have trouble updating my personal info", "digitalIdPersonalInfoFeedbackUnnecessaryInfo": "Saved unnecessary info", "digitalIdPersonalInfoFirstName": "First name", "digitalIdPersonalInfoFullName": "Full name", "digitalIdPersonalInfoGetStart": "Add personal info to get started", "digitalIdPersonalInfoGoToSettings": "Personal info settings", "digitalIdPersonalInfoLastName": "Surname", "digitalIdPersonalInfoMiddleName": "Middle name", "digitalIdPersonalInfoMultipulDeleteMessageSyncOff": "These personal info will be directly deleted from Edge.", "digitalIdPersonalInfoMultipulDeleteMessageSyncOn": "These personal info will be directly deleted from your Microsoft Account.", "digitalIdPersonalInfoMutipulDeleted": "$1 personal info deleted", "digitalIdPersonalInfoNewAddedMessage": "New personal info added", "digitalIdPersonalInfoSaveErrorMessage": "Please enter either a name, email, phone number, or address to save", "digitalIdPersonalInfoSaveMessageSyncOn": "You can use saved personal info across Microsoft services. This info will be saved in your Microsoft Account", "digitalIdPersonalInfoSaveSuccessMessage": "Personal info updated", "digitalIdPersonalInfoSelectAddress": "Select $1", "digitalIdPersonalInfoSelectAll": "Select all", "digitalIdPersonalInfoSelected": "$1 selected", "digitalIdSettingsLinkText": "settings", "digitalIdTickets": "Tickets", "digitalIdTicketsBookingID": "Booking ID", "digitalIdTicketsCancel": "Cancel", "digitalIdTicketsCheckIn": "Check-in", "digitalIdTicketsCheckOut": "Check-out", "digitalIdTicketsEmptyDisplayOffSubtitle": "Turn syncing on to experience the one-stop service for your travel and event tickets", "digitalIdTicketsEmptyNotLogInSubtitle": "Sign in to sync your tickets to Wallet.", "digitalIdTicketsEmptyNotLogInTitle": "No tickets yet", "digitalIdTicketsEmptySubtitle": "Your tickets will be synced from Bing Travel to this wallet.", "digitalIdTicketsEmptyTitle": "No tickets yet", "digitalIdTicketsExpiredTickets": "Expired tickets", "digitalIdTicketsManageBooking": "Manage booking", "digitalIdTicketsProvidedBy": "Provided by $1", "digitalIdTicketsRemove": "Remove", "digitalIdTicketsRemoveTitle": "Remove this pass from Wallet?", "digitalIdTicketsRemovedSuccessMessage": "The ticket has been removed", "digitalIdTicketsReservationDetail": "Reservation details", "digitalIdTicketsSettingsDescription": "Easy access to all your travel data", "digitalIdTicketsSettingsTitle": "Display your travel reservation data from Bing", "digitalIdTicketsStatusBooked": "Booked", "digitalIdTicketsStatusCancelledByUser": "Cancelled By User", "digitalIdTicketsStatusCancelledByVendor": "Cancelled By <PERSON><PERSON><PERSON>", "digitalIdTicketsStatusCompleted": "Completed", "digitalIdTicketsStatusNone": "None", "digitalIdTicketsStatusPending": "Pending", "digitalIdTicketsStatusUnknown": "Unknown", "digitalIdTicketsTurnOn": "Turn it on", "digitalIdTicketsVisitExpiredTickets": "Visit $1 expired tickets", "digitalIdTicketsVisitExpiredTicketsSingular": "Visit 1 expired ticket", "digitalIdTicketsVisitOfficialWebsite": "Visit official website", "digitalIdUpdateSettingsDescription": "Saving personal info and autofill are disabled, turn them on in $1", "digitalIdUpdateSettingsTitle": "Update settings to save and fill", "digitalIdWalletFeedbackDislikeMembershipDataIssue": "Membership information saved in the wallet is inaccurate or lost", "digitalIdWalletFeedbackDislikeMembershipMoreTypes": "Request to support more membership types", "digitalIdWalletFeedbackDislikeMembershipUnclear": "Unclear how memberships work", "digitalIdWalletFeedbackDislikeMembershipUsabilityIssue": "Usability issue when checking, editing, deleting, or changing memberships", "digitalIdWalletFeedbackDislikeOther": "Other", "digitalIdWalletFeedbackDislikePasswordAutosaveAutofillIssue": "Password autosave or autofill is not working well", "digitalIdWalletFeedbackDislikePasswordCannotAccessWithPin": "Cannot access passwords with primary password or PIN", "digitalIdWalletFeedbackDislikePasswordDataIssue": "Password data saved in Wallet is inaccurate or lost", "digitalIdWalletFeedbackDislikePasswordUsabilityIssue": "Usability issue when checking, editing, deleting, or changing passwords", "digitalIdWalletFeedbackDislikePasswordsApprear": "I don’t want my passwords to appear in the wallet", "digitalIdWalletFeedbackDislikePasswordsFeatures": "I would like to see more password features", "digitalIdWalletFeedbackDislikePasswordsInaccurate": "Password information is not accurate", "digitalIdWalletFeedbackDislikePersonalInfoApprear": "I don’t want my address info to appear in the wallet", "digitalIdWalletFeedbackDislikePersonalInfoAutosaveAutofillIssue": "Personal info autosave or autofill is not working well", "digitalIdWalletFeedbackDislikePersonalInfoDataIssue": "Personal info saved in Wallet is inaccurate or lost", "digitalIdWalletFeedbackDislikePersonalInfoFeatrures": "I would like to see features to better manage my addresses", "digitalIdWalletFeedbackDislikePersonalInfoUsabilityIssue": "Usability issue when checking, editing, deleting, or changing personal info", "donateCashDisclaimer": "By making a donation, you agree to our $1 and acknowledge that your name, address, and donation amount will be shared with <PERSON>evity, in accordance with our $2, and with AOGF, in accordance with and subject to Benevity's $3 and $4. $5.$6 Non-profits are not endorsed by Microsoft. Microsoft partners with Benevity to facilitate donations. You are donating to a donor advised fund at American Online Giving Foundation (AOGF) with a recommendation that they make a corresponding grant to $7. You agree to let AOGF take exclusive legal control of your donation. Donations are non-refundable.", "donateCashDisclaimerV1": "By making a donation, you agree to our $1 and acknowledge that your name, address, and donation amount will be shared with <PERSON>evity, in accordance with our $2, and with AOGF, in accordance with and subject to Benevity's $3 and $4.$5 Non-profits are not endorsed by Microsoft. Microsoft partners with Benevity to facilitate donations. You are donating to a donor advised fund at American Online Giving Foundation (AOGF) with a recommendation that they make a corresponding grant to $6. You agree to let AOGF take exclusive legal control of your donation. Donations are non-refundable.", "donateRewardsPointsDisclaimer": "Non-profits are not endorsed by Microsoft. Microsoft partners with Benevity to donate to non-profits based on Rewards points redeemed by supporters like you. $1", "donatedBack": "Back", "donatedNPODefautlDescription": "A gift from you helps make an impact. Send a gift with your own funds or Rewards points that you've earned. After you've donated, the donation result will be sent to your email. $1 Thank you for considering a donation to $2.", "donatedPerMonth": "/month", "donatedPointsCount": "$1 points", "donationAddCardButton": "+ Add a new card", "donationAmount": "Amount", "donationAmountAriaLabel": "Donation Amont", "donationAvailablePoints": "Available points", "donationBenevityPrivacy": "Privacy Policy", "donationCVVInputError": "Invalid card verification code", "donationCVVInputTitle": "Security code(CVC/CVV)", "donationCancel": "Cancel", "donationCashPivot": "With cash", "donationClose": "Close", "donationContinueAction": "Continue", "donationDate": "Date", "donationDialogButtonAriaLabel": "$1 for $2", "donationDonateAction": "Donate", "donationDoneButton": "Done", "donationEmail": "Your receipt will be sent to", "donationEmailInvalid": "Please check your email address again", "donationEmailPlaceholder": "Email", "donationEmptyHistoryDescription": "Your donations can help make a bigger impact", "donationEmptyHistoryExploreNPO": "Explore other non-profit organisations", "donationEmptyHistoryTitle": "No donation history yet", "donationEmptySummary": "Empty", "donationExceedMaxAmount": "Donation amount can't exceed $1", "donationExploreMore": "Explore more", "donationExploreSearchPlaceholder": "Enter organisation's name", "donationExploreTab": "Explore", "donationExploreTitle": "Explore organisations around the world", "donationExportCardError": "Sorry, this donation was not created. Please try again with a different card.", "donationFailedDonation": "Failed donation", "donationFeedbackDislikeDonateMoney": "I prefer to help by other means instead of donating money", "donationFeedbackDislikeDonationProcess": "The process to donate is complicated for me", "donationFeedbackDislikeDonationUsage": "I don't know how my donation is used", "donationFeedbackDislikeOrganizationInterest": "I can't find an organisation that matches my interests", "donationFeedbackDislikeOrganizationRegion": "I can't find the organisations in my regions", "donationFeedbackDislikeOther": "Other", "donationFrequencyMonthly": "Monthly donation", "donationFrequencyOneTime": "One-time donation", "donationGenericTransactionError": "Sorry, this donation was not created because we're experiencing technical issues processing your payment. Please try again later", "donationGenericTransactionErrorWithReferenceId": "Sorry, this donation was not created because we're experiencing technical issues processing your payment. Please try again later. (reference ID: $1)", "donationGiveAction": "Give", "donationHistory": "Donation history", "donationHistoryDetailTitle": "Donation details", "donationHistoryRecurringTitle": "Thanks for your on-going gifts", "donationHistoryTab": "Donation History", "donationIncentivePointsBannerTitle": "Give now and earn $1 Microsoft Rewards points before June 30th.", "donationIncentivePointsSuccessMessage": "You've received $1 points for this giving.", "donationJoinRewards": "Join to donate with points", "donationJoinRewardsDescription": "With Microsoft Rewards, you'll earn points just for using Bing. Redeem points for free gift cards, games, and more--or donate them to a non-profit to help make a difference. $1", "donationJoinRewardsTermsAndPrivacy": "You will receive emails about Microsoft Rewards, which include offers about Microsoft and partner products. $1 | $2", "donationJoinRewardsTitle": "Join Microsoft Rewards and receive 100 points to start giving on us!", "donationLessThanMin": "Donation amount can't be less than $1", "donationLoadingMore": "Loading...", "donationMSPrivacyLink": "Microsoft Privacy statement", "donationManagement": "Donation", "donationMonthly": "Monthly", "donationNPOCardAriaLabel": "$1 card", "donationNoMoreNpos": "No more result", "donationOneTime": "One time", "donationOrganizations": "Organisations", "donationOtherAmount": "Other", "donationOtherAmountAriaLabel": "Any other donation amount(in $1)", "donationOverviewBannerTitle": "Your donations can help $1 make a bigger impact", "donationOverviewDonatedToTitle": "Spread the love again", "donationOverviewExploreMore": "Explore more", "donationOverviewHaveSupported": "You've supported", "donationOverviewSummaryDonation": "Donations you made", "donationOverviewSummaryGiving": "Lifetime giving", "donationOverviewSummarySupportNPO": "Organisations supported", "donationOverviewSummaryTitle": "Donation summary", "donationOverviewTab": "Overview", "donationOverviewTrendingNPOsSubtitle": "Recommended organisations", "donationPaymentMethodTitle": "Payment method", "donationPlanAriaLabel": "Donation Plan", "donationPointsButtonLabel": "Give $1 $2 points", "donationPointsDescription": "For every 1,000 points redeemed, Microsoft will donate at least $1 to $2", "donationPointsPivot": "With Rewards points", "donationPrivacyAndCookies": "Privacy Statement & Cookies", "donationPrivacyLink": "Privacy", "donationReceiveReceipt": "Donation receipt will be sent to $1 $2", "donationRecurringByRewardPoints": "Reward points", "donationRecurringDonateWith": "Donate with", "donationRecurringNextDate": "Next donation", "donationRecurringStartDate": "Start from", "donationRecurringStopDonation": "Stop donation", "donationRewardsLearnMoreLink": "Learn more", "donationSearchEmptyTipsContent1": "1. Check your spelling and try again", "donationSearchEmptyTipsContent2": "2. You can also $1 for more information", "donationSearchEmptyTipsContent2Link": "search \"$1\" on Bing", "donationSearchEmptyTipsTitle": "Search tips", "donationSearchEmptyTitle": "Oops! We couldn't find any results for \"$1\"", "donationSearchLoading": "Searching...", "donationSearchResult": "$1 results found for \"$2\"", "donationSeeAll": "See all", "donationSettingsShowSupportNpoDescription": "We will automatically identify when non-profit sites you visit can be supported with Microsoft Rewards points or cash donations", "donationSettingsShowSupportNpoTitle": "Show opportunities to support causes and non-profits you care about", "donationShareNpo": "Share", "donationShareNpoOnFacebook": "Facebook", "donationShareNpoOnLinkedIn": "LinkedIn", "donationShareNpoOnSocialMedia": "Share on", "donationShareNpoOnTwitter": "X", "donationShowMore": "Show more", "donationShowMoreRecurringDonation": "$1 more", "donationStopRecurringDonationDialogConfirm": "End donation", "donationStopRecurringDonationDialogContent": "Your monthly gift of $1 to $2 will end and future donations won't be made.", "donationStopRecurringDonationDialogTitle": "End monthly donation?", "donationSubmitButton": "Donate $1", "donationSuccessDescription": "Your gift of $1 $2 has been sent to $3", "donationSuccessTitle": "Thanks for your gift!", "donationSupportAgain": "Give again", "donationSupportingString": "You're supporting", "donationTermsLink": "Terms", "donationTermsOfUse": "Terms of Use", "donationTo": "Donate to $1", "donationsMicroFeedbackPromptText": "Satisfied with donations?", "dropdownDefauleSelect": "Select", "eTree": "E-tree", "eTreeActivityCountDownToTheDay": "Special Event:  $1 days left", "eTreeBackButton": "Go back to $1", "eTreeBoard": "planting board", "eTreeCancel": "Cancel", "eTreeCertificate": "My certificates", "eTreeCertificateContent": "Your tree will be the $1 tree planted with $2 in $3.", "eTreeCertificateFirstRankTag": "$1st", "eTreeCertificateLocationTitle": "Location:", "eTreeCertificateNormalRankTag": "$1th", "eTreeCertificateRankTitle": "Rank:", "eTreeCertificateSecondRankTag": "$1nd", "eTreeCertificateThirdRankTag": "$1rd", "eTreeCertificateTitle": "E-tree Certificate", "eTreeCertificateTypeTitle": "Type of tree:", "eTreeCheckCertificateButtonLabel": "View certificate", "eTreeClose": "Close", "eTreeCloseButtonLabel": "Close", "eTreeCloseLable": "Close", "eTreeCompletedStatusForDailyCheckinTask": "completed day $1", "eTreeDailyCheckInSubTitleConsecutiveCheckIn3Days": "Check in for 3 days straight and earn a holiday tile", "eTreeDailyCheckInTitle": "Daily water drops", "eTreeDailyCheckinTaksTitle": "Water drops from daily check-ins", "eTreeDailyCheckinToastLable": "$1 water drops for your daily check-in", "eTreeEnrollButton": "Get started", "eTreeEnrollDescription": "Grow a virtual seed into a tree. Once it's fully grown, a real mangrove will be planted on your behalf.", "eTreeEnrollTitle": "Plant real trees with Wallet", "eTreeErrorClaimTask": "Couldn't show tasks. Wait a bit, then try again.", "eTreeErrorDailyCheckIn": "Couldn't check in. Close and reopen E-tree to try again.", "eTreeErrorEditName": "Couldn't edit name. Wait a bit, then try again.", "eTreeErrorEnroll": "We couldn't start an E-tree. Wait a bit, then try again.", "eTreeErrorPlantTree": "Couldn't plant another tree. Wait a bit, then try again.", "eTreeErrorStartAnotherTree": "Couldn't start another tree. Wait a bit, then try again.", "eTreeErrorUnkown": "We're sorry, we're experiencing some problems. Please wait a bit and try again.", "eTreeErrorWaterTree": "Couldn't water plant. Wait a bit, then try again.", "eTreeFeedbackDislikeOption1": "I can’t complete tasks", "eTreeFeedbackDislikeOption2": "I didn’t get drops after completing tasks", "eTreeFeedbackDislikeOption3": "I didn’t enjoy the tasks", "eTreeFeedbackDislikeOption4": "I don’t want E-tree", "eTreeFeedbackDislikePrompt": "Dissatisfied with E-tree", "eTreeFeedbackLikeOption1": "I feel I’m doing good for the planet", "eTreeFeedbackLikeOption2": "E-tree is a good way to plant real trees", "eTreeFeedbackLikeOption3": "E-tree is delightful", "eTreeFeedbackLikeOption4": "I’m learning more about Wall<PERSON> through E-tree", "eTreeFeedbackLikePrompt": "Satisfied with E-tree", "eTreeFeedbackOtherOption": "Other feedback", "eTreeFeedbackThanksText": "Thank you for your feedback. It will help us improve E-tree.", "eTreeGotItButtonLabel": "Got it", "eTreeGuidebook": "Guidebook", "eTreeGuidebookAnyQuestion": "Have any questions?", "eTreeGuidebookFAQ": "FAQ", "eTreeGuidebookSeeFAQ": "See our $1", "eTreeGuidebookStep": "Step $1", "eTreeGuidebookStepFour": "Reach level 10 to plant a real tree", "eTreeGuidebookStepOne": "Get a seed", "eTreeGuidebookStepThree": "Water your tree with the water drops you collected", "eTreeGuidebookStepTwo": "Collect water drops", "eTreeGuidebookTipsOne": "Visit your tree every day.", "eTreeGuidebookTipsTitle": "Tips on water drops", "eTreeGuidebookTipsTwo": "Complete daily tasks to collect water drops.", "eTreeGuidebookTitle": "How to grow your tree?", "eTreeHalloweeGameCompeletedTip": "Come back tomorrow to get up to $1 water drops.", "eTreeHalloweeGameDescription": "Will you get tricks or treats today? Earn up to $1 water drops when you play.", "eTreeHalloweeGameFailDescription": "Play again for a chance to earn some water drops.", "eTreeHalloweeGameFailTitle": "Boo! You got a trick!", "eTreeHalloweeGameSuccessDescription": "You just got $1 water drops!$2Come back tomorrow to play again.", "eTreeHalloweeGameSuccessTitle": "You got a treat!", "eTreeHalloweeGameTitle": "Trick or treat!", "eTreeHalloweeGameWaiteDescription": "Getting your surprise ready...", "eTreeHalloweenDailyCheckInSubTitle": "Get an ornament when you check in for $1 straight", "eTreeHalloweenDailyCheckInSubTitleDays": "5 days", "eTreeHalloweenOrnamentDecorateTip": "When your tree grows a little more, you can decorate it. Water it every day to help it get bigger!", "eTreeHalloweenOrnamentDescription": "You earned it after checking in for the Halloween event. It's available until November 3.", "eTreeHalloweenOrnamentEnjoyTip": "While Halloween is over, you can enjoy this ornament until November 3.", "eTreeHalloweenOrnamentExpireTip": "The Halloween ornament won't be available tomorrow, stay tuned for more events.", "eTreeHalloweenOrnamentObtainDescription": "Decorate your tree with a Halloween ornament, available until November 3.", "eTreeHalloweenOrnamentObtainTitle": "You got a Halloween ornament!", "eTreeHalloweenOrnamentTitle": "Halloween ornament", "eTreeHide": "<PERSON>de", "eTreeHolidayEmptyFragmentsHistoryMessage": "Holiday tiles will appear here. Start collecting?", "eTreeHolidayFragmentsHistoryTitle": "Award History", "eTreeHolidayGameBoardCompleteAllTaskTitle": "Get $1 random ornament for all today‘s tasks completed.", "eTreeHolidayGameBoardCompleteAllTaskTitleNew": "Earn a holiday tile when you complete both of today's tasks.", "eTreeHolidayGameBoardDailyCkeckInTaskTitle": "Get $1 random ornament for $2 days ticked in straight.", "eTreeHolidayGameBoardDailyCkeckInTaskTitleNew": "Earn a holiday tile when you check in for 3 days straight.", "eTreeHolidayGameBoardEntryCTALabel": "Go now", "eTreeHolidayGameBoardEntryCompleteTitle": "You did it! Redeem your real tree.", "eTreeHolidayGameBoardEntryTooltip": "Click me for holiday fun!", "eTreeHolidayGameBoardEntryUncompleteTitle": "Earn holiday tiles to plant a real tree.", "eTreeHolidayGameBoardLuckyDrawTimesButtonLable": "Lucky draw x $1", "eTreeHolidayGameBoardNavigationHistoryButtonLable": "History", "eTreeHolidayGameBoardNavigationRulesButtonLable": "Rules", "eTreeHolidayGameBoardPuzzleUpATreeButtonLable": "Redeem a tree", "eTreeHolidayGameBoardPuzzleUpATreeButtonTip": "Redeem your real tree with a complete set of holiday tiles.", "eTreeHolidayGameBoardTaskFinishedLabel": "Got it today!", "eTreeHolidayGameBoardTaskProgressLabel": "Progress $1/$2", "eTreeHolidayGameBoardTitleLine1": "Collect 7 different holiday tiles to earn a real tree.", "eTreeHolidayGameBoardTitleLine2": "More fun and more trees!", "eTreeHolidayGameBoardTryLuckPromptMessage": "3 chances to win tiles or drops per day", "eTreeHolidayGameRulesExplanationPromptMessage": "How to get holiday tiles?", "eTreeHolidayGameRulesExplanationRule1": "You have 3 chances to win holiday tiles through Lucky draw each day.", "eTreeHolidayGameRulesExplanationRule2": "You can earn a new unique tile by checking in for 3 days straight.", "eTreeHolidayGameRulesExplanationRule3": "You can earn a new unique tile by completing both tasks each day.", "eTreeHolidayGameRulesExplanationRule4": "Once you collect all 7 holiday tiles, you can redeem them for a real tree that will be planted on your behalf. And you can keep going! Collect and redeem all 7 holiday pieces again, and another tree will be planted!", "eTreeHolidayGameRulesExplanationRule5": "As you advance your E-trees, each level will require more water drops to level up.", "eTreeHolidayGameRulesExplanationRule6": "After the event is over, all unused tiles for tree redemption will disappear.", "eTreeHolidayGameRulesExplanationSubTitle": "Collect 7 different holiday tiles to plant a real tree in Mozambique.", "eTreeHolidayGameRulesExplanationTitle": "Holiday Game Rules", "eTreeHolidayLuckyDrawGotEmptyDescription": "3 chances to win holiday tiles or drops each day", "eTreeHolidayLuckyDrawGotEmptyTitle": "BETTER LUCK NEXT TIME! ", "eTreeHolidayLuckyDrawGotFragmentsDescription": "You won a holiday tile", "eTreeHolidayLuckyDrawGotRewardsTitle": "CONGRATULATIONS! ", "eTreeHolidayLuckyDrawGotWaterDropsDescription": "You won $1 water drops for your tree", "eTreeHolidayLuckyDrawWaitingDescription": "Loading your surprise...", "eTreeHolidayLuckyDrawWaitingTitle": "READY TO WIN? ", "eTreeLevelTitle": "Level $1", "eTreeLocationKenya": "Kenya", "eTreeLocationMozambique": "Mozambique", "eTreeMilestoneDescription": "Help your tree reach level 10,$1and a real one will be planted on your behalf.", "eTreeMilestoneSubTitle": "You've reached $1", "eTreeMilestoneTitle": "CONGRATULATIONS!", "eTreeModalLoading": "Loading...", "eTreeModalSettingsDescription": "Grow an E-tree with <PERSON><PERSON> and we'll plant a real one on your behalf.", "eTreeModalSettingsDialogDescription": "Are you sure you want to hide E-tree? You can re-open E-tree from Set<PERSON>s page.", "eTreeModalSettingsDialogTitle": "Hide E-tree", "eTreeNameConfirmButtonLabel": "confirm tree name", "eTreeNameEditButtonLabel": "edit tree name", "eTreeNameLabel": "tree name", "eTreeOkButtonLabel": "OK", "eTreeOrganizationEden": "Eden Reforestation Projects", "eTreeOrganizationMicrosoft": "Microsoft", "eTreeOrnamentDecorateButtonLabel": "Decorate tree", "eTreeOrnamentTakeOffButtonLabel": "Take it off the tree", "eTreePlantAnotherTreeButtonLabel": "Plant another tree", "eTreePlantingCertificateArialLable": "Click to learn more about the $1 certificate.", "eTreePlantingCertificateLabel": "planting certificate", "eTreePlantingProgressBarDescription": "$1 water drops left to level up", "eTreePlayAgainButtonLabel": "Play again", "eTreePlayButtonLabel": "Play", "eTreeSettings": "Settings", "eTreeSettingsShowETreeDescription": "Grow a virtual seed into a tree with <PERSON><PERSON>. Once it's grown, a real mangrove will be planted on behalf of you.", "eTreeSettingsShowETreePrivacy": "Privacy & Cookies", "eTreeSettingsShowETreeTitle": "Show E-tree in Wallet", "eTreeTaskAddAMembershipDescription": "Add a membership to your Wallet.", "eTreeTaskAddAMembershipName": "Add a membership", "eTreeTaskAddAPaymentCardDescription": "Add a credit or debit card to your Wallet.", "eTreeTaskAddAPaymentCardName": "Add a payment method", "eTreeTaskAddAVirtualCardDescription": "Create a virtual card for eligible credit cards.", "eTreeTaskAddAVirtualCardName": "Set up a virtual card", "eTreeTaskAutofillMembershipsDescription": "Turn this on to autosave and autofill your memberships.", "eTreeTaskAutofillMembershipsName": "Autofill memberships", "eTreeTaskBoardCompletedLabel": "Daily tasks complete, see you tomorrow!", "eTreeTaskBoardRefreshLabel": "Show other tasks", "eTreeTaskBoardTitle": "Today's tasks ($1 of $2)", "eTreeTaskDonationExploreDescription": "Learn how you can help organisations that change the world.", "eTreeTaskDonationExploreName": "Explore non-profit organisations", "eTreeTaskDonationOverviewDescription": "Explore giving opportunities in Donations.", "eTreeTaskDonationOverviewName": "Do more good with <PERSON><PERSON>", "eTreeTaskOneTimeDonationDescription": "Make a one-time donation to a non-profit organisation.", "eTreeTaskOneTimeDonationName": "Spread the love", "eTreeTaskRecurringDonationDescription": "Set up monthly donations to non-profits you care about.", "eTreeTaskRecurringDonationName": "Keep giving", "eTreeTaskSyncCardDescription": "Save your card across devices.", "eTreeTaskSyncCardName": "Sync card", "eTreeTaskTipCombineRestWaterDrops": "$1 drops you left last time", "eTreeTaskTipPlantTreeReward": "$1 drops to start a new tree", "eTreeTaskUpdateCardInfoDescription": "Add card nickname or update billing address for easier use.", "eTreeTaskUpdateCardInfoName": "Update card info", "eTreeTaskUpdateMembershipInfoDescription": "Add missing name to membership.", "eTreeTaskUpdateMembershipInfoName": "Update membership info", "eTreeTaskUpdatePasswordDescription": "Update leaked password to keep your info safe.", "eTreeTaskUpdatePasswordName": "Update password", "eTreeTaskWalletHomeDescription": "Explore your Wallet homepage and learn more about it.", "eTreeTaskWalletHomeName": "Visit your Wallet", "eTreeTomorrowWaterLable": "Get $1 drops tomorrow", "eTreeTreeTypeMangrove": "Mangrove", "eTreeUncompletedStatusForDailyCheckinTask": "uncomplete day $1", "earnCashback": "Earn cashback", "earnCashbackV2": "Earn cash back", "earnRewards": "Earn rewards", "editCreditCardSuccessfully": "$1 saved", "emptyOrderDescription": "Wallet automatically tracks your orders from major retailers. Updates about your orders appear here.", "emptyOrderTitle": "There aren't orders to track yet", "enableCryptoWalletSettingsButtonTitle": "Enable", "enableCryptoWalletSettingsDescriptionLine1": "Crypto asset transactions carry a certain level of risk. Before engaging with crypto assets, carefully consider your objectives, experience, appetite for risk, and whether trading crypto assets is appropriate for your financial situation.", "enableCryptoWalletSettingsDescriptionLine2": "If you still want to engage in this space, our Crypto Wallet can help you safely access cryptocurrency and other crypto assets, engage with third-party services, and explore Web3 decentralised applications.", "enableCryptoWalletSettingsFRETitle": "Enable Crypto Wallet", "enableCryptoWalletSettingsTitle": "Turn on Crypto Wallet", "enrollCardPanelActivateVirtualCardTitle": "Activate virtual card", "enrollCardPanelSaveToMSATitle": "Save to Microsoft account", "enrollCardPanelUseVirtualCardTitle": "Use virtual card", "enrollCardToMSA": "Access this card for payments on Edge across all your devices.", "enrollCardToMSATitle": "Save to Microsoft account?", "enrollCardToMSAWithEligibleTitle": "Get more convinience and security from your card", "enrollCardTokenized": "Secure your purchases with a virtual card so merchants don't see your card number.", "enrollCardTokenizedTitle": "Get more security from your card", "errorHandlingInvalidNetworkType": "This card network is not supported at this time.", "errorHandlingUploadCardGeneralError": "To add this card to your Microsoft account, update the highlighted information: $1.", "errorHandlingUploadCardUnknownError": "To save your card to your Microsoft account, review the info below and try again.", "errorHandlingUploadCardUnknownErrorPartialCard": "Some info shown below may not be correct. Please review before you save again.", "etreeAwardSourceDescriptionCheckIn3Days": "3-day check-in", "etreeAwardSourceDescriptionCompleteAllTasks": "Daily tasks", "etreeAwardSourceDescriptionLuckyDraw": "Lucky draw", "etreeNewOrnamentToastCheckIn3Days": "You've earned a new holiday tile for checking in for 3 days straight!", "etreeNewOrnamentToastCompleteAllTasks": "You've earned a new holiday tile for completing today's tasks!", "etreePlantingProgressBarAnnounceArialLable": "Successfully watered! $1 water drops left to level up.", "etreeTaskScoreButtonArialLable": "to get $1 water drops.", "etreeWaterDropArialLable": "$1 water drops. Click the water bubbles to level up your tree.", "etreeWaterDropLabel": "$1 water drops in your E-tree", "etreeWaterDropTipFromAutofillMemberships": "Autofill memberships", "etreeWaterDropTipFromDailyCheckIn": "Daily check-in", "etreeWaterDropTipFromMembership": "Manage memberships", "etreeWaterDropTipFromOneTimeDonation": "Donate", "etreeWaterDropTipFromPayment": "Manage payments", "etreeWaterDropTipFromRecurringDonation": "Keep giving", "etreeWaterDropTipFromUpdatePassword": "Update password", "etreeWaterDropTipFromVisitDonation": "Do good", "etreeWaterDropTipFromVisitWallet": "Explore Wallet", "fallbackContent": "It's not you, but us. We're experiencing some issues. Please try again later.", "formFieldOptional": "(Optional)", "generalErrorMessage": "Something went wrong.", "giftCardRedemptionHistoryCardDetailUnavailable": "Unavailable", "giftCardRedemptionHistoryCashbackDescription": "$1 Microsoft Cashback", "giftCardRedemptionHistoryCopyCardNumber": "Copy card number", "giftCardRedemptionHistoryCopyCardNumberTips": "Card number copied to clipboard", "giftCardRedemptionHistoryCopyPin": "Copy pin", "giftCardRedemptionHistoryCopyPinTips": "Pin number copied to clipboard", "giftCardRedemptionHistoryGetCardDetailfailed": "Retrieve card details failed", "giftCardRedemptionHistoryHideCardDetails": "Hide card details", "giftCardRedemptionHistoryPinTitle": "Pin:$1", "giftCardRedemptionHistoryRetrievingCardDetails": "Retrieving card", "giftCardRedemptionHistoryRewardsDescription": "$1 Microsoft Rewards", "giftCardRedemptionHistoryShowCardDetails": "Show details", "giftCardRedemptionHistoryStatusDeclined": "The redemption wasn't completed", "giftCardRedemptionHistoryStatusDeclinedV2": "The redemption wasn't completed; reach out to customer support for further assistance", "giftCardRedemptionHistoryStatusInProgress": "The redemption is in the process of being completed", "giftCardRedemptionHistoryStatusInReview": "This redemption requires a manual review before we can proceed", "giftCardRedemptionHistoryStatusInReviewV2": "This redemption requires a manual review from the Microsoft Reward's Team before we can proceed", "giftCardRedemptionHistorySubTitle": "Your redemption history", "giftCardRedemptionHistoryTableDateColumnTitle": "Date", "giftCardRedemptionHistoryTableDescriptionTitle": "Description", "giftCardRedemptionHistoryTableDetailsTitle": "Card details", "giftCardRedemptionHistoryTableStatusDeclined": "Declined", "giftCardRedemptionHistoryTableStatusInProgress": "In Progress", "giftCardRedemptionHistoryTableStatusInReview": "In Review", "giftCardRedemptionHistoryTableStatusSent": "Redeemed", "giftCardRedemptionHistoryTableStatusTitle": "Status", "giftCardRewardCustomerServiceTitle": "Contact customer service", "gotoMicrosoftAccount": "Go to Microsoft account", "hideCvv": "<PERSON>de", "homeActionCardPersonalizedOfferCTAText": "Shop now", "homeActionCardTokenizationCTAText": "Activate now", "homeActionCardTokenizationCTAText_V2": "Set up for $1 $2", "homeActionCardTokenizationTitle": "Increase your card security and convenience with a virtual card", "homeActionExpiredCardCTAText": "Update or remove now", "homeActionExpiredCardDescription": "Edit your card information to continue use of $1 $2", "homeActionExpiredCardTitle": "Your card has expired", "homeActionExpiringCardCTAText": "Manage", "homeActionExpiringCardCTAText_V2": "Manage $1 $2", "homeActionExpiringCardExpTag": "Exp.", "homeActionExpiringCardLabelPlural": "In $1 days", "homeActionExpiringCardLabelSingular": "In $1 day", "homeActionExpiringCardTitle": "Card expiring soon", "homeActionPasswordLeakageCardCTAText": "Manage", "homeActionPasswordLeakageCardTitlePlural": "$1 passwords have appeared in leaks", "homeActionPasswordLeakageCardTitleSingular": "$1 password has appeared in leaks", "homeActionPersonalizedOfferLabel": "Offer expires in $1", "homeActionPersonalizedOfferTitle": "Up to $1 cash back", "homeActionSectionTitle": "Actions and upcoming", "homeAssetSectionTitle": "Your assets", "homeAssetsAddPaymentMethodTitle": "Add a payment method", "homeAssetsAvailableText": "$1 available", "homeAssetsMembershipViewAll": "View all in Memberships", "homeAssetsPasswordNeedAttention": "$1 items need attention", "homeAssetsPasswordTitle": "Passwords", "homeAssetsPasswordViewAll": "View all in Passwords", "homeAssetsPaymentMethodTitle": "Payment methods", "homeAssetsPaymentMethodViewAll": "View all in Payment methods", "homeFREAutosaveActionButtonText": "Turn on autosave", "homeFREAutosaveDescription": "Browsing is even easier when you autosave passwords and payment methods.", "homeFREAutosaveLearnMoreLink": "Learn more", "homeFREAutosaveTitle": "Autosave your assets", "homeFRECollapseButtonArialabel": "Close First Run Experience", "homeFRECollapseButtonArialabel2": "Collapse", "homeFRECompleteSteps": "$1 of $2 steps complete", "homeFRECompletionBannerButton": "Got it", "homeFRECompletionBannerMessage": "You've completed setup! Unlock the full potential of your Wallet, browse securely, find deals, make a difference, and more.", "homeFRECompletionBannerTitle": "Success! You've set up your Wallet.", "homeFREDescription": "Wallet makes it easy to have what you need while browsing with Edge. Here are some steps to get set up:", "homeFREExpandButtonArialabel": "Open First Run Experience", "homeFREExpandButtonArialabel2": "Expand", "homeFREMembershipActionButtonText": "Add memberships", "homeFREMembershipCompletedActionButtonText": "View", "homeFREMembershipCompletedDescription": "View and manage memberships you've already added to your Wallet.", "homeFREMembershipCompletedTitle": "View your memberships", "homeFREMembershipDescription": "View all your loyalty programmes, airline programmes, and more—all in one place.", "homeFREMembershipTitle": "Add your memberships", "homeFREPasswordActionButtonText": "View passwords", "homeFREPasswordCompletedActionButtonText": "Review", "homeFREPasswordCompletedDescription": "View saved passwords and get notified if any have been compromised.", "homeFREPasswordCompletedTitle": "Review your passwords", "homeFREPasswordDescription": "Auto save is turned on, so saving your passwords from any site is just one click.", "homeFREPasswordTitle": "Add a password", "homeFREPaymentActionButtonText": "Add card", "homeFREPaymentCompletedActionButtonText": "Manage", "homeFREPaymentCompletedDescription": "View or update your saved cards.", "homeFREPaymentCompletedTitle": "Manage payment methods", "homeFREPaymentDescription": "Start using your Wallet by adding a credit or debit card.", "homeFREPaymentTitle": "Add your first card", "homeFRESkip": "Skip setup", "homeFREStepSkipButtonText": "<PERSON><PERSON>", "homeFRETitleExpanded": "Let's get started", "homeFRETitleFolded": "Here are some steps to get set up with <PERSON><PERSON>", "homeFreSkipConfirmationModalDescription": "If you do, you'll miss out on Microsoft Rewards points. You get $1 points for each completed step and $2 points when you complete setup.", "homeFreSkipConfirmationModalDescription2": "Setting up Wallet with payment methods, passwords, and more makes Microsoft Edge even easier to use.", "homeFreSkipConfirmationModalGoBackButtonText": "Go back", "homeFreSkipConfirmationModalTitle": "Skip Wallet setup?", "homeFreStepCompletedLabel": "Completed", "homeHeaderAdd": "Add", "homeHeaderAddToWallet": "Add to Wallet", "homeHeaderMembership": "Membership", "homeHeaderPassword": "Password", "homeHeaderPersonalInformation": "Personal information", "homeMicroFeedbackPromptText": "Satisfied with <PERSON><PERSON> home?", "homeNotificationBellNoNotifications": "No notifications", "homeNotificationTitle": "Notification", "homeRewardsCardAadLinkedAccountManageLink": "Manage", "homeRewardsCardAadLinkedAccountMessage": "Includes Rewards points from your linked Microsoft account.", "homeRewardsSectionEarnWithGoalTitle": "You're almost there! Keep earning to add your next gift card to Wallet", "homeRewardsSectionEarnWithoutGoalTitle": "You're almost there! Keep earning to add gift cards to your Wallet", "homeRewardsSectionGoalProgressLabel": "$1 / $2", "homeRewardsSectionManageGoalButtonText": "Manage your goal", "homeRewardsSectionRedeemNowButtonText": "Redeem now", "homeRewardsSectionRedeemWithGoalTitle": "Woohoo! You have enough points to redeem a free gift card", "homeRewardsSectionRedeemWithoutGoalTitle": "Congrats, you've earned enough points for a gift card!", "homeRewardsSectionRewardsPointsAway": "$1 points away", "homeRewardsSectionRewardsPointsBalance": "$1 points", "homeRewardsSectionViewAllOffersButtonText": "View all offers in Rewards", "installCryptoWalletFailedError": "We're unable to download the Crypto Wallet right now. Please try again later.", "installCryptoWalletLoaingButton": "Installing...", "installCryptoWalletSettingsDescription": "A secure and safe gateway for you to explore Web3 and crypto", "installCryptoWalletSettingsTitle": "Crypto Wallet", "installCryptoWalletSuccessMessage": "Your Crypto Wallet is now enabled.", "manageCreditCardReminder": "You can manage cards saved across devices in your $1", "manageSubscription": "Manage subscription", "membershipGoToLinkLabel": "Go to $1.", "membershipGoToSettings": "Membership settings", "membershipsMicroFeedbackPromptText": "Satisfied with memberships?", "messageBarLink": "Link", "messageBarText": "We're making changes to Wallet. To learn more, please visit:", "microsoftAccount": "Microsoft account", "microsoftWalletTitle": "Microsoft Wallet", "msaUpsellCardDescription": "Securely save and access your payment info across devices when you sign in with your Microsoft account.", "myOffersFeedbackOption1": "I would like to see more offers", "myOffersFeedbackOption2": "My offers are not relevant to me", "myOffersFeedbackOption3": "I do not like the My offers experience", "myOffersFeedbackOption4": "Other", "new": "New", "nicknamePlaceholder": "Enter nickname", "nicknameSectionTitle": "Card nickname", "orderTrackingCardDescription": "The order item card for $1. Current order tracking status: $2. Click or press enter to visit this order item's website.", "orderTrackingItemLabel": "$1 in $2", "orderTrackingMicroFeedbackPromptText": "Satisfied with order tracking?", "ordersSettingsDescription": "Get shipping and delivery updates on your order", "ordersSettingsTitle": "Track your order status", "paginationShowRows": "Show rows", "paginationShowingNumberOfItems": "Showing $1 - $2 out of $3", "passwordGoToSettings": "Password settings", "passwordsMicroFeedbackPromptText": "Satisfied with passwords?", "passwordsPageHeaderAddButtonLabel": "Add password", "paymentHomeCreditCardTab": "Credit cards", "paymentHomeGiftCardTab": "Gift cards", "paymentMethodGoToSettings": "Payment method settings", "paymentMethodPageHeaderAddButtonLabel": "Add payment method", "paymentMethodsEmptyStateDescription": "Keep your card information safe while you shop across devices", "paymentMethodsEmptyStateTitle": "Add a payment method to get started with Wallet", "paymentMethodsFeedbackOption1": "It's difficult to add a new card", "paymentMethodsFeedbackOption2": "It's difficult to save a card to my Microsoft account", "paymentMethodsFeedbackOption3": "I can't find where to edit or delete a card", "paymentMethodsFeedbackOption4": "I get an error when saving or deleting a card", "paymentMethodsFeedbackOption5": "Other", "paymentMethodsMicroFeedbackPromptText": "Satisfied with payments?", "paymentsMethodsPagePaymentsCardsTabPaymentsSettingsButtonText": "Payment card settings", "paymentsMethodsPagePaymentsCardsTabTitleText": "Your saved cards", "pendingCashback": "+$1 pending", "personalInfoMicroFeedbackPromptText": "Satisfied with personal info?", "personalInfoPageHeaderAddButtonLabel": "Add personal info", "qrcodeDescription": "QR Code graphic. Scan the QR code to view and manage the passwords on your phone.", "qrcodeSectionDescription": "You can easily access your passwords everywhere in Microsoft Edge mobile app.", "qrcodeSectionDescription2": "Easy access to all your digital assets on the Edge mobile app.", "qrcodeSectionTitle": "Scan the QR code to view and manage the passwords on your phone!", "qrcodeSectionTitle2": "Scan the QR code to view and manage passwords on your phone!", "recentChanges": "Recent changes", "reload": "Reload", "removeCard": "Remove card", "removeCardFromMSA": "This payment method is associated with an active subscription. To remove this card from your Wallet, you must first change the subscription in your Microsoft account to a new payment method.", "removeCardFromMSATitle": "Remove this card from your Microsoft account", "removeCardFromMSAUnknownErrorMessage": "To remove this card from your Wallet, you need to remove it from your Microsoft account.", "removeCardFromWalletHub": "Are you sure you want to remove this card from your Wallet?", "removeCardWithSubscription": "To remove this card from your Wallet, you need to update its payment method in your Microsoft account.", "removeCardWithSubscriptionTitle": "This card is used with a subscription", "removeCryptoWalletButton": "Remove", "removeCryptoWalletFromBrowserTitle": "Crypto Wallet will be removed from Browser", "removeOrderModalDescription": "Once removed, you won't be able to view updates about this order's status.", "removeOrderModalSuccessMessage": "Order removed from tracking list", "removeOrderModalTitle": "Remove tracked order?", "removeSuccessfully": "Removed successfully", "removeTrackingButtonDescription": "Remove Tracking for $1", "removingCryptoWalletProcessTitle": "By proceeding:", "reviewCardInfoFloatingBanner": "Review your card info before you save it.", "saveCardToMicrosoftNotAvailableRegion": "Saving to Microsoft account is not currently available for cards in this billing address region.", "savingsStatementResponsive": "Save up to $1 a year", "savingsUpsellTileResonsiveText1": "Auto applied coupons at checkout", "savingsUpsellTileResonsiveText2": "Cashback on select purchases", "savingsUpsellTileResonsiveText3": "Personalised deals on products", "securityRecoveryButtonLabel": "View recovery phrase", "securityRecoverySubtitle": "Enter your password to view your recovery phrase", "settingsFeedbackDislikeMissing": "You don't have the setting I'm looking for", "settingsFeedbackDislikeOnDefault": "Settings shouldn't be turned on by default", "settingsFeedbackDislikeOptions": "There are too many options", "settingsFeedbackDislikeOther": "Other", "settingsFeedbackDislikeReset": "My settings were reset", "settingsFeedbackDislikeUnclear": "It isn't clear what each setting does", "settingsLessSettingsButtonText": "Less settings", "settingsMicroFeedbackPromptText": "Satisfied with settings?", "settingsMoreSettingsButtonText": "More settings", "settingsPasswordsAutofillPasswords": "Autofill passwords and passkeys", "settingsPasswordsAutofillPasswordsAutofillOption": "Fill website password and sign in automatically, or suggest available passkeys", "settingsPasswordsAutofillPasswordsAutofillOptionTooltip": "Your saved passwords get auto-filled in web forms. Also, you can automatically sign-in to websites where this functionality exists.", "settingsPasswordsAutofillPasswordsDescription": "Allow Microsoft Edge to automatically fill passwords and suggest available passkeys.", "settingsPasswordsAutofillPasswordsPromptCustomPasswordNoSyncDescription": "You're not syncing your passwords. If you forget your customised primary password, we won't be able to recover your data.", "settingsPasswordsAutofillPasswordsPromptCustomPasswordOption": "Prompt for the customised primary password before filling website password", "settingsPasswordsAutofillPasswordsPromptDevicePasswordOption": "Prompt for the device password before filling website password", "settingsPasswordsChangePrimaryPassword": "Change primary password", "settingsPasswordsChangePrimaryPasswordTitle": "Change your primary password", "settingsPasswordsConfirmNewPrimaryPassword": "Confirm new primary password", "settingsPasswordsCreateOrChangePrimaryPasswordNote": "Note: Your primary password will be saved only on this device and isn't shared with Microsoft. If you're using multiple devices or profiles, you'll need to use a separate primary password for each.", "settingsPasswordsCreatePrimaryPasswordTitle": "Create primary password", "settingsPasswordsCurrentCustomPasswordErrorMessage": "Password entered is incorrect. Please try again.", "settingsPasswordsCustomPasswordLengthErrorMessage": "Enter at least 4 characters", "settingsPasswordsCustomPasswordMismatchedMessage": "Please make sure your passwords match.", "settingsPasswordsEnableSavePasswords": "Offer to save passwords", "settingsPasswordsEnableSavePasswordsDescription": "Allow Microsoft Edge to save your passwords and help keep them secure", "settingsPasswordsEnterCurrentPrimaryPassword": "Enter current primary password", "settingsPasswordsEnterNewPrimaryPassword": "Enter new primary password", "settingsPasswordsEnterNewPrimaryPasswordPlaceholder": "Min. 4 characters (ex: 123abc)", "settingsPasswordsGoToSyncSettingsLinkText": "Go to sync settings", "settingsPasswordsLeakDetection": "Scan for leaked passwords", "settingsPasswordsLeakDetectionDescription": "We check your passwords saved in Edge against a known repository of exposed credentials and alert you if a match is found.", "settingsPasswordsLeakDetectionDisabledDescription": "To use this setting, sign in to Microsoft Edge", "settingsPasswordsOfferToSavePasswordsOffMessage": "Edge will no longer save or suggest passwords", "settingsPasswordsPrimaryPasswordChangedMessage": "Customised primary password changed", "settingsPasswordsPrimaryPasswordCreatedMessage": "Customised primary password created", "settingsPasswordsPrimaryPasswordWarningContent": "If you forget your primary password, you'll need to delete and create a new Edge profile. Your primary password will be saved only on this device and isn't shared with Microsoft. If you're using multiple devices or profiles, you'll need to use a separate primary password for each.", "settingsPasswordsPrimaryPasswordWarningTitle": "Your primary password is not recoverable", "settingsPasswordsPromptCustomPasswordOption": "Prompt for the customised primary password before viewing or filling website password", "settingsPasswordsPromptDevicePasswordOption": "Prompt for the device sign-in options before viewing or filling website password", "settingsPasswordsPromptOptionAlways": "Always ask permission", "settingsPasswordsPromptOptionOnce": "Ask permission once per browsing session", "settingsPasswordsRemovedPrimaryPasswordMessage": "Customised primary password removed", "settingsPasswordsShowRevealButton": "Show the \"Reveal password\" button in password fields", "settingsPasswordsShowRevealButtonDescription": "Selecting this button shows what you've typed. Some sites may override this setting", "settingsPasswordsSuggestStrong": "Suggest strong passwords", "settingsPasswordsSuggestStrongDescription": "Microsoft Edge will suggest strong passwords and, if you choose to use them, they'll be saved and filled automatically next time", "settingsPasswordsSuggestStrongDisabledDescription": "Password suggestions work when Password Sync and Offer to save passwords are both set to on", "settingsPasswordsSuggestStrongOffMessage": "Strong password suggestions are off", "settingsPasswordsViewAndAutofillPasswords": "View and autofill passwords and passkeys", "settingsPersonalInfoAISuggestion": "Enable AI-powered autofill suggestions", "settingsPersonalInfoAISuggestionDescription": "Allows AI technology to predict and fill in forms and text fields to enhance browsing experience. Your personal data is secure and won’t be used elsewhere. Please verify AI suggestions for accuracy.", "settingsPersonalInfoAutofillInfo": "Automatically fill my info on sign up forms", "settingsPersonalInfoAutofillInfoDescription": "Adds saved basic info and chooses a strong password (if \"Suggest strong passwords\" is on).", "settingsPersonalInfoMLSuggestion": "Enable machine learning powered autofill suggestions", "settingsPersonalInfoMLSuggestionDescription": "Allows ML technology to predict and fill in forms and text fields for better browsing. Your personal data is secure and will not be used elsewhere.", "settingsPersonalInfoSaveBasicInfo": "Save and fill basic info", "settingsPersonalInfoSaveBasicInfoDescription": "Also saves phone numbers, email addresses and delivery addresses", "settingsReminderFinishSettingUpWallet": "Finish setting up Wallet", "settingsReminderForMembershipInfoCleared": "Your membership info will be cleared when you close the browser", "settingsReminderForMembershipSaveAndFill": "Saving memberships and autofill are disabled, turn them on in $1.", "settingsReminderForPasswordInfoCleared": "Your password info will be cleared when you close the browser", "settingsReminderForPasswordSaveAndFill": "Saving passwords and autofill are disabled, turn them on in $1.", "settingsReminderForPaymentInfoCleared": "Your payment info will be cleared when you close the browser", "settingsReminderForPaymentSaveAndFill": "Saving payment methods and autofill are disabled, turn them on in $1.", "settingsReminderForPersonalInfoCleared": "Your personal info will be cleared when you close the browser", "settingsReminderForPersonalInfoSaveAndFill": "Saving personal info and autofill are disabled, turn them on in $1.", "settingsReminderOrganizationManageMemberships": "Your organisation doesn't allow saving memberships", "settingsReminderOrganizationManagePasswords": "Your organisation doesn't allow saving passwords", "settingsReminderOrganizationManagePaymentMethods": "Your organisation doesn't allow saving payment methods", "settingsReminderOrganizationManagePersonalInfo": "Your organisation doesn't allow saving personal info", "settingsReminderSaveNewData": "Update settings to use save new assets", "settingsReminderToAddMembership": "Add membership to get started", "settingsReminderToAddPassword": "Add password to get started", "settingsReminderToAddPaymentMethod": "Add payment method to get started", "settingsReminderToAddPersonalInfo": "Add personal info to get started", "settingsReminderToKeepDataSaved": "To keep it saved, update your $1.", "settingsReminderToUpdateToUseWallet": "Update settings to use Wallet", "settingsReminderUsePersonalAccount": "Try using a personal account to use Wallet", "settingsWalletFeedbackDislikeCannotAccess": "Cannot access data with primary password or PIN", "settingsWalletFeedbackDislikeDataLost": "Previously saved data in Wallet is lost", "settingsWalletFeedbackDislikeSecurityConcern": "Security concern with saving data", "settingsWalletFeedbackDislikeTurnOnByDefault": "Wallet is turned on by default", "shoppingDealsNav": "Shopping deals", "shoppingOffersMicroFeedbackPromptText": "Satisfied with shopping offers?", "shortcutSettingsDescription": "To get here quicker, add a shortcut to Microsoft Wallet", "shortcutSettingsTitle": "Add shortcut", "showCvv": "Show", "showLessText": "Show less", "showMoreText": "Show more", "signInUpsellButtonText": "Sign in", "signInUpsellDescription": "Sign in to back up your browsing data and see your favourites, passwords, history, and more on all your devices.", "signInUpsellTitle": "Sign in with your Microsoft account", "statusBarExploreDealsButton": "Explore deals for Microsoft Cashback", "statusBarRedeemButton": "Redeem Microsoft Rewards", "syncPending": "Sync pending", "syncPendingDescription": "We'll keep trying to sync your card info.", "tagBeta": "Beta", "ticketsMicroFeedbackPromptText": "Satisfied with tickets?", "tokenHistoryCreateCurrentValueLabel": "New card number ends in $1", "tokenHistoryCreatePreviousValueLabel": "Old card number ended in $1", "tokenHistoryCreateTitle": "Your virtual card number has been updated by $1", "tokenHistoryCreateTitleCoherence": "Card number updated by $1", "tokenHistoryExpirationDateChangeLabel": "It'll now expire on $1", "tokenHistoryExpirationDateChangeTitle": "Your card's expiration date has been updated", "tokenHistoryExpirationDateChangeTitleCoherence": "Expiration date updated", "tokenHistoryUpdateLabel": "Virtual card ends in $1", "tokenHistoryUpdateTitle": "Your virtual card has been generated.", "tokenHistoryUpdateTitleCoherence": "Virtual card generated", "tokenizedSuccessModalDescription": "Your card now has a virtual card number which will be inserted automatcially when checking out on Edge.", "tokenizedSuccessModalTitle": "Virtual card activated!", "totalPoints": "Total points", "trackOrderButton": "Track order", "trackOrderSubTitle": "Automatically track your order when you purchase from certain sites", "trackOrderTitle": "Wallet helps track your order", "uninstallCryptoWalletFailedError": "Removing Crypto Wallet fails. Please try it again", "updateBillingAddressDescription": "To update billing address, go to your $1.", "updateBillingInformationSuccess": "All set! Your billing information has been updated", "updateTokenizedCardBillingAddressFailed": "Unable to update card's address. Please check the information is correct and try again.", "updateTokenizedPartialCardCvvRequired": "To update this card, enter the CVV security code associated with your $1 ending in $2.", "updateVirtualCardNicknameSuccess": "All set! Your nickname has been updated.", "updateVirtualCardSuccess": "All set! Your virtual card has been updated", "updatingBillingInformation": "Updating billing information…", "updatingVirtualCard": "Updating virtual card…", "useVirtualCardDescription": "Use this virtual card when making purchases on Microsoft Edge.", "verifyAndEnroll": "Verify and enrol", "verifyButtonLabel": "Verify", "verifyTokenizedModalDescription": "When shopping online, Edge can substitute your card number with a virtual card number. If the merchant has a data breach, this can help protect your true card details. Your card issuer may send you a verification code.", "verifyTokenizedModalTitle": "Check out safely with virtual card?", "viewHistory": "View history", "viewRecoverPhrase": "Recovery phrase", "virtualCard": "Virtual card", "virtualCardDescription": "A virtual card secures your card details and hides them from merchants when you shop online.", "virtualCardHistoryTitle": "Virtual card history", "virtualCardUsageDescription": "Your virtual card will appear at checkout and on receipts as a card ending in", "walletComponentUpdateBannerAction": "Refresh now", "walletComponentUpdateBannerTitle": "An update is available for Wallet. Please refresh this page to use the latest version.", "walletOrdersTrackingNav": "Order tracking", "walletSearchResultsClear": "Clear", "walletSettingsNav": "Settings", "walletSettingsSearchResults": "Showing settings which match your search", "warningLevel": "Important:", "warningMessage": "Ensure you have your recovery phrase to regain access to your Crypto Wallet."}