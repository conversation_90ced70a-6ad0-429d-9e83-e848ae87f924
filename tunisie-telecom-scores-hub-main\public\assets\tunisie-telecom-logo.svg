<svg width="200" height="80" viewBox="0 0 200 80" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="ttGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1E40AF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E3A8A;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="40" cy="40" r="35" fill="url(#ttGradient)" stroke="#1E3A8A" stroke-width="2"/>
  
  <!-- TT Letters -->
  <text x="40" y="50" font-family="Arial, sans-serif" font-size="28" font-weight="bold" text-anchor="middle" fill="white">TT</text>
  
  <!-- Company name -->
  <text x="90" y="30" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#1E40AF">TUNISIE</text>
  <text x="90" y="50" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#1E40AF">TELECOM</text>
  
  <!-- Decorative elements -->
  <rect x="85" y="55" width="80" height="2" fill="#1E40AF" opacity="0.6"/>
  <circle cx="175" cy="56" r="2" fill="#1E40AF"/>
  <circle cx="182" cy="56" r="1.5" fill="#1E3A8A"/>
  <circle cx="188" cy="56" r="1" fill="#1E40AF"/>
</svg>
