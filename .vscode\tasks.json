{"version": "2.0.0", "tasks": [{"label": "start-dev-server-simple", "type": "shell", "command": "npm", "args": ["run", "dev"], "options": {"cwd": "./tunisie-telecom-scores-hub-main"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "isBackground": true, "problemMatcher": {"background": {"activeOnStart": true, "beginsPattern": ".*Local:.*", "endsPattern": ".*ready.*"}}}, {"label": "start-dev-server-direct", "type": "shell", "command": "npm", "args": ["run", "dev"], "options": {"cwd": "${workspaceFolder}\\tunisie-telecom-scores-hub-main"}, "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "isBackground": true}, {"label": "stop-dev-server", "type": "shell", "command": "taskkill", "args": ["/F", "/IM", "node.exe"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}]}