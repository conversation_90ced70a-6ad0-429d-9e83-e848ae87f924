<!doctype html><html dir="$i18n{textdirection}" lang="$i18n{language}"><head><meta charset="utf-8"><script src="/app-setup.js"></script><script src="/base-error-reporting.js"></script><meta version="40067032/20314 - 2023-10-09T11:57:22.952Z"><title>Wallet BNPL</title><script src="chrome://resources/js/load_time_data.m.js" type="module"></script><script src="/strings.m.js" type="module"></script><style>/* Copyright (C) Microsoft Corporation. All rights reserved.
 * Use of this source code is governed by a BSD-style license that can be
 * found in the LICENSE file. */

 body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    font-size: 14px;
    background-color: rgb(247, 247, 247);
    margin: 0;
  }
  
  @media (forced-colors:none) {
    input::selection {
      color: #FFF;
      background: #0078D4;
    }
  }
  
  @media (prefers-color-scheme: dark) {
    body {
      background-color: rgb(51, 51, 51);
    }
  }
  @media (forced-colors:none) and (prefers-color-scheme: dark) {
    input::selection {
      color: #000;
      /* RGBA because Blink applies an opacity otherwise */
      background: rgba(147, 184, 231, 0.996);
      opacity: 1;
    }
  }</style></head><body style="margin: 0"><div id="app-root"></div><script defer="defer" src="/bnpl.bundle.js"></script></body></html>