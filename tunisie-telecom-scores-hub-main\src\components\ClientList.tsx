
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Users, Mail, Phone, MapPin, Calendar, Star } from 'lucide-react';

interface Client {
  id: string;
  nom: string;
  prenom: string;
  score: number;
  email: string;
  telephone: string;
  adresse: string;
  notes: string;
  dateCreation: string;
}

interface ClientListProps {
  clients: Client[];
}

const ClientList = ({ clients }: ClientListProps) => {
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'bg-green-500';
    if (score >= 60) return 'bg-yellow-500';
    if (score >= 40) return 'bg-orange-500';
    return 'bg-red-500';
  };

  const getScoreLabel = (score: number) => {
    if (score >= 80) return 'Excellent';
    if (score >= 60) return 'Bon';
    if (score >= 40) return 'Moyen';
    return 'Faible';
  };

  if (clients.length === 0) {
    return (
      <Card className="border-tunisietelecom-blue/20">
        <CardContent className="p-8 text-center">
          <Users size={48} className="mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-semibold text-gray-600 mb-2">
            Aucun client enregistré
          </h3>
          <p className="text-gray-500">
            Commencez par ajouter votre premier client avec le formulaire ci-dessus.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-tunisietelecom-blue/20 shadow-lg">
      <CardHeader className="bg-gradient-to-r from-tunisietelecom-blue to-tunisietelecom-darkblue text-white">
        <CardTitle className="flex items-center gap-2">
          <Users size={20} />
          Liste des Clients ({clients.length})
        </CardTitle>
      </CardHeader>
      
      <CardContent className="p-6">
        <div className="space-y-4">
          {clients.map((client) => (
            <div
              key={client.id}
              className="border rounded-lg p-4 hover:shadow-md transition-shadow duration-200 bg-white"
            >
              <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="text-lg font-semibold text-tunisietelecom-darkgray">
                      {client.prenom} {client.nom}
                    </h3>
                    <Badge className={`${getScoreColor(client.score)} text-white`}>
                      <Star size={12} className="mr-1" />
                      {client.score}/100 - {getScoreLabel(client.score)}
                    </Badge>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-600">
                    {client.email && (
                      <div className="flex items-center gap-2">
                        <Mail size={14} className="text-tunisietelecom-blue" />
                        <span>{client.email}</span>
                      </div>
                    )}
                    
                    {client.telephone && (
                      <div className="flex items-center gap-2">
                        <Phone size={14} className="text-tunisietelecom-blue" />
                        <span>{client.telephone}</span>
                      </div>
                    )}
                    
                    {client.adresse && (
                      <div className="flex items-center gap-2">
                        <MapPin size={14} className="text-tunisietelecom-blue" />
                        <span>{client.adresse}</span>
                      </div>
                    )}
                    
                    <div className="flex items-center gap-2">
                      <Calendar size={14} className="text-tunisietelecom-blue" />
                      <span>Ajouté le {new Date(client.dateCreation).toLocaleDateString('fr-FR')}</span>
                    </div>
                  </div>
                  
                  {client.notes && (
                    <div className="mt-3 p-3 bg-gray-50 rounded-md">
                      <p className="text-sm text-gray-700">
                        <strong>Notes :</strong> {client.notes}
                      </p>
                    </div>
                  )}
                </div>
                
                <div className="flex flex-col items-center">
                  <div className={`w-16 h-16 rounded-full ${getScoreColor(client.score)} flex items-center justify-center text-white font-bold text-lg shadow-lg`}>
                    {client.score}
                  </div>
                  <span className="text-xs text-gray-500 mt-1">Score</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default ClientList;
