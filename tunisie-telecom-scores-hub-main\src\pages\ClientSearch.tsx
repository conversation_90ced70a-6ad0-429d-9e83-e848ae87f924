import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Search, User, Phone, CreditCard } from "lucide-react";
import Header from "@/components/Header";

interface ClientSearchForm {
  nom: string;
  prenom: string;
  telephone: string;
  numeroClient: string;
}

const ClientSearch = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState<ClientSearchForm>({
    nom: "",
    prenom: "",
    telephone: "",
    numeroClient: "",
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleInputChange = (field: keyof ClientSearchForm, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSearch = async () => {
    // Validation basique
    if (!formData.nom || !formData.prenom) {
      alert("Veuillez renseigner au moins le nom et prénom du client");
      return;
    }

    setIsLoading(true);
    
    // Simulation d'une recherche (remplacer par votre API)
    setTimeout(() => {
      // Rediriger vers le dashboard avec les données du client
      navigate("/dashboard", { 
        state: { 
          clientData: formData 
        } 
      });
      setIsLoading(false);
    }, 1000);
  };

  const isFormValid = formData.nom.trim() && formData.prenom.trim();

  return (
    <div className="min-h-screen bg-gray-50">
      <Header 
        isAuthenticated={true}
        onLogin={() => {}}
        onLogout={() => {}}
        onProfile={() => {}}
        userName="Utilisateur"
      />
      
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-tunisietelecom-darkgray mb-2">
              Recherche Client
            </h1>
            <p className="text-gray-600">
              Saisissez les informations du client pour consulter son score
            </p>
          </div>

          <Card className="shadow-lg">
            <CardHeader className="bg-tunisietelecom-blue text-white">
              <CardTitle className="flex items-center gap-2">
                <Search size={24} />
                Informations Client
              </CardTitle>
              <CardDescription className="text-blue-100">
                Remplissez les champs ci-dessous pour rechercher le client
              </CardDescription>
            </CardHeader>
            
            <CardContent className="p-6 space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="nom" className="flex items-center gap-2">
                    <User size={16} />
                    Nom *
                  </Label>
                  <Input
                    id="nom"
                    placeholder="Nom de famille"
                    value={formData.nom}
                    onChange={(e) => handleInputChange("nom", e.target.value)}
                    className="border-gray-300 focus:border-tunisietelecom-blue"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="prenom" className="flex items-center gap-2">
                    <User size={16} />
                    Prénom *
                  </Label>
                  <Input
                    id="prenom"
                    placeholder="Prénom"
                    value={formData.prenom}
                    onChange={(e) => handleInputChange("prenom", e.target.value)}
                    className="border-gray-300 focus:border-tunisietelecom-blue"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="telephone" className="flex items-center gap-2">
                    <Phone size={16} />
                    Numéro de téléphone
                  </Label>
                  <Input
                    id="telephone"
                    placeholder="Ex: +216 XX XXX XXX"
                    value={formData.telephone}
                    onChange={(e) => handleInputChange("telephone", e.target.value)}
                    className="border-gray-300 focus:border-tunisietelecom-blue"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="numeroClient" className="flex items-center gap-2">
                    <CreditCard size={16} />
                    Numéro client
                  </Label>
                  <Input
                    id="numeroClient"
                    placeholder="Ex: TT123456789"
                    value={formData.numeroClient}
                    onChange={(e) => handleInputChange("numeroClient", e.target.value)}
                    className="border-gray-300 focus:border-tunisietelecom-blue"
                  />
                </div>
              </div>

              <div className="pt-4">
                <Button
                  onClick={handleSearch}
                  disabled={!isFormValid || isLoading}
                  className="w-full bg-tunisietelecom-blue hover:bg-tunisietelecom-darkblue text-white py-3"
                >
                  {isLoading ? (
                    <div className="flex items-center gap-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      Recherche en cours...
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <Search size={20} />
                      Rechercher et voir le score
                    </div>
                  )}
                </Button>
              </div>

              <div className="text-sm text-gray-500 text-center">
                * Champs obligatoires
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ClientSearch;
