{"nurturing_campaign_metadata": [{"campaign_id": "Bing_AddressBarEducation", "notification_id_int": 738, "notes": "Show notifications to users who frequently search google.", "team": "Bing Growth and Distribution", "notification_scope": "profile", "triggering_data": [{"trigger_name": "frequent_google_user"}], "targeting_data": {"min_version": "92", "max_version": "150", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingFeatureBingAddressBarEducation", "should_trigger_usage": true}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 6, "secondary_button_action_id": 92, "anchor_type": "bookmark_star", "close_button_state": "show"}}, {"campaign_id": "EdgeOnMac_ReEnableOptionalData", "notification_id_int": 88, "notes": "Show dialog to make users enable optional telemetry setting.", "team": "Edge Max team", "notification_scope": "profile", "should_show_ui_without_managed_preferences": false, "triggering_data": [{"trigger_name": "re_enable_optional_data"}], "targeting_data": {"min_version": "97", "max_version": "200", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Mac OS X"], "os_version": "any"}, "dynamic_targeting_data": {"is_aad_user": false, "is_msa_minor": false, "has_aadc_risk": true}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 80, "secondary_button_action_id": 92}}, {"campaign_id": "Dummy_Campaign", "notification_id_int": 363, "notes": "Show a dummy campaign to be shown for testing dynamic histograms.", "team": "EDGE/onRamp and Growth/FRE", "notification_scope": "browser", "should_show_ui_without_managed_preferences": true, "server_driven_feature_flag_details": {"name": "msNurturingDummyCampaign", "is_flag_enabled_by_default": false, "should_trigger_usage": false}, "triggering_data": [{"trigger_name": "ipl_url_navigation"}], "targeting_data": {"min_version": "109", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT", "Mac OS X", "Linux"], "os_version": "any"}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 99, "secondary_button_action_id": 100, "anchor_type": "app_menu_button"}}, {"campaign_id": "WebPlat_IPL_Theme_Experiment", "notification_id_int": 360, "courtesy_engine_data": {"acceptance_cap": 1, "dismissal_cap": 2, "dismissal_cool_down": 7, "impression_cap": 2, "shown_cool_down": 14}, "dynamic_targeting_data": {"is_signed_in": true}, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingIplThemeExperiment", "should_trigger_usage": true}, "notes": "A banner is shown which helps user to navigate to IPL themes", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "130", "min_version": "112", "os_version": "any", "supported_os": ["Windows NT", "Mac OS X", "Linux"]}, "team": "Edge Web Platform", "triggering_data": [{"trigger_name": "ipl_url_navigation"}], "ui_data": {"surface_type": "exploratory_banner", "primary_button_action_id": 209, "secondary_button_action_id": 210, "number_of_buttons": 2, "close_button_state": "show", "banner_data": {"infobar_identifier": 1007, "show_banner_only_on_this_tab": true, "is_closeable": true}}}, {"campaign_id": "MSBBCE_BannerCampaign", "notification_id_int": 572, "dynamic_targeting_data": {"is_aad_user": true, "is_signed_in": true, "is_msb_or_bce_enabled": true}, "notes": "Show Bing Chat for Enterprise Banner Notification when user navigates to Non BCE.", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "130", "min_version": "114", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "MSB", "triggering_data": [{"trigger_name": "omnibox_navigation_nonbce"}], "ui_data": {"surface_type": "banner", "primary_button_action_id": 235, "secondary_button_action_id": 92, "number_of_buttons": 2, "banner_data": {"infobar_identifier": 1007, "show_banner_only_on_this_tab": true, "is_closeable": true}}}, {"campaign_id": "Underside_BCE_Flyout_Campaign", "notification_id_int": 582, "dynamic_targeting_data": {"is_aad_user": true, "is_signed_in": true, "is_bce_enabled": true}, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingUndersideBCEFlyoutFeature", "should_trigger_usage": true}, "notes": "Show flyout to upsell BCE summerization in Underside from Edge Desktop.", "notification_scope": "profile", "should_show_ui_without_managed_preferences": true, "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "130", "min_version": "114", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "MSB", "triggering_data": [{"trigger_name": "omnibox_navigation_sites_undersidebce"}], "ui_data": {"surface_type": "flyout", "primary_button_action_id": 244, "secondary_button_action_id": 92, "anchor_type": "app_menu_button", "number_of_buttons": 2, "close_button_state": "show", "focus_behaviour": "notify"}}, {"campaign_id": "Shopping_EnableShoppingAssistance", "notification_id_int": 608, "dynamic_targeting_data": {"exclude_on_pref_true": ["edge_shopping_assistant_enabled"], "is_san_switch_on": true, "is_shopping_reclaim_rewards_eligible": true}, "notes": "Show flyout to enable shopping assistance feature.", "notification_scope": "profile", "should_show_ui_without_managed_preferences": true, "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "130", "min_version": "118", "os_version": "any", "supported_os": ["Windows NT", "Mac OS X", "Linux"]}, "team": "Edge Shopping", "triggering_data": [{"trigger_name": "shopping_retarget"}]}, {"campaign_id": "Shopping_EnableShoppingAssistance_Banner", "notification_id_int": 723, "dynamic_targeting_data": {"exclude_on_pref_true": ["edge_shopping_assistant_enabled"], "is_san_switch_on": true, "is_shopping_reclaim_rewards_eligible": true}, "notes": "Show banner to enable shopping assistance feature.", "notification_scope": "profile", "should_show_ui_without_managed_preferences": true, "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "140", "min_version": "125", "os_version": "any", "supported_os": ["Windows NT", "Mac OS X", "Linux"]}, "team": "Edge Shopping", "triggering_data": [{"trigger_name": "shopping_retarget_banner"}]}, {"campaign_id": "Shopping_EnableShoppingAssistance_Optin_Generic_Banner", "notification_id_int": 724, "dynamic_targeting_data": {"exclude_on_pref_true": ["edge_shopping_assistant_enabled"], "is_san_switch_on": true, "is_shopping_reclaim_rewards_eligible": false}, "notes": "Show banner to enable shopping assistance feature for non-rewards users", "notification_scope": "profile", "should_show_ui_without_managed_preferences": true, "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "135", "min_version": "124", "os_version": "any", "supported_os": ["Windows NT", "Mac OS X", "Linux"]}, "team": "Edge Shopping", "triggering_data": [{"trigger_name": "shopping_retarget_banner"}, {"trigger_name": "shopping_retarget_optin_generic_banner"}, {"trigger_name": "shopping_retarget_cashback_banner"}]}, {"campaign_id": "Shopping_EnableShoppingAssistance_Cashback_Banner", "notification_id_int": 730, "dynamic_targeting_data": {"exclude_on_pref_true": ["edge_shopping_assistant_enabled"], "is_san_switch_on": true, "is_shopping_rebates_eligible": true}, "notes": "Show banner to enable shopping assistance feature for cashback users", "notification_scope": "profile", "rank": 17, "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "135", "min_version": "125", "os_version": "any", "supported_os": ["Windows NT", "Mac OS X", "Linux"]}, "team": "Edge Shopping", "triggering_data": [{"trigger_name": "shopping_retarget_cashback_banner"}], "ui_data": {"surface_type": "banner", "primary_button_action_id": 274, "secondary_button_action_id": 92, "number_of_buttons": 2, "banner_data": {"infobar_identifier": 1007, "show_banner_only_on_this_tab": true, "is_closeable": true}}}, {"campaign_id": "MSBFileSearch_BannerCampaign", "notification_id_int": 437, "dynamic_targeting_data": {"is_aad_user": true, "is_signed_in": true, "is_msb_enabled": true}, "notes": "Show Microsoft Search in Bing Banner Notification when user navigates to Bing.", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "130", "min_version": "114", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "MSB", "triggering_data": [{"trigger_name": "omnibox_navigation_bing"}], "ui_data": {"surface_type": "banner", "primary_button_action_id": 228, "secondary_button_action_id": 92, "number_of_buttons": 2, "banner_data": {"infobar_identifier": 1007, "show_banner_only_on_this_tab": true, "is_closeable": true}}}, {"campaign_id": "MSBAwarenessInMSO_BannerCampaign", "notification_id_int": 606, "dynamic_targeting_data": {"is_aad_user": true, "is_signed_in": true, "is_msb_enabled": true}, "notes": "Show Microsoft Search in Bing Banner Notification for eligibale users when user is searching on MSO.", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "130", "min_version": "118", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Microsoft Search in Bing", "triggering_data": [{"trigger_name": "omnibox_navigation_msb_while_on_mso"}], "ui_data": {"surface_type": "banner", "primary_button_action_id": 245, "secondary_button_action_id": 92, "number_of_buttons": 2, "banner_data": {"infobar_identifier": 1007, "show_banner_only_on_this_tab": true, "is_closeable": true}}}, {"campaign_id": "BCE_New_Tab_Banner_Campaign", "notification_id_int": 610, "dynamic_targeting_data": {"is_aad_user": true, "is_signed_in": true, "is_bce_enabled": true}, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingBCENewTabBannerFeature", "should_trigger_usage": true}, "notes": "Show Bing Search Enterprice Banner Notification for eligibale users when user opens a new tab.", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "130", "min_version": "119", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Microsoft Search in Bing", "triggering_data": [{"trigger_name": "open_new_tab_to_ntp"}, {"trigger_name": "new_tab_opened_to_new_tab_page"}], "ui_data": {"surface_type": "banner", "primary_button_action_id": 249, "secondary_button_action_id": 92, "number_of_buttons": 2, "banner_data": {"infobar_identifier": 1007, "show_banner_only_on_this_tab": true, "is_closeable": true}}}, {"campaign_id": "EdgeMobile_MobileUpsell_Email_AF_AnimationSearch", "notification_id_int": 13, "dynamic_targeting_data": {"has_synced_on_mobile": false, "is_pure_consumer_client": true, "more_than_number_of_days_after_fre": 21}, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": true, "name": "msFeatureMmxUpsellOnEmailAnimationAFSearch", "should_trigger_usage": false}, "group_id": "Group_EdgeMobile_MobileUpsell", "notes": "Show notification to install mobile application when user navigate 6 type high volumn urls.", "notification_scope": "browser", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "102", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "EDGE Mobile Growth", "triggering_data": [{"trigger_name": "search_url_navigation"}], "ui_data": {"is_custom_surface_type": true, "surface_type": "flyout", "anchor_type": "app_menu_button", "focus_behaviour": "notify", "waiting_state_params": {"progress": {"secondary_button_action_id": 92}, "success": {"secondary_button_action_id": 92}, "failure": {"primary_button_action_id": 45, "secondary_button_action_id": 92}}, "load_time_data_bool": {"isAnimationCampaign": true, "isThumbnailPresent": false, "isContentIconPresent": false, "showContact": true, "showFirstLinkInTab": false, "showContactPhoneNumber": false}, "load_time_data_integer": {"closeButtonAction": 92, "closeButtonState": 1, "descriptionSupportLinks": 1, "descriptionSupportAction1": 15, "footerNumberOfButtons": 2, "footerPrimaryCtaAction": 45, "footerSecondaryCtaAction": 92, "firstDescriptionLinkAction": 134}, "load_time_data_string": {"descriptionSupportLink1": "https://go.microsoft.com/fwlink/?LinkId=521839"}}}, {"campaign_id": "EdgeMobile_MobileUpsell_Email_AF_AnimationNews", "notification_id_int": 14, "dynamic_targeting_data": {"has_synced_on_mobile": false, "is_pure_consumer_client": true, "more_than_number_of_days_after_fre": 21}, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": true, "name": "msFeatureMmxUpsellOnEmailAnimationAFNews", "should_trigger_usage": false}, "group_id": "Group_EdgeMobile_MobileUpsell", "notes": "Show notification to install mobile application when user navigate 6 type high volumn urls.", "notification_scope": "browser", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "102", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "EDGE Mobile Growth", "triggering_data": [{"trigger_name": "news_url_navigation"}], "ui_data": {"is_custom_surface_type": true, "surface_type": "flyout", "anchor_type": "app_menu_button", "focus_behaviour": "notify", "waiting_state_params": {"progress": {"secondary_button_action_id": 92}, "success": {"secondary_button_action_id": 92}, "failure": {"primary_button_action_id": 45, "secondary_button_action_id": 92}}, "load_time_data_bool": {"isAnimationCampaign": true, "isThumbnailPresent": false, "isContentIconPresent": false, "showContact": true, "showFirstLinkInTab": false, "showContactPhoneNumber": false}, "load_time_data_integer": {"closeButtonAction": 92, "closeButtonState": 1, "descriptionSupportLinks": 1, "descriptionSupportAction1": 15, "footerNumberOfButtons": 2, "footerPrimaryCtaAction": 45, "footerSecondaryCtaAction": 92, "firstDescriptionLinkAction": 134}, "load_time_data_string": {"descriptionSupportLink1": "https://go.microsoft.com/fwlink/?LinkId=521839"}}}, {"campaign_id": "EdgeMobile_MobileUpsell_Email_AF_AnimationVideo", "notification_id_int": 15, "dynamic_targeting_data": {"has_synced_on_mobile": false, "is_pure_consumer_client": true, "more_than_number_of_days_after_fre": 21}, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": true, "name": "msFeatureMmxUpsellOnEmailAnimationAFVideo", "should_trigger_usage": false}, "group_id": "Group_EdgeMobile_MobileUpsell", "notes": "Show notification to install mobile application when user navigate 6 type high volumn urls.", "notification_scope": "browser", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "102", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "EDGE Mobile Growth", "triggering_data": [{"trigger_name": "video_url_navigation"}], "ui_data": {"is_custom_surface_type": true, "surface_type": "flyout", "anchor_type": "app_menu_button", "focus_behaviour": "notify", "waiting_state_params": {"progress": {"secondary_button_action_id": 92}, "success": {"secondary_button_action_id": 92}, "failure": {"primary_button_action_id": 45, "secondary_button_action_id": 92}}, "load_time_data_bool": {"isAnimationCampaign": true, "isThumbnailPresent": false, "isContentIconPresent": false, "showContact": true, "showFirstLinkInTab": false, "showContactPhoneNumber": false}, "load_time_data_integer": {"closeButtonAction": 92, "closeButtonState": 1, "descriptionSupportLinks": 1, "descriptionSupportAction1": 15, "footerNumberOfButtons": 2, "footerPrimaryCtaAction": 45, "footerSecondaryCtaAction": 92, "firstDescriptionLinkAction": 134}, "load_time_data_string": {"descriptionSupportLink1": "https://go.microsoft.com/fwlink/?LinkId=521839"}}}, {"campaign_id": "EdgeMobile_MobileUpsell_Email_AF_AnimationShopping", "notification_id_int": 16, "dynamic_targeting_data": {"has_synced_on_mobile": false, "is_pure_consumer_client": true, "more_than_number_of_days_after_fre": 21}, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msFeatureMmxUpsellOnEmailAnimationAFShopping", "should_trigger_usage": false}, "group_id": "Group_EdgeMobile_MobileUpsell", "notes": "Show notification to install mobile application when user navigate 6 type high volumn urls.", "notification_scope": "browser", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "102", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "EDGE Mobile Growth", "triggering_data": [{"trigger_name": "shopping_url_navigation"}], "ui_data": {"is_custom_surface_type": true, "surface_type": "flyout", "anchor_type": "app_menu_button", "focus_behaviour": "notify", "waiting_state_params": {"progress": {"secondary_button_action_id": 92}, "success": {"secondary_button_action_id": 92}, "failure": {"primary_button_action_id": 45, "secondary_button_action_id": 92}}, "load_time_data_bool": {"isAnimationCampaign": true, "isThumbnailPresent": false, "isContentIconPresent": false, "showContact": true, "showFirstLinkInTab": false, "showContactPhoneNumber": false}, "load_time_data_integer": {"closeButtonAction": 92, "closeButtonState": 1, "descriptionSupportLinks": 1, "descriptionSupportAction1": 15, "footerNumberOfButtons": 2, "footerPrimaryCtaAction": 45, "footerSecondaryCtaAction": 92, "firstDescriptionLinkAction": 134}, "load_time_data_string": {"descriptionSupportLink1": "https://go.microsoft.com/fwlink/?LinkId=521839"}}}, {"campaign_id": "EdgeMobile_MobileUpsell_Email_AF_AnimationMicrosoftService", "notification_id_int": 17, "dynamic_targeting_data": {"has_synced_on_mobile": false, "is_pure_consumer_client": true, "more_than_number_of_days_after_fre": 21}, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": true, "name": "msFeatureMmxUpsellOnEmailAnimationAFMicrosoftService", "should_trigger_usage": false}, "group_id": "Group_EdgeMobile_MobileUpsell", "notes": "Show notification to install mobile application when user navigate 6 type high volumn urls.", "notification_scope": "browser", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "102", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "EDGE Mobile Growth", "triggering_data": [{"trigger_name": "microsoft_service_url_navigation"}], "ui_data": {"is_custom_surface_type": true, "surface_type": "flyout", "anchor_type": "app_menu_button", "focus_behaviour": "notify", "waiting_state_params": {"progress": {"secondary_button_action_id": 92}, "success": {"secondary_button_action_id": 92}, "failure": {"primary_button_action_id": 45, "secondary_button_action_id": 92}}, "load_time_data_bool": {"isAnimationCampaign": true, "isThumbnailPresent": false, "isContentIconPresent": false, "showContact": true, "showFirstLinkInTab": false, "showContactPhoneNumber": false}, "load_time_data_integer": {"closeButtonAction": 92, "closeButtonState": 1, "descriptionSupportLinks": 1, "descriptionSupportAction1": 15, "footerNumberOfButtons": 2, "footerPrimaryCtaAction": 45, "footerSecondaryCtaAction": 92, "firstDescriptionLinkAction": 134}, "load_time_data_string": {"descriptionSupportLink1": "https://go.microsoft.com/fwlink/?LinkId=521839"}}}, {"campaign_id": "EdgeMobile_MobileUpsell_Email_AF_AnimationOther", "notification_id_int": 18, "dynamic_targeting_data": {"has_synced_on_mobile": false, "is_pure_consumer_client": true, "more_than_number_of_days_after_fre": 21}, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": true, "name": "msFeatureMmxUpsellOnEmailAnimationAFOther", "should_trigger_usage": false}, "group_id": "Group_EdgeMobile_MobileUpsell", "notes": "Show notification to install mobile application when user navigate 6 type high volumn urls.", "notification_scope": "browser", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "102", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "EDGE Mobile Growth", "triggering_data": [{"trigger_name": "mobile_other_url_navigation"}], "ui_data": {"is_custom_surface_type": true, "surface_type": "flyout", "anchor_type": "app_menu_button", "focus_behaviour": "notify", "waiting_state_params": {"progress": {"secondary_button_action_id": 92}, "success": {"secondary_button_action_id": 92}, "failure": {"primary_button_action_id": 45, "secondary_button_action_id": 92}}, "load_time_data_bool": {"isAnimationCampaign": true, "isThumbnailPresent": false, "isContentIconPresent": false, "showContact": true, "showFirstLinkInTab": false, "showContactPhoneNumber": false}, "load_time_data_integer": {"closeButtonAction": 92, "closeButtonState": 1, "descriptionSupportLinks": 1, "descriptionSupportAction1": 15, "footerNumberOfButtons": 2, "footerPrimaryCtaAction": 45, "footerSecondaryCtaAction": 92, "firstDescriptionLinkAction": 134}, "load_time_data_string": {"descriptionSupportLink1": "https://go.microsoft.com/fwlink/?LinkId=521839"}}}, {"campaign_id": "OnRamp_CIUpsellSaveFavPass", "notification_id_int": 40, "dynamic_targeting_data": {"is_aad_user": false, "is_chrome_installed": true, "is_ci_advance_consent_available": false, "is_ciupsell_targetted": true, "primary_browser": [1], "check_dma_compliance_for": "ContinuousMigration"}, "group_id": "Group_OnRamp_ContinuousImportUpsellCampaigns", "notes": "Show notification to migrate data from Chrome when favorite or password is added.", "notification_scope": "profile", "surface_id": "88000504", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "92", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "EDGE/onRamp and Growth/Migration", "triggering_data": [{"trigger_name": "favorite_added"}, {"trigger_name": "password_added"}]}, {"campaign_id": "EdgeTipping_FRE", "notification_id_int": 30, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingEdgeTipping", "should_trigger_usage": true}, "notes": "Show first run experience when user navigates to website which is eligible for tipping.", "notification_scope": "profile", "should_show_ui_without_managed_preferences": true, "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "130", "min_version": "92", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Tipping (XPay-WebXT)", "triggering_data": [{"trigger_name": "edge_tipping_first_run_experience"}], "ui_data": {"surface_type": "flyout", "anchor_type": "edge_tipping_fre", "primary_button_action_id": 18, "secondary_button_action_id": 92, "close_button_state": "show", "should_set_soft_dismiss_behaviour": true}}, {"campaign_id": "BingChina_BannerDefaultSettings", "notification_id_int": 34, "dynamic_targeting_data": {"edge_bing_pdf_startup_homepage_not_all_default": true, "is_aad_user": false}, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": true, "name": "msNurturingFeatureBannerDefaultSettingsChina", "should_trigger_usage": true}, "notes": "Show banner to upsell default settings.", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "130", "min_version": "92", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge/onRamp and Growth/Nurturing", "triggering_data": [{"trigger_name": "edge_welcome_ie_retire_url_navigation"}], "ui_data": {"surface_type": "banner", "primary_button_action_id": 131, "secondary_button_action_id": 92, "banner_data": {"infobar_identifier": 1007, "show_banner_only_on_this_tab": true, "is_closeable": false}}}, {"campaign_id": "ThirdPartySearchConsent", "notification_id_int": 48, "courtesy_engine_data": {"impression_cap": 2, "shown_cool_down": 28}, "dynamic_targeting_data": {"exclude_external_managed_devices": true, "excluded_region_list": ["AT", "BE", "BG", "HR", "CY", "CZ", "DK", "EE", "FI", "FR", "DE", "GR", "HU", "IE", "IT", "LV", "LT", "LU", "MT", "NL", "PL", "PT", "RO", "SK", "SI", "ES", "SE", "GB", "CA", "NO", "LI", "IS", "CH", "BR", "KR", "CN"], "is_msa_minor": false, "more_than_number_of_days_after_fre": 28}, "notes": "Show notification for Third Party Search consent.", "notification_scope": "profile", "should_show_ui_without_managed_preferences": true, "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "130", "min_version": "93", "os_version": "any", "supported_os": ["Windows NT", "Mac OS X"]}, "team": "Edge Privacy And Trust", "triggering_data": [{"trigger_name": "third_party_search_consent"}], "ui_data": {"surface_type": "flyout", "primary_button_action_id": 25, "secondary_button_action_id": 27, "focus_behaviour": "notify", "close_button_state": "show"}}, {"campaign_id": "ThirdPartySearchConsentEU", "notification_id_int": 49, "courtesy_engine_data": {"impression_cap": 2, "shown_cool_down": 28}, "dynamic_targeting_data": {"exclude_external_managed_devices": true, "is_msa_minor": false, "more_than_number_of_days_after_fre": 28, "targeted_region_list": ["AT", "BE", "BG", "HR", "CY", "CZ", "DK", "EE", "FI", "FR", "DE", "GR", "HU", "IE", "IT", "LV", "LT", "LU", "MT", "NL", "PL", "PT", "RO", "SK", "SI", "ES", "SE", "GB", "CA", "NO", "LI", "IS", "CH", "BR", "KR"]}, "notes": "Show notification for Third Party Search consent in the EU.", "notification_scope": "profile", "should_show_ui_without_managed_preferences": true, "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "130", "min_version": "93", "os_version": "any", "supported_os": ["Windows NT", "Mac OS X"]}, "team": "Edge Privacy And Trust", "triggering_data": [{"trigger_name": "third_party_search_consent"}], "ui_data": {"surface_type": "flyout", "primary_button_action_id": 26, "secondary_button_action_id": 25, "focus_behaviour": "notify", "close_button_state": "show"}}, {"campaign_id": "ThirdPartySearchConsentCH", "notification_id_int": 50, "courtesy_engine_data": {"impression_cap": 2, "shown_cool_down": 28}, "dynamic_targeting_data": {"exclude_external_managed_devices": true, "is_msa_minor": false, "more_than_number_of_days_after_fre": 28, "targeted_region_list": ["CN"]}, "notes": "Show notification for Third Party Search consent in China.", "notification_scope": "profile", "should_show_ui_without_managed_preferences": true, "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "130", "min_version": "93", "os_version": "any", "supported_os": ["Windows NT", "Mac OS X"]}, "team": "Edge Privacy And Trust", "triggering_data": [{"trigger_name": "third_party_search_consent"}], "ui_data": {"surface_type": "flyout", "primary_button_action_id": 26, "secondary_button_action_id": 25, "focus_behaviour": "notify", "close_button_state": "show"}}, {"campaign_id": "Bing_RecommendedSettingsWsbDialog", "notification_id_int": 59, "dynamic_targeting_data": {"bing_or_edge_not_default": true, "non_chrome_pb_cool_down_eligible": true, "has_defaults_blocking_policies": false, "is_aad_user": false, "is_protocol_launch_with_default_browser_of": {"browser_name_list": ["Opera", "Firefox", "Brave"], "excluded_region_list": ["CN"], "is_targeted": false}}, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRecommendedSettingsWsbDialog", "should_trigger_usage": false}, "group_id": "Group_Bing_BrowserSettings", "has_custom_iris_data_fetcher": false, "notes": "Show modal dialog to upsell recommended defaults when user comes from Windows Search Box.", "notification_scope": "profile", "rank": 5, "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "144", "min_version": "93", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Bing Growth and Distribution", "triggering_data": [{"trigger_name": "windows_search_box"}], "courtesy_engine_data": {"browser_launch_cooldown_applied": false}, "ui_data": {"surface_type": "modal", "primary_button_action_id": 43, "secondary_button_action_id": 92, "close_button_state": "show"}}, {"campaign_id": "Bing_RecommendedSettingsCodexNonChrome", "notification_id_int": 355, "dynamic_targeting_data": {"bing_or_edge_not_default": true, "non_chrome_pb_cool_down_eligible": true, "has_defaults_blocking_policies": false, "is_firefox_default_and_bing_chat_protocol_launch": false}, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRecommendedSettingsCodexNonChrome", "should_trigger_usage": true}, "has_custom_iris_data_fetcher": false, "notes": "Show dialog to upsell recommended defaults when user comes from the New Bing Waitlist.", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "135", "min_version": "111", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Bing Growth and Distribution", "triggering_data": [{"trigger_name": "bing_chat"}], "ui_data": {"surface_type": "modal", "primary_button_action_id": 43, "secondary_button_action_id": 92, "close_button_state": "show"}}, {"campaign_id": "EdgePerf_PerfGameMode", "notification_id_int": 162, "courtesy_engine_data": {"impression_cap": 1}, "dynamic_targeting_data": {"is_aad_user": false}, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": true, "name": "msNurturingPerfGameModeAwareness", "should_trigger_usage": false}, "notes": "Show notification to inform user about perf game mode.", "notification_scope": "profile", "should_show_ui_without_managed_preferences": true, "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "117", "min_version": "107", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "EDGE Fundamentals Performance", "triggering_data": [{"trigger_name": "perf_game_mode_promotion"}], "ui_data": {"surface_type": "coachmark", "primary_button_action_id": 161, "secondary_button_action_id": 92, "anchor_type": "app_menu_button", "arrow_type": "top_right", "number_of_buttons": 1, "focus_behaviour": "notify"}}, {"campaign_id": "EdgePerf_GamingHomepage", "notification_id_int": 386, "courtesy_engine_data": {"impression_cap": 1}, "dynamic_targeting_data": {"is_aad_user": false}, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingGamingHomepageNotification", "should_trigger_usage": true}, "notes": "Show notification directing gamers to Edge gaming homepage.", "notification_scope": "profile", "should_show_ui_without_managed_preferences": true, "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "125", "min_version": "114", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "EDGE Fundamentals Performance", "triggering_data": [{"trigger_name": "gaming_homepage_promotion"}], "ui_data": {"surface_type": "coachmark", "primary_button_action_id": 216, "secondary_button_action_id": 92, "anchor_type": "app_menu_button", "arrow_type": "top_right", "number_of_buttons": 1, "focus_behaviour": "notify"}}, {"campaign_id": "Bing_NtpHomeStartpageUpsellFlyout", "notification_id_int": 128, "courtesy_engine_data": {"dismissal_cap": 5, "impression_cap": 10, "shown_cool_down": 7}, "dynamic_targeting_data": {"excluded_region_list": ["CN", "RU"], "is_aad_user": false}, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingFeatureNtpHomeStartpageUpsellFlyout", "should_trigger_usage": true}, "group_id": "Group_Bing_NtpHomeStartpage", "notes": "Show flyout to set Homepage and Startpage to NTP upon Edge launch.", "notification_scope": "profile", "target_goal_data": {"home_or_startpage_not_ntp": false}, "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "104", "min_version": "104", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Content Services Anaheim NTP", "triggering_data": [{"trigger_name": "open_new_tab_to_ntp"}], "ui_data": {"surface_type": "flyout", "primary_button_action_id": 137, "secondary_button_action_id": 154, "anchor_type": "app_menu_button", "number_of_buttons": 2, "close_button_state": "hide", "focus_behaviour": "notify"}}, {"campaign_id": "ContinuousImport_PasswordZeroState", "notification_id_int": 92, "dynamic_targeting_data": {"exclude_on_local_state_pref_false": ["continuous_migration.is_allowed_via_policy"], "is_aad_user": false, "is_chrome_installed": true, "is_ci_advance_consent_available": false, "is_ciupsell_targetted": true, "primary_browser": [1], "check_dma_compliance_for": "ContinuousMigration"}, "notes": "Show CI campaign to users when zero password state is detected.", "notification_scope": "profile", "should_show_ui_without_managed_preferences": true, "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "99", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge/onRamp and Growth/FRE", "triggering_data": [{"trigger_name": "no_autofill_suggestions_password_form"}, {"trigger_name": "zero_password_suggestions_password_field"}], "ui_data": {"surface_type": "flyout", "primary_button_action_id": 90, "secondary_button_action_id": 92, "anchor_type": "web_content_point", "arrow_type": "left_top", "close_button_state": "show", "waiting_state_params": {"progress": {"secondary_button_action_id": 92}, "success": {"secondary_button_action_id": 92, "header_present": true, "footer_button_present": true}, "failure": {"secondary_button_action_id": 92}}}}, {"campaign_id": "ContinuousImport_PasswordZeroStateV2", "notification_id_int": 93, "courtesy_engine_data": {"acceptance_cap": 1, "catch_all_cool_down": 0, "dismissal_cap": 2, "dismissal_cool_down": 28, "impression_cap": 10, "shown_cool_down": 1, "browser_launch_cooldown_applied": false}, "dynamic_targeting_data": {"exclude_on_local_state_pref_false": ["continuous_migration.is_allowed_via_policy"], "is_aad_user": false, "is_chrome_installed": true, "is_ci_advance_consent_available": false, "is_ciupsell_targetted": true, "primary_browser": [1], "check_dma_compliance_for": "ContinuousMigration"}, "notes": "Show CI campaign to users when zero password state is detected.", "notification_scope": "profile", "should_show_ui_without_managed_preferences": true, "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "99", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge/onRamp and Growth/Migration", "triggering_data": [{"trigger_name": "no_autofill_suggestions_password_form"}, {"trigger_name": "zero_password_suggestions_password_field"}], "ui_data": {"surface_type": "flyout", "primary_button_action_id": 90, "secondary_button_action_id": 92, "anchor_type": "web_content_point", "arrow_type": "left_top", "close_button_state": "show", "waiting_state_params": {"progress": {"secondary_button_action_id": 92}, "success": {"secondary_button_action_id": 92, "header_present": true, "footer_button_present": true}, "failure": {"secondary_button_action_id": 92}}}}, {"campaign_id": "EdgeOnMac_ImportBrowserDataOnMac", "notification_id_int": 103, "dynamic_targeting_data": {"is_aad_user": false, "is_auto_import_done": false, "is_manual_import_done": false, "is_msa_minor": false, "max_saved_passwords_count": 10}, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingImportBrowserDataOnMac", "should_trigger_usage": true}, "group_id": "Group_EdgeOnMac_RetentionCampaigns", "hub_data": {"show_card_in_hub": true}, "notes": "Show dialog to make users aware to import the data from other browsers.", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "125", "min_version": "99", "os_version": "any", "supported_os": ["Mac OS X"]}, "team": "Edge Max team", "triggering_data": [{"trigger_name": "login_page_visited"}], "ui_data": {"surface_type": "flyout", "primary_button_action_id": 101, "secondary_button_action_id": 92}}, {"campaign_id": "EdgeOnMac_SafariGetFullDiskAccess", "notification_id_int": 700, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingSafariGetFullDiskAccess", "should_trigger_usage": true}, "dynamic_targeting_data": {}, "hub_data": {"show_card_in_hub": false}, "notes": "Show dialog for mac users to import data from safari", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "200", "min_version": "124", "os_version": "any", "supported_os": ["Mac OS X"]}, "team": "Edge Max team", "triggering_data": [{"trigger_name": "one_time_safari_import_mac_fulldisk_access"}], "ui_data": {"surface_type": "flyout", "primary_button_action_id": 263, "secondary_button_action_id": 92, "should_set_soft_dismiss_behaviour": true}}, {"campaign_id": "EdgeVpn_VpnPublicWifiInBackground", "notification_id_int": 112, "group_id": "Group_EdgeVpn_FirstUseCampaigns", "notes": "Show toast notification to users to try Edge VPN on public wifi.", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "134", "min_version": "101", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Web Platform Networking and Storage", "triggering_data": [{"trigger_name": "is_public_wifi_in_background"}], "ui_data": {"surface_type": "toast", "toast_data": {"toast_identifier": "EdgeVpn_VpnPublicWifiInBackground"}}}, {"campaign_id": "EdgeEDU_BrowserFocusedTranslateNudge", "notification_id_int": 196, "courtesy_engine_data": {"catch_all_cool_down": 3, "dismissal_cap": 1, "impression_cap": 3, "shown_cool_down": 14}, "dynamic_targeting_data": {"is_edge_default_browser": false, "primary_browser": [1], "targeted_region_list": ["CN", "HK", "MO", "TW"]}, "group_id": "Group_EdgeEDU_TranslateToast", "notes": "Show browser focused toast notification", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "130", "min_version": "101", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge EDU Readng and Translation", "triggering_data": [{"trigger_name": "windows_session_start"}], "ui_data": {"surface_type": "toast", "toast_data": {"toast_identifier": "EdgeEDU_BrowserFocusedTranslateNudge"}, "should_set_soft_dismiss_behaviour": true}}, {"campaign_id": "EdgeVpn_VpnFre", "notification_id_int": 113, "group_id": "Group_EdgeVpn_FirstUseCampaigns", "notes": "Show an FRE flyout to users to try Edge VPN.", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "134", "min_version": "101", "os_version": "any", "supported_os": ["Windows NT", "Mac OS X", "Linux"]}, "team": "Edge Web Platform Networking and Storage", "triggering_data": [{"trigger_name": "is_public_wifi_in_foreground"}, {"trigger_name": "inprivate_window_closed"}, {"trigger_name": "is_chrome_primary_on_launch"}, {"trigger_name": "vpn_first_new_tab"}, {"trigger_name": "unsecure_site_opened"}]}, {"campaign_id": "EdgeIndiaGrowth_PromoteShoppingOnCloseModal", "notification_id_int": 126, "dynamic_targeting_data": {"maximum_number_of_open_tabs": 5, "primary_browser": [1], "targeted_region_list": ["IN"]}, "notes": "Show modal promoting Shopping in Edge.", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "130", "min_version": "104", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge/onRamp and Growth/Nurturing", "triggering_data": [{"trigger_name": "browser_close"}], "ui_data": {"surface_type": "modal", "primary_button_action_id": 135, "secondary_button_action_id": 98, "close_button_state": "show", "escape_behaviour": "persist", "configure_close_button_for_browser_close_as_trigger": true}}, {"campaign_id": "EdgeIndiaGrowth_SitePinningOnCloseModalVariant1", "notification_id_int": 165, "dynamic_targeting_data": {"is_aad_user": false, "is_taskbar_pinning_supported": true, "targeted_region_list": ["IN"]}, "group_id": "Group_IndiaGrowth_SitePinning", "notes": "Show modal promoting sites for pinning.", "notification_scope": "browser", "targeting_data": {"channels": ["stable", "canary"], "max_version": "150", "min_version": "108", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge/Edge India Personalization/Edge India Growth", "triggering_data": [{"trigger_name": "browser_close"}], "ui_data": {"surface_type": "modal", "primary_button_action_id": 180, "secondary_button_action_id": 181, "close_button_state": "show", "escape_behaviour": "persist", "configure_close_button_for_browser_close_as_trigger": true}}, {"campaign_id": "EdgeIndiaGrowth_SitePinningOnCloseModalVariant2", "notification_id_int": 166, "dynamic_targeting_data": {"is_aad_user": false, "is_taskbar_pinning_supported": true, "targeted_region_list": ["IN"]}, "group_id": "Group_IndiaGrowth_SitePinning", "notes": "Show modal promoting sites for pinning.", "notification_scope": "browser", "targeting_data": {"channels": ["stable", "canary"], "max_version": "130", "min_version": "108", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge/Edge India Personalization/Edge India Growth", "triggering_data": [{"trigger_name": "browser_close"}], "ui_data": {"surface_type": "modal", "primary_button_action_id": 180, "secondary_button_action_id": 181, "close_button_state": "show", "escape_behaviour": "persist", "configure_close_button_for_browser_close_as_trigger": true}}, {"campaign_id": "EdgeGrowth_SitePinningWithWindowsConsent", "notification_id_int": 630, "dynamic_targeting_data": {"is_aad_user": false, "number_of_sites_pinned_to_taskbar": 0, "is_taskbar_pinning_supported": true}, "group_id": "Group_IndiaGrowth_SitePinning", "notes": "Show modal promoting sites for pinning.", "notification_scope": "browser", "targeting_data": {"channels": ["stable", "canary"], "max_version": "150", "min_version": "110", "os_version": "any", "supported_os": ["Windows NT"]}, "courtesy_engine_data": {"browser_launch_cooldown_applied": false}, "team": "Edge/Edge India Personalization/Edge India Growth", "triggering_data": [{"trigger_name": "browser_close"}], "ui_data": {"surface_type": "modal", "primary_button_action_id": 180, "secondary_button_action_id": 181, "configure_close_button_for_browser_close_as_trigger": true, "close_button_state": "show", "escape_behaviour": "persist"}}, {"campaign_id": "EdgeGrowth_GlobalSitePinningOnCloseModal", "notification_id_int": 396, "dynamic_targeting_data": {"is_aad_user": false, "is_ci_enabled_for_profile": false, "is_taskbar_pinning_supported": true}, "group_id": "Group_IndiaGrowth_SitePinning", "notes": "Show modal promoting sites for pinning.", "notification_scope": "browser", "targeting_data": {"channels": ["stable", "canary"], "max_version": "150", "min_version": "110", "os_version": "any", "supported_os": ["Windows NT"]}, "courtesy_engine_data": {"browser_launch_cooldown_applied": false, "acceptance_cap": 1}, "team": "Edge/Edge India Personalization/Edge India Growth", "triggering_data": [{"trigger_name": "browser_close"}], "ui_data": {"surface_type": "modal", "primary_button_action_id": 180, "secondary_button_action_id": 181, "configure_close_button_for_browser_close_as_trigger": true, "close_button_state": "show", "escape_behaviour": "persist"}}, {"campaign_id": "EdgeGrowth_SitePinningCITopSites", "notification_id_int": 400, "dynamic_targeting_data": {"is_aad_user": false, "is_ci_enabled_for_profile": true, "is_taskbar_pinning_supported": true}, "group_id": "Group_IndiaGrowth_SitePinning", "notes": "Show modal promoting sites for pinning.", "notification_scope": "browser", "courtesy_engine_data": {"browser_launch_cooldown_applied": false, "acceptance_cap": 1}, "targeting_data": {"channels": ["stable", "canary"], "max_version": "150", "min_version": "110", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge/Edge India Personalization/Edge India Growth", "triggering_data": [{"trigger_name": "browser_close"}], "ui_data": {"surface_type": "modal", "primary_button_action_id": 180, "secondary_button_action_id": 181, "configure_close_button_for_browser_close_as_trigger": true, "close_button_state": "show", "escape_behaviour": "persist"}}, {"campaign_id": "EdgeChinaGrowth_DoubleClickToClose", "notification_id_int": 118, "dynamic_targeting_data": {"is_double_click_to_close_tab_campaign_enabled": true}, "notes": "Promote double click to close the tab feature", "notification_scope": "profile", "should_show_ui_without_managed_preferences": true, "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "130", "min_version": "101", "os_version": "any", "supported_os": ["Windows NT", "Mac OS X", "Linux"]}, "team": "Edge China Consumer", "triggering_data": [{"trigger_name": "close_tab"}], "ui_data": {"surface_type": "flyout", "primary_button_action_id": 125, "secondary_button_action_id": 92, "anchor_type": "current_tab", "close_button_state": "show", "should_set_soft_dismiss_behaviour": true, "arrow_type": "top_left"}}, {"campaign_id": "EdgeChinaGrowth_EdgeBarCNUpsell", "notification_id_int": 685, "notes": "Show notification to promote Edge Bar in CN", "team": "Edge China Consumer", "notification_scope": "profile", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "edge_bar_cn_upsell"}], "server_driven_feature_flag_details": {"name": "msNurturingEdgeBarCNUpsell", "is_flag_enabled_by_default": false, "should_trigger_usage": true}, "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "min_version": "122", "max_version": "130", "supported_os": ["Windows NT"], "os_version": "any"}, "dynamic_targeting_data": {"targeted_region_list": ["CN"]}, "courtesy_engine_data": {"dismissal_cap": 2, "impression_cap": 2, "dismissal_cool_down": "28", "shown_cool_down": "28", "browser_launch_cooldown_applied": false}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 264, "secondary_button_action_id": 92, "anchor_type": "app_menu_button", "should_set_soft_dismiss_behaviour": true, "close_button_state": "show"}}, {"campaign_id": "EdgeChinaGrowth_MouseGesture", "notification_id_int": 150, "courtesy_engine_data": {"acceptance_cap": 1, "dismissal_cap": 1, "impression_cap": 1}, "dynamic_targeting_data": {"exclude_on_pref_true": ["edge_mouse_gesture.enabled"], "excluded_on_policies": ["edge_mouse_gesture.enabled"]}, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingMouseGesture", "should_trigger_usage": true}, "notes": "Promote mouse gesture feature", "notification_scope": "profile", "should_show_ui_without_managed_preferences": true, "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "130", "min_version": "106", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge China Consumer", "triggering_data": [{"trigger_name": "mouse_gesture_go_back_navigation"}], "ui_data": {"surface_type": "flyout", "primary_button_action_id": 158, "secondary_button_action_id": 92, "anchor_type": "first_tab", "should_set_soft_dismiss_behaviour": false, "close_button_state": "show"}}, {"campaign_id": "Identity_RemoveIgnoreCAWV2", "notification_id_int": 120, "courtesy_engine_data": {"dismissal_cap": 1, "impression_cap": 3}, "dynamic_targeting_data": {"is_aad_user": false, "is_signed_in": true}, "notes": "Show v2 prompt to remove the IgnoreCAW cookie.", "notification_scope": "profile", "should_show_ui_without_managed_preferences": true, "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "130", "min_version": "100", "os_version": "any", "supported_os": ["Windows NT", "Mac OS X", "Linux"]}, "team": "Edge Identity Foundation", "triggering_data": [{"trigger_name": "ignore_caw_prompt_v2"}]}, {"campaign_id": "Bing_DefaultBrowserBannerCloseBtnUpdate", "notification_id_int": 125, "courtesy_engine_data": {"browser_launch_cooldown_applied": false}, "dynamic_targeting_data": {"default_browser_banner_capped": true, "excluded_region_list": ["CN"], "has_defaults_blocking_policies": false, "is_edge_default_browser": false}, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingDefaultBrowserBannerCloseBtn", "should_trigger_usage": true}, "group_id": "Group_Bing_DefaultBrowserBanner", "has_custom_iris_data_fetcher": false, "notes": "Banner to promote setting edge as default browser", "notification_scope": "profile", "targeting_data": {"channels": ["stable"], "max_version": "134", "min_version": "108", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Bing Growth and Distribution", "triggering_data": [{"trigger_name": "new_tab_not_whats_new_page"}], "ui_data": {"surface_type": "banner", "primary_button_action_id": 136, "secondary_button_action_id": 92, "number_of_buttons": 2, "close_button_state": "show", "is_close_button_set_to_ignore_action": true, "banner_data": {"infobar_identifier": 1007, "show_banner_only_on_this_tab": false, "is_closeable": true, "should_disable_colour_override": true, "infobar_button_alignment": 0}}}, {"campaign_id": "EnterpriseSecurity_StartSettingsBadUrlMitigationBanner", "notification_id_int": 142, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": true, "name": "msNurturingBadUrlMitigationBanner", "should_trigger_usage": true}, "group_id": "Group_EnterpriseSecurity_BadUrlMitigationBanner", "notes": "Show notification when mitigated bad url in start settings.", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "140", "min_version": "105", "os_version": "any", "supported_os": ["Windows NT"]}, "triggering_data": [{"trigger_name": "start_settings_bad_url_mitigated"}], "ui_data": {"surface_type": "banner", "primary_button_action_id": 141, "secondary_button_action_id": 142, "number_of_buttons": 2, "banner_data": {"infobar_identifier": 1023, "show_banner_only_on_this_tab": true, "is_closeable": false}}}, {"campaign_id": "Rewards_RedemptionCoachmark_Test", "notification_id_int": 604, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmarkTest", "should_trigger_usage": true}, "group_id": "Group_Rewards_RedemptionCoachmark", "notes": "Test campaign for validation of rewards redemption coachmarks", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "119", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "FIRST_MATCH_FOR_TEST"}}, {"campaign_id": "Rewards_RedemptionCoachmark_1", "notification_id_int": 555, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark1", "should_trigger_usage": true}, "group_id": "Group_Rewards_RedemptionCoachmark", "notes": "Minecraft 330 Minecoins Redemption Coachmark", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "118", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Redemption_Minecraft_330_Minecoins"}}, {"campaign_id": "Rewards_RedemptionCoachmark_2", "notification_id_int": 556, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark2", "should_trigger_usage": true}, "group_id": "Group_Rewards_RedemptionCoachmark", "notes": "Amazon 5 Dollars Redemption Coachmark", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "118", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Redemption_Amazon_5_Dollars"}}, {"campaign_id": "Rewards_RedemptionCoachmark_3", "notification_id_int": 557, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark3", "should_trigger_usage": true}, "group_id": "Group_Rewards_RedemptionCoachmark", "notes": "Roblox 100 Robux Redemption Coachmark", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "118", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Redemption_Roblox_100_Robux"}}, {"campaign_id": "Rewards_RedemptionCoachmark_4", "notification_id_int": 558, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark4", "should_trigger_usage": true}, "group_id": "Group_Rewards_RedemptionCoachmark", "notes": "League of Legends 100 Riot Points Redemption Coachmark", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "118", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Redemption_LeagueOfLegends_100_RiotPoints"}}, {"campaign_id": "Rewards_RedemptionCoachmark_5", "notification_id_int": 559, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark5", "should_trigger_usage": true}, "group_id": "Group_Rewards_RedemptionCoachmark", "notes": "Uber Eats Campaign Coach Mark for JPN - engagement", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "119", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Engagement_UberEats_Jpn"}}, {"campaign_id": "Rewards_RedemptionCoachmark_6", "notification_id_int": 584, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark6", "should_trigger_usage": true}, "group_id": "Group_Rewards_RedemptionCoachmark", "notes": "News Campaign Coach Mark for JPN - engagement", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "119", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Engagement_News_Jpn"}}, {"campaign_id": "Rewards_RedemptionCoachmark_7", "notification_id_int": 585, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark7", "should_trigger_usage": true}, "group_id": "Group_Rewards_RedemptionCoachmark", "notes": "Travel Campaign Coach Mark for JPN - engagement", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "119", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Engagement_Travel_Jpn"}}, {"campaign_id": "Rewards_RedemptionCoachmark_8", "notification_id_int": 586, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark8", "should_trigger_usage": true}, "group_id": "Group_Rewards_RedemptionCoachmark", "notes": "Finance Campaign Coach Mark for JPN - engagement", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "119", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Engagement_Finance_Jpn"}}, {"campaign_id": "Rewards_RedemptionCoachmark_9", "notification_id_int": 587, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark9", "should_trigger_usage": true}, "group_id": "Group_Rewards_RedemptionCoachmark", "notes": "Weather Campaign Coach Mark for JPN - engagement", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "119", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Engagement_Weather_Jpn"}}, {"campaign_id": "Rewards_RedemptionCoachmark_10", "notification_id_int": 588, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark10", "should_trigger_usage": true}, "group_id": "Group_Rewards_RedemptionCoachmark", "notes": "TopSite Campaign Coach Mark for JPN - engagement", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "119", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Engagement_TopSite_Jpn"}}, {"campaign_id": "Rewards_RedemptionCoachmark_11", "notification_id_int": 589, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark11", "should_trigger_usage": true}, "group_id": "Group_Rewards_RedemptionCoachmark", "notes": "MS1P Campaign Coach Mark for JPN - engagement", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "119", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Engagement_MS1P_Jpn"}}, {"campaign_id": "Rewards_RedemptionCoachmark_12", "notification_id_int": 590, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark12", "should_trigger_usage": true}, "group_id": "Group_Rewards_RedemptionCoachmark", "notes": "YG Compete Campaign Coach Mark for JPN - engagement", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "119", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Engagement_YG_Compete_Jpn"}}, {"campaign_id": "Rewards_RedemptionCoachmark_13", "notification_id_int": 591, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark13", "should_trigger_usage": true}, "group_id": "Group_Rewards_RedemptionCoachmark", "notes": "Roblox coachmark - unconscious to conscious users for US", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "119", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Redemption_Roblox_Unconscious_Us"}}, {"campaign_id": "Rewards_RedemptionCoachmark_14", "notification_id_int": 592, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark14", "should_trigger_usage": true}, "group_id": "Group_Rewards_RedemptionCoachmark", "notes": "Target coachmark - unconscious to conscious users for US", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "119", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Redemption_Target_Unconscious_Us"}}, {"campaign_id": "Rewards_RedemptionCoachmark_15", "notification_id_int": 593, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark15", "should_trigger_usage": true}, "group_id": "Group_Rewards_RedemptionCoachmark", "notes": "Redemption coachmark - Overwatch 1000 for US", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Redemption_Overwatch_1000_Us"}}, {"campaign_id": "Rewards_RedemptionCoachmark_16", "notification_id_int": 619, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark16", "should_trigger_usage": true}, "group_id": "Group_Rewards_RedemptionCoachmark", "notes": "Redemption coachmark - League Of Legends 575 for US", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Redemption_LeagueOfLegends_575_Us"}}, {"campaign_id": "Rewards_RedemptionCoachmark_17", "notification_id_int": 620, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark17", "should_trigger_usage": true}, "group_id": "Group_Rewards_RedemptionCoachmark", "notes": "Redemption coachmark - XBox Game Pass 3 Months Level 1 for US", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Redemption_Xbox_Game_Pass_3Month_Level1_Us"}}, {"campaign_id": "Rewards_RedemptionCoachmark_18", "notification_id_int": 621, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark18", "should_trigger_usage": true}, "group_id": "Group_Rewards_RedemptionCoachmark", "notes": "Redemption coachmark - XBox Game Pass 3 Months Level 2 for US", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Redemption_Xbox_Game_Pass_3Month_Level2_Us"}}, {"campaign_id": "Rewards_RedemptionCoachmark_19", "notification_id_int": 622, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark19", "should_trigger_usage": true}, "group_id": "Group_Rewards_RedemptionCoachmark", "notes": "Redemption coachmark - Solitaire 12 Months for US", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Redemption_Solitaire_12Month_Us"}}, {"campaign_id": "Rewards_RedemptionCoachmark_20", "notification_id_int": 623, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark20", "should_trigger_usage": true}, "group_id": "Group_Rewards_RedemptionCoachmark", "notes": "Redemption coachmark - PC Game Pass 3 Months Level 1 for US", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Redemption_PC_Game_Pass_3Month_Level1_Us"}}, {"campaign_id": "Rewards_RedemptionCoachmark_21", "notification_id_int": 624, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark21", "should_trigger_usage": true}, "group_id": "Group_Rewards_RedemptionCoachmark", "notes": "Redemption coachmark - PC Game Pass 3 Months Level 2 for US", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Redemption_PC_Game_Pass_3Month_Level2_Us"}}, {"campaign_id": "Rewards_RedemptionCoachmark_22", "notification_id_int": 625, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark22", "should_trigger_usage": true}, "group_id": "Group_Rewards_RedemptionCoachmark", "notes": "Redemption coachmark - Minecraft 330 Minecoins Level 1 for US", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Redemption_Minecraft_330_Minecoins_Level1_Us"}}, {"campaign_id": "Rewards_RedemptionCoachmark_23", "notification_id_int": 626, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark23", "should_trigger_usage": true}, "group_id": "Group_Rewards_RedemptionCoachmark", "notes": "Redemption coachmark - Minecraft 330 Minecoins Level 2 for US", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Redemption_Minecraft_330_Minecoins_Level2_Us"}}, {"campaign_id": "Rewards_RedemptionCoachmark_24", "notification_id_int": 627, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark24", "should_trigger_usage": true}, "group_id": "Group_Bing_DefaultBrowserBanner", "notes": "Redemption coachmark - Set Bing DSE 1", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Redemption_Set_Bing_DSE_1"}}, {"campaign_id": "Rewards_RedemptionCoachmark_25", "notification_id_int": 628, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark25", "should_trigger_usage": true}, "group_id": "Group_Bing_DefaultBrowserBanner", "notes": "Redemption coachmark - Set Bing DSE 2", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Redemption_Set_Bing_DSE_2"}}, {"campaign_id": "Rewards_RedemptionCoachmark_26", "notification_id_int": 660, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark26", "should_trigger_usage": true}, "group_id": "Group_Bing_DefaultBrowserBanner", "notes": "Redemption coachmark - Set Bing DSE 3", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Redemption_Set_Bing_DSE_3"}}, {"campaign_id": "Rewards_RedemptionCoachmark_27", "notification_id_int": 661, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark27", "should_trigger_usage": true}, "group_id": "Group_Bing_DefaultBrowserBanner", "notes": "Redemption coachmark - Set Bing DSE 4", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Redemption_Set_Bing_DSE_4"}}, {"campaign_id": "Rewards_RedemptionCoachmark_28", "notification_id_int": 662, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark28", "should_trigger_usage": true}, "group_id": "Group_Bing_DefaultBrowserBanner", "notes": "Redemption coachmark - Set Bing DSE 5", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Redemption_Set_Bing_DSE_5"}}, {"campaign_id": "Rewards_RedemptionCoachmark_29", "notification_id_int": 663, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark29", "should_trigger_usage": true}, "group_id": "Group_Bing_DefaultBrowserBanner", "notes": "Redemption coachmark - Set Bing DSE 6", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Redemption_Set_Bing_DSE_6"}}, {"campaign_id": "Rewards_RedemptionCoachmark_30", "notification_id_int": 664, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark30", "should_trigger_usage": true}, "group_id": "Group_Bing_DefaultBrowserBanner", "notes": "Redemption coachmark - Set Bing DSE 7", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Redemption_Set_Bing_DSE_7"}}, {"campaign_id": "Rewards_RedemptionCoachmark_31", "notification_id_int": 665, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark31", "should_trigger_usage": true}, "group_id": "Group_Bing_DefaultBrowserBanner", "notes": "Redemption coachmark - Set Bing DSE 8", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Redemption_Set_Bing_DSE_8"}}, {"campaign_id": "Rewards_RedemptionCoachmark_32", "notification_id_int": 666, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark32", "should_trigger_usage": true}, "group_id": "Group_Bing_DefaultBrowserBanner", "notes": "Redemption coachmark - Set Bing DSE 9", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Redemption_Set_Bing_DSE_9"}}, {"campaign_id": "Rewards_RedemptionCoachmark_33", "notification_id_int": 667, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark33", "should_trigger_usage": true}, "group_id": "Group_Bing_DefaultBrowserBanner", "notes": "Redemption coachmark - Set Bing DSE 10", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Redemption_Set_Bing_DSE_10"}}, {"campaign_id": "Rewards_RedemptionCoachmark_34", "notification_id_int": 668, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark34", "should_trigger_usage": true}, "group_id": "Group_Bing_DefaultBrowserBanner", "notes": "Redemption coachmark - Set Bing DSE 11", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Redemption_Set_Bing_DSE_11"}}, {"campaign_id": "Rewards_RedemptionCoachmark_35", "notification_id_int": 669, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark35", "should_trigger_usage": true}, "group_id": "Group_Bing_DefaultBrowserBanner", "notes": "Redemption coachmark - Set Bing DSE 12", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Redemption_Set_Bing_DSE_12"}}, {"campaign_id": "Rewards_RedemptionCoachmark_36", "notification_id_int": 670, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark36", "should_trigger_usage": true}, "group_id": "Group_Bing_DefaultBrowserBanner", "notes": "Redemption coachmark - Set Bing DSE 13", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Redemption_Set_Bing_DSE_13"}}, {"campaign_id": "Rewards_RedemptionCoachmark_37", "notification_id_int": 671, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark37", "should_trigger_usage": true}, "group_id": "Group_Bing_DefaultBrowserBanner", "notes": "Redemption coachmark - Set Bing DSE 14", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Redemption_Set_Bing_DSE_14"}}, {"campaign_id": "Rewards_RedemptionCoachmark_38", "notification_id_int": 672, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark38", "should_trigger_usage": true}, "group_id": "Group_Bing_DefaultBrowserBanner", "notes": "Redemption coachmark - Set Bing DSE 15", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Redemption_Set_Bing_DSE_15"}}, {"campaign_id": "Rewards_RedemptionCoachmark_39", "notification_id_int": 673, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark39", "should_trigger_usage": true}, "group_id": "Group_Bing_DefaultBrowserBanner", "notes": "Redemption coachmark - Set Bing DSE 16", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Redemption_Set_Bing_DSE_16"}}, {"campaign_id": "Rewards_RedemptionCoachmark_40", "notification_id_int": 674, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark40", "should_trigger_usage": true}, "group_id": "Group_Bing_DefaultBrowserBanner", "notes": "Redemption coachmark - Set Bing DSE 17", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Redemption_Set_Bing_DSE_17"}}, {"campaign_id": "Rewards_RedemptionCoachmark_41", "notification_id_int": 675, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark41", "should_trigger_usage": true}, "group_id": "Group_Bing_DefaultBrowserBanner", "notes": "Redemption coachmark - Set Bing DSE 18", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Redemption_Set_Bing_DSE_18"}}, {"campaign_id": "Rewards_RedemptionCoachmark_42", "notification_id_int": 676, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark42", "should_trigger_usage": true}, "group_id": "Group_Bing_DefaultBrowserBanner", "notes": "Redemption coachmark - Set Bing DSE 19", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Redemption_Set_Bing_DSE_19"}}, {"campaign_id": "Rewards_RedemptionCoachmark_43", "notification_id_int": 677, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark43", "should_trigger_usage": true}, "group_id": "Group_Bing_DefaultBrowserBanner", "notes": "Redemption coachmark - Set Bing DSE 20", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "Redemption_Set_Bing_DSE_20"}}, {"campaign_id": "Rewards_RedemptionCoachmark_44", "notification_id_int": 678, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark44", "should_trigger_usage": true}, "group_id": "Group_Rewards_RedemptionCoachmark", "notes": "Placeholder", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "INVALID"}}, {"campaign_id": "Rewards_RedemptionCoachmark_45", "notification_id_int": 679, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark45", "should_trigger_usage": true}, "group_id": "Group_Rewards_RedemptionCoachmark", "notes": "Placeholder", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "INVALID"}}, {"campaign_id": "Rewards_RedemptionCoachmark_46", "notification_id_int": 680, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark46", "should_trigger_usage": true}, "group_id": "Group_Rewards_RedemptionCoachmark", "notes": "Placeholder", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "INVALID"}}, {"campaign_id": "Rewards_RedemptionCoachmark_47", "notification_id_int": 681, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark47", "should_trigger_usage": true}, "group_id": "Group_Rewards_RedemptionCoachmark", "notes": "Placeholder", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "INVALID"}}, {"campaign_id": "Rewards_RedemptionCoachmark_48", "notification_id_int": 682, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark48", "should_trigger_usage": true}, "group_id": "Group_Rewards_RedemptionCoachmark", "notes": "Placeholder", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "INVALID"}}, {"campaign_id": "Rewards_RedemptionCoachmark_49", "notification_id_int": 683, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark49", "should_trigger_usage": true}, "group_id": "Group_Rewards_RedemptionCoachmark", "notes": "Placeholder", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "INVALID"}}, {"campaign_id": "Rewards_RedemptionCoachmark_50", "notification_id_int": 684, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsRedemptionCoachmark50", "should_trigger_usage": true}, "group_id": "Group_Rewards_RedemptionCoachmark", "notes": "Placeholder", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_redemption"}], "dynamic_targeting_data": {"rewards_redemption_campaign_id": "INVALID"}}, {"campaign_id": "Rewards_Coachmark_Test", "notification_id_int": 605, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRewardsCoachmarkTest", "should_trigger_usage": true}, "group_id": "Group_Rewards_Coachmark", "notes": "Test campaign for validation of rewards coachmarks", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "119", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Growth", "triggering_data": [{"trigger_name": "edge_rewards_coachmark"}], "dynamic_targeting_data": {"rewards_coachmark_campaign_id": "FIRST_MATCH_FOR_TEST"}}, {"campaign_id": "M365OpenLinks_BrowserSignIn", "notification_id_int": 159, "courtesy_engine_data": {"dismissal_cap": 5}, "dynamic_targeting_data": {"is_signed_in": false}, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": true, "name": "msNurturingM365OpenLinksSigninPrompt", "should_trigger_usage": true}, "notes": "Show prompt for browser sign-in when using M365 enhanced open links protocol.", "notification_scope": "profile", "should_show_ui_without_managed_preferences": true, "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "140", "min_version": "107", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge/Enterprise/M365 App Integration", "triggering_data": [{"trigger_name": "m365_open_links_signin"}]}, {"campaign_id": "EdgeWS_CurrentWindowOnTabGroupCreatedToWorkspace", "group_id": "Group_EnterpriseFremont_SuggestWorkspaces", "notification_id_int": 715, "notes": "Show a flyout suggesting that the current window be converted to a workspace when user creates a tab group.", "team": "Edge/Enterprise/Fremont/Fremont Client", "notification_scope": "profile", "dynamic_targeting_data": {"is_signed_in": true, "is_sync_on": true}, "targeting_data": {"channels": ["stable", "beta", "dev", "canary"], "min_version": "124", "max_version": "150", "os_version": "any", "supported_os": ["Windows NT", "Mac OS X", "Linux"]}, "triggering_data": [{"trigger_name": "convert_window_to_workspace_on_tab_group_created"}], "ui_data": {"surface_type": "flyout", "primary_button_action_id": 260, "secondary_button_action_id": 92, "number_of_buttons": 1, "anchor_type": "app_menu_button", "close_button_state": "show"}, "courtesy_engine_data": {"catch_all_cool_down": 24}}, {"campaign_id": "EdgeWS_WorkspaceFromRestoreHistoryWindow", "group_id": "Group_EnterpriseFremont_SuggestWorkspaces", "notification_id_int": 733, "notes": "Show a flyout suggesting that a window restored from history can be saved as a Workspace.", "team": "Edge/Enterprise/Fremont/Fremont Client", "notification_scope": "profile", "dynamic_targeting_data": {"is_signed_in": true, "is_sync_on": true}, "targeting_data": {"channels": ["stable", "beta", "dev", "canary"], "min_version": "126", "max_version": "150", "os_version": "any", "supported_os": ["Windows NT", "Mac OS X", "Linux"]}, "triggering_data": [{"trigger_name": "convert_workspace_from_history_restored_window"}], "ui_data": {"surface_type": "flyout", "primary_button_action_id": 260, "secondary_button_action_id": 92, "number_of_buttons": 1, "anchor_type": "app_menu_button", "close_button_state": "show"}, "courtesy_engine_data": {"catch_all_cool_down": 24}}, {"campaign_id": "Workspaces_MsaTrySkypeChatFlyout", "notification_id_int": 460, "dynamic_targeting_data": {"is_aad_user": false, "is_signed_in": true}, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingWorkspacesMsaTrySkypeChat", "should_trigger_usage": true}, "notes": "Show notification to users when MSA workspace is opened.", "notification_scope": "browser", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "140", "min_version": "117", "os_version": "any", "supported_os": ["Windows NT", "Mac OS X", "Linux"]}, "team": "Edge/Enterprise/Fremont/Fremont Experiences", "triggering_data": [{"trigger_name": "workspaces_msa_on_try_skype_chat_flyout"}]}, {"campaign_id": "Fundamentals_DefaultBrowserMonitorOnStart", "notification_id_int": 613, "group_id": "Group_AppDefaults_DefaultBrowser", "notes": "Monitor default browser on start", "team": "Edge Fundamentals/App Defaults", "notification_scope": "profile", "triggering_data": [{"trigger_name": "default_browser_monitor_variant2"}], "server_driven_feature_flag_details": {"name": "msNurturingDefaultBrowserMonitor", "is_flag_enabled_by_default": true, "should_trigger_usage": true}, "targeting_data": {"min_version": "109", "max_version": "140", "channels": ["stable", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "dynamic_targeting_data": {"has_defaults_blocking_policies": false, "is_aad_user": false}, "courtesy_engine_data": {"acceptance_cap": 6, "dismissal_cap": 6, "impression_cap": 6, "shown_cool_down": 3, "dismissal_cool_down": 3, "acceptance_cool_down": 3, "browser_launch_cooldown_applied": false}, "ui_data": {"surface_type": "banner", "primary_button_action_id": 171, "secondary_button_action_id": 172, "number_of_buttons": 2, "banner_data": {"infobar_identifier": 1025, "show_banner_only_on_this_tab": false, "is_closeable": false}}}, {"campaign_id": "Fundamentals_DefaultBrowserMonitor", "notification_id_int": 151, "group_id": "Group_AppDefaults_DefaultBrowser", "notes": "Monitor default browser", "team": "Edge Fundamentals/App Defaults", "notification_scope": "profile", "frequency_capping_template": "skip_caps", "triggering_data": [{"trigger_name": "default_browser_monitor"}], "server_driven_feature_flag_details": {"name": "msNurturingDefaultBrowserMonitor", "is_flag_enabled_by_default": true, "should_trigger_usage": true}, "targeting_data": {"min_version": "109", "max_version": "140", "channels": ["stable", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "dynamic_targeting_data": {"has_defaults_blocking_policies": false, "is_aad_user": false}, "courtesy_engine_data": {"dismissal_cap": 4, "dismissal_cool_down": 2, "acceptance_cool_down": 1, "shown_cool_down": 1, "browser_launch_cooldown_applied": false}, "ui_data": {"surface_type": "banner", "primary_button_action_id": 171, "secondary_button_action_id": 172, "number_of_buttons": 2, "banner_data": {"infobar_identifier": 1025, "show_banner_only_on_this_tab": false, "is_closeable": false}}}, {"campaign_id": "Fundamentals_DefaultBrowserMonitorVariant2", "notification_id_int": 152, "group_id": "Group_AppDefaults_DefaultBrowser", "notes": "Monitor default browser", "team": "Edge Fundamentals/App Defaults", "notification_scope": "profile", "frequency_capping_template": "skip_caps", "triggering_data": [{"trigger_name": "default_browser_monitor"}, {"trigger_name": "default_browser_monitor_variant2"}], "server_driven_feature_flag_details": {"name": "msNurturingDefaultBrowserMonitorVariant2", "is_flag_enabled_by_default": false, "should_trigger_usage": true}, "targeting_data": {"min_version": "110", "max_version": "140", "channels": ["stable"], "supported_os": ["Windows NT"], "os_version": "any"}, "dynamic_targeting_data": {"has_defaults_blocking_policies": false, "is_aad_user": false, "exclude_on_local_state_pref_true": ["app_defaults.default_browser_safeguard"], "targeted_region_list": ["CN"]}, "courtesy_engine_data": {"dismissal_cap": 4, "dismissal_cool_down": 2, "acceptance_cool_down": 1, "shown_cool_down": 1, "browser_launch_cooldown_applied": false}, "ui_data": {"surface_type": "banner", "primary_button_action_id": 195, "secondary_button_action_id": 171, "number_of_buttons": 2, "banner_data": {"infobar_identifier": 1025, "show_banner_only_on_this_tab": false, "is_closeable": true}}}, {"campaign_id": "Fundamentals_FC_SetDBForEdgeProtocol", "notification_id_int": 707, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingSetDBForEdgeProtocol", "should_trigger_usage": false}, "notes": "Show banner to set default for Microsoft-Edge protocol", "notification_scope": "profile", "dynamic_targeting_data": {"is_aad_user": false, "is_enterprise_user": false}, "courtesy_engine_data": {"acceptance_cap": 6, "dismissal_cap": 6, "impression_cap": 12, "shown_cool_down": 14, "dismissal_cool_down": 14, "acceptance_cool_down": 14}, "targeting_data": {"channels": ["stable"], "max_version": "160", "min_version": "123", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Fundamentals/App Defaults", "triggering_data": [{"trigger_name": "set_DB_for_Edge_protocol"}, {"trigger_name": "set_DB_for_Edge_protocol_type2"}], "ui_data": {"surface_type": "banner", "primary_button_action_id": 267, "secondary_button_action_id": 268, "number_of_buttons": 2, "auto_close_time_in_seconds": 30, "banner_data": {"infobar_identifier": 1041, "show_banner_only_on_this_tab": false, "is_closeable": true}}}, {"campaign_id": "Fundamentals_FC_DefaultPDFHandlerMonitor", "notification_id_int": 725, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": true, "name": "msNurturingDefaultPDFHandlerMonitor", "should_trigger_usage": true}, "notes": "Show banner to set default PDF handler.", "notification_scope": "profile", "dynamic_targeting_data": {"is_aad_user": false, "is_enterprise_user": false}, "courtesy_engine_data": {"acceptance_cap": 3}, "targeting_data": {"channels": ["stable"], "max_version": "160", "min_version": "126", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge Fundamentals/App Defaults", "triggering_data": [{"trigger_name": "default_PDF_handler_monitor"}], "ui_data": {"surface_type": "banner", "primary_button_action_id": 272, "secondary_button_action_id": 273, "number_of_buttons": 2, "banner_data": {"infobar_identifier": 1043, "show_banner_only_on_this_tab": false, "is_closeable": false}}}, {"campaign_id": "Identity_ReauthNotification", "notification_id_int": 561, "notes": "push reauth notification.", "team": "Edge Identity Foundation", "notification_scope": "profile", "triggering_data": [{"trigger_name": "push_reauth_notification"}], "dynamic_targeting_data": {"is_signed_in": true}, "server_driven_feature_flag_details": {"name": "msNurturingReauthNotification", "is_flag_enabled_by_default": false, "should_trigger_usage": true}, "targeting_data": {"min_version": "119", "max_version": "125", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "ui_data": {"surface_type": "toast", "toast_data": {"toast_identifier": "Identity_ReauthNotification"}, "should_set_soft_dismiss_behaviour": true}}, {"campaign_id": "Identity_ReauthNotification_2", "notification_id_int": 580, "notes": "push reauth notification 2.", "team": "Edge Identity Foundation", "notification_scope": "profile", "triggering_data": [{"trigger_name": "push_reauth_notification"}], "dynamic_targeting_data": {"is_signed_in": true}, "server_driven_feature_flag_details": {"name": "msNurturingReauthNotification2", "is_flag_enabled_by_default": false, "should_trigger_usage": true}, "targeting_data": {"min_version": "119", "max_version": "125", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "ui_data": {"surface_type": "toast", "toast_data": {"toast_identifier": "Identity_ReauthNotification_2"}, "should_set_soft_dismiss_behaviour": true}}, {"campaign_id": "Identity_ReauthNotification_3", "notification_id_int": 581, "notes": "push reauth notification 3", "team": "Edge Identity Foundation", "notification_scope": "profile", "triggering_data": [{"trigger_name": "push_reauth_notification"}], "dynamic_targeting_data": {"is_signed_in": true}, "server_driven_feature_flag_details": {"name": "msNurturingReauthNotification3", "is_flag_enabled_by_default": false, "should_trigger_usage": true}, "targeting_data": {"min_version": "119", "max_version": "125", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "ui_data": {"surface_type": "toast", "toast_data": {"toast_identifier": "Identity_ReauthNotification_3"}, "should_set_soft_dismiss_behaviour": true}}, {"campaign_id": "Bing_NewBingChatFlyout", "notification_id_int": 354, "notes": "Show flyout for user who has not previously engaged with Bing chat.", "team": "Bing Growth and Distribution", "notification_scope": "profile", "triggering_data": [{"trigger_name": "new_bing_chat_on_browser_launch_excluded_urls"}], "targeting_data": {"min_version": "111", "max_version": "130", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT", "Mac OS X", "Linux"], "os_version": "any"}, "server_driven_feature_flag_details": {"name": "msNurturingNewBingChatFlyout", "is_flag_enabled_by_default": false, "should_trigger_usage": true}, "dynamic_targeting_data": {"user_engaged_with_bing_chat": false, "is_retrigger_fre_or_bing_applicable": true, "is_aad_user": false, "excluded_region_list": ["CN", "RU"]}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 205, "secondary_button_action_id": 92, "anchor_type": "app_menu_button", "number_of_buttons": 2, "close_button_state": "show"}}, {"campaign_id": "Identity_ReauthSignInCTAFlyout", "notification_id_int": 197, "notes": "Show reauth sign in CTA flyout when websso fails due to UIR.", "team": "Edge Identity Foundation", "notification_scope": "profile", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "show_reauth_sign_in_flyout_when_websso_fails_due_to_UIR"}], "dynamic_targeting_data": {"is_signed_in": true}, "server_driven_feature_flag_details": {"name": "msNurturingShowReauthSignInCTAFlyout", "is_flag_enabled_by_default": true, "should_trigger_usage": false}, "targeting_data": {"min_version": "110", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT", "Mac OS X", "Linux"], "os_version": "any"}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 200, "secondary_button_action_id": 92, "escape_behaviour": "dismiss", "arrow_type": "top_right", "close_button_state": "show"}}, {"campaign_id": "Fundamentals_ExtensionPathFinder", "notification_id_int": 579, "notes": "Show notification to install extension in Edge Ext Store.", "team": "Edge Fundamentals/App Defaults", "notification_scope": "profile", "triggering_data": [{"trigger_name": "extension_path_finder"}], "server_driven_feature_flag_details": {"name": "msNurturingExtensionPathFinder", "is_flag_enabled_by_default": false, "should_trigger_usage": false}, "dynamic_targeting_data": {"is_aad_user": false}, "targeting_data": {"min_version": "117", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "ui_data": {"surface_type": "banner", "primary_button_action_id": 242, "secondary_button_action_id": 243, "number_of_buttons": 2, "banner_data": {"infobar_identifier": 1036, "show_banner_only_on_this_tab": true, "is_closeable": false}}}, {"campaign_id": "Bing_MSStartYJCompete", "notification_id_int": 583, "dynamic_targeting_data": {"targeted_region_list": ["JP"], "is_aad_user": false}, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": true, "name": "msNurturingMSStartYJCompete", "should_trigger_usage": true}, "group_id": "Group_Bing_NtpHomeStartpage", "notes": "Show coachmark to upsell MS Start to compete Yahoo Japan.", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Content Services Anaheim NTP", "triggering_data": [{"trigger_name": "yahoo_japan_url_navigation"}], "courtesy_engine_data": {"browser_launch_cooldown_applied": false}, "ui_data": {"surface_type": "coachmark", "primary_button_action_id": 167, "secondary_button_action_id": 92, "anchor_type": "toolbar", "arrow_type": "top_right", "number_of_buttons": 1}}, {"campaign_id": "Bing_MSStartYJExtCompete", "notification_id_int": 607, "dynamic_targeting_data": {"targeted_region_list": ["JP"], "is_aad_user": false}, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingMSStartYJExtCompete", "should_trigger_usage": true}, "notes": "Show coachmark for users who install YJ extension", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Content Services Anaheim NTP", "triggering_data": [{"trigger_name": "yj_extension_url_navigation"}], "courtesy_engine_data": {"dismissal_cap": 5, "impression_cap": 10, "shown_cool_down": 7, "browser_launch_cooldown_applied": false}, "ui_data": {"surface_type": "coachmark", "primary_button_action_id": 246, "secondary_button_action_id": 92, "anchor_type": "app_menu_button", "arrow_type": "top_right", "number_of_buttons": 1}}, {"campaign_id": "Bing_MSStartYJExtOptOut", "notification_id_int": 609, "dynamic_targeting_data": {"targeted_region_list": ["JP"], "is_aad_user": false}, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": true, "name": "msNurturingMSStartYJExtOptOut", "should_trigger_usage": true}, "notes": "Disable YJ Extensiona and show coachmark for re-enabling it", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Content Services Anaheim NTP", "triggering_data": [{"trigger_name": "yj_extension_url_navigation"}], "courtesy_engine_data": {"dismissal_cap": 1, "acceptance_cap": 1, "impression_cap": 1, "browser_launch_cooldown_applied": false}, "ui_data": {"surface_type": "coachmark", "primary_button_action_id": 248, "secondary_button_action_id": 92, "anchor_type": "app_menu_button", "arrow_type": "top_right", "number_of_buttons": 1}}, {"campaign_id": "Bing_YGCompeteJP", "notification_id_int": 614, "dynamic_targeting_data": {"targeted_region_list": ["JP"], "is_aad_user": false}, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingBingYGCompeteJP", "should_trigger_usage": true}, "notes": "Show coachmark to upsell Bing for Google or Yahoo users", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT", "Mac OS X", "Linux"]}, "team": "Bing Growth and Distribution.", "triggering_data": [{"trigger_name": "yg_compete_domain_based_triggering"}, {"trigger_name": "yg_compete_query_based_triggering"}]}, {"campaign_id": "Bing_BingGrowthToastNotification", "notification_id_int": 647, "dynamic_targeting_data": {"targeted_region_list": ["JP"], "days_since_last_edge_opened": 4}, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingBingGrowthToastNotification", "should_trigger_usage": true}, "notes": "Encouraging users to use Bing", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "courtesy_engine_data": {"acceptance_cap": 5, "dismissal_cap": 2, "dismissal_cool_down": 28, "impression_cap": 5, "shown_cool_down": 14, "browser_launch_cooldown_applied": false}, "team": "Bing Growth and Distribution.", "triggering_data": [{"trigger_name": "no_browser_process_launch"}], "ui_data": {"surface_type": "toast", "toast_data": {"toast_identifier": "Bing_BingGrowthToastNotification"}, "should_set_soft_dismiss_behaviour": true, "auto_close_time_in_seconds": 30}}, {"campaign_id": "ImageViewer_DefaultImageHandlerPromotion", "notification_id_int": 654, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingDefaultImageHandlerPromotion", "should_trigger_usage": false}, "notes": "Upsell Edge as default image handler", "notification_scope": "profile", "targeting_data": {"channels": ["stable"], "max_version": "150", "min_version": "121", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge\\Consumer\\Experiences and Delighters\\Image Viewer", "dynamic_targeting_data": {"is_edge_default_image_handler": false, "is_aad_user": false, "is_enterprise_user": false, "targeted_region_list": ["CN", "HK", "MO", "TW"]}, "triggering_data": [{"trigger_name": "default_image_handler_promotion"}], "ui_data": {"surface_type": "flyout", "primary_button_action_id": 261, "secondary_button_action_id": 92, "should_set_soft_dismiss_behaviour": false, "anchor_type": "app_menu_button", "focus_behaviour": "notify", "close_button_state": "show", "number_of_buttons": 1}}, {"campaign_id": "EngagementVerticals_GamerModeOnGamingSite", "notification_id_int": 734, "notification_scope": "profile", "notes": "Notification to show entry for Gamer Mode on Gaming sites", "team": "Edge Consumer Content and Verticals", "triggering_data": [{"trigger_name": "gamer_mode_domain_based_triggering"}], "ui_data": {"surface_type": "banner", "primary_button_action_id": 277, "secondary_button_action_id": 92, "number_of_buttons": 2, "banner_data": {"infobar_identifier": 1044, "show_banner_only_on_this_tab": true, "is_closeable": true}}, "dynamic_targeting_data": {"is_gamer_mode_fre": true}, "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "140", "min_version": "128", "supported_os": ["Windows NT"], "os_version": "any"}}, {"campaign_id": "FN_AIThemeUserConsentDialog", "notification_id_int": 732, "notes": "Shows UI consent dialog when user applies AI theme from Marketing page", "notification_scope": "profile", "notification_type": "functional_notification", "targeting_data": {"min_version": "124", "max_version": "200", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT", "Mac OS X", "Linux"], "os_version": "any"}, "team": "Edge/Edge India/OnRamp and Growth/Personalization", "triggering_data": [{"trigger_name": "apply_ai_theme"}], "server_driven_feature_flag_details": {"name": "msNurturingAIThemeUserConsentDialog", "is_flag_enabled_by_default": true, "should_trigger_usage": true}, "ui_data": {"surface_type": "modal", "primary_button_action_id": 275, "secondary_button_action_id": 276, "number_of_buttons": 2, "close_button_state": "show", "focus_behaviour": "notify", "escape_behaviour": "persist"}}, {"campaign_id": "BingChina_BannerDefaultBrowser", "notification_id_int": 33, "group_id": "Group_BingChina_RecommendedSettings", "notes": "Show banner to upsell bing as default browser and default settings.", "team": "Edge/onRamp and Growth/Nurturing", "notification_scope": "profile", "triggering_data": [{"trigger_name": "explicit_browser_launch"}], "server_driven_feature_flag_details": {"name": "msNurturingFeatureBannerDefaultBrowserChina", "is_flag_enabled_by_default": false, "should_trigger_usage": true}, "targeting_data": {"min_version": "92", "max_version": "130", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "dynamic_targeting_data": {"edge_bing_pdf_startup_not_all_default": true, "is_aad_user": false, "targeted_region_list": ["CN"]}, "courtesy_engine_data": {"browser_launch_cooldown_applied": false}, "ui_data": {"surface_type": "banner", "primary_button_action_id": 22, "secondary_button_action_id": 92, "banner_data": {"infobar_identifier": 1007, "show_banner_only_on_this_tab": false, "is_closeable": true}}}, {"campaign_id": "Shopping_EnableShoppingAssistance_Cashback_Success_Banner", "notification_id_int": 731, "notes": "Show success banner to enable shopping assistance feature for cashback users.", "team": "Edge Shopping", "notification_scope": "profile", "notification_type": "functional_notification", "triggering_data": [{"trigger_name": "shopping_retarget_cashback_success_banner"}], "targeting_data": {"min_version": "125", "max_version": "130", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT", "Mac OS X", "Linux"], "os_version": "any"}, "dynamic_targeting_data": {"is_san_switch_on": true}, "ui_data": {"surface_type": "banner", "primary_button_action_id": 92, "secondary_button_action_id": 92, "number_of_buttons": 1, "should_set_soft_dismiss_behaviour": true, "banner_data": {"infobar_identifier": 1007, "show_banner_only_on_this_tab": true, "is_closeable": false}}}, {"campaign_id": "RecommendedSettingsChinaGlobal", "notification_id_int": 36, "notes": "Show dialog to upsell recommended settings (Japan).", "team": "Edge/onRamp and Growth/Nurturing", "notification_scope": "profile", "rank": 11, "triggering_data": [{"trigger_name": "explicit_browser_launch"}], "server_driven_feature_flag_details": {"name": "msNurturingFeatureRecommendedSettingsChinaGlobal", "is_flag_enabled_by_default": false, "should_trigger_usage": true}, "group_id": "Group_BingChina_RecommendedSettings", "targeting_data": {"min_version": "92", "max_version": "150", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "dynamic_targeting_data": {"edge_bing_pdf_startup_not_default_or_set_by_user": true, "is_aad_user": false, "targeted_region_list": ["JP"]}, "hub_data": {"show_card_in_hub": true}, "courtesy_engine_data": {"browser_launch_cooldown_applied": false}, "ui_data": {"is_custom_surface_type": true, "surface_type": "modal", "load_time_data_bool": {"standardLaunch": false, "standardLaunchWithDSE": false, "noImageFeature": false, "standardLaunchV2Ui": false, "v2UiCloseBtn": false}, "load_time_data_string": {"setAsDefaultImageFormat": "webp"}}}, {"campaign_id": "OnRamp_CIExplicitLaunchBannerUpsell", "notification_id_int": 90, "group_id": "Group_OnRamp_ContinuousImportUpsellCampaigns", "surface_id": "88000504", "notes": "Show banner to enable continuous import when user launches edge.", "team": "EDGE/onRamp and Growth/Migration", "notification_scope": "profile", "triggering_data": [{"trigger_name": "explicit_browser_launch"}], "targeting_data": {"min_version": "97", "max_version": "150", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "dynamic_targeting_data": {"primary_browser": [1], "is_chrome_installed": true, "is_aad_user": false, "is_ciupsell_targetted": true, "should_cap_ci_non_contextual_campaigns": false, "is_ci_advance_consent_available": false, "exclude_on_local_state_pref_false": ["continuous_migration.is_allowed_via_policy"], "check_dma_compliance_for": "ContinuousMigration"}, "courtesy_engine_data": {"browser_launch_cooldown_applied": false}, "ui_data": {"surface_type": "banner", "primary_button_action_id": 250, "secondary_button_action_id": 39, "banner_data": {"infobar_identifier": 1007, "show_banner_only_on_this_tab": true, "is_closeable": true}}}, {"campaign_id": "EdgeOnMac_SetEdgeAsDefaultBrowserOnMac", "notification_id_int": 104, "group_id": "Group_EdgeOnMac_RetentionCampaigns", "notes": "Show notification for users on mac to make edge as default browser.", "team": "Edge Max team", "notification_scope": "profile", "triggering_data": [{"trigger_name": "browser_launch_with_delay"}], "targeting_data": {"min_version": "99", "max_version": "200", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Mac OS X"], "os_version": "any"}, "dynamic_targeting_data": {"is_aad_user": false, "is_msa_minor": false}, "server_driven_feature_flag_details": {"name": "msNurturingSetEdgeAsDefaultBrowserOnMac", "is_flag_enabled_by_default": false, "should_trigger_usage": true}, "target_goal_data": {"is_edge_default_browser": true}, "courtesy_engine_data": {"browser_launch_cooldown_applied": false}, "ui_data": {"surface_type": "banner", "primary_button_action_id": 8, "secondary_button_action_id": 92, "banner_data": {"infobar_identifier": 1042, "show_banner_only_on_this_tab": false, "is_closeable": true}}}, {"campaign_id": "EdgeMobile_MobileUpsell_Email_AF_AnimationExtensionInstall", "notification_id_int": 801, "notes": "Show mobile upsell for users who install extension on desktop.", "team": "EDGE Mobile Growth", "notification_scope": "browser", "histogram_triggers": [{"histogram_name": "Microsoft.Extensions.InstallPrompt.MobileSupportedExtensionInstalled", "histogram_sample_value": "0"}], "targeting_data": {"min_version": "99", "max_version": "200", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "dynamic_targeting_data": {"has_synced_on_mobile": false, "number_of_days_after_fre": 21, "excluded_region_list": ["CN"]}, "server_driven_feature_flag_details": {"name": "msFeatureMmxUpsellOnEmailAnimationAFExtensionInstall", "is_flag_enabled_by_default": false, "should_trigger_usage": true}, "courtesy_engine_data": {"impression_cap": 3, "shown_cool_down": 14}, "ui_data": {"is_custom_surface_type": true, "surface_type": "flyout", "anchor_type": "app_menu_button", "focus_behaviour": "notify", "waiting_state_params": {"progress": {"secondary_button_action_id": 92}, "success": {"secondary_button_action_id": 92}, "failure": {"primary_button_action_id": 45, "secondary_button_action_id": 92}}, "load_time_data_bool": {"isAnimationCampaign": true, "isThumbnailPresent": false, "isContentIconPresent": false, "showContact": true, "showFirstLinkInTab": false, "showContactPhoneNumber": false}, "load_time_data_integer": {"closeButtonAction": 92, "closeButtonState": 1, "descriptionSupportLinks": 1, "descriptionSupportAction1": 15, "footerNumberOfButtons": 2, "footerPrimaryCtaAction": 45, "footerSecondaryCtaAction": 92, "firstDescriptionLinkAction": 134}, "load_time_data_string": {"descriptionSupportLink1": "https://go.microsoft.com/fwlink/?LinkId=521839"}}}, {"campaign_id": "EdgeMobile_MobileUpsell_Email_AF_AnimationExtensionInstallCN", "notification_id_int": 802, "notes": "Show mobile upsell for users who install extension on desktop.", "team": "EDGE Mobile Growth", "notification_scope": "browser", "histogram_triggers": [{"histogram_name": "Microsoft.Extensions.InstallPrompt.MobileSupportedExtensionInstalled", "histogram_sample_value": ""}], "targeting_data": {"min_version": "99", "max_version": "200", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "dynamic_targeting_data": {"has_synced_on_mobile": false, "number_of_days_after_fre": 21, "targeted_region_list": ["CN"]}, "server_driven_feature_flag_details": {"name": "msFeatureMmxUpsellOnEmailAnimationAFExtensionInstall", "is_flag_enabled_by_default": false, "should_trigger_usage": true}, "courtesy_engine_data": {"impression_cap": 3, "shown_cool_down": 14}, "ui_data": {"is_custom_surface_type": true, "surface_type": "flyout", "anchor_type": "app_menu_button", "focus_behaviour": "notify", "waiting_state_params": {"progress": {"secondary_button_action_id": 92}, "success": {"secondary_button_action_id": 92}, "failure": {"primary_button_action_id": 45, "secondary_button_action_id": 92}}, "load_time_data_bool": {"isAnimationCampaign": true, "isThumbnailPresent": false, "isContentIconPresent": false, "showContact": true, "showFirstLinkInTab": false, "showContactPhoneNumber": false}, "load_time_data_integer": {"closeButtonAction": 92, "closeButtonState": 1, "descriptionSupportLinks": 1, "descriptionSupportAction1": 15, "footerNumberOfButtons": 2, "footerPrimaryCtaAction": 45, "footerSecondaryCtaAction": 92, "firstDescriptionLinkAction": 134}, "load_time_data_string": {"descriptionSupportLink1": "https://go.microsoft.com/fwlink/?LinkId=521839"}}}, {"campaign_id": "OnRamp_ReTriggerOnWinUpgrade", "notification_id_int": 114, "notes": "Show re-trigger FRE on windows upgrade after explicit launch of edge", "team": "EDGE/onRamp and Growth/FRE", "notification_scope": "profile", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "explicit_browser_launch"}], "targeting_data": {"min_version": "101", "max_version": "130", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "11"}, "dynamic_targeting_data": {"is_aad_user": false, "max_chrome_network_usage_in_percentage": 90, "is_chrome_installed": true, "has_user_upgraded_to_win11": true, "has_user_seen_new_fre": false}, "courtesy_engine_data": {"browser_launch_cooldown_applied": false}}, {"campaign_id": "OnRamp_ReTriggerFREOnAutoLaunch", "notification_id_int": 338, "notes": "Show re-trigger FRE on auto launch of edge for non CI users", "team": "EDGE/onRamp and Growth/Migration", "notification_scope": "profile", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "visible_auto_launch"}], "targeting_data": {"min_version": "111", "max_version": "130", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "dynamic_targeting_data": {"is_aad_user": false, "max_chrome_network_usage_in_percentage": 90, "is_chrome_installed": true, "has_user_seen_new_fre": false, "is_ci_advance_consent_available": false, "is_ci_enabled_for_profile": false, "is_ci_active_on_remote_device": false}}, {"campaign_id": "OnRamp_RetriggerFREOnEdgeVersionUpgradeViaExplicitLaunch", "notification_id_int": 138, "group_id": "Group_RetriggerFREExplicitLaunch", "notes": "Show re-trigger FRE on edge version upgrade via explicit launch", "team": "EDGE/onRamp and Growth/FRE", "should_show_ui_without_managed_preferences": true, "notification_scope": "browser", "triggering_data": [{"trigger_name": "explicit_browser_launch"}], "targeting_data": {"min_version": "104", "max_version": "150", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "dynamic_targeting_data": {"is_aad_user": false, "max_chrome_network_usage_in_percentage": 90, "is_chrome_installed": true, "should_show_fre_on_edge_upgrade": true, "is_country_under_dma_regulation": false, "is_retrigger_fre_applicable": true}, "courtesy_engine_data": {"dismissal_cap": 3, "acceptance_cap": 2, "browser_launch_cooldown_applied": false}}, {"campaign_id": "OnRamp_RetriggerFRECodexCoPilotExplicitAprd", "notification_id_int": 349, "group_id": "Group_RetriggerFREExplicitLaunch", "notes": "Show Re-Trigger FRE on AutoLaunch for CoPilot explicitly approved", "team": "EDGE/onRamp and Growth/FRE", "notification_scope": "browser", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "visible_auto_launch"}], "targeting_data": {"min_version": "112", "max_version": "130", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "dynamic_targeting_data": {"is_aad_user": false, "max_chrome_network_usage_in_percentage": 90, "is_chrome_installed": true, "is_retrigger_fre_applicable": true, "copilot_approval_state": [2]}, "target_goal_data": {"has_tried_copilot_on_edge": true}, "courtesy_engine_data": {"dismissal_cap": 1, "acceptance_cap": 1}}, {"campaign_id": "OnRamp_RetriggerFRECodexCoPilotAutoAprd", "notification_id_int": 350, "group_id": "Group_RetriggerFREExplicitLaunch", "notes": "Show Re-Trigger FRE on AutoLaunch for CoPilot explicitly approved", "team": "EDGE/onRamp and Growth/FRE", "notification_scope": "browser", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "visible_auto_launch"}], "targeting_data": {"min_version": "112", "max_version": "150", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "dynamic_targeting_data": {"is_aad_user": false, "max_chrome_network_usage_in_percentage": 90, "is_chrome_installed": true, "is_retrigger_fre_applicable": true, "is_retrigger_fre_or_bing_applicable": true, "is_country_under_dma_regulation": false, "copilot_approval_state": [1, 2]}, "target_goal_data": {"has_tried_copilot_on_edge": true}, "courtesy_engine_data": {"dismissal_cap": 2, "acceptance_cap": 1, "catch_all_cool_down": 0, "browser_launch_cooldown_applied": false}}, {"campaign_id": "OnRamp_RetriggerFREOnEdgeVersionUpgradeExplicitLaunchForEdgePB", "notification_id_int": 157, "notes": "Show re-trigger FRE for Edge PB users on edge version upgrade via explicit launch", "team": "EDGE/onRamp and Growth/FRE", "notification_scope": "profile", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "explicit_browser_launch"}], "targeting_data": {"min_version": "104", "max_version": "130", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "dynamic_targeting_data": {"min_chrome_network_usage_in_percentage": 50, "is_chrome_installed": true, "is_san_switch_on": false, "has_user_seen_new_fre": false, "should_show_fre_on_edge_upgrade": true}, "courtesy_engine_data": {"browser_launch_cooldown_applied": false}}, {"campaign_id": "OnRamp_RetriggerFREOnWinUpgradeExplicitLaunchWithRewards", "notification_id_int": 156, "notes": "Show re-trigger FRE with rewards on win upgrade and explicit launch", "team": "EDGE/onRamp and Growth/FRE", "notification_scope": "profile", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "explicit_browser_launch"}], "targeting_data": {"min_version": "106", "max_version": "130", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "11"}, "dynamic_targeting_data": {"is_aad_user": false, "max_chrome_network_usage_in_percentage": 90, "is_chrome_installed": true, "has_user_seen_new_fre": false, "has_user_upgraded_to_win11": true, "rewards_exp_feature_flag": "msNurturingReTriggerFreOnWinUpgradeExplicitLaunchWithRewards"}, "courtesy_engine_data": {"browser_launch_cooldown_applied": false}}, {"campaign_id": "OnRamp_RetriggerFREOnEdgeVersionUpgradeViaProtocolLaunch", "notification_id_int": 137, "courtesy_engine_data": {"dismissal_cap": 2, "shown_cool_down": 7}, "dynamic_targeting_data": {"has_user_seen_new_fre": false, "is_chrome_installed": true, "max_chrome_network_usage_in_percentage": 90, "should_show_fre_on_edge_upgrade": true}, "notes": "Show re-trigger FRE on edge version upgrade via protocol launch", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "130", "min_version": "104", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "EDGE/onRamp and Growth/FRE", "triggering_data": [{"trigger_name": "prong2ProtocolLaunch"}, {"trigger_name": "windows_prong1"}, {"trigger_name": "ci_upsell_for_prong1_protocol_launch"}], "ui_data": {"surface_type": "exploratory_banner", "primary_button_action_id": 130, "secondary_button_action_id": 92, "number_of_buttons": 1, "banner_data": {"infobar_identifier": 1007, "show_banner_only_on_this_tab": false, "is_closeable": true}}}, {"campaign_id": "OnRamp_RetriggerFREOnWinUpgradeViaProtocolLaunch", "notification_id_int": 121, "courtesy_engine_data": {"dismissal_cap": 2, "shown_cool_down": 7}, "dynamic_targeting_data": {"has_user_seen_new_fre": false, "has_user_upgraded_to_win11": true, "is_chrome_installed": true, "max_chrome_network_usage_in_percentage": 90}, "notes": "Show re-trigger FRE on windows upgrade after protocol launch of edge", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "130", "min_version": "103", "os_version": "11", "supported_os": ["Windows NT"]}, "team": "EDGE/onRamp and Growth/FRE", "triggering_data": [{"trigger_name": "prong2ProtocolLaunch"}, {"trigger_name": "windows_prong1"}, {"trigger_name": "ci_upsell_for_prong1_protocol_launch"}], "ui_data": {"surface_type": "exploratory_banner", "primary_button_action_id": 130, "secondary_button_action_id": 92, "number_of_buttons": 1, "banner_data": {"infobar_identifier": 1007, "show_banner_only_on_this_tab": false, "is_closeable": true}}}, {"campaign_id": "OnRamp_RetriggerFREOnExplicitProtocolPDFLaunchForEdgePostDMA", "notification_id_int": 612, "notes": "Show re-trigger FRE for EU Edge users post DMA via browser launch", "team": "EDGE/onRamp and Growth/FRE", "notification_scope": "browser", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "signin_and_odd_consent_missing_for_dma"}], "targeting_data": {"min_version": "119", "max_version": "150", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "dynamic_targeting_data": {"is_dma": true, "is_post_dma_retrigger_fre_cooldown_applied": false}, "courtesy_engine_data": {"impression_cap": 2, "shown_cool_down": 60, "catch_all_cool_down": 0, "fre_cooldown_policy_applied": false, "browser_launch_cooldown_applied": false}}, {"campaign_id": "OnRamp_RetriggerFREForODDReconsentForEdgePostDMA", "notification_id_int": 703, "notes": "Show re-trigger FRE for DMA users affected by ODD overwrite issue", "team": "EDGE/onRamp and Growth/FRE", "notification_scope": "browser", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "odd_reconsent_for_dma"}], "targeting_data": {"min_version": "119", "max_version": "150", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "dynamic_targeting_data": {"is_dma": true, "should_show_dma_reconsent_asap": true}, "courtesy_engine_data": {"impression_cap": 1, "catch_all_cool_down": 0, "fre_cooldown_policy_applied": false, "browser_launch_cooldown_applied": false}}, {"campaign_id": "OnRamp_Autolaunch", "notification_id_int": 81, "notes": "Show notification to users when autolaunch is enabled for first time or after long duration snooze.", "team": "EDGE/onRamp and Growth/Migration", "notification_scope": "profile", "notification_type": "functional_notification", "should_show_ui_without_managed_preferences": true, "histogram_triggers": [{"histogram_name": "Microsoft.ContinuousMigration.Autolaunch.InitialActivation", "histogram_sample_value": ""}, {"histogram_name": "Microsoft.ContinuousMigration.Autolaunch.ActivatedAfterLongSnoozeDuration", "histogram_sample_value": ""}, {"histogram_name": "Microsoft.ContinuousMigration.Autolaunch.ReActivatedSession", "histogram_sample_value": ""}, {"histogram_name": "Microsoft.ContinuousMigration.Autolaunch.NotificationManagerState", "histogram_sample_value": "4"}], "server_driven_feature_flag_details": {"name": "msNurturingAutolaunchOptOutCampaign", "is_flag_enabled_by_default": true, "should_trigger_usage": false}, "targeting_data": {"min_version": "95", "max_version": "150", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 72, "secondary_button_action_id": 120, "close_button_action_id": 269, "anchor_type": "app_menu_button", "close_button_state": "show"}}, {"campaign_id": "OnRamp_AutolaunchAndOpenTabsIfChromePreInstalled", "notification_id_int": 806, "notes": "Show notification to users when autolaunch is enabled for first time and open tabs import is enabled", "team": "EDGE/onRamp and Growth/FRE", "notification_scope": "profile", "notification_type": "functional_notification", "should_show_ui_without_managed_preferences": true, "histogram_triggers": [{"histogram_name": "Microsoft.ContinuousMigration.Autolaunch.ActivationIfChromePreInstalled", "histogram_sample_value": ""}], "server_driven_feature_flag_details": {"name": "msNurturingFeatureAutolaunchAndOpenTabsCampaign", "is_flag_enabled_by_default": false, "should_trigger_usage": false}, "targeting_data": {"min_version": "128", "max_version": "150", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 278, "secondary_button_action_id": 279, "close_button_action_id": 280, "anchor_type": "app_menu_button", "close_button_state": "show"}}, {"campaign_id": "OnRamp_AdvanceCIConsent", "notification_id_int": 84, "notes": "Show notification on first time CI on new devices.", "team": "EDGE/onRamp and Growth/Migration", "notification_scope": "profile", "notification_type": "functional_notification", "frequency_capping_template": "fyi", "should_show_ui_without_managed_preferences": true, "histogram_triggers": [{"histogram_name": "Microsoft.Nurturing.Campaign.OnRamp_AdvanceCIConsent.CiUpsellState", "histogram_sample_value": "0"}], "targeting_data": {"min_version": "94", "max_version": "150", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "courtesy_engine_data": {"impression_cap": 1}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 75, "secondary_button_action_id": 74, "anchor_type": "app_menu_button", "close_button_state": "show"}}, {"campaign_id": "EdgeIndiaGrowth_SitePinningOnBrowserLaunchModalVariant1", "notification_id_int": 167, "group_id": "Group_IndiaGrowth_SitePinning", "notes": "Show modal promoting sites for pinning.", "team": "Edge/Edge India Personalization/Edge India Growth", "notification_scope": "browser", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "explicit_browser_launch_with_delay"}, {"trigger_name": "browser_launch"}], "targeting_data": {"min_version": "108", "max_version": "130", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "dynamic_targeting_data": {"targeted_region_list": ["IN"]}, "courtesy_engine_data": {"impression_cap": 1, "browser_launch_cooldown_applied": false}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 180, "secondary_button_action_id": 181, "anchor_type": "app_menu_button", "number_of_buttons": 2, "close_button_state": "show", "escape_behaviour": "persist", "configure_close_button_for_browser_close_as_trigger": true}}, {"campaign_id": "EdgeIndiaGrowth_SitePinningOnBrowserLaunchModalVariant2", "notification_id_int": 168, "group_id": "Group_IndiaGrowth_SitePinning", "notes": "Show modal promoting sites for pinning.", "team": "Edge/Edge India Personalization/Edge India Growth", "notification_scope": "browser", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "explicit_browser_launch_with_delay"}, {"trigger_name": "browser_launch"}], "targeting_data": {"min_version": "108", "max_version": "130", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "dynamic_targeting_data": {"targeted_region_list": ["IN"]}, "courtesy_engine_data": {"impression_cap": 1, "browser_launch_cooldown_applied": false}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 180, "secondary_button_action_id": 181, "anchor_type": "app_menu_button", "number_of_buttons": 2, "close_button_state": "show", "escape_behaviour": "persist", "configure_close_button_for_browser_close_as_trigger": true}}, {"campaign_id": "M365OpenLinks_ManagedBanner", "notification_id_int": 362, "notes": "Show managed banner for non-Edge default browser users when using M365 enhanced open links protocol.", "team": "Edge/Enterprise/M365 App Integration", "notification_scope": "browser", "notification_type": "functional_notification", "triggering_data": [{"trigger_name": "m365_open_links_managed_banner"}], "server_driven_feature_flag_details": {"name": "msNurturingM365OpenLinksOptOutPrompt", "is_flag_enabled_by_default": true, "should_trigger_usage": true}, "targeting_data": {"min_version": "109", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "dynamic_targeting_data": {"is_enterprise_user": true}, "courtesy_engine_data": {"impression_cap": 1}, "ui_data": {"surface_type": "banner", "primary_button_action_id": 192, "secondary_button_action_id": 193, "number_of_buttons": 2, "primary_button_same_colour": true, "banner_data": {"is_managed": true, "show_banner_only_on_this_tab": false, "infobar_identifier": 1007, "is_closeable": true, "infobar_button_alignment": 0}}}, {"campaign_id": "M365OpenLinks_TeamsManagedBanner", "notification_id_int": 378, "notes": "Show managed banner for non-Edge default browser users when using M365 enhanced open links protocol.", "team": "Edge/Enterprise/M365 App Integration", "notification_scope": "browser", "notification_type": "functional_notification", "triggering_data": [{"trigger_name": "m365_open_links_teams_managed_banner"}], "server_driven_feature_flag_details": {"name": "msNurturingM365OpenLinksOptOutPrompt", "is_flag_enabled_by_default": true, "should_trigger_usage": true}, "targeting_data": {"min_version": "109", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT", "Mac OS X"], "os_version": "any"}, "courtesy_engine_data": {"impression_cap": 1}, "ui_data": {"surface_type": "banner", "primary_button_action_id": 192, "secondary_button_action_id": 193, "number_of_buttons": 2, "primary_button_same_colour": true, "banner_data": {"is_managed": true, "show_banner_only_on_this_tab": false, "infobar_identifier": 1007, "is_closeable": true, "infobar_button_alignment": 0}}}, {"campaign_id": "OnRamp_PhoenixAvatar", "notification_id_int": 364, "notes": "Show prompt when Phoenix Avatar is enabled.", "team": "EDGE/onRamp and Growth/Phoenix", "notification_scope": "browser", "should_show_ui_without_managed_preferences": true, "histogram_triggers": [{"histogram_name": "Microsoft.Nurturing.Framework.CourtesyEngine.FNState.FN_ProfileChooserView", "histogram_sample_value": "1", "histogram_trigger_on_timeout": true, "histogram_timeout_in_minutes": 15, "histogram_trigger_in_background": false}], "targeting_data": {"min_version": "112", "max_version": "122", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT", "Mac OS X", "Linux"], "os_version": "any"}, "dynamic_targeting_data": {"exclude_on_local_state_pref_false": ["phoenix.avatar_icon_move_enabled"]}, "courtesy_engine_data": {"dismissal_cap": 1, "impression_cap": 1}, "ui_data": {"surface_type": "coachmark", "primary_button_action_id": 92, "secondary_button_action_id": 0, "anchor_type": "avatar", "number_of_buttons": 0, "arrow_type": "top_left", "focus_behaviour": "notify", "close_button_state": "show", "should_set_soft_dismiss_behaviour": true, "coachmark_data": {"should_show_beak": true}}}, {"campaign_id": "Bing_RecommendedSettingsWaitlist", "notification_id_int": 62, "group_id": "Group_Bing_BrowserSettings", "notes": "Show dialog to upsell recommended defaults when user comes from the New Bing Waitlist.", "team": "Bing Growth and Distribution", "notification_scope": "profile", "notification_type": "functional_notification", "has_custom_iris_data_fetcher": false, "triggering_data": [{"trigger_name": "waitlist_defaults"}], "server_driven_feature_flag_details": {"name": "msNurturingRecommendedSettingsWaitlist", "is_flag_enabled_by_default": false, "should_trigger_usage": true}, "targeting_data": {"min_version": "109", "max_version": "130", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "dynamic_targeting_data": {"bing_or_edge_not_default": true, "has_defaults_blocking_policies": false}, "ui_data": {"surface_type": "modal", "primary_button_action_id": 43, "secondary_button_action_id": 92, "close_button_state": "show"}}, {"campaign_id": "OnRamp_CIOpenTabs", "notification_id_int": 76, "notes": "Show notification to users when the tabs are continously imported.", "team": "EDGE/onRamp and Growth/FRE", "notification_scope": "profile", "should_show_ui_without_managed_preferences": true, "notification_type": "functional_notification", "frequency_capping_template": "fyi", "triggering_data": [{"trigger_name": "ci_open_tabs_import_finished"}], "targeting_data": {"min_version": "97", "max_version": "150", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "courtesy_engine_data": {"impression_cap": 1}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 65, "secondary_button_action_id": 66, "close_button_state": "show", "anchor_type": "app_menu_button"}}, {"campaign_id": "EnterpriseSecurity_MitigationBanner", "notification_id_int": 107, "notes": "Show notification when mitigated user settings.", "team": "Edge Fundamentals/App Defaults", "notification_scope": "profile", "notification_type": "functional_notification", "frequency_capping_template": "fyi", "triggering_data": [{"trigger_name": "hijacking_mitigated"}], "server_driven_feature_flag_details": {"name": "msNurturingDefaultsProtectionMitigationBanner", "is_flag_enabled_by_default": true, "should_trigger_usage": true}, "targeting_data": {"min_version": "115", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "dynamic_targeting_data": {"is_enterprise_user": false}, "courtesy_engine_data": {"impression_cap": 1}, "ui_data": {"surface_type": "banner", "primary_button_action_id": 106, "secondary_button_action_id": 107, "number_of_buttons": 2, "banner_data": {"infobar_identifier": 1021, "show_banner_only_on_this_tab": true, "is_closeable": false}}}, {"campaign_id": "EnterpriseSecurity_CmdLineBadUrlMitigationBanner", "notification_id_int": 143, "notes": "Show notification when mitigated bad url in cmd line.", "team": "Edge Fundamentals/App Defaults", "notification_scope": "profile", "notification_type": "functional_notification", "frequency_capping_template": "fyi", "triggering_data": [{"trigger_name": "cmd_line_bad_url_mitigated"}], "server_driven_feature_flag_details": {"name": "msNurturingBadUrlMitigationBanner", "is_flag_enabled_by_default": true, "should_trigger_usage": true}, "targeting_data": {"min_version": "105", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "dynamic_targeting_data": {"is_enterprise_user": false}, "courtesy_engine_data": {"impression_cap": 1}, "ui_data": {"surface_type": "banner", "primary_button_action_id": 141, "secondary_button_action_id": 142, "number_of_buttons": 2, "banner_data": {"infobar_identifier": 1023, "show_banner_only_on_this_tab": true, "is_closeable": false}}}, {"campaign_id": "DualEngine_PinEdge", "notification_id_int": 351, "notes": "Dual Engine Pin Edge", "team": "Edge Enterprise Dual Engine", "notification_scope": "profile", "notification_type": "functional_notification", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "dualengine_pinedge"}], "server_driven_feature_flag_details": {"name": "msNurturingPinEdge", "is_flag_enabled_by_default": true, "should_trigger_usage": true}, "targeting_data": {"min_version": "112", "max_version": "130", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "dynamic_targeting_data": {"should_show_pin_edge_notification": true}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 201, "secondary_button_action_id": 202, "anchor_type": "toolbar", "should_set_soft_dismiss_behaviour": false, "arrow_type": "top_center"}}, {"campaign_id": "DualEngine_IEModePrompt", "notification_id_int": 640, "notes": "Dual Engine IE Mode Prompt", "team": "Edge Enterprise Dual Engine", "notification_scope": "profile", "notification_type": "functional_notification", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "dualengine_iemodeprompt"}], "server_driven_feature_flag_details": {"name": "msNurturingIEModePrompt", "is_flag_enabled_by_default": false, "should_trigger_usage": true}, "targeting_data": {"min_version": "121", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 256, "secondary_button_action_id": 256, "number_of_buttons": 1, "anchor_type": "iemode_button", "should_set_soft_dismiss_behaviour": true, "close_button_state": "show", "arrow_type": "top_right"}}, {"campaign_id": "DualEngine_IEModeSwitch", "notification_id_int": 641, "notes": "Dual Engine IE Mode Switch", "team": "Edge Enterprise Dual Engine", "notification_scope": "profile", "notification_type": "functional_notification", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "dualengine_iemodeswitch"}], "server_driven_feature_flag_details": {"name": "msNurturingIEModeSwitch", "is_flag_enabled_by_default": false, "should_trigger_usage": true}, "targeting_data": {"min_version": "121", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 257, "secondary_button_action_id": 255, "number_of_buttons": 2, "anchor_type": "iemode_button", "should_set_soft_dismiss_behaviour": true, "close_button_state": "show", "arrow_type": "top_right"}}, {"campaign_id": "FN_EdgeMitigateSuspiciousDSE", "notification_id_int": 434, "notes": "Mitigate Suspicious DSE", "team": "Edge Fundamentals/App Defaults", "notification_scope": "profile", "notification_type": "functional_notification", "triggering_data": [{"trigger_name": "suspicious_dse"}], "targeting_data": {"min_version": "115", "max_version": "135", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT", "Mac OS X", "Linux"], "os_version": "any"}, "ui_data": {"surface_type": "banner", "primary_button_action_id": 226, "secondary_button_action_id": 227, "number_of_buttons": 2, "banner_data": {"infobar_identifier": 1034, "show_banner_only_on_this_tab": true, "is_closeable": false}}}, {"campaign_id": "SyncGrowthAndActivation_AccountLevelSyncConsentSyncOff", "notification_id_int": 147, "notes": "Show notification to inform user we have turned sync on for all devices associated with their account.", "team": "EDGE Sync Growth and Activation", "group_id": "Group_SyncGrowthAndActivation_AccountLevelConsent", "notification_scope": "profile", "should_show_ui_without_managed_preferences": true, "rank": 1, "notification_type": "functional_notification", "triggering_data": [{"trigger_name": "account_level_sync_consent"}], "server_driven_feature_flag_details": {"name": "msNurturingAccountLevelSyncConsentSyncOff", "is_flag_enabled_by_default": true, "should_trigger_usage": true}, "targeting_data": {"min_version": "90", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT", "Mac OS X", "Linux"], "os_version": "any"}, "dynamic_targeting_data": {"is_signed_in": true, "is_sync_on": false, "exclude_on_pref_true": ["nurturing.has_engaged_with_account_level_sync_consent"]}, "courtesy_engine_data": {"impression_cap": 1, "catch_all_cool_down": 0, "fre_cooldown_policy_applied": true}, "ui_data": {"surface_type": "coachmark", "primary_button_action_id": 92, "secondary_button_action_id": 92, "anchor_type": "avatar", "number_of_buttons": 0, "arrow_type": "top_right", "campaign_shown_action_id": 151}}, {"campaign_id": "SyncGrowthAndActivation_AccountLevelSyncConsentSyncOn", "notification_id_int": 148, "notes": "Show notification to inform user we have turned history sync on for all devices associated with their account.", "team": "EDGE Sync Growth and Activation", "group_id": "Group_SyncGrowthAndActivation_AccountLevelConsent", "notification_scope": "profile", "should_show_ui_without_managed_preferences": true, "rank": 2, "notification_type": "functional_notification", "triggering_data": [{"trigger_name": "account_level_sync_consent"}], "targeting_data": {"min_version": "90", "max_version": "120", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT", "Mac OS X", "Linux"], "os_version": "any"}, "server_driven_feature_flag_details": {"name": "msNurturingAccountLevelSyncConsentSyncOn", "is_flag_enabled_by_default": true, "should_trigger_usage": true}, "dynamic_targeting_data": {"is_signed_in": true, "is_sync_on": true, "exclude_on_pref_true": ["nurturing.has_engaged_with_account_level_sync_consent"]}, "courtesy_engine_data": {"impression_cap": 1, "catch_all_cool_down": 0}, "ui_data": {"surface_type": "coachmark", "primary_button_action_id": 92, "secondary_button_action_id": 92, "anchor_type": "avatar", "number_of_buttons": 0, "arrow_type": "top_right", "campaign_shown_action_id": 152}}, {"campaign_id": "EdgePreview_EnrollmentNotification", "notification_id_int": 155, "notes": "Shows flyout when user is first enrolled into Edge Preview.", "team": "Edge/Engineering Systems and Deployment/Deployment/Update", "notification_scope": "profile", "should_show_ui_without_managed_preferences": true, "notification_type": "functional_notification", "frequency_capping_template": "fyi", "triggering_data": [{"trigger_name": "edge_preview_enrollment_notification"}], "server_driven_feature_flag_details": {"name": "msNurturingEdgePreview", "is_flag_enabled_by_default": false, "should_trigger_usage": true}, "targeting_data": {"min_version": "106", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "courtesy_engine_data": {"acceptance_cap": 1, "impression_cap": 2, "dismissal_cap": 1}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 92, "secondary_button_action_id": 153, "anchor_type": "app_menu_button", "number_of_buttons": 2, "close_button_state": "show", "focus_behaviour": "notify"}}, {"campaign_id": "M365OpenLinks_InformVariant1", "notification_id_int": 169, "notes": "Show inform prompt with variant 1 for Edge default browser users when using M365 enhanced open links protocol.", "team": "Edge/Enterprise/M365 App Integration", "group_id": "Group_M365OpenLinks_InformOptOut", "notification_scope": "browser", "should_show_ui_without_managed_preferences": true, "notification_type": "functional_notification", "frequency_capping_template": "fyi", "triggering_data": [{"trigger_name": "m365_open_links_inform_variant1"}], "targeting_data": {"min_version": "108", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "courtesy_engine_data": {"impression_cap": 1}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 183, "secondary_button_action_id": 184, "anchor_type": "toolbar", "number_of_buttons": 1, "arrow_type": "top_center"}}, {"campaign_id": "M365OpenLinks_InformVariant2", "notification_id_int": 170, "notes": "Show inform prompt with variant 2 for Edge default browser users when using M365 enhanced open links protocol.", "team": "Edge/Enterprise/M365 App Integration", "group_id": "Group_M365OpenLinks_InformOptOut", "notification_scope": "browser", "notification_type": "functional_notification", "frequency_capping_template": "fyi", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "m365_open_links_inform_variant2"}], "targeting_data": {"min_version": "108", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "courtesy_engine_data": {"impression_cap": 1}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 183, "secondary_button_action_id": 184, "anchor_type": "toolbar", "number_of_buttons": 1, "arrow_type": "top_center"}}, {"campaign_id": "M365OpenLinks_InformVariant3", "notification_id_int": 171, "notes": "Show inform prompt with variant 3 for Edge default browser users when using M365 enhanced open links protocol.", "team": "Edge/Enterprise/M365 App Integration", "group_id": "Group_M365OpenLinks_InformOptOut", "notification_scope": "browser", "notification_type": "functional_notification", "frequency_capping_template": "fyi", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "m365_open_links_inform_variant3"}], "targeting_data": {"min_version": "108", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "courtesy_engine_data": {"impression_cap": 1}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 183, "secondary_button_action_id": 184, "anchor_type": "toolbar", "number_of_buttons": 1, "arrow_type": "top_center"}}, {"campaign_id": "M365OpenLinks_InformVariant4", "notification_id_int": 172, "notes": "Show inform prompt with variant 4 for Edge default browser users when using M365 enhanced open links protocol.", "team": "Edge/Enterprise/M365 App Integration", "group_id": "Group_M365OpenLinks_InformOptOut", "notification_scope": "browser", "notification_type": "functional_notification", "frequency_capping_template": "fyi", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "m365_open_links_inform_variant4"}], "targeting_data": {"min_version": "108", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "courtesy_engine_data": {"impression_cap": 1}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 183, "secondary_button_action_id": 184, "anchor_type": "toolbar", "number_of_buttons": 1, "arrow_type": "top_center"}}, {"campaign_id": "M365OpenLinks_TeamsInformPrompt", "notification_id_int": 368, "notes": "Show Teams inform prompt for Edge default browser users when using M365 enhanced open links protocol.", "team": "Edge/Enterprise/M365 App Integration", "group_id": "Group_M365OpenLinks_InformOptOut", "notification_scope": "browser", "notification_type": "functional_notification", "frequency_capping_template": "fyi", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "m365_open_links_teams_inform_prompt"}], "targeting_data": {"min_version": "109", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "courtesy_engine_data": {"impression_cap": 1}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 183, "secondary_button_action_id": 184, "anchor_type": "toolbar", "number_of_buttons": 1, "arrow_type": "top_center"}}, {"campaign_id": "M365OpenLinks_OptOutVariant1", "notification_id_int": 176, "notes": "Show OptOut prompt with variant 1 for non-Edge default browser users when using M365 enhanced open links protocol.", "team": "Edge/Enterprise/M365 App Integration", "notification_scope": "browser", "notification_type": "functional_notification", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "m365_open_links_optout_variant1"}], "targeting_data": {"min_version": "109", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "server_driven_feature_flag_details": {"name": "msNurturingM365OpenLinksOptOutPrompt", "is_flag_enabled_by_default": true, "should_trigger_usage": true}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 183, "secondary_button_action_id": 184, "anchor_type": "toolbar", "number_of_buttons": 2, "arrow_type": "top_center", "close_button_state": "hide", "primary_button_same_colour": true, "set_focus_on_secondary_button": true}}, {"campaign_id": "OnRamp_CIForEnterpriseBanner", "notification_id_int": 611, "notes": "Show CI campaign to users when they accepted opening Edge via M365 link", "team": "Edge/onRamp and Growth/Migration", "notification_scope": "profile", "courtesy_engine_data": {"shown_cool_down": 60, "dismissal_cool_down": 120}, "dynamic_targeting_data": {"is_aad_user": true, "is_chrome_installed": true, "is_ci_advance_consent_available": false, "is_ciupsell_targetted": true, "check_dma_compliance_for": "ContinuousMigration", "exclude_on_local_state_pref_false": ["continuous_migration.is_allowed_via_policy"], "primary_browser": [1]}, "triggering_data": [{"trigger_name": "protocol_launch_with_m365_link_opted_in"}], "targeting_data": {"min_version": "118", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "ui_data": {"surface_type": "banner", "primary_button_action_id": 250, "secondary_button_action_id": 39, "banner_data": {"infobar_identifier": 1007, "show_banner_only_on_this_tab": true, "is_closeable": true}}}, {"campaign_id": "OnRamp_CIAutoLaunchDogFoodCampaign", "notification_id_int": 717, "notes": " Show modal to gather CI and Auto launch consents from internal Edge India users", "team": "Edge/onRamp and Growth/Migration", "notification_scope": "profile", "notification_type": "functional_notification", "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingCIAutoLaunchDogFoodCampaign", "should_trigger_usage": true}, "courtesy_engine_data": {"browser_launch_cooldown_applied": false, "acceptance_cap": 1, "shown_cool_down": 7, "impression_cap": 2, "dismissal_cool_down": 7, "catch_all_cool_down": 72}, "dynamic_targeting_data": {"is_aad_user": true, "is_chrome_installed": true, "is_ci_or_autolaunch_inactive_for_profile": true, "is_msft_user": true}, "triggering_data": [{"trigger_name": "explicit_browser_launch"}, {"trigger_name": "windows_prong1"}, {"trigger_name": "ci_upsell_for_prong1_protocol_launch"}, {"trigger_name": "prong2ProtocolLaunch"}, {"trigger_name": "windows_search_box"}, {"trigger_name": "spotlightProtocolLaunch"}, {"trigger_name": "ci_upsell_default_browser"}, {"trigger_name": "ci_upsell_default_pdf_reader"}, {"trigger_name": "browser_launch"}], "targeting_data": {"min_version": "124", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "ui_data": {"surface_type": "modal", "primary_button_action_id": 270, "secondary_button_action_id": 39, "close_button_state": "show"}}, {"campaign_id": "M365OpenLinks_OptOutVariant2", "notification_id_int": 177, "notes": "Show OptOut prompt with variant 2 for non-Edge default browser users when using M365 enhanced open links protocol.", "team": "Edge/Enterprise/M365 App Integration", "notification_scope": "browser", "notification_type": "functional_notification", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "m365_open_links_optout_variant2"}], "targeting_data": {"min_version": "109", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "server_driven_feature_flag_details": {"name": "msNurturingM365OpenLinksOptOutPrompt", "is_flag_enabled_by_default": true, "should_trigger_usage": true}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 183, "secondary_button_action_id": 184, "anchor_type": "toolbar", "number_of_buttons": 2, "arrow_type": "top_center", "close_button_state": "hide", "primary_button_same_colour": true, "set_focus_on_secondary_button": true}}, {"campaign_id": "M365OpenLinks_OptOutVariant3", "notification_id_int": 178, "notes": "Show OptOut prompt with variant 3 for non-Edge default browser users when using M365 enhanced open links protocol.", "team": "Edge/Enterprise/M365 App Integration", "notification_scope": "browser", "notification_type": "functional_notification", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "m365_open_links_optout_variant3"}], "server_driven_feature_flag_details": {"name": "msNurturingM365OpenLinksOptOutPrompt", "is_flag_enabled_by_default": true, "should_trigger_usage": true}, "targeting_data": {"min_version": "109", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 183, "secondary_button_action_id": 184, "anchor_type": "toolbar", "number_of_buttons": 2, "arrow_type": "top_center", "close_button_state": "hide", "primary_button_same_colour": true, "set_focus_on_secondary_button": true}}, {"campaign_id": "M365OpenLinks_OptOutVariant4", "notification_id_int": 179, "notes": "Show OptOut prompt with variant 4 for non-Edge default browser users when using M365 enhanced open links protocol.", "team": "Edge/Enterprise/M365 App Integration", "notification_scope": "browser", "notification_type": "functional_notification", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "m365_open_links_optout_variant4"}], "server_driven_feature_flag_details": {"name": "msNurturingM365OpenLinksOptOutPrompt", "is_flag_enabled_by_default": true, "should_trigger_usage": true}, "targeting_data": {"min_version": "109", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 183, "secondary_button_action_id": 184, "anchor_type": "toolbar", "number_of_buttons": 2, "arrow_type": "top_center", "close_button_state": "hide", "primary_button_same_colour": true, "set_focus_on_secondary_button": true}}, {"campaign_id": "M365OpenLinks_OptOutLastFlyOutBingVariant", "notification_id_int": 379, "notes": "Show last OptOut flyout with Bing AI variant for non-Edge default browser users when using M365 enhanced open links protocol.", "team": "Edge/Enterprise/M365 App Integration", "notification_scope": "browser", "notification_type": "functional_notification", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "m365_open_links_optout_last_flyout_bing_variant"}], "server_driven_feature_flag_details": {"name": "msNurturingM365OpenLinksOptOutPrompt", "is_flag_enabled_by_default": true, "should_trigger_usage": true}, "targeting_data": {"min_version": "109", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 183, "secondary_button_action_id": 184, "anchor_type": "toolbar", "number_of_buttons": 2, "arrow_type": "top_center", "close_button_state": "hide", "primary_button_same_colour": true, "set_focus_on_secondary_button": true}}, {"campaign_id": "M365OpenLinks_OptOutSidebarAlwaysShow", "notification_id_int": 387, "notes": "Show OptOut prompt with side bar always show for non-Edge default browser users when using M365 enhanced open links protocol.", "team": "Edge/Enterprise/M365 App Integration", "notification_scope": "browser", "notification_type": "functional_notification", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "m365_open_links_optout_sidebar_always_show"}], "server_driven_feature_flag_details": {"name": "msNurturingM365OpenLinksOptOutPrompt", "is_flag_enabled_by_default": true, "should_trigger_usage": true}, "targeting_data": {"min_version": "109", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 183, "secondary_button_action_id": 184, "anchor_type": "toolbar", "number_of_buttons": 2, "arrow_type": "top_center", "close_button_state": "hide", "primary_button_same_colour": true, "set_focus_on_secondary_button": true}}, {"campaign_id": "M365OpenLinks_OptOutSidebarAutoHide", "notification_id_int": 388, "notes": "Show OptOut prompt with sidebar auto hide for non-Edge default browser users when using M365 enhanced open links protocol.", "team": "Edge/Enterprise/M365 App Integration", "notification_scope": "browser", "notification_type": "functional_notification", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "m365_open_links_optout_sidebar_auto_hide"}], "server_driven_feature_flag_details": {"name": "msNurturingM365OpenLinksOptOutPrompt", "is_flag_enabled_by_default": true, "should_trigger_usage": true}, "targeting_data": {"min_version": "109", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 183, "secondary_button_action_id": 184, "anchor_type": "toolbar", "number_of_buttons": 2, "arrow_type": "top_center", "close_button_state": "hide", "primary_button_same_colour": true, "set_focus_on_secondary_button": true}}, {"campaign_id": "M365OpenLinks_OptOutSidebarAlwaysShowLast", "notification_id_int": 389, "notes": "Show OptOut prompt with side bar always show for non-Edge default browser users when using M365 enhanced open links protocol.", "team": "Edge/Enterprise/M365 App Integration", "notification_scope": "browser", "notification_type": "functional_notification", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "m365_open_links_optout_sidebar_always_show_last"}], "server_driven_feature_flag_details": {"name": "msNurturingM365OpenLinksOptOutPrompt", "is_flag_enabled_by_default": true, "should_trigger_usage": true}, "targeting_data": {"min_version": "109", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 183, "secondary_button_action_id": 184, "anchor_type": "toolbar", "number_of_buttons": 2, "arrow_type": "top_center", "close_button_state": "hide", "primary_button_same_colour": true, "set_focus_on_secondary_button": true}}, {"campaign_id": "M365OpenLinks_OptOutSidebarAutoHideLast", "notification_id_int": 390, "notes": "Show OptOut prompt with sidebar auto hide for non-Edge default browser users when using M365 enhanced open links protocol.", "team": "Edge/Enterprise/M365 App Integration", "notification_scope": "browser", "notification_type": "functional_notification", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "m365_open_links_optout_sidebar_auto_hide_last"}], "server_driven_feature_flag_details": {"name": "msNurturingM365OpenLinksOptOutPrompt", "is_flag_enabled_by_default": true, "should_trigger_usage": true}, "targeting_data": {"min_version": "109", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 183, "secondary_button_action_id": 184, "anchor_type": "toolbar", "number_of_buttons": 2, "arrow_type": "top_center", "close_button_state": "hide", "primary_button_same_colour": true, "set_focus_on_secondary_button": true}}, {"campaign_id": "M365OpenLinks_OptOutBannerVariant1", "notification_id_int": 180, "notes": "Show OptOut banner with variant 1 for non-Edge default browser users when using M365 enhanced open links protocol.", "team": "Edge/Enterprise/M365 App Integration", "notification_scope": "browser", "notification_type": "functional_notification", "triggering_data": [{"trigger_name": "m365_open_links_optout_banner_variant1"}], "targeting_data": {"min_version": "109", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "server_driven_feature_flag_details": {"name": "msNurturingM365OpenLinksOptOutPrompt", "is_flag_enabled_by_default": true, "should_trigger_usage": true}, "ui_data": {"surface_type": "banner", "primary_button_action_id": 192, "secondary_button_action_id": 193, "number_of_buttons": 2, "primary_button_same_colour": true, "banner_data": {"is_managed": false, "show_banner_only_on_this_tab": false, "infobar_identifier": 1007, "is_closeable": false, "infobar_button_alignment": 0}}}, {"campaign_id": "M365OpenLinks_OptOutBannerVariant2", "notification_id_int": 181, "notes": "Show OptOut banner with variant 2 for non-Edge default browser users when using M365 enhanced open links protocol.", "team": "Edge/Enterprise/M365 App Integration", "notification_scope": "browser", "notification_type": "functional_notification", "triggering_data": [{"trigger_name": "m365_open_links_optout_banner_variant2"}], "targeting_data": {"min_version": "109", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "server_driven_feature_flag_details": {"name": "msNurturingM365OpenLinksOptOutPrompt", "is_flag_enabled_by_default": true, "should_trigger_usage": true}, "ui_data": {"surface_type": "banner", "primary_button_action_id": 192, "secondary_button_action_id": 193, "number_of_buttons": 2, "primary_button_same_colour": true, "banner_data": {"is_managed": false, "show_banner_only_on_this_tab": false, "infobar_identifier": 1007, "is_closeable": false, "infobar_button_alignment": 0}}}, {"campaign_id": "M365OpenLinks_OptOutBannerVariant3", "notification_id_int": 182, "notes": "Show OptOut banner with variant 3 for non-Edge default browser users when using M365 enhanced open links protocol.", "team": "Edge/Enterprise/M365 App Integration", "notification_scope": "browser", "notification_type": "functional_notification", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "m365_open_links_optout_banner_variant3"}], "server_driven_feature_flag_details": {"name": "msNurturingM365OpenLinksOptOutPrompt", "is_flag_enabled_by_default": true, "should_trigger_usage": true}, "targeting_data": {"min_version": "109", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}}, {"campaign_id": "M365OpenLinks_OptOutBannerVariant4", "notification_id_int": 183, "notes": "Show OptOut banner with variant 4 for non-Edge default browser users when using M365 enhanced open links protocol.", "team": "Edge/Enterprise/M365 App Integration", "notification_scope": "browser", "notification_type": "functional_notification", "triggering_data": [{"trigger_name": "m365_open_links_optout_banner_variant4"}], "targeting_data": {"min_version": "109", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "server_driven_feature_flag_details": {"name": "msNurturingM365OpenLinksOptOutPrompt", "is_flag_enabled_by_default": true, "should_trigger_usage": true}, "ui_data": {"surface_type": "banner", "primary_button_action_id": 192, "secondary_button_action_id": 193, "number_of_buttons": 2, "primary_button_same_colour": true, "banner_data": {"is_managed": false, "show_banner_only_on_this_tab": false, "infobar_identifier": 1007, "is_closeable": false, "infobar_button_alignment": 0}}}, {"campaign_id": "M365OpenLinks_OptOutModalVariant1", "notification_id_int": 189, "notes": "Show OptOut blocking prompt with variant 1 for non-Edge default browser users when using M365 enhanced open links protocol.", "team": "Edge/Enterprise/M365 App Integration", "notification_scope": "browser", "notification_type": "functional_notification", "triggering_data": [{"trigger_name": "m365_open_links_optout_modal_variant1"}], "targeting_data": {"min_version": "109", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "server_driven_feature_flag_details": {"name": "msNurturingM365OpenLinksOptOutPrompt", "is_flag_enabled_by_default": true, "should_trigger_usage": true}, "ui_data": {"surface_type": "modal", "primary_button_action_id": 183, "secondary_button_action_id": 184, "close_button_state": "hide", "primary_button_same_colour": true, "set_focus_on_secondary_button": true}}, {"campaign_id": "M365OpenLinks_OptOutModalVariant2", "notification_id_int": 190, "notes": "Show OptOut blocking prompt with variant 2 for non-Edge default browser users when using M365 enhanced open links protocol.", "team": "Edge/Enterprise/M365 App Integration", "notification_scope": "browser", "notification_type": "functional_notification", "triggering_data": [{"trigger_name": "m365_open_links_optout_modal_variant2"}], "server_driven_feature_flag_details": {"name": "msNurturingM365OpenLinksOptOutPrompt", "is_flag_enabled_by_default": true, "should_trigger_usage": true}, "targeting_data": {"min_version": "109", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "ui_data": {"surface_type": "modal", "primary_button_action_id": 183, "secondary_button_action_id": 184, "close_button_state": "hide", "primary_button_same_colour": true, "set_focus_on_secondary_button": true}}, {"campaign_id": "M365OpenLinks_OptOutModalVariant4", "notification_id_int": 192, "notes": "Show OptOut blocking prompt with variant 4 for non-Edge default browser users when using M365 enhanced open links protocol.", "team": "Edge/Enterprise/M365 App Integration", "notification_scope": "browser", "notification_type": "functional_notification", "triggering_data": [{"trigger_name": "m365_open_links_optout_modal_variant4"}], "server_driven_feature_flag_details": {"name": "msNurturingM365OpenLinksOptOutPrompt", "is_flag_enabled_by_default": true, "should_trigger_usage": true}, "targeting_data": {"min_version": "109", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "ui_data": {"surface_type": "modal", "primary_button_action_id": 183, "secondary_button_action_id": 184, "close_button_state": "hide", "primary_button_same_colour": true, "set_focus_on_secondary_button": true}}, {"campaign_id": "M365OpenLinks_TeamsOptOutVariant1", "notification_id_int": 370, "notes": "Show Teams OptOut prompt with variant 1 for non-Edge default browser users when using M365 enhanced open links protocol.", "team": "Edge/Enterprise/M365 App Integration", "notification_scope": "browser", "notification_type": "functional_notification", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "m365_open_links_teams_optout_prompt_variant1"}], "targeting_data": {"min_version": "109", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "server_driven_feature_flag_details": {"name": "msNurturingM365OpenLinksOptOutPrompt", "is_flag_enabled_by_default": true, "should_trigger_usage": true}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 183, "secondary_button_action_id": 184, "anchor_type": "toolbar", "number_of_buttons": 2, "arrow_type": "top_center", "close_button_state": "hide", "primary_button_same_colour": true, "set_focus_on_secondary_button": true}}, {"campaign_id": "M365OpenLinks_TeamsOptOutVariant2", "notification_id_int": 371, "notes": "Show OptOut prompt with variant 2 for non-Edge default browser users when using M365 enhanced open links protocol.", "team": "Edge/Enterprise/M365 App Integration", "notification_scope": "browser", "notification_type": "functional_notification", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "m365_open_links_teams_optout_prompt_variant2"}], "server_driven_feature_flag_details": {"name": "msNurturingM365OpenLinksOptOutPrompt", "is_flag_enabled_by_default": true, "should_trigger_usage": true}, "targeting_data": {"min_version": "109", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 183, "secondary_button_action_id": 184, "anchor_type": "toolbar", "number_of_buttons": 2, "arrow_type": "top_center", "close_button_state": "hide", "primary_button_same_colour": true, "set_focus_on_secondary_button": true}}, {"campaign_id": "M365OpenLinks_TeamsOptOutVariant3", "notification_id_int": 372, "notes": "Show OptOut prompt with variant 3 for non-Edge default browser users when using M365 enhanced open links protocol.", "team": "Edge/Enterprise/M365 App Integration", "notification_scope": "browser", "notification_type": "functional_notification", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "m365_open_links_teams_optout_prompt_variant3"}], "server_driven_feature_flag_details": {"name": "msNurturingM365OpenLinksOptOutPrompt", "is_flag_enabled_by_default": true, "should_trigger_usage": true}, "targeting_data": {"min_version": "109", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 183, "secondary_button_action_id": 184, "anchor_type": "toolbar", "number_of_buttons": 2, "arrow_type": "top_center", "close_button_state": "hide", "primary_button_same_colour": true, "set_focus_on_secondary_button": true}}, {"campaign_id": "M365OpenLinks_TeamsOptOutBannerVariant1", "notification_id_int": 374, "notes": "Show Teams OptOut banner with variant 1 for non-Edge default browser users when using M365 enhanced open links protocol.", "team": "Edge/Enterprise/M365 App Integration", "notification_scope": "browser", "notification_type": "functional_notification", "triggering_data": [{"trigger_name": "m365_open_links_teams_optout_banner_variant1"}], "targeting_data": {"min_version": "109", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "server_driven_feature_flag_details": {"name": "msNurturingM365OpenLinksOptOutPrompt", "is_flag_enabled_by_default": true, "should_trigger_usage": true}, "ui_data": {"surface_type": "banner", "primary_button_action_id": 192, "secondary_button_action_id": 193, "number_of_buttons": 2, "primary_button_same_colour": true, "banner_data": {"is_managed": false, "show_banner_only_on_this_tab": false, "infobar_identifier": 1007, "is_closeable": false, "infobar_button_alignment": 0}}}, {"campaign_id": "M365OpenLinks_TeamsOptOutBannerVariant2", "notification_id_int": 375, "notes": "Show Teams OptOut banner with variant 2 for non-Edge default browser users when using M365 enhanced open links protocol.", "team": "Edge/Enterprise/M365 App Integration", "notification_scope": "browser", "notification_type": "functional_notification", "triggering_data": [{"trigger_name": "m365_open_links_teams_optout_banner_variant2"}], "targeting_data": {"min_version": "109", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "server_driven_feature_flag_details": {"name": "msNurturingM365OpenLinksOptOutPrompt", "is_flag_enabled_by_default": true, "should_trigger_usage": true}, "ui_data": {"surface_type": "banner", "primary_button_action_id": 192, "secondary_button_action_id": 193, "number_of_buttons": 2, "primary_button_same_colour": true, "banner_data": {"is_managed": false, "show_banner_only_on_this_tab": false, "infobar_identifier": 1007, "is_closeable": false, "infobar_button_alignment": 0}}}, {"campaign_id": "M365OpenLinks_TeamsInformBanner", "notification_id_int": 369, "notes": "Show Teams OptOut banner for Edge default browser users when using M365 enhanced open links protocol.", "team": "Edge/Enterprise/M365 App Integration", "group_id": "Group_M365OpenLinks_InformOptOut", "notification_scope": "browser", "notification_type": "functional_notification", "frequency_capping_template": "fyi", "triggering_data": [{"trigger_name": "m365_open_links_teams_inform_banner"}], "targeting_data": {"min_version": "109", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "courtesy_engine_data": {"impression_cap": 1}, "ui_data": {"surface_type": "banner", "primary_button_action_id": 192, "secondary_button_action_id": 193, "number_of_buttons": 1, "banner_data": {"show_banner_only_on_this_tab": false, "infobar_identifier": 1007, "is_closeable": true, "infobar_button_alignment": 0}}}, {"campaign_id": "M365OpenLinks_InformBannerVariant1", "notification_id_int": 184, "notes": "Show OptOut banner with variant 1 for Edge default browser users when using M365 enhanced open links protocol.", "team": "Edge/Enterprise/M365 App Integration", "group_id": "Group_M365OpenLinks_InformOptOut", "notification_scope": "browser", "notification_type": "functional_notification", "frequency_capping_template": "fyi", "triggering_data": [{"trigger_name": "m365_open_links_inform_banner_variant1"}], "targeting_data": {"min_version": "109", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "courtesy_engine_data": {"impression_cap": 1}, "ui_data": {"surface_type": "banner", "primary_button_action_id": 192, "secondary_button_action_id": 193, "number_of_buttons": 1, "banner_data": {"show_banner_only_on_this_tab": false, "infobar_identifier": 1007, "is_closeable": true, "infobar_button_alignment": 0}}}, {"campaign_id": "M365OpenLinks_InformBannerVariant2", "notification_id_int": 185, "notes": "Show OptOut banner with variant 2 for Edge default browser users when using M365 enhanced open links protocol.", "team": "Edge/Enterprise/M365 App Integration", "group_id": "Group_M365OpenLinks_InformOptOut", "notification_scope": "browser", "notification_type": "functional_notification", "frequency_capping_template": "fyi", "triggering_data": [{"trigger_name": "m365_open_links_inform_banner_variant2"}], "targeting_data": {"min_version": "109", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "courtesy_engine_data": {"impression_cap": 1}, "ui_data": {"surface_type": "banner", "primary_button_action_id": 192, "secondary_button_action_id": 193, "number_of_buttons": 1, "banner_data": {"show_banner_only_on_this_tab": false, "infobar_identifier": 1007, "is_closeable": true, "infobar_button_alignment": 0}}}, {"campaign_id": "M365OpenLinks_InformBannerVariant3", "notification_id_int": 186, "notes": "Show OptOut banner with variant 3 for Edge default browser users when using M365 enhanced open links protocol.", "team": "Edge/Enterprise/M365 App Integration", "group_id": "Group_M365OpenLinks_InformOptOut", "notification_scope": "browser", "notification_type": "functional_notification", "frequency_capping_template": "fyi", "triggering_data": [{"trigger_name": "m365_open_links_inform_banner_variant3"}], "targeting_data": {"min_version": "109", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "courtesy_engine_data": {"impression_cap": 1}, "ui_data": {"surface_type": "banner", "primary_button_action_id": 192, "secondary_button_action_id": 193, "number_of_buttons": 1, "banner_data": {"show_banner_only_on_this_tab": false, "infobar_identifier": 1007, "is_closeable": true, "infobar_button_alignment": 0}}}, {"campaign_id": "M365OpenLinks_InformBannerVariant4", "notification_id_int": 187, "notes": "Show OptOut banner with variant 4 for Edge default browser users when using M365 enhanced open links protocol.", "team": "Edge/Enterprise/M365 App Integration", "group_id": "Group_M365OpenLinks_InformOptOut", "notification_scope": "browser", "notification_type": "functional_notification", "frequency_capping_template": "fyi", "triggering_data": [{"trigger_name": "m365_open_links_inform_banner_variant4"}], "targeting_data": {"min_version": "109", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "courtesy_engine_data": {"impression_cap": 1}, "ui_data": {"surface_type": "banner", "primary_button_action_id": 192, "secondary_button_action_id": 193, "number_of_buttons": 1, "banner_data": {"show_banner_only_on_this_tab": false, "infobar_identifier": 1007, "is_closeable": true, "infobar_button_alignment": 0}}}, {"campaign_id": "FN_OnRamp_AutolaunchOnWindowsUnlock", "notification_id_int": 361, "notes": "Show notification to users when window unlocks and edge autolaunches.", "team": "EDGE/onRamp and Growth/Migration", "notification_scope": "profile", "notification_type": "functional_notification", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "autolaunch_on_windows_unlock"}], "targeting_data": {"min_version": "112", "max_version": "130", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "courtesy_engine_data": {"impression_cap": 1}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 211, "secondary_button_action_id": 212, "anchor_type": "app_menu_button", "close_button_state": "show"}}, {"campaign_id": "OnRamp_CIAllDataTypesEnabled", "notification_id_int": 393, "notes": "Show flyout to users when all datatypes are enabled for advance converted user.", "team": "EDGE/onRamp and Growth/Migration", "notification_scope": "profile", "notification_type": "functional_notification", "should_show_ui_without_managed_preferences": true, "rank": 7, "triggering_data": [{"trigger_name": "ci_import_ended"}], "targeting_data": {"min_version": "114", "max_version": "150", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "courtesy_engine_data": {"impression_cap": 1}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 219, "secondary_button_action_id": 220, "anchor_type": "app_menu_button", "close_button_state": "show", "focus_behaviour": "notify", "waiting_state_params": {"progress": {"footer_button_present": false}, "success": {"header_present": true, "footer_button_present": false}, "execute_waiting_state_action_on_primary_cta_clicked": false, "execute_waiting_state_action_on_secondary_cta_clicked": true}}}, {"campaign_id": "Workspaces_MsaOnRestoreFlyout", "notification_id_int": 395, "surface_id": "88000504", "notes": "Show notification to users when they try to restore previously closed windows.", "team": "Edge/Enterprise/Fremont", "notification_scope": "profile", "notification_type": "functional_notification", "frequency_capping_template": "fyi", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "workspaces_msa_on_restore_flyout"}], "targeting_data": {"min_version": "115", "max_version": "140", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT", "Mac OS X", "Linux"], "os_version": "any"}, "hub_data": {"show_card_in_hub": true}, "courtesy_engine_data": {"impression_cap": 1}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 224, "secondary_button_action_id": 225, "anchor_type": "app_menu_button", "number_of_buttons": 2, "close_button_state": "show"}}, {"campaign_id": "Bing_RecommendedSettingsExplicitLaunch", "notification_id_int": 391, "dynamic_targeting_data": {"bing_or_edge_not_default": true, "has_defaults_blocking_policies": false, "non_chrome_pb_cool_down_eligible": true, "is_aad_user": false, "excluded_region_list": ["CN"]}, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRecommendedSettingsExplicitLaunch", "should_trigger_usage": true}, "group_id": "Group_Bing_BrowserSettings", "has_custom_iris_data_fetcher": false, "notes": "Show modal dialog to upsell recommended defaults when user comes from Windows Search Box.", "notification_scope": "profile", "rank": 4, "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "140", "min_version": "113", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Bing Growth and Distribution", "triggering_data": [{"trigger_name": "explicit_browser_launch"}], "courtesy_engine_data": {"browser_launch_cooldown_applied": false}, "ui_data": {"surface_type": "modal", "primary_button_action_id": 217, "secondary_button_action_id": 92, "close_button_state": "show"}}, {"campaign_id": "RecommendedSettingsChina", "notification_id_int": 35, "dynamic_targeting_data": {"edge_bing_pdf_startup_not_all_default": true, "is_aad_user": false, "targeted_region_list": ["CN", "TW", "HK", "KR", "ID", "PH", "VN", "TH"]}, "courtesy_engine_data": {"browser_launch_cooldown_applied": false}, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": true, "name": "msNurturingFeatureRecommendedSettingsChina", "should_trigger_usage": true}, "group_id": "Group_BingChina_RecommendedSettings", "hub_data": {"show_card_in_hub": true}, "notes": "Show dialog to upsell recommended settings.", "notification_scope": "profile", "rank": 16, "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "92", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge/onRamp and Growth/Nurturing", "triggering_data": [{"trigger_name": "explicit_browser_launch_with_delay"}], "ui_data": {"is_custom_surface_type": true, "surface_type": "modal", "load_time_data_bool": {"standardLaunch": false, "standardLaunchWithDSE": false, "noImageFeature": false, "standardLaunchV2Ui": false, "v2UiCloseBtn": false}, "load_time_data_string": {"setAsDefaultImageFormat": "webp"}}}, {"campaign_id": "BingChina_UpsellDefaultSettingsFlyout", "notification_id_int": 136, "dynamic_targeting_data": {"edge_bing_pdf_startup_not_all_default": true, "is_aad_user": false, "targeted_region_list": ["CN"]}, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingUpsellDefaultSettingsFlyout", "should_trigger_usage": true}, "group_id": "Group_BingChina_RecommendedSettings", "hub_data": {"show_card_in_hub": true}, "notes": "Show flyout to upsell default settings.", "notification_scope": "profile", "should_show_ui_without_managed_preferences": true, "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "120", "min_version": "104", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge/onRamp and Growth/Nurturing", "triggering_data": [{"trigger_name": "explicit_browser_launch_with_delay"}], "ui_data": {"surface_type": "flyout", "primary_button_action_id": 22, "secondary_button_action_id": 92, "number_of_buttons": 2, "close_button_state": "show"}}, {"campaign_id": "Toolbar_PersonalizationFlyout", "notes": "Show flyout to users when Aoraki can declutter features from their toolbar.", "team": "Edge/Experiences and Delighters - Consumer/FRSH - Full Page Exp", "notification_scope": "profile", "notification_type": "functional_notification", "notification_id_int": 573, "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "aoraki_can_declutter_toolbar"}], "server_driven_feature_flag_details": {"name": "msNurturingToolbarPersonalization", "is_flag_enabled_by_default": false, "should_trigger_usage": true}, "targeting_data": {"min_version": "118", "max_version": "130", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}}, {"campaign_id": "EdgeOnMac_OneTimeManualImportStatusSuccess", "notification_id_int": 577, "notes": "Show notification after importing browser data from fre is completed", "team": "Edge on Mac", "notification_scope": "profile", "notification_type": "functional_notification", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "onetime_import_success_on_mac"}], "server_driven_feature_flag_details": {"name": "msNurturingOneTimeImportStatusSuccess", "is_flag_enabled_by_default": true, "should_trigger_usage": false}, "targeting_data": {"min_version": "120", "max_version": "200", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Mac OS X"], "os_version": "any"}, "courtesy_engine_data": {"impression_cap": 1}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 92, "secondary_button_action_id": 92, "number_of_buttons": 1, "should_set_soft_dismiss_behaviour": true}}, {"campaign_id": "EdgeOnMac_OneTimeManualImportStatusFailure", "notification_id_int": 578, "notes": "Show notification after importing browser data from fre is completed", "team": "Edge on Mac", "notification_scope": "profile", "notification_type": "functional_notification", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "onetime_import_failed_on_mac"}], "server_driven_feature_flag_details": {"name": "msNurturingOneTimeImportStatusFailure", "is_flag_enabled_by_default": true, "should_trigger_usage": false}, "targeting_data": {"min_version": "120", "max_version": "200", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Mac OS X"], "os_version": "any"}, "courtesy_engine_data": {"impression_cap": 1}, "ui_data": {"surface_type": "flyout", "primary_button_action_id": 92, "secondary_button_action_id": 241, "should_set_soft_dismiss_behaviour": true}}, {"campaign_id": "EdgeOnMac_SafariPasswordImportFailure", "notification_id_int": 701, "notes": "Show notification after importing passwords from safari is failed", "team": "Edge on Mac", "notification_scope": "profile", "notification_type": "functional_notification", "should_show_ui_without_managed_preferences": false, "triggering_data": [{"trigger_name": "one_time_safari_import_password_failure"}], "server_driven_feature_flag_details": {"name": "msNurturingSafariPasswordImportFailure", "is_flag_enabled_by_default": false, "should_trigger_usage": true}, "targeting_data": {"min_version": "124", "max_version": "200", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Mac OS X"], "os_version": "any"}, "courtesy_engine_data": {"impression_cap": 1}, "ui_data": {"surface_type": "banner", "primary_button_action_id": 241, "secondary_button_action_id": 92, "number_of_buttons": 2, "should_set_soft_dismiss_behaviour": true, "banner_data": {"infobar_identifier": 1038, "show_banner_only_on_this_tab": true, "is_closeable": true}}}, {"campaign_id": "OnRamp_NurturingViaProng_Experiment", "notification_id_int": 713, "notes": "Shows 2nd tab when one launches edge from prong which is a Marketing page", "team": "EDGE/onRamp and Growth/FRE", "notification_scope": "browser", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "prong2ProtocolLaunch"}, {"trigger_name": "windows_prong1"}, {"trigger_name": "ci_upsell_for_prong1_protocol_launch"}], "targeting_data": {"min_version": "125", "max_version": "150", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "dynamic_targeting_data": {"max_chrome_network_usage_in_percentage": 90, "is_chrome_installed": true, "is_ci_advance_consent_available": false, "is_ci_enabled_for_profile": false, "is_non_mvu": true, "get_visible_window_count": 1, "maximum_number_of_open_tabs": 1, "is_aad_user": false, "should_show_campaign_based_on_total_browser_usage": true, "is_country_under_dma_regulation": false, "excluded_region_list": ["CN", "RU"]}, "courtesy_engine_data": {"acceptance_cap": 1, "dismissal_cap": 2, "acceptance_cool_down": 14, "dismissal_cool_down": 14, "browser_launch_cooldown_applied": false}}, {"campaign_id": "OnRamp_DriveTravelUsageViaProng_Experiment", "notification_id_int": 737, "notes": "Shows 2nd tab when one launches edge from prong which is a travel page if it is a travel article", "team": "EDGE/onRamp and Growth/FRE", "notification_scope": "browser", "should_show_ui_without_managed_preferences": true, "triggering_data": [{"trigger_name": "prong2ProtocolLaunch"}, {"trigger_name": "windows_prong1"}, {"trigger_name": "ci_upsell_for_prong1_protocol_launch"}], "targeting_data": {"min_version": "125", "max_version": "150", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "dynamic_targeting_data": {"max_chrome_network_usage_in_percentage": 90, "is_chrome_installed": true, "is_ci_advance_consent_available": false, "is_ci_enabled_for_profile": false, "is_non_mvu": true, "get_visible_window_count": 1, "maximum_number_of_open_tabs": 1, "is_aad_user": false, "should_show_campaign_based_on_total_browser_usage": true, "is_country_under_dma_regulation": false, "excluded_region_list": ["CN", "RU"]}, "courtesy_engine_data": {"acceptance_cap": 1, "dismissal_cap": 3, "acceptance_cool_down": 14, "dismissal_cool_down": 14, "browser_launch_cooldown_applied": false}}, {"campaign_id": "Bing_DefaultBrowserBannerCloseBtn", "notification_id_int": 123, "notes": "Banner to promote setting edge as default browser", "team": "Bing Growth and Distribution", "notification_scope": "profile", "rank": 12, "group_id": "Group_Bing_DefaultBrowserBanner", "has_custom_iris_data_fetcher": false, "triggering_data": [{"trigger_name": "explicit_browser_launch"}, {"trigger_name": "windows_prong1"}, {"trigger_name": "ci_upsell_for_prong1_protocol_launch"}, {"trigger_name": "prong2ProtocolLaunch"}, {"trigger_name": "windows_search_box"}, {"trigger_name": "browser_launch"}], "server_driven_feature_flag_details": {"name": "msNurturingDefaultBrowserBannerCloseBtn", "is_flag_enabled_by_default": false, "should_trigger_usage": true}, "targeting_data": {"min_version": "108", "max_version": "134", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT"], "os_version": "any"}, "dynamic_targeting_data": {"excluded_region_list": ["CN"], "is_defaults_banner_eligible": true, "has_defaults_blocking_policies": false, "is_protocol_launch_with_default_browser_of": {"browser_name_list": ["Opera", "Firefox", "Brave", "Edge"], "is_targeted": false}}, "courtesy_engine_data": {"dismissal_cap": 3, "dismissal_cool_down": 14, "impression_cap": 5, "shown_cool_down": 7, "browser_launch_cooldown_applied": false}, "ui_data": {"surface_type": "banner", "primary_button_action_id": 136, "secondary_button_action_id": 92, "number_of_buttons": 2, "close_button_state": "show", "is_close_button_set_to_ignore_action": true, "banner_data": {"infobar_identifier": 1007, "show_banner_only_on_this_tab": false, "is_closeable": true, "should_disable_colour_override": true, "infobar_button_alignment": 0}}}, {"campaign_id": "Bing_RecommendedSettingsExpandedTriggeringDialog", "notification_id_int": 60, "dynamic_targeting_data": {"bing_or_edge_not_default": true, "excluded_region_list": ["CN", "RU"], "non_chrome_pb_cool_down_eligible": true, "has_defaults_blocking_policies": false, "is_protocol_launch_with_default_browser_of": {"browser_name_list": ["Opera", "Firefox", "Brave"], "is_targeted": false}}, "group_id": "Group_Bing_BrowserSettings", "has_custom_iris_data_fetcher": false, "notes": "Show modal dialog to upsell recommended defaults when user comes from a variety of entrypoints.", "notification_scope": "profile", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "128", "min_version": "108", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Bing Growth and Distribution", "triggering_data": [{"trigger_name": "windows_prong1"}, {"trigger_name": "ci_upsell_for_prong1_protocol_launch"}, {"trigger_name": "prong2ProtocolLaunch"}, {"trigger_name": "spotlightProtocolLaunch"}], "ui_data": {"surface_type": "modal", "primary_button_action_id": 162, "secondary_button_action_id": 163, "close_button_state": "show"}}, {"campaign_id": "Bing_RecommendedSettingsExpandedTriggeringFlyout", "notification_id_int": 61, "dynamic_targeting_data": {"bing_or_edge_not_default": true, "non_chrome_pb_cool_down_eligible": true, "has_defaults_blocking_policies": false, "is_aad_user": false}, "group_id": "Group_Bing_BrowserSettings", "has_custom_iris_data_fetcher": false, "notes": "Show flyout to upsell recommended defaults when user comes from a variety of entrypoints.", "notification_scope": "profile", "should_show_ui_without_managed_preferences": true, "targeting_data": {"channels": ["stable"], "max_version": "128", "min_version": "108", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Bing Growth and Distribution", "triggering_data": [{"trigger_name": "windows_prong1"}, {"trigger_name": "ci_upsell_for_prong1_protocol_launch"}, {"trigger_name": "prong2ProtocolLaunch"}, {"trigger_name": "spotlightProtocolLaunch"}], "ui_data": {"surface_type": "flyout", "primary_button_action_id": 162, "secondary_button_action_id": 163, "anchor_type": "avatar", "number_of_buttons": 2, "close_button_state": "show"}}, {"campaign_id": "OnRamp_CIUpsell_ProtocolLaunch_Banner", "notification_id_int": 41, "dynamic_targeting_data": {"exclude_on_local_state_pref_false": ["continuous_migration.is_allowed_via_policy"], "is_aad_user": false, "is_chrome_installed": true, "is_ci_advance_consent_available": false, "is_ciupsell_targetted": true, "number_of_days_after_fre": 14, "primary_browser": [1], "check_dma_compliance_for": "ContinuousMigration", "should_cap_ci_non_contextual_campaigns": false}, "group_id": "Group_OnRamp_ContinuousImportUpsellCampaigns", "hub_data": {"show_card_in_hub": true}, "notes": "Show notification to enable continuous import when user visits edge through prong1.", "notification_scope": "profile", "surface_id": "88000504", "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "91", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "EDGE/onRamp and Growth/Migration", "triggering_data": [{"trigger_name": "ci_upsell_for_prong1_protocol_launch"}, {"trigger_name": "prong2ProtocolLaunch"}, {"trigger_name": "ci_upsell_default_pdf_reader"}, {"trigger_name": "ci_upsell_default_browser"}, {"trigger_name": "windows_prong1"}], "ui_data": {"surface_type": "banner", "primary_button_action_id": 250, "secondary_button_action_id": 39, "banner_data": {"infobar_identifier": 1007, "show_banner_only_on_this_tab": true, "is_closeable": true}}}, {"campaign_id": "ImageViewer_PreviewDownloadFile", "notification_id_int": 639, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingImageViewerPreviewDownload", "should_trigger_usage": true}, "notes": "Show banner to upsell using image viewer to preview downloaded image", "notification_scope": "profile", "notification_type": "functional_notification", "courtesy_engine_data": {"acceptance_cap": 1, "dismissal_cap": 1, "impression_cap": 1}, "targeting_data": {"channels": ["stable", "canary", "beta", "dev"], "max_version": "150", "min_version": "120", "os_version": "any", "supported_os": ["Windows NT"]}, "team": "Edge/Consumer/Experiences and Delighters/Image Viewer", "triggering_data": [{"trigger_name": "image_viewer_download_navigation"}], "ui_data": {"surface_type": "banner", "primary_button_action_id": 254, "secondary_button_action_id": 262, "number_of_buttons": 2, "should_set_soft_dismiss_behaviour": true, "auto_close_time_in_seconds": 30, "banner_data": {"infobar_identifier": 1037, "show_banner_only_on_this_tab": true, "is_closeable": true, "switch_bold_text": true, "label_font_size_delta": 2}}}, {"campaign_id": "Fundamentals_FN_ReclaimDBForEdgeProtocol", "notification_id_int": 706, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingReclaimDBForEdgeProtocol", "should_trigger_usage": false}, "notes": "Show banner to reclaim default for Microsoft-Edge protocol", "notification_scope": "profile", "notification_type": "functional_notification", "targeting_data": {"channels": ["stable"], "max_version": "160", "min_version": "123", "os_version": "any", "supported_os": ["Windows NT"]}, "courtesy_engine_data": {"dismissal_cap": 1}, "dynamic_targeting_data": {"is_aad_user": false, "is_enterprise_user": false}, "team": "Edge Fundamentals/App Defaults", "triggering_data": [{"trigger_name": "reclaim_DB_for_Edge_protocol"}, {"trigger_name": "reclaim_DB_for_Edge_protocol_type2"}], "ui_data": {"surface_type": "banner", "primary_button_action_id": 265, "secondary_button_action_id": 266, "number_of_buttons": 2, "auto_close_time_in_seconds": 30, "banner_data": {"infobar_identifier": 1040, "show_banner_only_on_this_tab": false, "is_closeable": true}}}], "nurturing_ce_campaign_metadata": [{"campaign_id": "NTP_CourtesyEngineCampaign", "notification_id_int": 198, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": true, "name": "msNurturingNTPCourtesyEngineCampaign", "should_trigger_usage": true}, "group_id": "Group_Bing_BrowserSettings", "notes": "Campaigns rendered by the New Tab Page JavaScript experience", "notification_scope": "profile", "notification_visibility_scope": "tab", "team": "Content Services Anaheim NTP"}, {"campaign_id": "Edge_TranslateVideoCoachmark", "notification_id_int": 810, "notes": "A coach mark for video translation icon", "notification_scope": "profile", "team": "Edge Web Platform", "courtesy_engine_data": {"dismissal_cool_down": 0, "catch_all_cool_down": 0, "shown_cool_down": 0, "browser_launch_cooldown_applied": false}}, {"campaign_id": "FC_NetworkWarningFlyout", "notification_id_int": 317, "notification_scope": "profile", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision"}, {"campaign_id": "SHOPPING_AUTO_SHOW_COUPONS_CHECKOUT", "notification_id_int": 464, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_LOWER_PRICE_FOUND", "notification_id_int": 465, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_REBATES", "notification_id_int": 467, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_REBATES_ORGANIC", "notification_id_int": 471, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_PRICE_HISTORY", "notification_id_int": 472, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_REBATES_ORGANIC_NEW", "notification_id_int": 473, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_REBATES_CHECKOUT", "notification_id_int": 474, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_SERVER_DRIVEN_CHECKOUT", "notification_id_int": 475, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_EXPRESS_CHECKOUT", "notification_id_int": 481, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_SERVER_DRIVEN_ALL_PAGES", "notification_id_int": 482, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_SERVER_DRIVEN_CONFIRMATION_PAGE", "notification_id_int": 483, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_SERVER_DRIVEN_HOME_PAGE", "notification_id_int": 484, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_SERVER_DRIVEN_REWARDS", "notification_id_int": 485, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_COUPONS_CLIPPING", "notification_id_int": 486, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_NOTIFICATION", "notification_id_int": 489, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_OTHER_SELLER", "notification_id_int": 490, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_PROFESSIONAL_REVIEWS", "notification_id_int": 492, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_PACKAGE_TRACKING", "notification_id_int": 495, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_SERVER_DRIVEN_NOTIFICATION", "notification_id_int": 496, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_GUEST_DOMAIN_COUPONS", "notification_id_int": 498, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_SUSTAINABILITY_LANDING", "notification_id_int": 499, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_ATTAINABLE_COUPONS", "notification_id_int": 503, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_GIFT_CARD", "notification_id_int": 504, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_PRINT_GROCERY", "notification_id_int": 505, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_GROCERY_ITEMIZED_CASHBACK", "notification_id_int": 506, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_CONTROLLER", "notification_id_int": 507, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_AUTOS_MARKETPLACE", "notification_id_int": 508, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_AWARENESS_EXPANSION", "notification_id_int": 509, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_SHOPRUNNER", "notification_id_int": 510, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_OUT_OF_STOCK", "notification_id_int": 512, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_GROCERY_ITEMIZED_CASHBACK_LANDING", "notification_id_int": 513, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_SPB_CASHBACK_LANDING", "notification_id_int": 514, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_AWARENESSEXP", "notification_id_int": 515, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AA_CONSENT", "notification_id_int": 516, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_SAN_CONSENT_CONFIRMATION", "notification_id_int": 517, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_CONFIRMATION_SCRIPT", "notification_id_int": 518, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_MOMENT_IN_TIME", "notification_id_int": 519, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_COUPONS_CHECKOUT_MICRO_NOTIFICATION", "notification_id_int": 520, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_TRENDING_COUPONS", "notification_id_int": 521, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_RECOMMENDATIONS_MICRO_NOTIFICATION", "notification_id_int": 522, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_PRODUCT_TRACKING_MICRO_NOTIFICATION", "notification_id_int": 523, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_SURFACE_UPSELL", "notification_id_int": 524, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_ABANDONED_CART", "notification_id_int": 525, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_PRODUCT_TRACKING_BACK_IN_STOCK", "notification_id_int": 526, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_PRODUCT_TRACKING_EXP_PRICE_DROP", "notification_id_int": 527, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_PRODUCT_TRACKING_IMP_PRICE_DROP", "notification_id_int": 528, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_PRODUCT_TRACKING_OUT_OF_STOCK", "notification_id_int": 529, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_CODEX_MICRO_NOTIFICATION", "notification_id_int": 530, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_PERSONALIZED_CASHBACK_MICRO_NOTIFICATION", "notification_id_int": 531, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_PRICE_HISTORY_MICRO_NOTIFICATION", "notification_id_int": 532, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_ORGANIC_CASHBACK_MICRO_NOTIFICATION", "notification_id_int": 533, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_GOODRX", "notification_id_int": 534, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_SIDEPANE_CASHBACK_MICRO_NOTIFICATION", "notification_id_int": 535, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_DISCOVER_MICRO_NOTIFICATION", "notification_id_int": 536, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_REBATES_PDP", "notification_id_int": 538, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_PRODUCT_BEST_COUPON_CONSENT", "notification_id_int": 539, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_CAMPAIGN_PDP_NOTIFICATION", "notification_id_int": 540, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_CAMPAIGN_AWARENESS_NOTIFICATION", "notification_id_int": 541, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_SPB_MICRO_NOTIFICATION", "notification_id_int": 542, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_PRICE_DROP_PLUS_COUPONS", "notification_id_int": 543, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_SPB_NUDGE_MICRO_NOTIFICATION", "notification_id_int": 544, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_ADD_SPB_TO_CART_MICRO_NOTIFICATION", "notification_id_int": 545, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_CASHBACK_PDP_PLUS_COUPONS", "notification_id_int": 547, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_PRICE_DROP_PLUS_CASHBACK", "notification_id_int": 548, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_IBC_UPSELL", "notification_id_int": 549, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_POST_PURCHASE_TRACKING", "notification_id_int": 550, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_TRAVEL_NOTIFICATION_CHEAPER_HOTEL_ROOM", "notification_id_int": 631, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_CJK_PRICE_DROP", "notification_id_int": 632, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_AMAZON_SEARCH_PC", "notification_id_int": 633, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_COUPONS_BACKGROUND_AUTO_APPLY", "notification_id_int": 634, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_CJK_COUPON_FOUND", "notification_id_int": 635, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_LOWER_PRICE_PLUS_CASHBACK_PLUS_COUPONS", "notification_id_int": 636, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_DISCOVER_NOTIFICATION", "notification_id_int": 637, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_TRAVEL_NOTIFICATION_CHEAPER_FLIGHTS", "notification_id_int": 638, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "SHOPPING_AUTO_SHOW_POST_PURCHASE_REWARDS_NOTIFICATION", "notification_id_int": 719, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"campaign_id": "FindOnPage_OpenInSidebar", "notification_id_int": 648, "notification_scope": "profile", "notes": "Open Find on page in sidebar button.", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Web Capabilities", "courtesy_engine_data": {"dismissal_cap": 1, "impression_cap": 1}, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingFindOnPageOpenInSidebarView", "should_trigger_usage": true}}, {"campaign_id": "TabGroupAutogroupingUpsell", "notification_id_int": 653, "notification_scope": "profile", "frequency_capping_template": "skip_caps_and_cooldown", "team": "Edge Consumer Tabs Management"}, {"campaign_id": "SHOPPING_AUTO_SHOW_BING_SEARCH", "notification_id_int": 466, "notification_scope": "profile", "notification_visibility_scope": "tab", "team": "Edge Consumer Content and Verticals", "notification_type": "functional_notification"}, {"campaign_id": "SHOPPING_AUTO_SHOW_REBATES_CONFIRMATION", "notification_id_int": 468, "notification_scope": "profile", "notification_visibility_scope": "tab", "team": "Edge Consumer Content and Verticals", "notification_type": "functional_notification"}, {"campaign_id": "SHOPPING_AUTO_SHOW_REBATES_DEACTIVATED", "notification_id_int": 469, "notification_scope": "profile", "notification_visibility_scope": "tab", "team": "Edge Consumer Content and Verticals", "notification_type": "functional_notification"}, {"campaign_id": "SHOPPING_AUTO_SHOW_REBATES_BING", "notification_id_int": 470, "notification_scope": "profile", "notification_visibility_scope": "tab", "team": "Edge Consumer Content and Verticals", "notification_type": "functional_notification"}, {"campaign_id": "SHOPPING_AUTO_SHOW_PERSONALIZED_CASHBACK", "notification_id_int": 476, "notification_scope": "profile", "notification_visibility_scope": "tab", "team": "Edge Consumer Content and Verticals", "notification_type": "functional_notification"}, {"campaign_id": "SHOPPING_AUTO_SHOW_PERSONALIZED_CASHBACK_CONFIRMATION", "notification_id_int": 477, "notification_scope": "profile", "notification_visibility_scope": "tab", "team": "Edge Consumer Content and Verticals", "notification_type": "functional_notification"}, {"campaign_id": "SHOPPING_AUTO_SHOW_URL_PARAM_REBATES", "notification_id_int": 478, "notification_scope": "profile", "notification_visibility_scope": "tab", "team": "Edge Consumer Content and Verticals", "notification_type": "functional_notification"}, {"campaign_id": "SHOPPING_AUTO_SHOW_URL_PARAM_PRICE_COMPARISON", "notification_id_int": 479, "notification_scope": "profile", "notification_visibility_scope": "tab", "team": "Edge Consumer Content and Verticals", "notification_type": "functional_notification"}, {"campaign_id": "SHOPPING_AUTO_SHOW_URL_PARAM_PRICE_HISTORY", "notification_id_int": 480, "notification_scope": "profile", "notification_visibility_scope": "tab", "team": "Edge Consumer Content and Verticals", "notification_type": "functional_notification"}, {"campaign_id": "SHOPPING_AUTO_SHOW_REBATES_ACTIVATION_FAILED", "notification_id_int": 487, "notification_scope": "profile", "notification_visibility_scope": "tab", "team": "Edge Consumer Content and Verticals", "notification_type": "functional_notification"}, {"campaign_id": "SHOPPING_AUTO_SHOW_REBATES_SWITCHED_TO_MSA", "notification_id_int": 488, "notification_scope": "profile", "notification_visibility_scope": "tab", "team": "Edge Consumer Content and Verticals", "notification_type": "functional_notification"}, {"campaign_id": "SHOPPING_AUTO_SHOW_ITEM_ADDED_TO_CART_FROM_OTHER_SELLER", "notification_id_int": 491, "notification_scope": "profile", "notification_visibility_scope": "tab", "team": "Edge Consumer Content and Verticals", "notification_type": "functional_notification"}, {"campaign_id": "SHOPPING_AUTO_SHOW_VIRTUAL_CARD", "notification_id_int": 493, "notification_scope": "profile", "notification_visibility_scope": "tab", "team": "Edge Consumer Content and Verticals", "notification_type": "functional_notification"}, {"campaign_id": "SHOPPING_AUTO_SHOW_BING_CAMPAIGN", "notification_id_int": 494, "notification_scope": "profile", "notification_visibility_scope": "tab", "team": "Edge Consumer Content and Verticals", "notification_type": "functional_notification"}, {"campaign_id": "SHOPPING_OPEN_FLYOUT_FROM_EDGE_DRIVER", "notification_id_int": 497, "notification_scope": "profile", "notification_visibility_scope": "tab", "team": "Edge Consumer Content and Verticals", "notification_type": "functional_notification"}, {"campaign_id": "SHOPPING_AUTO_SHOW_SUSTAINABILITY_PURCHASE", "notification_id_int": 500, "notification_scope": "profile", "notification_visibility_scope": "tab", "team": "Edge Consumer Content and Verticals", "notification_type": "functional_notification"}, {"campaign_id": "SHOPPING_AUTO_SHOW_REWARDS_ACTIVATION_FAILED", "notification_id_int": 501, "notification_scope": "profile", "notification_visibility_scope": "tab", "team": "Edge Consumer Content and Verticals", "notification_type": "functional_notification"}, {"campaign_id": "SHOPPING_AUTO_SHOW_REWARDS_SWITCHED_TO_MSA", "notification_id_int": 502, "notification_scope": "profile", "notification_visibility_scope": "tab", "team": "Edge Consumer Content and Verticals", "notification_type": "functional_notification"}, {"campaign_id": "SHOPPING_AUTO_SHOW_SHOPRUNNER_CONFIRMATION", "notification_id_int": 511, "notification_scope": "profile", "notification_visibility_scope": "tab", "team": "Edge Consumer Content and Verticals", "notification_type": "functional_notification"}, {"campaign_id": "SHOPPING_AUTO_SHOW_ENROLL_TO_CASHBACK_CONFIRMATION_NOTIFICATION", "notification_id_int": 546, "notification_scope": "profile", "notification_visibility_scope": "tab", "team": "Edge Consumer Content and Verticals", "notification_type": "functional_notification"}, {"campaign_id": "NTP_UpsellCampaign", "notification_id_int": 199, "notes": "Campaigns rendered by the New Tab Page JavaScript experience to upsell a feature or product", "team": "Content Services Anaheim NTP", "server_driven_feature_flag_details": {"name": "msNurturingNTPUpsellCampaign", "is_flag_enabled_by_default": true, "should_trigger_usage": true}, "notification_scope": "profile", "notification_type": "functional_notification", "notification_visibility_scope": "tab"}, {"campaign_id": "NTP_SettingsCampaign", "notification_id_int": 200, "notes": "Campaigns rendered by the New Tab Page JavaScript experience to change a Browser/OS setting", "team": "Content Services Anaheim NTP", "server_driven_feature_flag_details": {"name": "msNurturingNTPSettingsCampaign", "is_flag_enabled_by_default": true, "should_trigger_usage": true}, "notification_scope": "profile", "notification_visibility_scope": "tab", "notification_type": "functional_notification"}, {"campaign_id": "NTP_FunctionalCampaign", "notification_id_int": 201, "notes": "Campaigns rendered by the New Tab Page JavaScript experience for recommending its functionality ", "team": "Content Services Anaheim NTP", "server_driven_feature_flag_details": {"name": "msNurturingNTPFunctionalCampaign", "is_flag_enabled_by_default": true, "should_trigger_usage": true}, "notification_scope": "profile", "notification_visibility_scope": "tab", "notification_type": "functional_notification"}, {"campaign_id": "Bing_Instrumentation", "notification_id_int": 207, "notes": "Show notification to non rewards users", "team": "Bing Growth and Distribution", "server_driven_feature_flag_details": {"name": "msNurturingBingInstrumentation", "is_flag_enabled_by_default": true, "should_trigger_usage": true}, "notification_scope": "profile", "notification_visibility_scope": "tab", "notification_type": "functional_notification"}, {"campaign_id": "FN_DualEngineSwitchMenuView", "notification_id_int": 708, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_IgnoreCawView", "notification_id_int": 208, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_IgnoreCawView2", "notification_id_int": 209, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_IncognitoWindowCountView", "notification_id_int": 210, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_SSOLoginHintConsentView", "notification_id_int": 212, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_WebToBrowserPostSigninView", "notification_id_int": 213, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_WebToBrowserSigninConsentView", "notification_id_int": 214, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_IncognitoMenuView", "notification_id_int": 215, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_ProfileCustomizationView", "notification_id_int": 216, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_DiceWebSignInInterceptionView", "notification_id_int": 218, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_ProfileChooserView", "notification_id_int": 219, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_PasswordAccountChooserDialog", "notification_id_int": 221, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_AppBlockDialogView", "notification_id_int": 222, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_AppPauseDialogView", "notification_id_int": 223, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_ChromeLabsFlyout", "notification_id_int": 304, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_ExtWebstoreModal", "notification_id_int": 312, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_UserSettingsExtensionDisabledBubbleView", "notification_id_int": 722, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_GlobalErrorFlyout", "notification_id_int": 335, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_FileSystemAccessFlyout", "notification_id_int": 316, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_HCWithThemeBubble", "notification_id_int": 297, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_ExtOtherStoreInstallModal", "notification_id_int": 314, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_ExtInstallFrictionModal", "notification_id_int": 313, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_ZoomBubbleView", "notification_id_int": 224, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_ExtensionMigrationFlyout", "notification_id_int": 298, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_GeoLocationPermissionFlyout", "notification_id_int": 334, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_LowDiskSpaceFlyout", "notification_id_int": 324, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_InternetExplorerFlyout", "notification_id_int": 329, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_IndicatorInfoFlyout", "notification_id_int": 330, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_IncognitoClearDataFlyout", "notification_id_int": 336, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_HomeButtonFlyout", "notification_id_int": 302, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_PriceTrackingBubbleCoordinator", "notification_id_int": 226, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_DesktopMediaPickerDialogView", "notification_id_int": 228, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_DownloadToolbarButtonView", "notification_id_int": 229, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_AdAndTrackerInfoBubbleView", "notification_id_int": 230, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_CollaboratorHoverCard", "notification_id_int": 232, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_ContentSettingImageView", "notification_id_int": 233, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_EdgeIntentPickerBubbleView", "notification_id_int": 234, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_ChipController", "notification_id_int": 235, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_FlyingIndicatorBubbleView", "notification_id_int": 236, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_Modal_EdgeCustomizedHRDView", "notification_id_int": 237, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_EdgeWorkspaceBubbleView", "notification_id_int": 238, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_EdgeWorkspaceInviteBubbleView", "notification_id_int": 461, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_EdgeWebshotBubbleView", "notification_id_int": 243, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_EdgeEasyAuthSignInPromptView", "notification_id_int": 246, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_EdgeTippingBubbleView", "notification_id_int": 247, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_EdgeSuperResolutionPopup", "notification_id_int": 348, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_Modal_EdgeOneAuthSigninView", "notification_id_int": 250, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_ExtensionMenuView", "notification_id_int": 251, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_ExtensionWidget", "notification_id_int": 252, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_ExtensionInstallDialog", "notification_id_int": 253, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_Modal_ExtensionInstalledDialog", "notification_id_int": 254, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_Modal_ExtensionPopup", "notification_id_int": 255, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_ReadAloudSettingsBubbleView", "notification_id_int": 257, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_Modal_EdgePermission", "notification_id_int": 258, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_PageInfoPopup", "notification_id_int": 259, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_BookmarkBubleView", "notification_id_int": 260, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_SendTabToSelfPromoView", "notification_id_int": 262, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_SendTabToSelfMobileUpsell", "notification_id_int": 263, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_SendTabToSelfToolbarBubble", "notification_id_int": 264, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_SetAsideSnoozeBubbleView", "notification_id_int": 265, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_TabGroupHelperView", "notification_id_int": 266, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_TabGroupAutogroupingBubbleView", "notification_id_int": 344, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_Identity_SignInReauthCTA", "notification_id_int": 337, "notification_scope": "profile", "server_driven_feature_flag_details": {"name": "msNurturingSignInReauthCTA", "is_flag_enabled_by_default": true, "should_trigger_usage": false}, "notification_type": "functional_notification"}, {"campaign_id": "FN_EdgeTranslateBubbleView", "notification_id_int": 269, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_PrismExplorerBubble", "notification_id_int": 271, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_PasswordBubbleView", "notification_id_int": 340, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_CaptionBubble", "notification_id_int": 273, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_EdgePwaConfirmationFlyout", "notification_id_int": 300, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_WebAppPostInstallView", "notification_id_int": 278, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_WebAppInstallPromptNotification", "notification_id_int": 571, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_BrowserExperiences_VerticalTabsFeedback", "notification_id_int": 280, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_TryProfileSwitcherView", "notification_id_int": 281, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_M365LinksOptOutAdjustSettingView", "notification_id_int": 346, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_RelaunchRecommendedBubble", "notification_id_int": 283, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_SmartExploreVideoFlyout", "notification_id_int": 284, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_AccountSelectionView", "notification_id_int": 285, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_ThrobberBubble", "notification_id_int": 289, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_CustomPasswordAuthDialog", "notification_id_int": 290, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_TabHoverCardBubble", "notification_id_int": 291, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_EmojiFlyout", "notification_id_int": 292, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_MiniCardBubbleView", "notification_id_int": 293, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_BNPLVirtualCardFlyout", "notification_id_int": 343, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_VirtualCardFlyout", "notification_id_int": 803, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_RoamingCardTokenizationFailureFlyout", "notification_id_int": 804, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_HistoryHoverCardView", "notification_id_int": 347, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_SmartSwitchNotificationView", "notification_id_int": 357, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_SmartSwitchBubbleView", "notification_id_int": 366, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_EdgeFundamentals_AlwaysUpToDate", "notification_id_int": 459, "notes": "Shows a dialog box before we restart the browser.", "team": "Edge Fundamentals.", "notification_scope": "browser", "notification_type": "functional_notification"}, {"campaign_id": "FN_SmartSwitchFeatureInfoView", "notification_id_int": 552, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_SplitScreenPermissionFlyout", "notification_id_int": 650, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_Overflow_Menu", "notification_id_int": 659, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "Identity_AccountTransfer", "notification_id_int": 655, "notes": "Show card on flyout to allow user transfer their account to mobile", "notification_scope": "profile", "notification_type": "functional_notification", "team": "Edge Identity Foundation", "server_driven_feature_flag_details": {"name": "msNurturingIdentityAccountTransfer", "is_flag_enabled_by_default": false, "should_trigger_usage": true}, "targeting_data": {"min_version": "122", "channels": ["stable", "canary", "beta", "dev"], "supported_os": ["Windows NT", "Mac OS X", "Linux"], "os_version": "any"}}, {"campaign_id": "FN_ShorelineHoverCard", "notification_id_int": 710, "notification_scope": "browser", "notification_type": "functional_notification"}, {"campaign_id": "FN_MediaFlyout", "notification_id_int": 328, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_StoragePressureFlyout", "notification_id_int": 303, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_DLP_Dialog", "notification_id_int": 712, "notification_scope": "profile", "notification_type": "functional_notification", "server_driven_feature_flag_details": {"name": "msNurturingDLPDialog", "is_flag_enabled_by_default": true, "should_trigger_usage": false}}, {"campaign_id": "FN_ToastDialogFlyout", "notification_id_int": 325, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_ToolbarHoverFlyout", "notification_id_int": 301, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_WdagFlyout", "notification_id_int": 299, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_XboxGameModeFlyout", "notification_id_int": 333, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_SessionAdminFlyout", "notification_id_int": 306, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_ManagedSiteFlyout", "notification_id_int": 327, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_QRGeneratorFlyout", "notification_id_int": 332, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_ExtFriction_Dialog", "notification_id_int": 721, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_VideoTranslate_ParametersSelection_Flyout", "notification_id_int": 745, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_VideoTranslate_Installation_Flyout", "notification_id_int": 746, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_VideoTranslate_Preparation_Flyout", "notification_id_int": 747, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_VideoTranslate_Error_Flyouts", "notification_id_int": 748, "notification_scope": "profile", "notification_type": "functional_notification"}, {"campaign_id": "FN_CriticalNotificationFlyout", "notification_id_int": 310, "notification_scope": "profile", "should_show_ui_without_managed_preferences": true, "notification_type": "functional_notification"}, {"campaign_id": "FN_BatterySaverFlyout", "notification_id_int": 308, "notification_scope": "profile", "should_show_ui_without_managed_preferences": true, "notification_type": "functional_notification"}, {"campaign_id": "FN_HighEfficiencyFlyout", "notification_id_int": 309, "notification_scope": "profile", "should_show_ui_without_managed_preferences": true, "notification_type": "functional_notification"}, {"campaign_id": "FN_EdgeWorkspaceErrorModal", "notification_id_int": 311, "notification_scope": "profile", "should_show_ui_without_managed_preferences": true, "notification_type": "functional_notification"}, {"campaign_id": "FN_CopilotPersonalizationPopup", "notification_id_int": 809, "notification_scope": "profile", "should_show_ui_without_managed_preferences": true, "notification_type": "functional_notification"}], "nurturing_ui_campaign_metadata": [{"campaign_id": "Pdf_SetAsDefault", "notification_id_int": 3, "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingPDFDefaultsFlyout", "should_trigger_usage": true}, "notes": "Show banner to set default browser", "notification_scope": "profile", "should_show_ui_without_managed_preferences": true, "team": "EdgeEducation and PDFPDF Annotations and Experience."}, {"campaign_id": "RecommendedSettings", "notification_id_int": 295, "notes": "Show banner to set default browser", "notification_scope": "profile", "team": "Bing Growth and Distribution.", "server_driven_feature_flag_details": {"is_flag_enabled_by_default": false, "name": "msNurturingRecommendedSettings", "should_trigger_usage": true}, "ui_data": {"is_custom_surface_type": true, "surface_type": "modal", "load_time_data_bool": {"standardLaunch": false, "standardLaunchWithDSE": false, "noImageFeature": false, "standardLaunchV2Ui": false, "v2UiCloseBtn": false}, "load_time_data_string": {"setAsDefaultImageFormat": "webp"}}}], "nurturing_group_metadata": [{"courtesy_engine_data": {"acceptance_cap": 1, "dismissal_cap": 13, "dismissal_cool_down": 28, "impression_cap": 13, "shown_cool_down": 28}, "group_id": "Group_EdgeMobile_MobileUpsell", "notification_id_int": 402, "notes": "Show notification to install mobile application when user navigate several scenario web sites.", "notification_scope": "browser", "server_driven_feature_flag_details": {"name": "msNurturingGroupEdgeMobileUpsell", "is_flag_enabled_by_default": true, "should_trigger_usage": false}, "team": "EDGE Mobile Growth"}, {"courtesy_engine_data": {"dismissal_cool_down": 14, "shown_cool_down": 7}, "group_id": "Group_Bing_WeatherTravel_CompeteJP", "notification_id_int": 618, "notes": "Show notification to users who visited weather or travel related pages.", "notification_scope": "profile", "team": "Bing Growth and Distribution."}, {"courtesy_engine_data": {"acceptance_cap": 3}, "group_id": "Group_OnRamp_PinningCampaigns", "notification_id_int": 403, "notes": "Group for campaigns promoting pinning a frequently visited site to taskbar", "notification_scope": "profile", "team": "EDGE/onRamp and Growth/FRE, Bing Growth and Distribution"}, {"courtesy_engine_data": {"acceptance_cap": 1, "acceptance_cool_down": 28, "campaign_snooze_days_in_managed_preferences": 14, "dismissal_cap": 2, "dismissal_cool_down": 14, "feature_snooze_days_in_managed_preferences": 90, "impression_cap": 3, "shown_cool_down": 14}, "group_id": "Group_Rewards_Coachmark", "notification_id_int": 565, "notes": "Group for coachmark campaigns in Microsoft Rewards", "notification_scope": "profile", "team": "Edge Growth"}, {"courtesy_engine_data": {"acceptance_cap": 1, "acceptance_cool_down": 28, "campaign_snooze_days_in_managed_preferences": 14, "dismissal_cap": 2, "dismissal_cool_down": 14, "feature_snooze_days_in_managed_preferences": 90, "impression_cap": 3, "shown_cool_down": 14}, "group_id": "Group_Rewards_RedemptionCoachmark", "notification_id_int": 554, "notes": "Group for redemption campaigns in Microsoft Rewards", "notification_scope": "profile", "team": "Edge Growth"}, {"group_id": "Group_Bing_NtpHomeStartpage", "notification_id_int": 411, "notes": "Group for campaigns promoting NTP as start page and home page.", "notification_scope": "profile", "team": "Bing Growth and Distribution"}, {"group_id": "Group_EdgeVpn_FirstUseCampaigns", "notification_id_int": 412, "notes": "Group for campaigns promoting Edge VPN discovery.", "notification_scope": "profile", "team": "Edge Web Platform Networking and Storage"}, {"group_id": "Group_VerticalTabs", "notification_id_int": 414, "notes": "Group for vertical tabs campaign.", "notification_scope": "profile", "team": "EDGE Browser Experience CRUX - Core UX"}, {"courtesy_engine_data": {"acceptance_cap": 1, "dismissal_cap": 1, "impression_cap": 1}, "group_id": "Group_EnterpriseSecurity_BadUrlMitigationBanner", "notification_id_int": 416, "notes": "Group for banners to notify users when mitigated bad url.", "notification_scope": "profile", "team": "Edge Fundamentals/App Defaults"}, {"courtesy_engine_data": {"impression_cap": 3}, "group_id": "Group_EdgeEDU_TranslateToast", "notification_id_int": 425, "notes": "Show windows toast notification to promote Translate feature.", "notification_scope": "profile", "team": "Edge EDU Readng and Translation"}, {"courtesy_engine_data": {"acceptance_cap": 1, "catch_all_cool_down": 0, "dismissal_cap": 3, "dismissal_cool_down": 14, "impression_cap": 5, "shown_cool_down": 3}, "group_id": "Group_CI_ZeroState", "notification_id_int": 426, "notes": "Show CI campaign to users when zero password state is detected.", "notification_scope": "profile", "team": "Edge/onRamp and Growth/Migration"}, {"group_id": "Group_EnterpriseFremont_SuggestWorkspaces", "notification_id_int": 656, "notes": "Group for campaigns to suggest workspaces to users.", "notification_scope": "profile", "team": "Edge/Enterprise/Fremont/Fremont Client", "courtesy_engine_data": {"acceptance_cap": 1, "dismissal_cap": 2}}, {"group_id": "Group_AppDefaults_DefaultBrowser", "notification_id_int": 401, "notes": "Show banner to upsell default browser.", "team": "Edge Fundamentals/App Defaults", "notification_scope": "profile"}, {"group_id": "Group_IndiaGrowth_SitePinning", "notification_id_int": 551, "notes": "Show modal promoting sites for pinning.", "team": "Edge/Edge India Personalization/Edge India Growth", "notification_scope": "browser", "courtesy_engine_data": {"impression_cap": 4, "acceptance_cap": 3, "dismissal_cap": 3, "shown_cool_down": 180}, "server_driven_feature_flag_details": {"name": "msNurturingGroupSitePinningGrowthCampaign", "is_flag_enabled_by_default": true, "should_trigger_usage": false}}, {"group_id": "Group_Bing_DefaultBrowserBanner", "notification_id_int": 406, "notes": "Group for campaigns recommending setting edge as default browser in an evergreen banner", "team": "Bing Growth and Distribution", "notification_scope": "profile"}, {"group_id": "Group_SyncGrowthAndActivation_UpsellConsentCampaigns", "notification_id_int": 408, "notes": "Group for campaigns targeted at EU+ users to turn on sync", "team": "Core Browser And Data Team Sync Growth and Activation", "notification_scope": "profile", "courtesy_engine_data": {"impression_cap": 2, "dismissal_cap": 2}}, {"group_id": "Group_OnRamp_ContinuousImportUpsellCampaigns", "notification_id_int": 409, "notes": "Group for continuous import campaigns", "team": "EDGE/onRamp and Growth/Migration", "notification_scope": "profile"}, {"group_id": "Group_EdgeOnMac_RetentionCampaigns", "notification_id_int": 410, "notes": "Group for macOS retention campaigns", "team": "EDGE/onRamp and Growth/MAX-Mac Experiences", "notification_scope": "profile"}, {"group_id": "Group_BingChina_RecommendedSettings", "notification_id_int": 413, "notes": "Group for campaigns upselling default settings in China.", "team": "Edge/onRamp and Growth/Nurturing", "notification_scope": "profile", "courtesy_engine_data": {"dismissal_cool_down": 30, "acceptance_cool_down": 7, "shown_cool_down": 7, "dismissal_cap": 4, "acceptance_cap": 5, "impression_cap": 5}}, {"group_id": "Group_RetriggerFREExplicitLaunch", "notification_id_int": 418, "notes": "Group for Retrigger FRE explicit launch for Windows and Edge Upgrade.", "team": "EDGE/onRamp and Growth/FRE", "notification_scope": "browser", "courtesy_engine_data": {"dismissal_cap": 3, "acceptance_cap": 2, "dismissal_cool_down": 30, "acceptance_cool_down": 60}}, {"group_id": "Group_Bing_BrowserSettings", "notification_id_int": 404, "notes": "Group for campaigns recommending setting bing as search engine and edge as default browser", "team": "Bing Growth and Distribution", "notification_scope": "profile"}, {"group_id": "Group_SyncGrowthAndActivation_AccountLevelConsent", "notification_id_int": 417, "notes": "Group for Account Level Sync Consent Notice.", "notification_scope": "profile", "team": "Edge Sync Growth and Activation"}, {"group_id": "Group_M365OpenLinks_InformOptOut", "notification_id_int": 421, "notes": "Show prompt to inform when using M365 enhanced open links protocol.", "team": "Edge/Enterprise/M365 App Integration", "notification_scope": "browser", "courtesy_engine_data": {"impression_cap": 1}}, {"group_id": "Group_Shopping", "notes": "Used to log histogram. Not a notification group.", "notification_id_int": 463, "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision", "team": "Edge Consumer Content and Verticals"}, {"group_id": "NTP", "notes": "Used to log histogram. Not a notification group.", "notification_id_int": 652, "notification_scope": "profile", "notification_visibility_scope": "tab", "static_policy_template": "only_avoid_collision", "team": "NTP"}], "metadata_templates": [{"template_id": "ntp_profile_scope_tab_functional_notification", "notes": "To be deprecated after the pure_funcational_notification template is integrated.", "template_id_int": 646, "template_owner": "edge-nurf-devs", "notification_scope": "profile", "notification_visibility_scope": "tab", "notification_type": "functional_notification", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision"}, {"template_id": "ntp_profile_scope_tab_feature_campaign", "notes": "Template should be used on feature campaign dialogs . These dialogs do not collide on other dialogs.", "template_id_int": 658, "template_owner": "edge-nurf-devs", "notification_scope": "profile", "notification_visibility_scope": "tab", "frequency_capping_template": "skip_caps_and_cooldown", "static_policy_template": "only_avoid_collision"}, {"template_id": "ntp_profile_scope_tab_pure_functional_notification", "notes": "Template should be used on pure functional notification dialogs. These dialogs may collide on other dialogs.", "template_id_int": 718, "template_owner": "edge-nurf-devs", "notification_scope": "profile", "notification_visibility_scope": "tab", "notification_type": "functional_notification"}]}