# 📊 Tunisie Telecom - Système de Gestion des Scores Clients

Application web moderne pour la gestion et le suivi des scores de performance des clients de Tunisie Telecom.

## 🚀 Fonctionnalités

- **Gestion des clients** : Ajout, modification et suppression des informations clients
- **Suivi des scores** : Calcul et affichage des scores de performance
- **Tableau de bord** : Vue d'ensemble des métriques importantes
- **Interface moderne** : Design responsive avec Tailwind CSS
- **Authentification** : Système de connexion sécurisé

## 🛠️ Technologies Utilisées

- **Frontend** : React 18 + TypeScript
- **Build Tool** : Vite
- **Styling** : Tailwind CSS + shadcn/ui
- **Icons** : Lucide React
- **State Management** : React Query (TanStack Query)
- **Routing** : React Router DOM

## 📦 Installation

### Prérequis
- Node.js (version 18 ou supérieure)
- npm ou yarn

### Étapes d'installation

```bash
# 1. C<PERSON>r le repository
git clone <URL_DU_REPOSITORY>

# 2. Naviguer vers le dossier du projet
cd tunisie-telecom-scores-hub-main

# 3. Installer les dépendances
npm install

# 4. Démarrer le serveur de développement
npm run dev
```

L'application sera accessible sur `http://localhost:3000`

## 🔧 Scripts Disponibles

```bash
# Démarrage du serveur de développement
npm run dev

# Build de production
npm run build

# Build de développement
npm run build:dev

# Linting du code
npm run lint

# Prévisualisation du build
npm run preview
```

## 🎨 Personnalisation

### Logo
Le logo de Tunisie Telecom se trouve dans :
- `public/assets/tunisie-telecom-logo.svg` (version vectorielle)
- `public/assets/tunisie-telecom-logo.png` (version bitmap)

### Couleurs
Les couleurs de la marque sont définies dans `tailwind.config.ts` :
- Bleu principal : `#1E40AF`
- Bleu foncé : `#1E3A8A`
- Gris clair : `#F8FAFC`
- Gris foncé : `#1E293B`

## 🚀 Déploiement

### Build de production
```bash
npm run build
```

Les fichiers de production seront générés dans le dossier `dist/`.

### Serveur web
Vous pouvez déployer les fichiers du dossier `dist/` sur n'importe quel serveur web statique.

## 🔍 Débogage

Pour déboguer l'application avec VS Code :
1. Appuyez sur `F5`
2. Sélectionnez "Launch Edge with Auth (Recommended)"
3. L'application s'ouvrira automatiquement dans le navigateur

## 📝 Structure du Projet

```
src/
├── components/          # Composants réutilisables
│   ├── ui/             # Composants UI de base (shadcn)
│   └── Header.tsx      # En-tête de l'application
├── pages/              # Pages de l'application
│   ├── Index.tsx       # Page d'accueil
│   └── NotFound.tsx    # Page 404
├── lib/                # Utilitaires et helpers
├── hooks/              # Hooks React personnalisés
└── App.tsx             # Composant principal

public/
├── assets/             # Assets statiques (logos, images)
└── favicon.svg         # Favicon de l'application
```

## 🤝 Contribution

1. Fork le projet
2. Créer une branche pour votre fonctionnalité (`git checkout -b feature/nouvelle-fonctionnalite`)
3. Commit vos changements (`git commit -m 'Ajout d'une nouvelle fonctionnalité'`)
4. Push vers la branche (`git push origin feature/nouvelle-fonctionnalite`)
5. Ouvrir une Pull Request

## 📄 Licence

Ce projet est la propriété de Tunisie Telecom.

## 📞 Support

Pour toute question ou support technique, contactez l'équipe de développement de Tunisie Telecom.
