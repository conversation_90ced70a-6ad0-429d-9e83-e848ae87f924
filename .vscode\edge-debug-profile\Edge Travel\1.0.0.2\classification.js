(()=>{"use strict";class s extends class{constructor(){}}{raiseMessageFromHost(t){t&&2==t.length&&"classify"===t[0]&&s.runClassification(t[1],2e3,3)}static runClassification(t,a,e){s.runClassificationInternal(t).then((t=>s.sendClassificationDataToHost(t))).catch((function(i){--e>0?setTimeout((()=>s.runClassification(t,a,e)),a):s.sendClassificationDataToHost(i)}))}static runClassificationInternal(s){return new Promise(((t,a)=>{let e,i="unsupported_page";try{e=JSON.parse(s)}catch(s){return void t(i)}for(let s of e){let a=!0;for(let e of Object.keys(s)){if(0==s[e]){a=!1;break}for(let t of s[e])if(null==document.querySelector(t)){a=!1;break}if(a)return i=e,void t(i)}}a(i)}))}static sendClassificationDataToHost(s){searchAssistJavascriptNativeHandler&&searchAssistJavascriptNativeHandler.sendClassifiedPageType(s)}}window.searchAssistanceClassifier=new s})();