(()=>{"use strict";class e{static FillTextField(e,t){if(!t||0==t.length)return!1;const r=document.querySelector(e.primary);return!!r&&(r.value=t,!0)}static FillTextFieldCustom(t,r,l,i){return"qatar"===r.sub_type&&e.FillTextFieldQatar(t,i)}static SetToggleField(e,t){const r=document.querySelector(e.primary);return!!r&&(r.checked=t,!0)}static ClickButton(e){const t=document.querySelector(e.primary);return!!t&&(t.click(),!0)}static FillDateTextField(t,r,l){if(!l||0==l.length)return!1;const i=document.querySelector(t.primary);if(i){var n=new Date(l);return i.value=e.dateFormat(n,null!=r.date_format?r.date_format:""),!0}return!1}static FillDateTextFieldCustom(t,r,l,i){return"spirit"===r.sub_type&&e.FillDateTextFieldSpirit(t,l,i)}static dateFormat(t,r){switch(r){case"mm/dd/yyyy":return e.mmddyyyy(t,"/");case"dd/mm/yyyy":default:return e.ddmmyyyy(t,"/");case"yyyy/mm/dd":return e.yyyymmdd(t,"/");case"mm-dd-yyyy":return e.mmddyyyy(t,"-");case"dd-mm-yyyy":return e.ddmmyyyy(t,"-");case"yyyy-mm-dd":return e.yyyymmdd(t,"-")}}static mmddyyyy(e,t){return("0"+(e.getMonth()+1)).slice(-2)+t+("0"+e.getDate()).slice(-2)+t+e.getFullYear()}static ddmmyyyy(e,t){return("0"+e.getDate()).slice(-2)+t+("0"+(e.getMonth()+1)).slice(-2)+t+e.getFullYear()}static yyyymmdd(e,t){return e.getFullYear()+t+("0"+(e.getMonth()+1)).slice(-2)+t+("0"+e.getDate()).slice(-2)}static SelectDropdownIndex(e,t){if(!t||0==t.length)return!1;const r=document.querySelector(e.primary);if(r)try{if(Number(t)<r.length)return r.selectedIndex=Number(t),!0}catch(e){return!1}return!1}static SimulateClickOnToggleField(e,t){const r=document.querySelector(e.primary);return!!r&&(r.checked!=t.value&&r.click(),!0)}static FillIncrementDecrementControl(t,r,l){if(!l||0==l.length)return!1;let i=document.querySelector(t.count);if(i){let r=Number(i.value);if(!isNaN(r)&&!isNaN(Number(l))){let n=Number(l)-r;if(0==n)return!0;if(n>0){for(let r=0;r<n;r++)if(0==e.ClickButton({primary:t.increment}))return!1}else if(n<0)for(let r=0;r<Math.abs(n);r++)if(0==e.ClickButton({primary:t.decrement}))return!1;if(i&&i.value==l)return!0}}return!1}static ClickMatchingTextContent(e,t){if(!t||0==t.length)return!1;const r=document.querySelectorAll(e.primary);if(r.length>0)for(let e of r)if(e.textContent.trim()==t)return e.click(),!0;return!1}static FillDateTextFieldSpirit(t,r,l){if(!l||0==l.length)return!1;const i=document.querySelector(t.primary);if(i){i.dispatchEvent(new Event("focus"));let t=new Date(l);if(isNaN(t.getTime()))return!1;const n=13,a=e.mmddyyyy(t,"/");if("ret_date"==r){if(!(i.value.length>n))return!1;i.value=i.value.slice(0,n)+a}else i.value.length>n?i.value=a+" - "+a:i.value=a;return i.dispatchEvent(new Event("change")),i.dispatchEvent(new Event("blur")),!0}return!1}static FillTextFieldQatar(e,t){if(!t||0==t.length)return!1;let r=document.querySelector(e.primary);return!(!r||(r.click(),r.value=t,r.dispatchEvent(new Event("focus")),r.dispatchEvent(new Event("change")),r.dispatchEvent(new Event("input")),r=document.querySelector(e.suggestion),!r)||(r.click(),0))}}class t extends class{constructor(){}}{raiseMessageFromHost(e){e&&3==e.length&&"automate"===e[0]&&this.runAutomation(e[1],e[2])}runAutomation(e,r){try{var l=JSON.parse(e),i=JSON.parse(r)}catch(e){return}let n,a=l.automation_fields;a&&(n=null==i.ret_date||i.ret_date<i.dep_date?l.automation_steps_oneway:l.automation_steps_twoway,n.forEach((e=>{let r=a[e];r&&t.automateField(r.field_selectors,r.field_attributes,r.field_type,r.field_name,1==r.travel_data_field?i[r.field_name]:"")})))}static automateField(t,r,l,i,n){if(!l||!t)return!1;let a=!1;switch(l){case"text_field":a=e.FillTextField(t,n);break;case"text_field_custom":a=e.FillTextFieldCustom(t,r,i,n);break;case"dropdown":a=e.SelectDropdownIndex(t,n);break;case"date_text_field":a=e.FillDateTextField(t,r,n);break;case"date_text_field_custom":a=e.FillDateTextFieldCustom(t,r,i,n);break;case"button":a=e.ClickButton(t);break;case"radio_button":a=e.SetToggleField(t,!0);break;case"toggle_field_click_required":a=e.SimulateClickOnToggleField(t,r);break;case"click_element_matching_text_content":a=e.ClickMatchingTextContent(t,n);break;case"increment_decrement_control":a=e.FillIncrementDecrementControl(t,r,n)}return a}logTelemetry(e){}}window.searchAssistanceAutomator=new t})();