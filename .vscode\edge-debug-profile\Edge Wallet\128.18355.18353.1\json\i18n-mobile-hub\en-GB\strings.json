{"actionCardSeeDetails": "See details", "actionsAndUpcomingTitle": "Actions and forthcoming", "addAPassword": "Add a password", "addMembership": "Add membership", "addPassword": "Add password", "address": "Addresses", "all": "All", "autoSignInToggleDescription": "Automatically sign in to websites using stored credentials. When the feature is off, you’ll be asked for verification every time before signing in to a website.", "autoSignInToggleTitle": "Auto Sign-in", "autofillAuthToggleDescription": "If this is on, you’ll need to verify your identify with a biometric or PIN each time you use autofill.", "autofillAuthToggleTitle": "Require biometric or PIN", "autofillToggleTitle": "Autofill for other Apps", "bookingId": "Booking ID: $1", "cancel": "Cancel", "cancelled": "Cancelled", "cards": "Cards", "cashback": "Cashback", "changePasswordActionButtonTitle": "Change password", "checkIn": "Check-in", "checkOut": "Check-out", "copiedMessage": "$1 copied", "copy": "Copy", "copyLabel": "Copy $1", "deleteExceptionSiteActionButtonTitle": "Remove", "deleteExceptionSiteMessage": "Remove from the declined list?", "deleteExceptionSiteTitle": "Remove", "deletePasswordActionButtonTitle": "Delete", "deletePasswordConfirmButton": "Delete password", "deletePasswordMessage": "Deleting this password will not delete your account on $1", "deletePasswordSuccessMessage": "Password deleted.", "deletePasswordTitle": "Delete password?", "digitalIdMembershipAddMembership": "Add new", "digitalIdMembershipEmptySubtitle": "We store your membership to help you quickly fill in the information when you book flight tickets and provide diverse capabilities to max your savings accordingly.", "digitalIdMembershipEmptyTitle": "There aren't any membership yet", "dismissButton": "<PERSON><PERSON><PERSON>", "done": "Done", "earn": "<PERSON><PERSON><PERSON>", "edit": "Edit", "editMembership": "Edit membership", "editPassword": "Edit password", "editPasswordActionButtonTitle": "Edit", "editPasswordDescription": "Changing the password here only updates it in your Microsoft account. Make sure this password is also updated for your account on $1.", "enterHerePlaceholder": "Enter here", "expiredTickets": "Expired tickets", "hide": "<PERSON>de", "hideLabel": "Hide $1", "hidePasswordButtonLabel": "Hide password", "ignoreLeakedPasswordActionButtonTitle": "Ignore warning", "importPasswordFromCSV": "Import from CSV file", "importPasswordFromGoogleChrome": "Import from Google Chrome", "importPasswordsTitle": "Import passwords", "in1Day": "In 1 day", "inUse": "In use", "inXDays": "In $1 days", "join": "Join", "loginExpired": "Your session has expired, please log on to your account in browser settings to continue.", "manage": "Manage", "membership": "MEMBERSHIP", "membershipAdded": "New membership added", "membershipCardHolderFirstName": "First name", "membershipCardHolderLastName": "Surname", "membershipCardHolderMiddleName": "Middle name", "membershipCardHolderName": "CARDHOLDER", "membershipCopied": "Membership number copied", "membershipCopyButtonLabel": "Copy membership number", "membershipDetails": "Membership details", "membershipFormAddButtonLabel": "Add", "membershipFormNumber": "Number", "membershipFormOptionalField": "(Optional)", "membershipFormSaveButtonLabel": "Save", "membershipInvalid": "Required field(s) missing", "membershipNumber": "MEMBERSHIP NO.", "membershipRemoveTitle": "Are you sure to remove?", "membershipRemoved": "Membership removed", "membershipType": "Membership type", "membershipUpdated": "Membership updated", "memberships": "Memberships", "moreActions": "More", "newPassword": "New password", "offerToSavePasswordsToggleTitle": "Offer to save passwords", "open": "Open", "openChangePasswordLinkDescription": "Open the \"$1\" page to change your password", "openChangePasswordLinkTitle": "Change password", "openPasswordSiteMessage": "Open the \"$1\" page to manage your password.", "openPasswordSiteTitle": "Open site", "openTicketUrlMessage": "Open the \"$1\" page to manage your reservations.", "openTicketUrlTitle": "Manage reservations", "openUrlTitle": "Open the \"$1\"", "passwordAccountCount": "$1 accounts", "passwordAccountDetails": "Account details", "passwordAddFailed": "Failed to add password, the password may already exist.", "passwordAddSuccess": "Password added.", "passwordCheck": "Password check", "passwordCheckDescription": "We check your passwords saved in Edge against a known repository of exposed credentials.", "passwordCheckLeakSubTitle": "Update these passwords now", "passwordCheckNoLeakButIgnoredSubTitle": "You have $1 ignored warnings", "passwordCheckNoLeakButIgnoredSubTitleSingular": "You have 1 ignored warning", "passwordCheckNoLeakSubTitle": "You'll get an alert if a password is leaked", "passwordCheckNoLeakTitle": "No leaked passwords", "passwordCheckNoReusedSubTitle": "You're not reusing any passwords", "passwordCheckNoReusedTitle": "Your passwords are unique", "passwordCheckNoWeakSubTitle": "Your passwords appear hard to guess", "passwordCheckNoWeakTitle": "You're using strong passwords", "passwordCheckReusedSubTitle": "Use unique passwords instead", "passwordCheckStatus": "Ticked passwords for $1 sites", "passwordCheckStatusSingular": "Ticked password for 1 site", "passwordCheckTimeStatus": "Last ticked $1", "passwordCheckTimeStatusXDaysAgo": "$1 days ago", "passwordCheckTimeStatusXDaysAgoSingular": "1 day ago", "passwordCheckWeakSubTitle": "Use stronger passwords instead", "passwordChecknow": "Check now", "passwordCheckup": "Password check", "passwordEditFailed": "Failed to add password.", "passwordEditFailedAlreadyExist": "Failed to add password, the password may already exist.", "passwordEditSuccess": "Password edited.", "passwordEmtpyDescription": "Improve your browsing and autofill experience when you add or import your passwords.", "passwordEmtpyTitle": "Add or import passwords to your Wallet", "passwordExceptionListSiteApps": "$1 declined sites and apps", "passwordIgnoredWarnings": "Ignored warnings", "passwordInvalidFieldMissing": "Required field(s) missing", "passwordLabel": "Password", "passwordLeakActionMessage": "$1 passwords have appeared in leaks", "passwordLeakActionMessageSingular": "1 password has appeared in leaks", "passwordLeakNoteBody": "To secure your accounts, change these passwords now.", "passwordLeakNoteTitle": "These passwords were found in a data breach", "passwordLeakTitle": "Leaked passwords", "passwordLeaked": "$1 leaked passwords", "passwordLeakedMessage": "Password leaked", "passwordLeakedSingular": "1 leaked password", "passwordListSiteApps": "$1 sites and apps", "passwordListSiteAppsFound": "$1 sites and apps found", "passwordListSummary": "$1 sites", "passwordListSummarySingular": "1 site", "passwordPassword": "Password", "passwordReuseAccountCountTitle": "$1 accounts using same password", "passwordReuseNoteBody": "If someone discovers a reused password, it can be used to access your other accounts.", "passwordReuseNoteTitle": "Use unique passwords for every site or app", "passwordReuseTitle": "Reused passwords", "passwordReused": "$1 reused passwords", "passwordReusedSingular": "1 reused password", "passwordSearch": "Search", "passwordSearchAria": "Search passwords", "passwordSearchEmpty": "No sites or apps match your search", "passwordSectionTitle": "$1 leaked passwords", "passwordSectionTitleSingular": "1 leaked password", "passwordSectionViewAll": "View all passwords ($1)", "passwordSectionViewAllCheck": "Check all passwords", "passwordSectionViewAllLeak": "View all leaked passwords ($1)", "passwordSectionViewMore": "View $1 more leaked passwords", "passwordSectionViewMoreSingular": "View 1 more leaked passwords", "passwordSettings": "Settings", "passwordSite": "Site", "passwordSiteDetails": "Site details", "passwordSyncPageTitle": "Passwords Sync", "passwordUsername": "Username", "passwordViewDetails": "View leakage details", "passwordWeak": "$1 weak passwords", "passwordWeakNoteBody": "To secure your accounts, change these passwords now.", "passwordWeakNoteTitle": "Weak passwords are easier to guess. Keep your accounts more secure with a stronger password.", "passwordWeakSingular": "1 weak password", "passwords": "Passwords", "paymentCard": "Payment card", "paymentCardExpireDate": "Expire date", "paymentSectionAddNew": "Add a new card", "paymentSectionTitle": "Payment methods", "prefManaged": "This setting is enforced by your administrator.", "providedBy": "Provided by $1", "remove": "Remove", "removeExceptionSiteSuccessMessage": "Removed", "removeTicketMessage": "Remove this pass from Wallet", "restoreLeakedPasswordActionButtonTitle": "Restore warning", "rewardPoints": "Rewards points", "rewards": "Rewards", "savePassword": "Save", "showPasswordButtonLabel": "Show password", "siteLabel": "Site", "suggestStrongPasswordToggleDescription": "Password suggestions only work when both Offer to save passwords and $1 are turned on.", "suggestStrongPasswordToggleManagedDescription": "Password suggestion setting is enforced by your administrator.", "suggestStrongPasswordToggleTitle": "Suggest strong passwords", "ticketBeRemovedAlert": "Removed $1", "tickets": "Tickets", "ticketsEmptySubTitle": "We can sync your tickets from Bing Travel to Wallet now.", "ticketsEmptyTitle": "There aren't any ticket yet", "ticketsViewExpired": "View $1 expired passes", "ticketsViewExpiredSingular": "View 1 expired pass", "today": "Today", "tomorrow": "Tomorrow", "turnOnSyncButton": "Sync", "turnOnSyncWalletTitle": "Sync your membership across all devices", "usernameLabel": "Username", "view": "View", "viewLabel": "View $1", "wallet": "Wallet"}