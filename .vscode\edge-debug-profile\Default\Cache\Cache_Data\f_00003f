import __vite__cjsImport0_react_jsxDevRuntime from "/node_modules/.vite/deps/react_jsx-dev-runtime.js?v=10e87200"; const jsxDEV = __vite__cjsImport0_react_jsxDevRuntime["jsxDEV"];
import { useLocation, useNavigate } from "/node_modules/.vite/deps/react-router-dom.js?v=10e87200";
import __vite__cjsImport2_react from "/node_modules/.vite/deps/react.js?v=10e87200"; const useEffect = __vite__cjsImport2_react["useEffect"]; const useState = __vite__cjsImport2_react["useState"];
import { Card, CardContent, CardHeader, CardTitle } from "/src/components/ui/card.tsx";
import { Button } from "/src/components/ui/button.tsx";
import { Badge } from "/src/components/ui/badge.tsx";
import { ArrowLeft, User, Phone, CreditCard, Star, TrendingUp, AlertCircle } from "/node_modules/.vite/deps/lucide-react.js?v=10e87200";
import Header from "/src/components/Header.tsx";
import Statistics from "/src/components/Statistics.tsx";
const Dashboard = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [clientData, setClientData] = useState(null);
  const [clientScore, setClientScore] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  useEffect(() => {
    const data = location.state?.clientData;
    if (!data) {
      navigate("/");
      return;
    }
    setClientData(data);
    setTimeout(() => {
      const mockScore = {
        score: Math.floor(Math.random() * 100) + 1,
        niveau: "",
        couleur: "",
        description: "",
        facteurs: {
          paiements: Math.floor(Math.random() * 100) + 1,
          anciennete: Math.floor(Math.random() * 100) + 1,
          utilisation: Math.floor(Math.random() * 100) + 1,
          incidents: Math.floor(Math.random() * 100) + 1
        }
      };
      if (mockScore.score >= 80) {
        mockScore.niveau = "Excellent";
        mockScore.couleur = "bg-green-500";
        mockScore.description = "Client très fiable avec un excellent historique";
      } else if (mockScore.score >= 60) {
        mockScore.niveau = "Bon";
        mockScore.couleur = "bg-blue-500";
        mockScore.description = "Client fiable avec un bon historique";
      } else if (mockScore.score >= 40) {
        mockScore.niveau = "Moyen";
        mockScore.couleur = "bg-yellow-500";
        mockScore.description = "Client avec quelques points d'attention";
      } else {
        mockScore.niveau = "Faible";
        mockScore.couleur = "bg-red-500";
        mockScore.description = "Client nécessitant une attention particulière";
      }
      setClientScore(mockScore);
      setIsLoading(false);
    }, 1500);
  }, [location.state, navigate]);
  const handleNewSearch = () => {
    navigate("/");
  };
  if (isLoading) {
    return /* @__PURE__ */ jsxDEV("div", { className: "min-h-screen bg-gray-50", children: [
      /* @__PURE__ */ jsxDEV(
        Header,
        {
          isAuthenticated: true,
          onLogin: () => {
          },
          onLogout: () => {
          },
          onProfile: () => {
          },
          userName: "Utilisateur"
        },
        void 0,
        false,
        {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
          lineNumber: 95,
          columnNumber: 9
        },
        this
      ),
      /* @__PURE__ */ jsxDEV("div", { className: "container mx-auto px-4 py-8", children: /* @__PURE__ */ jsxDEV("div", { className: "flex items-center justify-center min-h-[400px]", children: /* @__PURE__ */ jsxDEV("div", { className: "text-center", children: [
        /* @__PURE__ */ jsxDEV("div", { className: "animate-spin rounded-full h-12 w-12 border-b-2 border-tunisietelecom-blue mx-auto mb-4" }, void 0, false, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
          lineNumber: 105,
          columnNumber: 15
        }, this),
        /* @__PURE__ */ jsxDEV("p", { className: "text-gray-600", children: "Calcul du score en cours..." }, void 0, false, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
          lineNumber: 106,
          columnNumber: 15
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
        lineNumber: 104,
        columnNumber: 13
      }, this) }, void 0, false, {
        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
        lineNumber: 103,
        columnNumber: 11
      }, this) }, void 0, false, {
        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
        lineNumber: 102,
        columnNumber: 9
      }, this)
    ] }, void 0, true, {
      fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
      lineNumber: 94,
      columnNumber: 7
    }, this);
  }
  if (!clientData || !clientScore) {
    return null;
  }
  return /* @__PURE__ */ jsxDEV("div", { className: "min-h-screen bg-gray-50", children: [
    /* @__PURE__ */ jsxDEV(
      Header,
      {
        isAuthenticated: true,
        onLogin: () => {
        },
        onLogout: () => {
        },
        onProfile: () => {
        },
        userName: "Utilisateur"
      },
      void 0,
      false,
      {
        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
        lineNumber: 120,
        columnNumber: 7
      },
      this
    ),
    /* @__PURE__ */ jsxDEV("div", { className: "container mx-auto px-4 py-8", children: [
      /* @__PURE__ */ jsxDEV("div", { className: "mb-6", children: /* @__PURE__ */ jsxDEV(
        Button,
        {
          onClick: handleNewSearch,
          variant: "outline",
          className: "flex items-center gap-2",
          children: [
            /* @__PURE__ */ jsxDEV(ArrowLeft, { size: 16 }, void 0, false, {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
              lineNumber: 135,
              columnNumber: 13
            }, this),
            "Nouvelle recherche"
          ]
        },
        void 0,
        true,
        {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
          lineNumber: 130,
          columnNumber: 11
        },
        this
      ) }, void 0, false, {
        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
        lineNumber: 129,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDEV(Card, { className: "mb-6", children: [
        /* @__PURE__ */ jsxDEV(CardHeader, { className: "bg-tunisietelecom-blue text-white", children: /* @__PURE__ */ jsxDEV(CardTitle, { className: "flex items-center gap-2", children: [
          /* @__PURE__ */ jsxDEV(User, { size: 24 }, void 0, false, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
            lineNumber: 144,
            columnNumber: 15
          }, this),
          "Informations Client"
        ] }, void 0, true, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
          lineNumber: 143,
          columnNumber: 13
        }, this) }, void 0, false, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
          lineNumber: 142,
          columnNumber: 11
        }, this),
        /* @__PURE__ */ jsxDEV(CardContent, { className: "p-6", children: /* @__PURE__ */ jsxDEV("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4", children: [
          /* @__PURE__ */ jsxDEV("div", { children: [
            /* @__PURE__ */ jsxDEV("p", { className: "text-sm text-gray-500", children: "Nom complet" }, void 0, false, {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
              lineNumber: 151,
              columnNumber: 17
            }, this),
            /* @__PURE__ */ jsxDEV("p", { className: "font-semibold", children: [
              clientData.prenom,
              " ",
              clientData.nom
            ] }, void 0, true, {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
              lineNumber: 152,
              columnNumber: 17
            }, this)
          ] }, void 0, true, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
            lineNumber: 150,
            columnNumber: 15
          }, this),
          clientData.telephone && /* @__PURE__ */ jsxDEV("div", { children: [
            /* @__PURE__ */ jsxDEV("p", { className: "text-sm text-gray-500", children: "Téléphone" }, void 0, false, {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
              lineNumber: 156,
              columnNumber: 19
            }, this),
            /* @__PURE__ */ jsxDEV("p", { className: "font-semibold flex items-center gap-1", children: [
              /* @__PURE__ */ jsxDEV(Phone, { size: 14 }, void 0, false, {
                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
                lineNumber: 158,
                columnNumber: 21
              }, this),
              clientData.telephone
            ] }, void 0, true, {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
              lineNumber: 157,
              columnNumber: 19
            }, this)
          ] }, void 0, true, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
            lineNumber: 155,
            columnNumber: 17
          }, this),
          clientData.numeroClient && /* @__PURE__ */ jsxDEV("div", { children: [
            /* @__PURE__ */ jsxDEV("p", { className: "text-sm text-gray-500", children: "Numéro client" }, void 0, false, {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
              lineNumber: 165,
              columnNumber: 19
            }, this),
            /* @__PURE__ */ jsxDEV("p", { className: "font-semibold flex items-center gap-1", children: [
              /* @__PURE__ */ jsxDEV(CreditCard, { size: 14 }, void 0, false, {
                fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
                lineNumber: 167,
                columnNumber: 21
              }, this),
              clientData.numeroClient
            ] }, void 0, true, {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
              lineNumber: 166,
              columnNumber: 19
            }, this)
          ] }, void 0, true, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
            lineNumber: 164,
            columnNumber: 17
          }, this)
        ] }, void 0, true, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
          lineNumber: 149,
          columnNumber: 13
        }, this) }, void 0, false, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
          lineNumber: 148,
          columnNumber: 11
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
        lineNumber: 141,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDEV(Card, { className: "mb-6", children: [
        /* @__PURE__ */ jsxDEV(CardHeader, { children: /* @__PURE__ */ jsxDEV(CardTitle, { className: "flex items-center gap-2", children: [
          /* @__PURE__ */ jsxDEV(Star, { size: 24, className: "text-yellow-500" }, void 0, false, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
            lineNumber: 180,
            columnNumber: 15
          }, this),
          "Score Client"
        ] }, void 0, true, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
          lineNumber: 179,
          columnNumber: 13
        }, this) }, void 0, false, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
          lineNumber: 178,
          columnNumber: 11
        }, this),
        /* @__PURE__ */ jsxDEV(CardContent, { className: "p-6", children: /* @__PURE__ */ jsxDEV("div", { className: "text-center mb-6", children: [
          /* @__PURE__ */ jsxDEV("div", { className: "inline-flex items-center justify-center w-32 h-32 rounded-full bg-gray-100 mb-4", children: /* @__PURE__ */ jsxDEV("span", { className: "text-4xl font-bold text-tunisietelecom-blue", children: clientScore.score }, void 0, false, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
            lineNumber: 187,
            columnNumber: 17
          }, this) }, void 0, false, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
            lineNumber: 186,
            columnNumber: 15
          }, this),
          /* @__PURE__ */ jsxDEV("div", { className: "space-y-2", children: [
            /* @__PURE__ */ jsxDEV(Badge, { className: `${clientScore.couleur} text-white px-4 py-2 text-lg`, children: clientScore.niveau }, void 0, false, {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
              lineNumber: 192,
              columnNumber: 17
            }, this),
            /* @__PURE__ */ jsxDEV("p", { className: "text-gray-600", children: clientScore.description }, void 0, false, {
              fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
              lineNumber: 195,
              columnNumber: 17
            }, this)
          ] }, void 0, true, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
            lineNumber: 191,
            columnNumber: 15
          }, this)
        ] }, void 0, true, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
          lineNumber: 185,
          columnNumber: 13
        }, this) }, void 0, false, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
          lineNumber: 184,
          columnNumber: 11
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
        lineNumber: 177,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDEV("div", { className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6", children: [
        /* @__PURE__ */ jsxDEV(Card, { children: /* @__PURE__ */ jsxDEV(CardContent, { className: "p-4 text-center", children: [
          /* @__PURE__ */ jsxDEV(TrendingUp, { className: "mx-auto mb-2 text-green-500", size: 24 }, void 0, false, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
            lineNumber: 205,
            columnNumber: 15
          }, this),
          /* @__PURE__ */ jsxDEV("p", { className: "text-sm text-gray-500", children: "Historique paiements" }, void 0, false, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
            lineNumber: 206,
            columnNumber: 15
          }, this),
          /* @__PURE__ */ jsxDEV("p", { className: "text-2xl font-bold", children: [
            clientScore.facteurs.paiements,
            "%"
          ] }, void 0, true, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
            lineNumber: 207,
            columnNumber: 15
          }, this)
        ] }, void 0, true, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
          lineNumber: 204,
          columnNumber: 13
        }, this) }, void 0, false, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
          lineNumber: 203,
          columnNumber: 11
        }, this),
        /* @__PURE__ */ jsxDEV(Card, { children: /* @__PURE__ */ jsxDEV(CardContent, { className: "p-4 text-center", children: [
          /* @__PURE__ */ jsxDEV(User, { className: "mx-auto mb-2 text-blue-500", size: 24 }, void 0, false, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
            lineNumber: 213,
            columnNumber: 15
          }, this),
          /* @__PURE__ */ jsxDEV("p", { className: "text-sm text-gray-500", children: "Ancienneté" }, void 0, false, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
            lineNumber: 214,
            columnNumber: 15
          }, this),
          /* @__PURE__ */ jsxDEV("p", { className: "text-2xl font-bold", children: [
            clientScore.facteurs.anciennete,
            "%"
          ] }, void 0, true, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
            lineNumber: 215,
            columnNumber: 15
          }, this)
        ] }, void 0, true, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
          lineNumber: 212,
          columnNumber: 13
        }, this) }, void 0, false, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
          lineNumber: 211,
          columnNumber: 11
        }, this),
        /* @__PURE__ */ jsxDEV(Card, { children: /* @__PURE__ */ jsxDEV(CardContent, { className: "p-4 text-center", children: [
          /* @__PURE__ */ jsxDEV(Phone, { className: "mx-auto mb-2 text-purple-500", size: 24 }, void 0, false, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
            lineNumber: 221,
            columnNumber: 15
          }, this),
          /* @__PURE__ */ jsxDEV("p", { className: "text-sm text-gray-500", children: "Utilisation services" }, void 0, false, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
            lineNumber: 222,
            columnNumber: 15
          }, this),
          /* @__PURE__ */ jsxDEV("p", { className: "text-2xl font-bold", children: [
            clientScore.facteurs.utilisation,
            "%"
          ] }, void 0, true, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
            lineNumber: 223,
            columnNumber: 15
          }, this)
        ] }, void 0, true, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
          lineNumber: 220,
          columnNumber: 13
        }, this) }, void 0, false, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
          lineNumber: 219,
          columnNumber: 11
        }, this),
        /* @__PURE__ */ jsxDEV(Card, { children: /* @__PURE__ */ jsxDEV(CardContent, { className: "p-4 text-center", children: [
          /* @__PURE__ */ jsxDEV(AlertCircle, { className: "mx-auto mb-2 text-orange-500", size: 24 }, void 0, false, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
            lineNumber: 229,
            columnNumber: 15
          }, this),
          /* @__PURE__ */ jsxDEV("p", { className: "text-sm text-gray-500", children: "Incidents" }, void 0, false, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
            lineNumber: 230,
            columnNumber: 15
          }, this),
          /* @__PURE__ */ jsxDEV("p", { className: "text-2xl font-bold", children: [
            100 - clientScore.facteurs.incidents,
            "%"
          ] }, void 0, true, {
            fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
            lineNumber: 231,
            columnNumber: 15
          }, this)
        ] }, void 0, true, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
          lineNumber: 228,
          columnNumber: 13
        }, this) }, void 0, false, {
          fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
          lineNumber: 227,
          columnNumber: 11
        }, this)
      ] }, void 0, true, {
        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
        lineNumber: 202,
        columnNumber: 9
      }, this),
      /* @__PURE__ */ jsxDEV(Statistics, { clients: [] }, void 0, false, {
        fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
        lineNumber: 237,
        columnNumber: 9
      }, this)
    ] }, void 0, true, {
      fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
      lineNumber: 128,
      columnNumber: 7
    }, this)
  ] }, void 0, true, {
    fileName: "C:/Users/<USER>/Downloads/tunisie-telecom-scores-hub-main/tunisie-telecom-scores-hub-main/src/pages/Dashboard.tsx",
    lineNumber: 119,
    columnNumber: 5
  }, this);
};
export default Dashboard;

//# sourceMappingURL=data:application/json;base64,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