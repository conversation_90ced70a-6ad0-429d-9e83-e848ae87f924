import { useLocation, useNavigate } from "react-router-dom";
import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, User, Phone, CreditCard, Star, TrendingUp, AlertCircle } from "lucide-react";
import Header from "@/components/Header";
import Statistics from "@/components/Statistics";

interface ClientData {
  nom: string;
  prenom: string;
  telephone: string;
  numeroClient: string;
}

interface ClientScore {
  score: number;
  niveau: string;
  couleur: string;
  description: string;
  facteurs: {
    paiements: number;
    anciennete: number;
    utilisation: number;
    incidents: number;
  };
}

const Dashboard = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [clientData, setClientData] = useState<ClientData | null>(null);
  const [clientScore, setClientScore] = useState<ClientScore | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Récupérer les données du client depuis la navigation
    const data = location.state?.clientData;
    
    if (!data) {
      // Si pas de données, rediriger vers la recherche
      navigate("/");
      return;
    }

    setClientData(data);
    
    // Simuler le calcul du score (remplacer par votre API)
    setTimeout(() => {
      const mockScore: ClientScore = {
        score: Math.floor(Math.random() * 100) + 1,
        niveau: "",
        couleur: "",
        description: "",
        facteurs: {
          paiements: Math.floor(Math.random() * 100) + 1,
          anciennete: Math.floor(Math.random() * 100) + 1,
          utilisation: Math.floor(Math.random() * 100) + 1,
          incidents: Math.floor(Math.random() * 100) + 1,
        }
      };

      // Déterminer le niveau basé sur le score
      if (mockScore.score >= 80) {
        mockScore.niveau = "Excellent";
        mockScore.couleur = "bg-green-500";
        mockScore.description = "Client très fiable avec un excellent historique";
      } else if (mockScore.score >= 60) {
        mockScore.niveau = "Bon";
        mockScore.couleur = "bg-blue-500";
        mockScore.description = "Client fiable avec un bon historique";
      } else if (mockScore.score >= 40) {
        mockScore.niveau = "Moyen";
        mockScore.couleur = "bg-yellow-500";
        mockScore.description = "Client avec quelques points d'attention";
      } else {
        mockScore.niveau = "Faible";
        mockScore.couleur = "bg-red-500";
        mockScore.description = "Client nécessitant une attention particulière";
      }

      setClientScore(mockScore);
      setIsLoading(false);
    }, 1500);
  }, [location.state, navigate]);

  const handleNewSearch = () => {
    navigate("/");
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header 
          isAuthenticated={true}
          onLogin={() => {}}
          onLogout={() => {}}
          onProfile={() => {}}
          userName="Utilisateur"
        />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-tunisietelecom-blue mx-auto mb-4"></div>
              <p className="text-gray-600">Calcul du score en cours...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!clientData || !clientScore) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header 
        isAuthenticated={true}
        onLogin={() => {}}
        onLogout={() => {}}
        onProfile={() => {}}
        userName="Utilisateur"
      />
      
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <Button
            onClick={handleNewSearch}
            variant="outline"
            className="flex items-center gap-2"
          >
            <ArrowLeft size={16} />
            Nouvelle recherche
          </Button>
        </div>

        {/* Informations du client */}
        <Card className="mb-6">
          <CardHeader className="bg-tunisietelecom-blue text-white">
            <CardTitle className="flex items-center gap-2">
              <User size={24} />
              Informations Client
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <p className="text-sm text-gray-500">Nom complet</p>
                <p className="font-semibold">{clientData.prenom} {clientData.nom}</p>
              </div>
              {clientData.telephone && (
                <div>
                  <p className="text-sm text-gray-500">Téléphone</p>
                  <p className="font-semibold flex items-center gap-1">
                    <Phone size={14} />
                    {clientData.telephone}
                  </p>
                </div>
              )}
              {clientData.numeroClient && (
                <div>
                  <p className="text-sm text-gray-500">Numéro client</p>
                  <p className="font-semibold flex items-center gap-1">
                    <CreditCard size={14} />
                    {clientData.numeroClient}
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Score principal */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Star size={24} className="text-yellow-500" />
              Score Client
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="text-center mb-6">
              <div className="inline-flex items-center justify-center w-32 h-32 rounded-full bg-gray-100 mb-4">
                <span className="text-4xl font-bold text-tunisietelecom-blue">
                  {clientScore.score}
                </span>
              </div>
              <div className="space-y-2">
                <Badge className={`${clientScore.couleur} text-white px-4 py-2 text-lg`}>
                  {clientScore.niveau}
                </Badge>
                <p className="text-gray-600">{clientScore.description}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Facteurs détaillés */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-4 text-center">
              <TrendingUp className="mx-auto mb-2 text-green-500" size={24} />
              <p className="text-sm text-gray-500">Historique paiements</p>
              <p className="text-2xl font-bold">{clientScore.facteurs.paiements}%</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <User className="mx-auto mb-2 text-blue-500" size={24} />
              <p className="text-sm text-gray-500">Ancienneté</p>
              <p className="text-2xl font-bold">{clientScore.facteurs.anciennete}%</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <Phone className="mx-auto mb-2 text-purple-500" size={24} />
              <p className="text-sm text-gray-500">Utilisation services</p>
              <p className="text-2xl font-bold">{clientScore.facteurs.utilisation}%</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <AlertCircle className="mx-auto mb-2 text-orange-500" size={24} />
              <p className="text-sm text-gray-500">Incidents</p>
              <p className="text-2xl font-bold">{100 - clientScore.facteurs.incidents}%</p>
            </CardContent>
          </Card>
        </div>

        {/* Statistiques générales */}
        <Statistics clients={[]} />
      </div>
    </div>
  );
};

export default Dashboard;
