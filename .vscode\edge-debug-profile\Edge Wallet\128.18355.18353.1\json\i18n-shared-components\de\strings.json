{"aboutPrivacyStatementLinkText": "Microsoft-Datenschutzbestimmungen", "aboutTermsOfUseLinkText": "Microsoft Edge-Nutzungsbedingungen", "affirmDescriptionText": "Jetzt kaufen und über die Zeit mit Affirm bezahlen. $1", "affirmDisclaimerLinkText": "Affirm kontak<PERSON>en", "affirmDisclaimerText": "Die Affirm Card ist eine virtuelle Karte mit eingeschränktem Verwendungszweck, die je nach den Bedingungen Ihres Darlehensvertrags entweder von der Cross River Bank, FDIC-Mitglied, oder von Affirm Loan Services, LLC, finanziert und von der Sutton Bank, FDIC-Mitglied, gemäß einer Lizenz von Visa U.S.A. Inc. ausgegeben wird. Die Affirm Card wird von Marqeta herausgegeben. $1 bei Fragen.", "affirmEstimatedFeesText": "Es fallen keine Säumnisgebühren an.", "affirmLogoDesc": "<PERSON><PERSON><PERSON> Sie jetzt, zahlen Sie im Laufe der Zeit mit Affirm.", "affirmPolicy": "Indem Si<PERSON> fortfahren, erk<PERSON><PERSON><PERSON> sich mit $1 einverstanden und erkennen an, dass Ihr Gesamteinkaufsbetrag zusammen mit dem Ort, an dem Si<PERSON> einkaufen, an Affirm weitergegeben wird, in Übereinstimmung mit $2. Wenn Si<PERSON> kürzlich für eine Transaktion von Affirm genehmigt wurden, können die Zahlungsbedingungen mit dieser Genehmigung gelten.", "cancel": "Abbrechen", "challengeDescriptionAPPToAPP": "Melden Sie sich bei Ihrer Onlinebanking-App an, um einen Prüfcode zu erhalten.", "challengeDescriptionCustomerService": "Rufen Sie Ihre Bank unter $1 an, um einen Prüfcode zu erhalten.", "challengeDescriptionEmail": "Ihre Bank sendet Ihnen einen Prüfcode an Ihre E-Mail-Adresse $1.", "challengeDescriptionOnlineBanking": "<PERSON><PERSON><PERSON> Si<PERSON> unten den Code ein, den Sie von Ihrer Bank erhalten haben.", "challengeDescriptionOutboundCall": "Ihre Bank ruft Ihre Mobiltelefonnummer $1 mit einem Prüfcode an.", "challengeDescriptionSMS": "Ihre Bank sendet Ihnen einen Prüfcode an Ihre Mobiltelefonnummer $1.", "clearButtonLabel": "Löschen", "close": "Schließen", "commonSubtitle": "<PERSON><PERSON><PERSON><PERSON>, ob Sie die Voraussetzungen für eine Ratenzahlung erfüllen.", "dismiss": "Verwerfen", "donationPaymentMethodItemTitle": "Zahlungsmethode", "feedbackDislike": "G<PERSON>ä<PERSON><PERSON> mir nicht", "feedbackLike": "Gefällt mir", "feedbackOptionAutofillDontWork": "AutoAusfüllen hat nicht funktioniert", "feedbackOptionOthers": "<PERSON><PERSON>", "feedbackOptionTookTooMuchTime": "<PERSON> zu lange gedauert", "klarnaDescriptionText": "Jetzt kaufen. Ratenzahlung mit Klarna. $1", "klarnaDisclaimerText": "Die Klarna Visa® Commercial Card wird von der Sutton Bank, Mitglied der FDIC, gemäß einer Lizenz von Visa U.S.A Inc. ausgestellt. Die Klarna-Karte wird von Marqeta unterstützt. Kundendienst $1", "klarnaEstimatedFeesText": "Möglicherweise fallen an Klarna zu zahlende Bearbeitungsgebühren und Säumnisgebühren an.", "klarnaFeeTermLinkText": "<PERSON><PERSON><PERSON> bezahlen in $1", "klarnaLogoDesc": "<PERSON><PERSON> Si<PERSON> Geld mit Klarna.", "klarnaPolicy": "Indem Si<PERSON> fortfahren, erk<PERSON><PERSON><PERSON> sich mit $1 einverstanden und erkennen an, dass Ihr Gesamteinkaufsbetrag zusammen mit dem Ort, an dem Si<PERSON> einkaufen, an Klarna weitergegeben wird, in Übereinstimmung mit $2. Wenn <PERSON><PERSON> kürzlich für eine Transaktion von Klarna genehmigt wurden, können die Zahlungsbedingungen mit dieser Genehmigung gelten.", "learnMore": "Weitere Informationen", "manageYourPaymentMethodsLinkText": "Zahlungsmethoden verwalten", "msPayAddCardAgreement": "<PERSON>n Si<PERSON> fortfahren, akzeptieren Sie die $1, $2 und $3 in Bezug auf die Verarbeitung Ihrer Daten.", "nextButtonAriaLabel": "Zur nächsten Seite wechseln", "paginationButtonAriaLabel": "Zur Seite $1 wechseln", "paymentServiceTermsLinkText": "Zahlungsdienstbestimmungen", "previousButtonAriaLabel": "Zur vorherigen Seite wechseln", "textOfChangeButton": "Veränderung", "textOfCreateButton": "<PERSON><PERSON><PERSON><PERSON>", "tokenizationEnroll": "Registrieren", "tokenizationEnrollConfirmDialogTitle": "<PERSON><PERSON>es Bezahlen mit einer virtuellen Karte", "tokenizationEnrollConfirmDialogTitleForRewards": "<PERSON><PERSON><PERSON> einer virtuellen Karte und Sammeln von 20 Microsoft Rewards-Punkten", "tokenizationEnrollConfirmFooterDescription": "Ihr Kartenaussteller kann einen Prüfcode senden oder den Sicherheitscode der Karte anfordern, um ihre Identität zu bestätigen.", "tokenizationEnrollConfirmModalDescription": "Eine virtuelle Karte verbirgt Ihre Kartendaten vor den Händlern, wenn Sie online einkaufen. Sollte es bei einem von ihnen zu einer Datenschutzverletzung kommen, bleiben Ihre Daten geschützt. $1", "tokenizationTerms": "<PERSON>n <PERSON> fort<PERSON>, stimmen Sie den $1 zu.", "tokenizeCardBack": "Zurück", "tokenizeCardChallengeMethodAPPToAPP": "Mobile Banking-App", "tokenizeCardChallengeMethodCustomerService": "Bankkundendienst", "tokenizeCardChallengeMethodEmail": "E-Mail", "tokenizeCardChallengeMethodOnlineBanking": "Bankkonto", "tokenizeCardChallengeMethodOutboundCall": "<PERSON><PERSON><PERSON>", "tokenizeCardChallengeMethodSMS": "SMS", "tokenizeCardCvvInvalidError": "Überprüfen Sie Ihren Sicherheitscode, und versuchen Sie es noch mal.", "tokenizeCardEnterCode": "Code eingeben", "tokenizeCardEnterCodeStepTitle": "<PERSON>sen Si<PERSON> uns Ihre Überprüfung vornehmen", "tokenizeCardEnterCvvStepDescription": "Ihre Bank verlangt, dass Sie den CVV-Sicherheitscode überprüfen, der mit Ihrem $1 verknüpft ist, der auf $2 endet.", "tokenizeCardErrorStepTitle": "Da hat etwas nicht geklappt", "tokenizeCardFetchCodeError": "Beim Senden des Codes ist bei Ihrem Kartenaussteller ein Problem aufgetreten. Fordern Sie einen anderen Code an, oder verwenden Sie eine andere Überprüfungsmethode.", "tokenizeCardFetchCodeErrorCustomerService": "Beim Generieren des Codes ist bei Ihrem Kartenaussteller ein Problem aufgetreten. Versuchen Sie es mit einer anderen Überprüfungsmethode, oder versuchen Sie es später erneut.", "tokenizeCardGeneralError": "Ihr Kartenaussteller hat zurzeit Probleme. Versuchen Sie es bitte später noch mal.", "tokenizeCardLoadingTitle": "Kontaktaufnahme mit Ihrer Bank…", "tokenizeCardNext": "<PERSON><PERSON>", "tokenizeCardResendCode": "Code erneut senden", "tokenizeCardSelectMethodDescription": "Wählen Sie eine der folgenden Überprüfungsmethoden aus:", "tokenizeCardSelectMethodStepTitle": "<PERSON>sen Si<PERSON> uns Ihre Überprüfung vornehmen", "tokenizeCardSendingCode": "Code gesendet!", "tokenizeCardTryAgain": "Versuchen Sie es noch einmal", "tokenizeCardValidationError": "<PERSON>hr Kartenaussteller kann die Überprüfung nicht ausführen. Versuchen Sie es in Kürze noch einmal.", "tokenizeCardVerify": "Überprüfen", "tokenizeCardVerifyWithAnotherMethod": "Überprüfung mit einer anderen Methode", "tokenizeCardVerifying": "Wird überprüft", "walletCommonAccountCount": "$1 Konten", "walletDrawerAutoFillErrorMessage": "Wir können Ihre Karteninformationen nicht automatisch von $1 ausfüllen. Geben Sie ihre $1-Karteninformationen während des Auftragsabschluss manuell ein.", "walletDrawerAutoFillMessage": "Wir haben Ihre Karteninformationen aus $1 automatisch ausgefüllt.", "walletDrawerAutoFillMessageWithBillingAddress": "Ihre Zahlungsdaten aus $1 wurden ausgefüllt. <PERSON><PERSON> nicht, klicken Sie unten auf die Karten- und Adressdaten, um sie zu kopieren.", "walletDrawerAutoFillMessageWithoutBillingAddress": "Ihre Zahlungsdaten aus $1 wurden ausgefüllt. <PERSON><PERSON> nicht, klicken Sie unten auf die Kartendetails, um sie zu kopieren. Um diesen Kauf abzuschließen, geben Sie Ihre private Rechnungsadresse ein.", "walletDrawerCvcLabel": "CVC", "walletDrawerExpirationLabel": "<PERSON><PERSON><PERSON>", "walletDrawerLabelCopyCardNumber": "Kartennummer kopieren", "walletDrawerLabelCopyCvc": "CVC kopieren", "walletDrawerLabelCopyNameAddress": "Name und Adresse kopieren", "walletDrawerLabelShowCardNumber": "Kartennummer anzeigen", "walletDrawerLabelShowCvc": "CVC anzeigen", "walletDrawerLabelShowExpirationDate": "Ablaufdatum kop<PERSON>en", "walletDrawerLinkErrorMessage": "Wir können Ihr Microsoft-Konto nicht mit $1 verknüpfen. Versuchen Sie es später erneut.", "walletDrawerNameAndAddressLabel": "<PERSON>hr Name und Ihre Rechnungsadresse", "walletDrawerPayWithVirtualCard": "Bezahlen Sie mit Ihrer $1 $2-Karte", "walletDrawerSystemErrorHeader": "<PERSON><PERSON><PERSON>!", "walletDrawerVirtualCardNumberLabel": "<PERSON><PERSON>mer der virtuellen Karte", "walletFeedbackThankYouText": "Vielen Dank für Ihr Feedback. Es wird uns bei der Verbesserung unserer Brieftasche-App helfen.", "walletMicrofeedbackPrompt": "Zufrieden mit Brieftasche?", "walletNotInterestedText": "Nicht interessiert", "walletPWAAddShortcut": "Verknüpfung hinzufügen", "walletPWACommonPromoContent": "<PERSON>zt installieren, um schnelleren Zugriff auf die Brieftasche zu erhalten.", "walletPWAPromoContent": "Führen Sie die Installation jetzt aus, und sammeln Sie $1 Microsoft Rewards-Punkte.", "walletPWAPromoTitle": "Verknüpfung zur Wallet hinzufügen", "walletSeeDetailsAriaLabel": "Details anzeigen", "walletUXAddPaymentButtonTitle": "Neue Karte hinzufügen", "walletUXManagePaymentButtonTitle": "Zahlungsmethoden verwalten", "walletUXMaskedUnavailableCardsDescriptionNew": "Sie können $1 aufgrund von eingeschränktem Netzwerkzugriff oder Verbindungsproblemen nicht verwenden", "walletUXMaskedUnavailableCardsTheseCards": "dies<PERSON>", "walletUXMaskedUnavailableCardsThisCard": "diese <PERSON>", "walletUXMaskedUnavailableCardsTitle": "Nicht verfügbare Karten", "walletUXSInEligibleCardLogoContent": "Diese Website unterstützt keine virtuelle Karte.", "walletUXSTokenCardLogoContent": "Virtuelle Kartennummer •••• $1 aktiviert, um Ihre Zahlungsinformationen zu schützen", "zipDescriptionText": "Jetzt einkaufen und über 6 Wochen mit Zip bezahlen. $1", "zipEstimatedFeesText": "Säumnisgebühren können anfallen.", "zipFeeTermLinkText": "Nutzungsbedingungen von <PERSON>", "zipLogoDesc": "<PERSON><PERSON>, jetzt kaufen, später bezahlen", "zipPolicy": "Indem Sie fortfahren, erk<PERSON><PERSON><PERSON> Si<PERSON> sich mit unseren $1 einverstanden und erkennen an, dass Ihr Gesamtkaufbetrag in Übereinstimmung mit $2 an Zip weitergegeben wird."}