(()=>{"use strict";class t{static SetEventListener(t,e,i,r){const a=document.querySelector(e.primary);return!!a&&(a.addEventListener(i.event_type,(()=>t(r,i.terminal_event)),!1),!0)}static ExtractTextField(t){const e=document.querySelector(t.primary);return e?e.value:""}static ExtractTextFieldEndingWithIATACode(e,i){const r=t.ExtractTextField(e);return r?r.split(i.delimiter).slice(-1)[0]:""}static ExtractTextFieldCustom(e,i,r){switch(r.sub_type){case"alaska":return t.ExtractTextFieldAlaska(e);case"spirit":return t.ExtractTextFieldSpirit(e);default:return""}}static ExtractDateTextField(e,i){if(e.condition_field&&i.condition_field_type&&null!=i.condition_value_expected&&!this.IsConditionValid(e.condition_field,i.condition_field_type,i.condition_value_expected))return"";const r=t.ExtractTextField(e);if(r){var a=new Date(r);if("dd"===i.date_parse)return("0"+a.getDate()).slice(-2);if("mm"===i.date_parse)return("0"+(a.getMonth()+1)).slice(-2);if("yyyy"===i.date_parse)return a.getFullYear().toString()}return""}static ExtractDateTextFieldCustom(e,i,r){return"spirit"===r.sub_type?t.ExtractDateTextFieldSpirit(e,i):""}static IsConditionValid(t,e,i){if(!t||!e||null==i)return!1;let r;if("checkbox"===e){const e=document.querySelector(t);e&&(r=e.checked)}return r==i}static ExtractDropdownIndex(t){const e=document.querySelector(t.primary);return e&&e.selectedIndex>=0?e.selectedIndex.toString():""}static ExtractDropdownIndexCustom(e,i,r){return"aaadvanced"===r.sub_type?t.ExtractDropDownCountAAAdvanced(e,r):""}static ExtractDropDownCountAAAdvanced(t,e){const i=document.querySelectorAll(t.primary);if(i.length>0){let t=0;return i.forEach((i=>{(i.offsetWidth>0||i.offsetHeight>0)&&i.value==e.value&&t++})),t.toString()}return""}static ExtractTextFieldAlaska(t){const e=document.querySelector(t.primary);if(e){const t=e.value;if(3==t.length)return t;if(t.length>3&&t.includes("(")&&t.includes("-")){const e=t.lastIndexOf("(")+1;if(e+3<t.length&&"-"==t[e+3])return t.substring(e,e+3)}}return""}static ExtractDateTextFieldSpirit(e,i){const r=t.ExtractTextField(e);if(r.length<11)return"";if("dep_day"==i){const t=9;return r.substring(t,t+2)}let a=new Date;a.setHours(0,0,0,0);let s,n=(new Date).getFullYear(),l=r.substring(5,11)+" "+n,c=new Date(l);if(isNaN(c.getTime()))return"";if("dep_month"==i)return("0"+(c.getMonth()+1)).slice(-2);if(("dep_year"==i||"ret_month"==i||"ret_year"==i)&&(s=c.getTime()>=a.getTime()?n:n+1,"dep_year"==i))return s.toString();if(25!=r.length)return"";if("ret_day"==i){const t=23;return r.substring(t,t+2)}let d=r.substring(19,25)+" "+s,o=new Date(d);if(isNaN(o.getTime()))return"";if("ret_month"==i)return("0"+(o.getMonth()+1)).slice(-2);if("ret_year"==i){let t;return t=o.getTime()>=c.getTime()?s:s+1,t.toString()}return""}static ExtractTextFieldSpirit(e){const i=t.ExtractTextField(e);if(i&&-1!=i.lastIndexOf("(")&&-1!=i.lastIndexOf(")")){let t=i.substring(i.lastIndexOf("(")+1,i.lastIndexOf(")"));if(3==t.length)return t}return""}}window.searchAssistanceExtractor=new class extends class{constructor(){}}{constructor(){super(...arguments),this.travelFields={},this.shouldSaveData=!0}raiseMessageFromHost(t){!t||t.length<2||"extract"!==t[0]||(2==t.length?this.runExtraction(t[1]):3==t.length&&this.runExtraction(t[1],t[2]))}runExtraction(e,i="{}"){try{this.extractionConfigObject=JSON.parse(e),this.defaultFieldsObject=JSON.parse(i)}catch(t){return}if(!this.extractionConfigObject)return;let r=this.extractionConfigObject.events,a=this.extractionConfigObject.fields,s=this.extractionConfigObject.event_field_mapping;r&&a&&s&&r.forEach((e=>{let i=a[e];t.SetEventListener(this.eventCallback.bind(this),i.field_selectors,i.field_attributes,s[e])}))}eventCallback(t,e){t.forEach((t=>{let e=this.extractionConfigObject.fields[t];e&&e.travel_data_field&&this.extractField(e.field_selectors,e.field_type,e.field_name,e.field_attributes)})),this.shouldSaveData=this.shouldSaveTravelData(),e&&(this.defaultFieldsObject&&Object.entries(this.defaultFieldsObject).map((([t,e])=>{this.travelFields[t]=e})),this.travelFields&&window.location.host&&(this.travelFields.travel_domain=window.location.host,this.sendExtractedDataToHost(JSON.stringify(this.travelFields))))}shouldSaveTravelData(){return!(this.travelFields&&this.travelFields.seniors_count&&Number(this.travelFields.seniors_count)>0||this.travelFields.youth_count&&Number(this.travelFields.youth_count)>0||this.travelFields.children_count&&Number(this.travelFields.children_count)>0||this.travelFields.infant_count&&Number(this.travelFields.infant_count)>0)}extractField(e,i,r,a){if(i&&e&&r)switch(i){case"text_field":this.travelFields[r]=t.ExtractTextField(e);break;case"text_field_ending_with_IATA_code":this.travelFields[r]=t.ExtractTextFieldEndingWithIATACode(e,a);break;case"text_field_custom":this.travelFields[r]=t.ExtractTextFieldCustom(e,r,a);break;case"dropdown":this.travelFields[r]=t.ExtractDropdownIndex(e);break;case"dropdown_custom":this.travelFields[r]=t.ExtractDropdownIndexCustom(e,r,a);break;case"date_text_field":this.travelFields[r]=t.ExtractDateTextField(e,a);break;case"date_text_field_custom":this.travelFields[r]=t.ExtractDateTextFieldCustom(e,r,a)}}sendExtractedDataToHost(t){searchAssistJavascriptNativeHandler&&searchAssistJavascriptNativeHandler.sendExtractedData(t,this.shouldSaveData)}logTelemetry(t){}}})();