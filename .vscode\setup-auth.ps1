# Script PowerShell pour configurer l'authentification automatique
# Ce script aide à configurer l'environnement pour l'authentification automatique

param(
    [string]$Username = "admin",
    [SecureString]$Password,
    [string]$Port = "3000"
)

# Si aucun mot de passe sécurisé n'est fourni, utiliser la valeur par défaut
if (-not $Password) {
    $Password = ConvertTo-SecureString "admin" -AsPlainText -Force
}

Write-Host "Configuration de l'authentification automatique..." -ForegroundColor Green

# Créer les dossiers de profil s'ils n'existent pas
$edgeProfileDir = ".vscode\edge-debug-profile"
$chromeProfileDir = ".vscode\chrome-debug-profile"

if (!(Test-Path $edgeProfileDir)) {
    New-Item -ItemType Directory -Path $edgeProfileDir -Force
    Write-Host "Dossier de profil Edge créé: $edgeProfileDir" -ForegroundColor Yellow
}

if (!(Test-Path $chromeProfileDir)) {
    New-Item -ItemType Directory -Path $chromeProfileDir -Force
    Write-Host "Dossier de profil Chrome créé: $chromeProfileDir" -ForegroundColor Yellow
}

# Créer un fichier de configuration pour l'authentification
# Convertir le SecureString en texte pour la configuration (dans un environnement sécurisé)
$PlainPassword = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto([System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($Password))

$authConfig = @{
    username = $Username
    password = $PlainPassword  # Note: En production, utilisez une méthode plus sécurisée
    port = $Port
    url = "http://localhost:$Port"
} | ConvertTo-Json

$authConfig | Out-File -FilePath ".vscode\auth-config.json" -Encoding UTF8
Write-Host "Configuration d'authentification sauvegardée dans .vscode\auth-config.json" -ForegroundColor Yellow

# Créer un script batch pour lancer le serveur avec authentification
$batchScript = @"
@echo off
echo Démarrage du serveur de développement...
cd /d "%~dp0.."
npm run dev
"@

$batchScript | Out-File -FilePath ".vscode\start-server.bat" -Encoding ASCII
Write-Host "Script de démarrage créé: .vscode\start-server.bat" -ForegroundColor Yellow

Write-Host ""
Write-Host "Configuration terminée!" -ForegroundColor Green
Write-Host "Vous pouvez maintenant utiliser les configurations de débogage suivantes:" -ForegroundColor Cyan
Write-Host "1. 'Launch Edge with Auth (Recommended)' - Utilise Edge avec authentification automatique" -ForegroundColor White
Write-Host "2. 'Launch Chrome with Auth' - Utilise Chrome avec authentification automatique" -ForegroundColor White
Write-Host "3. 'Launch Edge - Simple (No Auth)' - Lance Edge sans configuration d'authentification" -ForegroundColor White
Write-Host ""
Write-Host "Conseils:" -ForegroundColor Cyan
Write-Host "- La première fois, vous devrez peut-être entrer manuellement les identifiants" -ForegroundColor White
Write-Host "- Les sessions suivantes devraient se connecter automatiquement" -ForegroundColor White
Write-Host "- Si vous avez des problèmes, essayez la configuration 'Simple (No Auth)'" -ForegroundColor White
