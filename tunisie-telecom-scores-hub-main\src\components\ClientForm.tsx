
import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { UserPlus, Save } from 'lucide-react';

interface Client {
  id: string;
  nom: string;
  prenom: string;
  score: number;
  email: string;
  telephone: string;
  adresse: string;
  notes: string;
  dateCreation: string;
}

interface ClientFormProps {
  onAddClient: (client: Omit<Client, 'id' | 'dateCreation'>) => void;
}

const ClientForm = ({ onAddClient }: ClientFormProps) => {
  const [formData, setFormData] = useState({
    nom: '',
    prenom: '',
    score: '',
    email: '',
    telephone: '',
    adresse: '',
    notes: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.nom || !formData.prenom || !formData.score) {
      alert('Veuillez remplir au moins le nom, prénom et score');
      return;
    }

    const score = parseInt(formData.score);
    if (isNaN(score) || score < 0 || score > 100) {
      alert('Le score doit être un nombre entre 0 et 100');
      return;
    }

    onAddClient({
      nom: formData.nom,
      prenom: formData.prenom,
      score: score,
      email: formData.email,
      telephone: formData.telephone,
      adresse: formData.adresse,
      notes: formData.notes
    });

    // Reset form
    setFormData({
      nom: '',
      prenom: '',
      score: '',
      email: '',
      telephone: '',
      adresse: '',
      notes: ''
    });
  };

  return (
    <Card className="border-tunisietelecom-blue/20 shadow-lg">
      <CardHeader className="bg-gradient-to-r from-tunisietelecom-blue to-tunisietelecom-darkblue text-white">
        <CardTitle className="flex items-center gap-2">
          <UserPlus size={20} />
          Ajouter un nouveau client
        </CardTitle>
      </CardHeader>
      
      <CardContent className="p-6">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="nom">Nom *</Label>
              <Input
                id="nom"
                type="text"
                placeholder="Nom du client"
                value={formData.nom}
                onChange={(e) => setFormData({ ...formData, nom: e.target.value })}
                className="border-gray-300 focus:border-tunisietelecom-blue"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="prenom">Prénom *</Label>
              <Input
                id="prenom"
                type="text"
                placeholder="Prénom du client"
                value={formData.prenom}
                onChange={(e) => setFormData({ ...formData, prenom: e.target.value })}
                className="border-gray-300 focus:border-tunisietelecom-blue"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="score">Score (0-100) *</Label>
              <Input
                id="score"
                type="number"
                min="0"
                max="100"
                placeholder="Score du client"
                value={formData.score}
                onChange={(e) => setFormData({ ...formData, score: e.target.value })}
                className="border-gray-300 focus:border-tunisietelecom-blue"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                className="border-gray-300 focus:border-tunisietelecom-blue"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="telephone">Téléphone</Label>
              <Input
                id="telephone"
                type="tel"
                placeholder="+216 XX XXX XXX"
                value={formData.telephone}
                onChange={(e) => setFormData({ ...formData, telephone: e.target.value })}
                className="border-gray-300 focus:border-tunisietelecom-blue"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="adresse">Adresse</Label>
              <Input
                id="adresse"
                type="text"
                placeholder="Adresse du client"
                value={formData.adresse}
                onChange={(e) => setFormData({ ...formData, adresse: e.target.value })}
                className="border-gray-300 focus:border-tunisietelecom-blue"
              />
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="notes">Notes additionnelles</Label>
            <Textarea
              id="notes"
              placeholder="Notes ou commentaires sur le client..."
              value={formData.notes}
              onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
              className="border-gray-300 focus:border-tunisietelecom-blue min-h-[80px]"
            />
          </div>
          
          <Button
            type="submit"
            className="w-full bg-tunisietelecom-blue hover:bg-tunisietelecom-darkblue text-white py-3"
          >
            <Save size={16} className="mr-2" />
            Ajouter le client
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};

export default ClientForm;
