# 🔧 Solution : Problème de chemin VS Code

## 🚨 Erreur résolue
```
The terminal process failed to launch: Starting directory (cwd) "tunisie-telecom-scores-hub-main" does not exist.
```

## 🔍 Cause du problème
VS Code cherche le dossier `tunisie-telecom-scores-hub-main` au mauvais endroit car votre workspace est ouvert au niveau parent.

## ✅ Solutions (par ordre de priorité)

### **Solution 1 : Ouvrir le bon dossier dans VS Code (Recommandé)**

1. **Fermer VS Code**
2. **Ouvrir le dossier correct** :
   - File → Open Folder
   - Naviguer vers : `tunisie-telecom-scores-hub-main` (le dossier du projet)
   - Cliquer "Sélectionner le dossier"
3. **Redémarrer** et essayer F5

### **Solution 2 : Utiliser les nouvelles configurations**

J'ai créé plusieurs configurations de débogage :

1. **"🚀 Démarrer l'application (Recommandé)"** - Sans tâche automatique
2. **"🌐 Démarrer avec Chrome"** - Alternative Chrome
3. **"⚡ Démarrage automatique Edge"** - Avec tâche automatique (corrigée)
4. **"🔧 Test démarrage simple"** - Pour tester

### **Solution 3 : Démarrage manuel (100% fiable)**

1. **Terminal VS Code** (Ctrl + `)
2. **Commande** :
   ```bash
   cd tunisie-telecom-scores-hub-main
   npm run dev
   ```
3. **F5** → "🚀 Démarrer l'application (Recommandé)"

### **Solution 4 : Vérifier la structure**

Votre structure doit être :
```
votre-dossier-parent/
├── .vscode/
│   ├── launch.json
│   └── tasks.json
└── tunisie-telecom-scores-hub-main/
    ├── package.json
    ├── src/
    └── ...
```

## 🎯 Test des configurations

### **Pour tester si ça marche** :

1. **F5** dans VS Code
2. **Choisir** : "⚡ Démarrage automatique Edge"
3. **Si erreur** → Essayer "🔧 Test démarrage simple"
4. **Si toujours erreur** → Utiliser Solution 1 ou 3

## 🔧 Configurations mises à jour

### **tasks.json** - Nouvelles tâches :
- `start-dev-server-simple` - Chemin relatif simple
- `start-dev-server-direct` - Chemin Windows direct
- `stop-dev-server` - Arrêt du serveur

### **launch.json** - Nouvelles configurations :
- Chemins corrigés pour Windows
- Plusieurs options de démarrage
- Configurations de test

## 🆘 Si rien ne fonctionne

### **Méthode de secours** :
```bash
# Terminal/PowerShell
cd tunisie-telecom-scores-hub-main
npm run dev

# Navigateur
http://localhost:5173/
```

### **Vérification rapide** :
```bash
# Vérifier que vous êtes au bon endroit
ls tunisie-telecom-scores-hub-main

# Doit afficher : package.json, src/, etc.
```

## 🎉 Application prête

Une fois démarrée, vous verrez :
- **Page de saisie client** avec logo Tunisie Telecom
- **Formulaire** : Nom*, Prénom*, Téléphone, Numéro client
- **Test** : Saisir "Dupont" + "Jean" → Voir le dashboard

---

**✅ Problème résolu ! Configurations VS Code corrigées pour Windows.**
