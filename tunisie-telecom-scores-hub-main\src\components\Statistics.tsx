
import { <PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>itle, CardContent } from '@/components/ui/card';
import { Users, TrendingUp, Award, BarChart3 } from 'lucide-react';

interface Client {
  id: string;
  nom: string;
  prenom: string;
  score: number;
  email: string;
  telephone: string;
  adresse: string;
  notes: string;
  dateCreation: string;
}

interface StatisticsProps {
  clients: Client[];
}

const Statistics = ({ clients }: StatisticsProps) => {
  const totalClients = clients.length;
  const averageScore = totalClients > 0 
    ? Math.round(clients.reduce((sum, client) => sum + client.score, 0) / totalClients)
    : 0;
  
  const excellentClients = clients.filter(client => client.score >= 80).length;
  const lowScoreClients = clients.filter(client => client.score < 40).length;

  const stats = [
    {
      title: 'Total Clients',
      value: totalClients,
      icon: Users,
      color: 'bg-tunisietelecom-blue',
      description: 'clients enregistrés'
    },
    {
      title: 'Score Moyen',
      value: averageScore,
      icon: BarChart3,
      color: 'bg-tunisietelecom-darkblue',
      description: 'sur 100 points'
    },
    {
      title: 'Clients Excellents',
      value: excellentClients,
      icon: Award,
      color: 'bg-green-500',
      description: 'score ≥ 80'
    },
    {
      title: 'Clients à Risque',
      value: lowScoreClients,
      icon: TrendingUp,
      color: 'bg-orange-500',
      description: 'score < 40'
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {stats.map((stat, index) => {
        const Icon = stat.icon;
        return (
          <Card key={index} className="border-tunisietelecom-blue/20 shadow-lg hover:shadow-xl transition-shadow duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">
                    {stat.title}
                  </p>
                  <p className="text-3xl font-bold text-tunisietelecom-darkgray">
                    {stat.value}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    {stat.description}
                  </p>
                </div>
                <div className={`w-12 h-12 ${stat.color} rounded-lg flex items-center justify-center`}>
                  <Icon className="text-white" size={24} />
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};

export default Statistics;
