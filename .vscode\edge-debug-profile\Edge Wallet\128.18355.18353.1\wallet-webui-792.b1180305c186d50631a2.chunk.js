"use strict";(self.webpackChunk_xpay_wallet_hub=self.webpackChunk_xpay_wallet_hub||[]).push([[792],{29792:(e,r,t)=>{t.d(r,{S:()=>p});var a=Object.defineProperty,l=Object.getOwnPropertySymbols,n=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable,b=(e,r,t)=>r in e?a(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t;const p=(e,r)=>new Intl.NumberFormat(e,((e,r)=>{for(var t in r||(r={}))n.call(r,t)&&b(e,t,r[t]);if(l)for(var t of l(r))o.call(r,t)&&b(e,t,r[t]);return e})({style:"currency"},r))}}]);