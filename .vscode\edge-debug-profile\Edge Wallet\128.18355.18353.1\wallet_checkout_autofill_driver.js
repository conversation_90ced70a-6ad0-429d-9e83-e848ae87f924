!function(){"use strict";var e=JSON.parse('{"AF":"AFG","AX":"ALA","AL":"ALB","DZ":"DZA","AS":"ASM","AD":"AND","AO":"AGO","AI":"AIA","AQ":"ATA","AG":"ATG","AR":"ARG","AM":"ARM","AW":"ABW","AU":"AUS","AT":"AUT","AZ":"AZE","BS":"BHS","BH":"BHR","BD":"BGD","BB":"BRB","BY":"BLR","BE":"BEL","BZ":"BLZ","BJ":"BEN","BM":"BMU","BT":"BTN","BO":"BOL","BQ":"BES","BA":"BIH","BW":"BWA","BV":"BVT","BR":"BRA","IO":"IOT","BN":"BRN","BG":"BGR","BF":"BFA","BI":"BDI","CV":"CPV","KH":"KHM","CM":"CMR","CA":"CAN","KY":"CYM","CF":"CAF","TD":"TCD","CL":"CHL","CN":"CHN","CX":"CXR","CC":"CCK","CO":"COL","KM":"COM","CG":"COG","CD":"COD","CK":"COK","CR":"CRI","HR":"HRV","CU":"CUB","CW":"CUW","CY":"CYP","CZ":"CZE","DK":"DNK","DJ":"DJI","DM":"DMA","DO":"DOM","EC":"ECU","EG":"EGY","SV":"SLV","GQ":"GNQ","ER":"ERI","EE":"EST","SZ":"SWZ","ET":"ETH","FK":"FLK","FO":"FRO","FJ":"FJI","FI":"FIN","FR":"FRA","GF":"GUF","PF":"PYF","TF":"ATF","GA":"GAB","GM":"GMB","GE":"GEO","DE":"DEU","GH":"GHA","GI":"GIB","GR":"GRC","GL":"GRL","GD":"GRD","GP":"GLP","GU":"GUM","GT":"GTM","GG":"GGY","GN":"GIN","GW":"GNB","GY":"GUY","HT":"HTI","HM":"HMD","VA":"VAT","HN":"HND","HK":"HKG","HU":"HUN","IS":"ISL","IN":"IND","ID":"IDN","IR":"IRN","IQ":"IRQ","IE":"IRL","IM":"IMN","IL":"ISR","IT":"ITA","CI":"CIV","JM":"JAM","JP":"JPN","JE":"JEY","JO":"JOR","KZ":"KAZ","KE":"KEN","KI":"KIR","KP":"PRK","KR":"KOR","KW":"KWT","KG":"KGZ","LA":"LAO","LV":"LVA","LB":"LBN","LS":"LSO","LR":"LBR","LY":"LBY","LI":"LIE","LT":"LTU","LU":"LUX","MO":"MAC","MG":"MDG","MW":"MWI","MY":"MYS","MV":"MDV","ML":"MLI","MT":"MLT","MH":"MHL","MQ":"MTQ","MR":"MRT","MU":"MUS","YT":"MYT","MX":"MEX","FM":"FSM","MD":"MDA","MC":"MCO","MN":"MNG","ME":"MNE","MS":"MSR","MA":"MAR","MZ":"MOZ","MM":"MMR","NA":"NAM","NR":"NRU","NP":"NPL","NL":"NLD","NC":"NCL","NZ":"NZL","NI":"NIC","NE":"NER","NG":"NGA","NU":"NIU","NF":"NFK","MK":"MKD","MP":"MNP","NO":"NOR","OM":"OMN","PK":"PAK","PW":"PLW","PS":"PSE","PA":"PAN","PG":"PNG","PY":"PRY","PE":"PER","PH":"PHL","PN":"PCN","PL":"POL","PT":"PRT","PR":"PRI","QA":"QAT","RE":"REU","RO":"ROU","RU":"RUS","RW":"RWA","BL":"BLM","SH":"SHN","KN":"KNA","LC":"LCA","MF":"MAF","PM":"SPM","VC":"VCT","WS":"WSM","SM":"SMR","ST":"STP","SA":"SAU","SN":"SEN","RS":"SRB","SC":"SYC","SL":"SLE","SG":"SGP","SX":"SXM","SK":"SVK","SI":"SVN","SB":"SLB","SO":"SOM","ZA":"ZAF","GS":"SGS","SS":"SSD","ES":"ESP","LK":"LKA","SD":"SDN","SR":"SUR","SJ":"SJM","SE":"SWE","CH":"CHE","SY":"SYR","TW":"TWN","TJ":"TJK","TZ":"TZA","TH":"THA","TL":"TLS","TG":"TGO","TK":"TKL","TO":"TON","TT":"TTO","TN":"TUN","TR":"TUR","TM":"TKM","TC":"TCA","TV":"TUV","UG":"UGA","UA":"UKR","AE":"ARE","GB":"GBR","US":"USA","UM":"UMI","UY":"URY","UZ":"UZB","VU":"VUT","VE":"VEN","VN":"VNM","VG":"VGB","VI":"VIR","WF":"WLF","EH":"ESH","YE":"YEM","ZM":"ZMB","ZW":"ZWE"}');class t{static IsValidDataField(e){return null!=e&&e.length>0&&"null"!==e}static isElementDisabled(e,a){const o=a??t.GetFirstVisibleElement(e);return o?.disabled}static RunQuerySelectorAll(e){const t=e.split("<");let a=document.querySelectorAll(t[0]);for(const e of t.slice(1)){const t=a[0]?.shadowRoot;if(!t)return[];a=t.querySelectorAll(e)}return a}static IsElementVisible(e){return e&&e.offsetWidth>0&&e.offsetHeight>0}static CountVisibleElements(e){if(!t.IsValidDataField(e))return 0;const a=t.RunQuerySelectorAll(e);let o=0;for(const e of a)t.IsElementVisible(e)&&o++;return o}static GetFirstVisibleElement(e){if(!t.IsValidDataField(e))return;const a=e.split(";");for(const e of a)try{const a=t.RunQuerySelectorAll(e);for(const e of a)if(t.IsElementVisible(e))return e}catch(e){window.loadTimeData.data_.isDebugEnabled&&console.log("XPay wallet debugging: GetFirstVisibleElement error",e)}}static Sleep(e){return new Promise((t=>setTimeout(t,e)))}static async WaitForCondition(e,a,o){const l=(new Date).getTime();for(window.loadTimeData.data_.isDebugEnabled&&console.log("XPay wallet debugging: waiting");!await e()&&l+a>(new Date).getTime();)await t.Sleep(o??100);return window.loadTimeData.data_.isDebugEnabled&&console.log("XPay wallet debugging: wait completed."),await e()}static async IsFieldVisible(e){return t.CountVisibleElements(e)>0}}class a{static Click(e,a,o){const l=a??t.GetFirstVisibleElement(e);return!!l&&(window.loadTimeData.data_.isDebugEnabled&&console.log("XPay wallet debugging: click element visible",l,l.innerText),t.isElementDisabled(void 0,l)||function(e){const t=["mousedown","click","mouseup"];if(o){let a=!1;t.forEach((t=>e.addEventListener(t,(()=>{a||(a=!0,o())}))))}t.forEach((t=>e.dispatchEvent(new MouseEvent(t,{bubbles:!0,buttons:1,cancelable:!0,view:window}))))}(l),!0)}static SetBoxValue(e,a){const o=document.createEvent("Events");o.initEvent("change",!0,!1);const l=document.createEvent("Events");l.initEvent("input",!0,!1);const i=new KeyboardEvent("keyup",{bubbles:!0,cancelable:!0,view:window}),n=t.GetFirstVisibleElement(e);return n?(n.blur(),n.dispatchEvent(o),n.focus(),n.setAttribute("value",a),n.value=a,n.dispatchEvent(i),n.dispatchEvent(l),n.dispatchEvent(o),n.blur(),!0):(window.loadTimeData.data_.isDebugEnabled&&console.log("XPay wallet debugging: input box undefined"),!1)}}const o="WalletCheckoutScriptAutofillRuntime";var l=function(e){return e[e.ExactMatch=0]="ExactMatch",e[e.RegMatch=1]="RegMatch",e}(l||{}),i=function(e){return e.WalletCheckoutAutofillDriverCompleteMessage="WalletCheckoutAutofillDriverCompleteMessage",e}(i||{});function n(e,t,a){window.loadTimeData.data_.isDebugEnabled&&console.log("XPay wallet debugging: SendAutofillCompleteMessage",e,t,a),t?(window.loadTimeData.data_.isDebugEnabled&&console.log("XPay wallet debugging: iframe postMessage to parent",e,a),parent.postMessage({type:i.WalletCheckoutAutofillDriverCompleteMessage,payload:e},a)):window[o].postMessageToHost("AutofillComplete",e)}function s(e,t){const a=e?.getAttributeNames();for(const o of a){if("value"===o)continue;const a=e?.getAttribute(o);if(a){let e;try{e=JSON.parse(a)}catch{e=a}return Array.isArray(e)?e.some((e=>t.includes(e.toLowerCase().trim())))||e.some((e=>t.some((t=>e.toLowerCase().trim().split(/\s+/)?.includes(t))))):t.includes(e.toLowerCase().trim())}}return!1}async function d(e){const{Name:o,Selector:l,Type:i,ValueList:n,WaitBefore:d,SecondarySelector:r}=e;"button"==i&&0==n?.length||await t.Sleep(d||0);const c=await t.IsFieldVisible(l);if(window.loadTimeData.data_.isDebugEnabled&&console.log("XPay wallet debugging: isSelectorVisible",o,c),!c)throw new Error("Selector not found");if("input"===i&&n?.[0]){const e=t.GetFirstVisibleElement(l);window.loadTimeData.data_.isDebugEnabled&&console.log("XPay wallet debugging: targetElement",o,e);const i=e.tagName;if("INPUT"===i)a.SetBoxValue(l,n?.[0]);else if("SELECT"===i){const t=e.getElementsByTagName("option"),i=n.map((e=>e.toLowerCase()));window.loadTimeData.data_.isDebugEnabled&&console.log("XPay wallet debugging: lowerValueList",o,i);for(const e of t){const t=e?.attributes?.getNamedItem("value")?.value?.trim(),n=e?.innerText?.trim();if(i.includes(t?.toLowerCase())||i.includes(n?.toLowerCase()))return a.SetBoxValue(l,t),void(window.loadTimeData.data_.isDebugEnabled&&console.log("XPay wallet debugging: shouldValue",o,t));if(s(e,i))return a.SetBoxValue(l,t),void(window.loadTimeData.data_.isDebugEnabled&&console.log("XPay wallet debugging: shouldValue",o,t))}window.loadTimeData.data_.isDebugEnabled&&console.log("XPay wallet debugging: shouldValue not match",o)}}else if("button"===i)if(r){const e=await t.IsFieldVisible(r);window.loadTimeData.data_.isDebugEnabled&&console.log("XPay wallet debugging: isSecondarySelectorVisible",o,e),e||(window.loadTimeData.data_.isDebugEnabled&&console.log("XPay wallet debugging: click",l),a.Click(l))}else window.loadTimeData.data_.isDebugEnabled&&console.log("XPay wallet debugging: click",l),a.Click(l),t.GetFirstVisibleElement(l).scrollIntoView()}function r(e,t){const a=new RegExp(`(?<![a-zA-Z0-9])${e}(?![a-zA-Z])`,"i");return t?.match(a)}function c(e,t,o){for(const i of e){const e=(i?.innerText||i?.innerHTML)?.toUpperCase()?.trim();let n=!1;if(o===l.ExactMatch){if(n=t.some((t=>t===e)),!n){const e=i?.getAttributeNames(),a=e.map((e=>i?.getAttribute(e)?.toUpperCase()?.trim()));n=t.some((e=>a.includes(e)))}}else n=t.some((t=>r(t,e)));if(n)return a.Click(void 0,i),!0}return!1}function u(e,a,o){const i=t.RunQuerySelectorAll(a),n=o?.map((e=>e.toUpperCase()));let s=c(i,n,l.ExactMatch);return window.loadTimeData.data_.isDebugEnabled&&console.log("XPay wallet debugging: after ExactMatch, canMatch",e,s),s||(s=c(i,n,l.RegMatch),window.loadTimeData.data_.isDebugEnabled&&console.log("XPay wallet debugging: after RegMatch, canRegMatch",e,s)),s}async function g(o){const{Name:l,WaitForVisible:i,ArrowDownSelector:n,SecondarySelector:s,SecondaryType:d}=o;let{ValueList:r}=o;if("expiryMonth"==l&&(r=function(e){if(e.length<1)return e;const t=new Map;return t.set("01",["01","1","January","Jan"]),t.set("02",["02","2","February","Feb"]),t.set("03",["03","3","March","Mar"]),t.set("04",["04","4","April","Apr"]),t.set("05",["05","5","May"]),t.set("06",["06","6","June","Jun"]),t.set("07",["07","7","July","Jul"]),t.set("08",["08","8","August","Aug"]),t.set("09",["09","9","September","Sep"]),t.set("10",["10","October","Oct"]),t.set("11",["11","November","Nov"]),t.set("12",["12","December","Dec"]),t.get(e[0])||e}(r),window.loadTimeData.data_.isDebugEnabled&&console.log("XPay wallet debugging: expiryMonth ValueList",r)),"country"==l&&(r=function(t){if(t.length<1)return t;for(const a of t){const o=e[a];o&&t.push(o)}return t}(r),window.loadTimeData.data_.isDebugEnabled&&console.log("XPay wallet debugging: country ValueList",r)),s){const e=await t.WaitForCondition((async()=>await t.IsFieldVisible(s)),i||0);if(window.loadTimeData.data_.isDebugEnabled&&console.log("XPay wallet debugging: isSecondarySelectorVisible",l,e),!e)throw new Error("Secondary selector not found");if("option"===d){let e=!1;if(n)for(;!e&&!t.isElementDisabled(n);)window.loadTimeData.data_.isDebugEnabled&&console.log("XPay wallet debugging: click ArrowDownSelector",n),a.Click(n,void 0,(()=>{e=u(l,s,r)}));else e=u(l,s,r);if(window.loadTimeData.data_.isDebugEnabled&&console.log("XPay wallet debugging: canMatch final result",l,e),!e)throw new Error("Secondary selector of option cannot match ValueList")}else"button"===d&&a.Click(s)}}window[o]=new class{initialize(e){return window.loadTimeData={data_:{isDebugEnabled:!0}},window.loadTimeData.data_.isDebugEnabled&&console.log("XPay wallet debugging: initialize, params: ",e),e.splice(0,2),window.ExecuteInitialize(e),!0}raiseMessageFromHost(e){window.loadTimeData.data_.isDebugEnabled&&console.log("XPay wallet debugging: raiseMessageFromHost",e);if("WalletCheckoutScriptAutofill"===e.shift())try{"interactive"===document.readyState||"complete"===document.readyState?window.ExecuteAutofill(e):window.addEventListener?.("DOMContentLoaded",(()=>{window.ExecuteAutofill(e)}))}catch(e){window.loadTimeData.data_.isDebugEnabled&&console.log("XPay wallet debugging: raiseMessageFromHost error",e)}}postMessageToHost(e,t){window.loadTimeData.data_.isDebugEnabled&&console.log("XPay wallet debugging: postMessageToHost",e,t);try{walletCheckoutAutofillNativeHandler&&walletCheckoutAutofillNativeHandler.sendMessageToHost(e,t)}catch(e){window.loadTimeData.data_.isDebugEnabled&&console.log("XPay wallet debugging: postMessageToHost error",e)}}},window.ExecuteInitialize=async function(e){window.loadTimeData.data_.isDebugEnabled&&console.log("XPay wallet debugging: ExecuteInitialize",e);const a=e?.[1],o=JSON.parse(a??"{}"),{IsDebugEnabled:l}=o;window.loadTimeData={data_:{isDebugEnabled:l}};const s=e?.[2],d=JSON.parse(s??"{}");window.loadTimeData.data_.isDebugEnabled&&console.log("XPay wallet debugging: primaryMainFrameData",d);const{ShouldListenToIframeMessage:r}=d;let c=!1;function u(e){window.loadTimeData.data_.isDebugEnabled&&console.log("XPay wallet debugging: receive message from iframe, event: ",e);const{type:t,payload:a}=e.data;if(t===i.WalletCheckoutAutofillDriverCompleteMessage){c=!0,window.loadTimeData.data_.isDebugEnabled&&console.log("XPay wallet debugging: receive WalletCheckoutAutofillDriverComplete message from iframe, payload: ",a);try{n(a)}catch(e){window.loadTimeData.data_.isDebugEnabled&&console.log("XPay wallet debugging: postMessageToHost error",e)}}}r&&(window.addEventListener("message",u),await t.WaitForCondition((async()=>c),1e4),window.loadTimeData.data_.isDebugEnabled&&console.log("XPay wallet debugging: isIframeResponseReturned",c),window.removeEventListener("message",u))},window.ExecuteAutofill=async function(e){window.loadTimeData.data_.isDebugEnabled&&console.log("XPay wallet debugging: ExecuteAutofill",e);const t=e?.[0],a=JSON.parse(t??"{}"),{CheckoutElements:o=[]}=a,l=e?.[3],i=JSON.parse(l??"{}");window.loadTimeData.data_.isDebugEnabled&&console.log("XPay wallet debugging: iframeData",i);const{IsIframeElement:s,ParentOrigin:r}=i;try{let e=!0;const t=[];for(const a of o){const{Name:o,Selector:l,SecondarySelector:i}=a;if(l)try{await d(a)}catch(a){e=!1,t.push({failedField:o,failureReason:a.message});continue}if(i)try{await g(a)}catch(a){e=!1,t.push({failedField:o,failureReason:a.message})}}n([JSON.stringify({isSuccess:e,failureDetails:t?.length>0?t:null})],s,r)}catch(e){window.loadTimeData.data_.isDebugEnabled&&console.log("XPay wallet debugging: ExecuteAutofill error",e),n([JSON.stringify({isSuccess:!1,failureDetails:null})],s,r)}}}();