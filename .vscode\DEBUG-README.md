# Configuration de Débogage - Guide d'Utilisation

## Problème Résolu ✅

Ce projet résout le problème où le débogage ouvrait une page d'authentification séparée au lieu d'utiliser la fenêtre du serveur synchronisé.

**Corrections apportées :**
- Configuration de profils de navigateur dédiés pour persister les sessions d'authentification
- Arguments runtime spéciaux pour gérer l'authentification automatique
- Démarrage automatique du serveur de développement avant l'ouverture du navigateur
- Port configuré sur 3000 (au lieu de 8080 qui était occupé)
- Structure de dossiers corrigée pour le projet imbriqué

## Configurations Disponibles

### 1. Launch Edge with Auth (Recommended) ⭐
- **Type**: Microsoft Edge
- **Fonctionnalités**: 
  - Authentification automatique avec les identifiants admin/admin
  - Profil de débogage dédié pour persister la session
  - Démarrage automatique du serveur de développement
  - Support complet du débogage avec source maps

### 2. Launch Chrome with Auth
- **Type**: Google Chrome
- **Fonctionnalités**: Identiques à Edge, mais utilise Chrome

### 3. Launch Edge - Simple (No Auth)
- **Type**: Microsoft Edge
- **Fonctionnalités**: Configuration simple sans gestion d'authentification automatique

## Comment Utiliser

### Première Utilisation

1. **Exécuter le script de configuration** (optionnel):
   ```powershell
   .\.vscode\setup-auth.ps1
   ```

2. **Démarrer le débogage**:
   - Appuyez sur `F5` ou allez dans `Run and Debug` (Ctrl+Shift+D)
   - Sélectionnez "Launch Edge with Auth (Recommended)"
   - Cliquez sur le bouton de démarrage

### Que se passe-t-il ?

1. **Démarrage automatique du serveur**: La tâche `start-dev-server` lance automatiquement `npm run dev`
2. **Ouverture du navigateur**: Edge s'ouvre avec un profil dédié au débogage
3. **Authentification**: 
   - Première fois: Vous devrez peut-être entrer admin/admin manuellement
   - Sessions suivantes: L'authentification sera automatique grâce au profil persistant

## Résolution des Problèmes

### Problème: Page d'authentification qui s'ouvre toujours
**Solution**: 
- Utilisez la configuration "Launch Edge with Auth (Recommended)"
- Assurez-vous que le serveur fonctionne sur localhost:8080
- Vérifiez que les dossiers de profil sont créés dans `.vscode/`

### Problème: Le serveur ne démarre pas automatiquement
**Solution**:
- Vérifiez que `npm` est installé et accessible
- Exécutez manuellement `npm run dev` dans le terminal
- Vérifiez le fichier `package.json` pour la commande `dev`

### Problème: Erreurs de débogage
**Solution**:
- Utilisez la configuration "Launch Edge - Simple (No Auth)" pour tester
- Vérifiez que le port 8080 n'est pas utilisé par un autre processus
- Redémarrez VS Code

## Structure des Fichiers

```
.vscode/
├── launch.json          # Configurations de débogage
├── tasks.json           # Tâches automatiques (démarrage serveur)
├── setup-auth.ps1       # Script de configuration
├── auto-auth.js         # Script d'authentification automatique
└── DEBUG-README.md      # Ce fichier
```

## Personnalisation

### Changer les Identifiants
Modifiez les valeurs dans `.vscode/launch.json`:
```json
"env": {
    "LOGIN": "votre_username",
    "PASSWORD": "votre_password"
}
```

### Changer le Port
Modifiez l'URL dans les configurations:
```json
"url": "http://localhost:VOTRE_PORT"
```

## Notes Techniques

- **userDataDir**: Crée un profil de navigateur dédié pour persister les sessions
- **runtimeArgs**: Arguments spéciaux pour désactiver la sécurité web et permettre l'authentification
- **preLaunchTask**: Démarre automatiquement le serveur avant d'ouvrir le navigateur
- **sourceMapPathOverrides**: Permet le débogage du code TypeScript/React

## Support

Si vous rencontrez des problèmes:
1. Essayez la configuration "Simple (No Auth)"
2. Vérifiez que le serveur fonctionne manuellement avec `npm run dev`
3. Consultez la console de débogage de VS Code pour les erreurs
