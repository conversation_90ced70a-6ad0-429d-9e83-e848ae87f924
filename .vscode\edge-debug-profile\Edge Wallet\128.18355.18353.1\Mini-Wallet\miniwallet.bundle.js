/*! For license information please see miniwallet.bundle.js.LICENSE.txt */
(()=>{"use strict";var e,t,i={236:(e,t)=>{if("function"==typeof Symbol&&Symbol.for){var i=Symbol.for;i("react.element"),i("react.portal"),i("react.fragment"),i("react.strict_mode"),i("react.profiler"),i("react.provider"),i("react.context"),i("react.forward_ref"),i("react.suspense"),i("react.suspense_list"),i("react.memo"),i("react.lazy"),i("react.block"),i("react.server.block"),i("react.fundamental"),i("react.debug_trace_mode"),i("react.legacy_hidden")}},732:(e,t,i)=>{i(236)},533:e=>{var t=Object.getOwnPropertySymbols,i=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable;function r(e){if(null==e)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(e)}e.exports=function(){try{if(!Object.assign)return!1;var e=new String("abc");if(e[5]="de","5"===Object.getOwnPropertyNames(e)[0])return!1;for(var t={},i=0;i<10;i++)t["_"+String.fromCharCode(i)]=i;if("**********"!==Object.getOwnPropertyNames(t).map((function(e){return t[e]})).join(""))return!1;var o={};return"abcdefghijklmnopqrst".split("").forEach((function(e){o[e]=e})),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},o)).join("")}catch(e){return!1}}()?Object.assign:function(e,n){for(var a,s,l=r(e),c=1;c<arguments.length;c++){for(var d in a=Object(arguments[c]))i.call(a,d)&&(l[d]=a[d]);if(t){s=t(a);for(var u=0;u<s.length;u++)o.call(a,s[u])&&(l[s[u]]=a[s[u]])}}return l}},873:(e,t,i)=>{var o=i(265),r=60103;if(t.Fragment=60107,"function"==typeof Symbol&&Symbol.for){var n=Symbol.for;r=n("react.element"),t.Fragment=n("react.fragment")}var a=o.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s=Object.prototype.hasOwnProperty,l={key:!0,ref:!0,__self:!0,__source:!0};function c(e,t,i){var o,n={},c=null,d=null;for(o in void 0!==i&&(c=""+i),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(d=t.ref),t)s.call(t,o)&&!l.hasOwnProperty(o)&&(n[o]=t[o]);if(e&&e.defaultProps)for(o in t=e.defaultProps)void 0===n[o]&&(n[o]=t[o]);return{$$typeof:r,type:e,key:c,ref:d,props:n,_owner:a.current}}t.jsx=c,t.jsxs=c},493:(e,t,i)=>{var o=i(533),r="function"==typeof Symbol&&Symbol.for,n=r?Symbol.for("react.element"):60103,a=r?Symbol.for("react.portal"):60106,s=r?Symbol.for("react.fragment"):60107,l=r?Symbol.for("react.strict_mode"):60108,c=r?Symbol.for("react.profiler"):60114,d=r?Symbol.for("react.provider"):60109,u=r?Symbol.for("react.context"):60110,p=r?Symbol.for("react.forward_ref"):60112,h=r?Symbol.for("react.suspense"):60113,f=r?Symbol.for("react.memo"):60115,g=r?Symbol.for("react.lazy"):60116,E="function"==typeof Symbol&&Symbol.iterator;function T(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,i=1;i<arguments.length;i++)t+="&args[]="+encodeURIComponent(arguments[i]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var m={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},N={};function _(e,t,i){this.props=e,this.context=t,this.refs=N,this.updater=i||m}function O(){}function b(e,t,i){this.props=e,this.context=t,this.refs=N,this.updater=i||m}_.prototype.isReactComponent={},_.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error(T(85));this.updater.enqueueSetState(this,e,t,"setState")},_.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},O.prototype=_.prototype;var I=b.prototype=new O;I.constructor=b,o(I,_.prototype),I.isPureReactComponent=!0;var A={current:null},C=Object.prototype.hasOwnProperty,v={key:!0,ref:!0,__self:!0,__source:!0};function R(e,t,i){var o,r={},a=null,s=null;if(null!=t)for(o in void 0!==t.ref&&(s=t.ref),void 0!==t.key&&(a=""+t.key),t)C.call(t,o)&&!v.hasOwnProperty(o)&&(r[o]=t[o]);var l=arguments.length-2;if(1===l)r.children=i;else if(1<l){for(var c=Array(l),d=0;d<l;d++)c[d]=arguments[d+2];r.children=c}if(e&&e.defaultProps)for(o in l=e.defaultProps)void 0===r[o]&&(r[o]=l[o]);return{$$typeof:n,type:e,key:a,ref:s,props:r,_owner:A.current}}function y(e){return"object"==typeof e&&null!==e&&e.$$typeof===n}var S=/\/+/g,w=[];function k(e,t,i,o){if(w.length){var r=w.pop();return r.result=e,r.keyPrefix=t,r.func=i,r.context=o,r.count=0,r}return{result:e,keyPrefix:t,func:i,context:o,count:0}}function M(e){e.result=null,e.keyPrefix=null,e.func=null,e.context=null,e.count=0,10>w.length&&w.push(e)}function x(e,t,i,o){var r=typeof e;"undefined"!==r&&"boolean"!==r||(e=null);var s=!1;if(null===e)s=!0;else switch(r){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case n:case a:s=!0}}if(s)return i(o,e,""===t?"."+D(e,0):t),1;if(s=0,t=""===t?".":t+":",Array.isArray(e))for(var l=0;l<e.length;l++){var c=t+D(r=e[l],l);s+=x(r,c,i,o)}else if("function"==typeof(c=null===e||"object"!=typeof e?null:"function"==typeof(c=E&&e[E]||e["@@iterator"])?c:null))for(e=c.call(e),l=0;!(r=e.next()).done;)s+=x(r=r.value,c=t+D(r,l++),i,o);else if("object"===r)throw i=""+e,Error(T(31,"[object Object]"===i?"object with keys {"+Object.keys(e).join(", ")+"}":i,""));return s}function P(e,t,i){return null==e?0:x(e,"",t,i)}function D(e,t){return"object"==typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+(""+e).replace(/[=:]/g,(function(e){return t[e]}))}(e.key):t.toString(36)}function L(e,t){e.func.call(e.context,t,e.count++)}function F(e,t,i){var o=e.result,r=e.keyPrefix;e=e.func.call(e.context,t,e.count++),Array.isArray(e)?W(e,o,i,(function(e){return e})):null!=e&&(y(e)&&(e=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(e,r+(!e.key||t&&t.key===e.key?"":(""+e.key).replace(S,"$&/")+"/")+i)),o.push(e))}function W(e,t,i,o,r){var n="";null!=i&&(n=(""+i).replace(S,"$&/")+"/"),P(e,F,t=k(t,n,o,r)),M(t)}var B={current:null};function G(){var e=B.current;if(null===e)throw Error(T(321));return e}var V={ReactCurrentDispatcher:B,ReactCurrentBatchConfig:{suspense:null},ReactCurrentOwner:A,IsSomeRendererActing:{current:!1},assign:o};t.Children={map:function(e,t,i){if(null==e)return e;var o=[];return W(e,o,null,t,i),o},forEach:function(e,t,i){if(null==e)return e;P(e,L,t=k(null,null,t,i)),M(t)},count:function(e){return P(e,(function(){return null}),null)},toArray:function(e){var t=[];return W(e,t,null,(function(e){return e})),t},only:function(e){if(!y(e))throw Error(T(143));return e}},t.Component=_,t.Fragment=s,t.Profiler=c,t.PureComponent=b,t.StrictMode=l,t.Suspense=h,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=V,t.cloneElement=function(e,t,i){if(null==e)throw Error(T(267,e));var r=o({},e.props),a=e.key,s=e.ref,l=e._owner;if(null!=t){if(void 0!==t.ref&&(s=t.ref,l=A.current),void 0!==t.key&&(a=""+t.key),e.type&&e.type.defaultProps)var c=e.type.defaultProps;for(d in t)C.call(t,d)&&!v.hasOwnProperty(d)&&(r[d]=void 0===t[d]&&void 0!==c?c[d]:t[d])}var d=arguments.length-2;if(1===d)r.children=i;else if(1<d){c=Array(d);for(var u=0;u<d;u++)c[u]=arguments[u+2];r.children=c}return{$$typeof:n,type:e.type,key:a,ref:s,props:r,_owner:l}},t.createContext=function(e,t){return void 0===t&&(t=null),(e={$$typeof:u,_calculateChangedBits:t,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider={$$typeof:d,_context:e},e.Consumer=e},t.createElement=R,t.createFactory=function(e){var t=R.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:p,render:e}},t.isValidElement=y,t.lazy=function(e){return{$$typeof:g,_ctor:e,_status:-1,_result:null}},t.memo=function(e,t){return{$$typeof:f,type:e,compare:void 0===t?null:t}},t.useCallback=function(e,t){return G().useCallback(e,t)},t.useContext=function(e,t){return G().useContext(e,t)},t.useDebugValue=function(){},t.useEffect=function(e,t){return G().useEffect(e,t)},t.useImperativeHandle=function(e,t,i){return G().useImperativeHandle(e,t,i)},t.useLayoutEffect=function(e,t){return G().useLayoutEffect(e,t)},t.useMemo=function(e,t){return G().useMemo(e,t)},t.useReducer=function(e,t,i){return G().useReducer(e,t,i)},t.useRef=function(e){return G().useRef(e)},t.useState=function(e){return G().useState(e)},t.version="16.14.0"},265:(e,t,i)=>{e.exports=i(493)},668:(e,t,i)=>{e.exports=i(873)}},o={};function r(e){var t=o[e];if(void 0!==t)return t.exports;var n=o[e]={exports:{}};return i[e](n,n.exports,r),n.exports}t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,r.t=function(i,o){if(1&o&&(i=this(i)),8&o)return i;if("object"==typeof i&&i){if(4&o&&i.__esModule)return i;if(16&o&&"function"==typeof i.then)return i}var n=Object.create(null);r.r(n);var a={};e=e||[null,t({}),t([]),t(t)];for(var s=2&o&&i;"object"==typeof s&&!~e.indexOf(s);s=t(s))Object.getOwnPropertyNames(s).forEach((e=>a[e]=()=>i[e]));return a.default=()=>i,r.d(n,a),n},r.d=(e,t)=>{for(var i in t)r.o(t,i)&&!r.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{class e{createCSS(){return""}createBehavior(){}}const t=function(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof global)return global;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;try{return new Function("return this")()}catch(e){return{}}}();void 0===t.trustedTypes&&(t.trustedTypes={createPolicy:(e,t)=>t});const i={configurable:!1,enumerable:!1,writable:!1};void 0===t.FAST&&Reflect.defineProperty(t,"FAST",Object.assign({value:Object.create(null)},i));const o=t.FAST;if(void 0===o.getById){const e=Object.create(null);Reflect.defineProperty(o,"getById",Object.assign({value(t,i){let o=e[t];return void 0===o&&(o=i?e[t]=i():null),o}},i))}const n=Object.freeze([]);function a(){const e=new WeakMap;return function(t){let i=e.get(t);if(void 0===i){let o=Reflect.getPrototypeOf(t);for(;void 0===i&&null!==o;)i=e.get(o),o=Reflect.getPrototypeOf(o);i=void 0===i?[]:i.slice(0),e.set(t,i)}return i}}const s=t.FAST.getById(1,(()=>{const e=[],i=[];function o(){if(i.length)throw i.shift()}function r(e){try{e.call()}catch(e){i.push(e),setTimeout(o,0)}}function n(){let t=0;for(;t<e.length;)if(r(e[t]),t++,t>1024){for(let i=0,o=e.length-t;i<o;i++)e[i]=e[i+t];e.length-=t,t=0}e.length=0}return Object.freeze({enqueue:function(i){e.length<1&&t.requestAnimationFrame(n),e.push(i)},process:n})})),l=t.trustedTypes.createPolicy("fast-html",{createHTML:e=>e});let c=l;const d=`fast-${Math.random().toString(36).substring(2,8)}`,u=`${d}{`,p=`}${d}`,h=Object.freeze({supportsAdoptedStyleSheets:Array.isArray(document.adoptedStyleSheets)&&"replace"in CSSStyleSheet.prototype,setHTMLPolicy(e){if(c!==l)throw new Error("The HTML policy can only be set once.");c=e},createHTML:e=>c.createHTML(e),isMarker:e=>e&&8===e.nodeType&&e.data.startsWith(d),extractDirectiveIndexFromMarker:e=>parseInt(e.data.replace(`${d}:`,"")),createInterpolationPlaceholder:e=>`${u}${e}${p}`,createCustomAttributePlaceholder(e,t){return`${e}="${this.createInterpolationPlaceholder(t)}"`},createBlockPlaceholder:e=>`\x3c!--${d}:${e}--\x3e`,queueUpdate:s.enqueue,processUpdates:s.process,nextUpdate:()=>new Promise(s.enqueue),setAttribute(e,t,i){null==i?e.removeAttribute(t):e.setAttribute(t,i)},setBooleanAttribute(e,t,i){i?e.setAttribute(t,""):e.removeAttribute(t)},removeChildNodes(e){for(let t=e.firstChild;null!==t;t=e.firstChild)e.removeChild(t)},createTemplateWalker:e=>document.createTreeWalker(e,133,null,!1)});class f{constructor(){this.targets=new WeakSet}addStylesTo(e){this.targets.add(e)}removeStylesFrom(e){this.targets.delete(e)}isAttachedTo(e){return this.targets.has(e)}withBehaviors(...e){return this.behaviors=null===this.behaviors?e:this.behaviors.concat(e),this}}function g(e){return e.map((e=>e instanceof f?g(e.styles):[e])).reduce(((e,t)=>e.concat(t)),[])}function E(e){return e.map((e=>e instanceof f?e.behaviors:null)).reduce(((e,t)=>null===t?e:(null===e&&(e=[]),e.concat(t))),null)}f.create=(()=>{if(h.supportsAdoptedStyleSheets){const e=new Map;return t=>new N(t,e)}return e=>new O(e)})();let T=(e,t)=>{e.adoptedStyleSheets=[...e.adoptedStyleSheets,...t]},m=(e,t)=>{e.adoptedStyleSheets=e.adoptedStyleSheets.filter((e=>-1===t.indexOf(e)))};if(h.supportsAdoptedStyleSheets)try{document.adoptedStyleSheets.push(),document.adoptedStyleSheets.splice(),T=(e,t)=>{e.adoptedStyleSheets.push(...t)},m=(e,t)=>{for(const i of t){const t=e.adoptedStyleSheets.indexOf(i);-1!==t&&e.adoptedStyleSheets.splice(t,1)}}}catch(e){}class N extends f{constructor(e,t){super(),this.styles=e,this.styleSheetCache=t,this._styleSheets=void 0,this.behaviors=E(e)}get styleSheets(){if(void 0===this._styleSheets){const e=this.styles,t=this.styleSheetCache;this._styleSheets=g(e).map((e=>{if(e instanceof CSSStyleSheet)return e;let i=t.get(e);return void 0===i&&(i=new CSSStyleSheet,i.replaceSync(e),t.set(e,i)),i}))}return this._styleSheets}addStylesTo(e){T(e,this.styleSheets),super.addStylesTo(e)}removeStylesFrom(e){m(e,this.styleSheets),super.removeStylesFrom(e)}}let _=0;class O extends f{constructor(e){super(),this.styles=e,this.behaviors=null,this.behaviors=E(e),this.styleSheets=g(e),this.styleClass="fast-style-class-"+ ++_}addStylesTo(e){const t=this.styleSheets,i=this.styleClass;e=this.normalizeTarget(e);for(let o=0;o<t.length;o++){const r=document.createElement("style");r.innerHTML=t[o],r.className=i,e.append(r)}super.addStylesTo(e)}removeStylesFrom(e){const t=(e=this.normalizeTarget(e)).querySelectorAll(`.${this.styleClass}`);for(let i=0,o=t.length;i<o;++i)e.removeChild(t[i]);super.removeStylesFrom(e)}isAttachedTo(e){return super.isAttachedTo(this.normalizeTarget(e))}normalizeTarget(e){return e===document?document.body:e}}function b(t,i){const o=[];let r="";const n=[];for(let a=0,s=t.length-1;a<s;++a){r+=t[a];let s=i[a];if(s instanceof e){const e=s.createBehavior();s=s.createCSS(),e&&n.push(e)}s instanceof f||s instanceof CSSStyleSheet?(""!==r.trim()&&(o.push(r),r=""),o.push(s)):r+=s}return r+=t[t.length-1],""!==r.trim()&&o.push(r),{styles:o,behaviors:n}}function I(e,...t){const{styles:i,behaviors:o}=b(e,t),r=f.create(i);return o.length&&r.withBehaviors(...o),r}class A extends e{constructor(e,t){super(),this.behaviors=t,this.css="";const i=e.reduce(((e,t)=>("string"==typeof t?this.css+=t:e.push(t),e)),[]);i.length&&(this.styles=f.create(i))}createBehavior(){return this}createCSS(){return this.css}bind(e){this.styles&&e.$fastController.addStyles(this.styles),this.behaviors.length&&e.$fastController.addBehaviors(this.behaviors)}unbind(e){this.styles&&e.$fastController.removeStyles(this.styles),this.behaviors.length&&e.$fastController.removeBehaviors(this.behaviors)}}class C{constructor(e,t){this.sub1=void 0,this.sub2=void 0,this.spillover=void 0,this.source=e,this.sub1=t}has(e){return void 0===this.spillover?this.sub1===e||this.sub2===e:-1!==this.spillover.indexOf(e)}subscribe(e){const t=this.spillover;if(void 0===t){if(this.has(e))return;if(void 0===this.sub1)return void(this.sub1=e);if(void 0===this.sub2)return void(this.sub2=e);this.spillover=[this.sub1,this.sub2,e],this.sub1=void 0,this.sub2=void 0}else-1===t.indexOf(e)&&t.push(e)}unsubscribe(e){const t=this.spillover;if(void 0===t)this.sub1===e?this.sub1=void 0:this.sub2===e&&(this.sub2=void 0);else{const i=t.indexOf(e);-1!==i&&t.splice(i,1)}}notify(e){const t=this.spillover,i=this.source;if(void 0===t){const t=this.sub1,o=this.sub2;void 0!==t&&t.handleChange(i,e),void 0!==o&&o.handleChange(i,e)}else for(let o=0,r=t.length;o<r;++o)t[o].handleChange(i,e)}}class v{constructor(e){this.subscribers={},this.sourceSubscribers=null,this.source=e}notify(e){var t;const i=this.subscribers[e];void 0!==i&&i.notify(e),null===(t=this.sourceSubscribers)||void 0===t||t.notify(e)}subscribe(e,t){var i;if(t){let i=this.subscribers[t];void 0===i&&(this.subscribers[t]=i=new C(this.source)),i.subscribe(e)}else this.sourceSubscribers=null!==(i=this.sourceSubscribers)&&void 0!==i?i:new C(this.source),this.sourceSubscribers.subscribe(e)}unsubscribe(e,t){var i;if(t){const i=this.subscribers[t];void 0!==i&&i.unsubscribe(e)}else null===(i=this.sourceSubscribers)||void 0===i||i.unsubscribe(e)}}const R=o.getById(2,(()=>{const e=/(:|&&|\|\||if)/,t=new WeakMap,i=h.queueUpdate;let o,r=e=>{throw new Error("Must call enableArrayObservation before observing arrays.")};function n(e){let i=e.$fastController||t.get(e);return void 0===i&&(Array.isArray(e)?i=r(e):t.set(e,i=new v(e))),i}const s=a();class l{constructor(e){this.name=e,this.field=`_${e}`,this.callback=`${e}Changed`}getValue(e){return void 0!==o&&o.watch(e,this.name),e[this.field]}setValue(e,t){const i=this.field,o=e[i];if(o!==t){e[i]=t;const r=e[this.callback];"function"==typeof r&&r.call(e,o,t),n(e).notify(this.name)}}}class c extends C{constructor(e,t,i=!1){super(e,t),this.binding=e,this.isVolatileBinding=i,this.needsRefresh=!0,this.needsQueue=!0,this.first=this,this.last=null,this.propertySource=void 0,this.propertyName=void 0,this.notifier=void 0,this.next=void 0}observe(e,t){this.needsRefresh&&null!==this.last&&this.disconnect();const i=o;o=this.needsRefresh?this:void 0,this.needsRefresh=this.isVolatileBinding;const r=this.binding(e,t);return o=i,r}disconnect(){if(null!==this.last){let e=this.first;for(;void 0!==e;)e.notifier.unsubscribe(this,e.propertyName),e=e.next;this.last=null,this.needsRefresh=this.needsQueue=!0}}watch(e,t){const i=this.last,r=n(e),a=null===i?this.first:{};if(a.propertySource=e,a.propertyName=t,a.notifier=r,r.subscribe(this,t),null!==i){if(!this.needsRefresh){let t;o=void 0,t=i.propertySource[i.propertyName],o=this,e===t&&(this.needsRefresh=!0)}i.next=a}this.last=a}handleChange(){this.needsQueue&&(this.needsQueue=!1,i(this))}call(){null!==this.last&&(this.needsQueue=!0,this.notify(this))}records(){let e=this.first;return{next:()=>{const t=e;return void 0===t?{value:void 0,done:!0}:(e=e.next,{value:t,done:!1})},[Symbol.iterator]:function(){return this}}}}return Object.freeze({setArrayObserverFactory(e){r=e},getNotifier:n,track(e,t){void 0!==o&&o.watch(e,t)},trackVolatile(){void 0!==o&&(o.needsRefresh=!0)},notify(e,t){n(e).notify(t)},defineProperty(e,t){"string"==typeof t&&(t=new l(t)),s(e).push(t),Reflect.defineProperty(e,t.name,{enumerable:!0,get:function(){return t.getValue(this)},set:function(e){t.setValue(this,e)}})},getAccessors:s,binding(e,t,i=this.isVolatileBinding(e)){return new c(e,t,i)},isVolatileBinding:t=>e.test(t.toString())})}));function y(e,t){R.defineProperty(e,t)}const S=o.getById(3,(()=>{let e=null;return{get:()=>e,set(t){e=t}}}));class w{constructor(){this.index=0,this.length=0,this.parent=null,this.parentContext=null}get event(){return S.get()}get isEven(){return this.index%2==0}get isOdd(){return this.index%2!=0}get isFirst(){return 0===this.index}get isInMiddle(){return!this.isFirst&&!this.isLast}get isLast(){return this.index===this.length-1}static setEvent(e){S.set(e)}}R.defineProperty(w.prototype,"index"),R.defineProperty(w.prototype,"length");const k=Object.seal(new w);class M{constructor(){this.targetIndex=0}}class x extends M{constructor(){super(...arguments),this.createPlaceholder=h.createInterpolationPlaceholder}}class P extends M{constructor(e,t,i){super(),this.name=e,this.behavior=t,this.options=i}createPlaceholder(e){return h.createCustomAttributePlaceholder(this.name,e)}createBehavior(e){return new this.behavior(e,this.options)}}function D(e,t){this.source=e,this.context=t,null===this.bindingObserver&&(this.bindingObserver=R.binding(this.binding,this,this.isBindingVolatile)),this.updateTarget(this.bindingObserver.observe(e,t))}function L(e,t){this.source=e,this.context=t,this.target.addEventListener(this.targetName,this)}function F(){this.bindingObserver.disconnect(),this.source=null,this.context=null}function W(){this.bindingObserver.disconnect(),this.source=null,this.context=null;const e=this.target.$fastView;void 0!==e&&e.isComposed&&(e.unbind(),e.needsBindOnly=!0)}function B(){this.target.removeEventListener(this.targetName,this),this.source=null,this.context=null}function G(e){h.setAttribute(this.target,this.targetName,e)}function V(e){h.setBooleanAttribute(this.target,this.targetName,e)}function U(e){if(null==e&&(e=""),e.create){this.target.textContent="";let t=this.target.$fastView;void 0===t?t=e.create():this.target.$fastTemplate!==e&&(t.isComposed&&(t.remove(),t.unbind()),t=e.create()),t.isComposed?t.needsBindOnly&&(t.needsBindOnly=!1,t.bind(this.source,this.context)):(t.isComposed=!0,t.bind(this.source,this.context),t.insertBefore(this.target),this.target.$fastView=t,this.target.$fastTemplate=e)}else{const t=this.target.$fastView;void 0!==t&&t.isComposed&&(t.isComposed=!1,t.remove(),t.needsBindOnly?t.needsBindOnly=!1:t.unbind()),this.target.textContent=e}}function $(e){this.target[this.targetName]=e}function H(e){const t=this.classVersions||Object.create(null),i=this.target;let o=this.version||0;if(null!=e&&e.length){const r=e.split(/\s+/);for(let e=0,n=r.length;e<n;++e){const n=r[e];""!==n&&(t[n]=o,i.classList.add(n))}}if(this.classVersions=t,this.version=o+1,0!==o){o-=1;for(const e in t)t[e]===o&&i.classList.remove(e)}}class z extends x{constructor(e){super(),this.binding=e,this.bind=D,this.unbind=F,this.updateTarget=G,this.isBindingVolatile=R.isVolatileBinding(this.binding)}get targetName(){return this.originalTargetName}set targetName(e){if(this.originalTargetName=e,void 0!==e)switch(e[0]){case":":if(this.cleanedTargetName=e.substr(1),this.updateTarget=$,"innerHTML"===this.cleanedTargetName){const e=this.binding;this.binding=(t,i)=>h.createHTML(e(t,i))}break;case"?":this.cleanedTargetName=e.substr(1),this.updateTarget=V;break;case"@":this.cleanedTargetName=e.substr(1),this.bind=L,this.unbind=B;break;default:this.cleanedTargetName=e,"class"===e&&(this.updateTarget=H)}}targetAtContent(){this.updateTarget=U,this.unbind=W}createBehavior(e){return new j(e,this.binding,this.isBindingVolatile,this.bind,this.unbind,this.updateTarget,this.cleanedTargetName)}}class j{constructor(e,t,i,o,r,n,a){this.source=null,this.context=null,this.bindingObserver=null,this.target=e,this.binding=t,this.isBindingVolatile=i,this.bind=o,this.unbind=r,this.updateTarget=n,this.targetName=a}handleChange(){this.updateTarget(this.bindingObserver.observe(this.source,this.context))}handleEvent(e){w.setEvent(e);const t=this.binding(this.source,this.context);w.setEvent(null),!0!==t&&e.preventDefault()}}let Z=null;class Y{addFactory(e){e.targetIndex=this.targetIndex,this.behaviorFactories.push(e)}captureContentBinding(e){e.targetAtContent(),this.addFactory(e)}reset(){this.behaviorFactories=[],this.targetIndex=-1}release(){Z=this}static borrow(e){const t=Z||new Y;return t.directives=e,t.reset(),Z=null,t}}function K(e){if(1===e.length)return e[0];let t;const i=e.length,o=e.map((e=>"string"==typeof e?()=>e:(t=e.targetName||t,e.binding))),r=new z(((e,t)=>{let r="";for(let n=0;n<i;++n)r+=o[n](e,t);return r}));return r.targetName=t,r}const q=p.length;function X(e,t){const i=t.split(u);if(1===i.length)return null;const o=[];for(let t=0,r=i.length;t<r;++t){const r=i[t],n=r.indexOf(p);let a;if(-1===n)a=r;else{const t=parseInt(r.substring(0,n));o.push(e.directives[t]),a=r.substring(n+q)}""!==a&&o.push(a)}return o}function Q(e,t,i=!1){const o=t.attributes;for(let r=0,n=o.length;r<n;++r){const a=o[r],s=a.value,l=X(e,s);let c=null;null===l?i&&(c=new z((()=>s)),c.targetName=a.name):c=K(l),null!==c&&(t.removeAttributeNode(a),r--,n--,e.addFactory(c))}}function J(e,t,i){const o=X(e,t.textContent);if(null!==o){let r=t;for(let n=0,a=o.length;n<a;++n){const a=o[n],s=0===n?t:r.parentNode.insertBefore(document.createTextNode(""),r.nextSibling);"string"==typeof a?s.textContent=a:(s.textContent=" ",e.captureContentBinding(a)),r=s,e.targetIndex++,s!==t&&i.nextNode()}e.targetIndex--}}const ee=document.createRange();class te{constructor(e,t){this.fragment=e,this.behaviors=t,this.source=null,this.context=null,this.firstChild=e.firstChild,this.lastChild=e.lastChild}appendTo(e){e.appendChild(this.fragment)}insertBefore(e){if(this.fragment.hasChildNodes())e.parentNode.insertBefore(this.fragment,e);else{const t=this.lastChild;if(e.previousSibling===t)return;const i=e.parentNode;let o,r=this.firstChild;for(;r!==t;)o=r.nextSibling,i.insertBefore(r,e),r=o;i.insertBefore(t,e)}}remove(){const e=this.fragment,t=this.lastChild;let i,o=this.firstChild;for(;o!==t;)i=o.nextSibling,e.appendChild(o),o=i;e.appendChild(t)}dispose(){const e=this.firstChild.parentNode,t=this.lastChild;let i,o=this.firstChild;for(;o!==t;)i=o.nextSibling,e.removeChild(o),o=i;e.removeChild(t);const r=this.behaviors,n=this.source;for(let e=0,t=r.length;e<t;++e)r[e].unbind(n)}bind(e,t){const i=this.behaviors;if(this.source!==e)if(null!==this.source){const o=this.source;this.source=e,this.context=t;for(let r=0,n=i.length;r<n;++r){const n=i[r];n.unbind(o),n.bind(e,t)}}else{this.source=e,this.context=t;for(let o=0,r=i.length;o<r;++o)i[o].bind(e,t)}}unbind(){if(null===this.source)return;const e=this.behaviors,t=this.source;for(let i=0,o=e.length;i<o;++i)e[i].unbind(t);this.source=null}static disposeContiguousBatch(e){if(0!==e.length){ee.setStartBefore(e[0].firstChild),ee.setEndAfter(e[e.length-1].lastChild),ee.deleteContents();for(let t=0,i=e.length;t<i;++t){const i=e[t],o=i.behaviors,r=i.source;for(let e=0,t=o.length;e<t;++e)o[e].unbind(r)}}}}class ie{constructor(e,t){this.behaviorCount=0,this.hasHostBehaviors=!1,this.fragment=null,this.targetOffset=0,this.viewBehaviorFactories=null,this.hostBehaviorFactories=null,this.html=e,this.directives=t}create(e){if(null===this.fragment){let e;const t=this.html;if("string"==typeof t){e=document.createElement("template"),e.innerHTML=h.createHTML(t);const i=e.content.firstElementChild;null!==i&&"TEMPLATE"===i.tagName&&(e=i)}else e=t;const i=function(e,t){const i=e.content;document.adoptNode(i);const o=Y.borrow(t);Q(o,e,!0);const r=o.behaviorFactories;o.reset();const n=h.createTemplateWalker(i);let a;for(;a=n.nextNode();)switch(o.targetIndex++,a.nodeType){case 1:Q(o,a);break;case 3:J(o,a,n);break;case 8:h.isMarker(a)&&o.addFactory(t[h.extractDirectiveIndexFromMarker(a)])}let s=0;(h.isMarker(i.firstChild)||1===i.childNodes.length&&t.length)&&(i.insertBefore(document.createComment(""),i.firstChild),s=-1);const l=o.behaviorFactories;return o.release(),{fragment:i,viewBehaviorFactories:l,hostBehaviorFactories:r,targetOffset:s}}(e,this.directives);this.fragment=i.fragment,this.viewBehaviorFactories=i.viewBehaviorFactories,this.hostBehaviorFactories=i.hostBehaviorFactories,this.targetOffset=i.targetOffset,this.behaviorCount=this.viewBehaviorFactories.length+this.hostBehaviorFactories.length,this.hasHostBehaviors=this.hostBehaviorFactories.length>0}const t=this.fragment.cloneNode(!0),i=this.viewBehaviorFactories,o=new Array(this.behaviorCount),r=h.createTemplateWalker(t);let n=0,a=this.targetOffset,s=r.nextNode();for(let e=i.length;n<e;++n){const e=i[n],t=e.targetIndex;for(;null!==s;){if(a===t){o[n]=e.createBehavior(s);break}s=r.nextNode(),a++}}if(this.hasHostBehaviors){const t=this.hostBehaviorFactories;for(let i=0,r=t.length;i<r;++i,++n)o[n]=t[i].createBehavior(e)}return new te(t,o)}render(e,t,i){"string"==typeof t&&(t=document.getElementById(t)),void 0===i&&(i=t);const o=this.create(i);return o.bind(e,k),o.appendTo(t),o}}const oe=/([ \x09\x0a\x0c\x0d])([^\0-\x1F\x7F-\x9F "'>=/]+)([ \x09\x0a\x0c\x0d]*=[ \x09\x0a\x0c\x0d]*(?:[^ \x09\x0a\x0c\x0d"'`<>=]*|"[^"]*|'[^']*))$/;function re(e,...t){const i=[];let o="";for(let r=0,n=e.length-1;r<n;++r){const n=e[r];let a=t[r];if(o+=n,a instanceof ie){const e=a;a=()=>e}if("function"==typeof a&&(a=new z(a)),a instanceof x){const e=oe.exec(n);null!==e&&(a.targetName=e[2])}a instanceof M?(o+=a.createPlaceholder(i.length),i.push(a)):o+=a}return o+=e[e.length-1],new ie(o,i)}const ne=e=>"function"==typeof e,ae=()=>null;function se(e){return void 0===e?ae:ne(e)?e:()=>e}function le(e,t,i){const o=ne(e)?e:()=>e,r=se(t),n=se(i);return(e,t)=>o(e,t)?r(e,t):n(e,t)}const ce=Object.freeze({locate:a()}),de={toView:e=>e?"true":"false",fromView:e=>null!=e&&"false"!==e&&!1!==e&&0!==e},ue={toView(e){if(null==e)return null;const t=1*e;return isNaN(t)?null:t.toString()},fromView(e){if(null==e)return null;const t=1*e;return isNaN(t)?null:t}};class pe{constructor(e,t,i=t.toLowerCase(),o="reflect",r){this.guards=new Set,this.Owner=e,this.name=t,this.attribute=i,this.mode=o,this.converter=r,this.fieldName=`_${t}`,this.callbackName=`${t}Changed`,this.hasCallback=this.callbackName in e.prototype,"boolean"===o&&void 0===r&&(this.converter=de)}setValue(e,t){const i=e[this.fieldName],o=this.converter;void 0!==o&&(t=o.fromView(t)),i!==t&&(e[this.fieldName]=t,this.tryReflectToAttribute(e),this.hasCallback&&e[this.callbackName](i,t),e.$fastController.notify(this.name))}getValue(e){return R.track(e,this.name),e[this.fieldName]}onAttributeChangedCallback(e,t){this.guards.has(e)||(this.guards.add(e),this.setValue(e,t),this.guards.delete(e))}tryReflectToAttribute(e){const t=this.mode,i=this.guards;i.has(e)||"fromView"===t||h.queueUpdate((()=>{i.add(e);const o=e[this.fieldName];switch(t){case"reflect":const t=this.converter;h.setAttribute(e,this.attribute,void 0!==t?t.toView(o):o);break;case"boolean":h.setBooleanAttribute(e,this.attribute,o)}i.delete(e)}))}static collect(e,...t){const i=[];t.push(ce.locate(e));for(let o=0,r=t.length;o<r;++o){const r=t[o];if(void 0!==r)for(let t=0,o=r.length;t<o;++t){const o=r[t];"string"==typeof o?i.push(new pe(e,o)):i.push(new pe(e,o.property,o.attribute,o.mode,o.converter))}}return i}}function he(e,t){let i;function o(e,t){arguments.length>1&&(i.property=t),ce.locate(e.constructor).push(i)}return arguments.length>1?(i={},void o(e,t)):(i=void 0===e?{}:e,o)}const fe={mode:"open"},ge={},Ee=o.getById(4,(()=>{const e=new Map;return Object.freeze({register:t=>!e.has(t.type)&&(e.set(t.type,t),!0),getByType:t=>e.get(t)})}));class Te{constructor(e,t=e.definition){"string"==typeof t&&(t={name:t}),this.type=e,this.name=t.name,this.template=t.template;const i=pe.collect(e,t.attributes),o=new Array(i.length),r={},n={};for(let e=0,t=i.length;e<t;++e){const t=i[e];o[e]=t.attribute,r[t.name]=t,n[t.attribute]=t}this.attributes=i,this.observedAttributes=o,this.propertyLookup=r,this.attributeLookup=n,this.shadowOptions=void 0===t.shadowOptions?fe:null===t.shadowOptions?void 0:Object.assign(Object.assign({},fe),t.shadowOptions),this.elementOptions=void 0===t.elementOptions?ge:Object.assign(Object.assign({},ge),t.elementOptions),this.styles=void 0===t.styles?void 0:Array.isArray(t.styles)?f.create(t.styles):t.styles instanceof f?t.styles:f.create([t.styles])}get isDefined(){return!!Ee.getByType(this.type)}define(e=customElements){const t=this.type;if(Ee.register(this)){const e=this.attributes,i=t.prototype;for(let t=0,o=e.length;t<o;++t)R.defineProperty(i,e[t]);Reflect.defineProperty(t,"observedAttributes",{value:this.observedAttributes,enumerable:!0})}return e.get(this.name)||e.define(this.name,t,this.elementOptions),this}}Te.forType=Ee.getByType;const me=new WeakMap,Ne={bubbles:!0,composed:!0,cancelable:!0};function _e(e){return e.shadowRoot||me.get(e)||null}class Oe extends v{constructor(e,t){super(e),this.boundObservables=null,this.behaviors=null,this.needsInitialization=!0,this._template=null,this._styles=null,this._isConnected=!1,this.$fastController=this,this.view=null,this.element=e,this.definition=t;const i=t.shadowOptions;if(void 0!==i){const t=e.attachShadow(i);"closed"===i.mode&&me.set(e,t)}const o=R.getAccessors(e);if(o.length>0){const t=this.boundObservables=Object.create(null);for(let i=0,r=o.length;i<r;++i){const r=o[i].name,n=e[r];void 0!==n&&(delete e[r],t[r]=n)}}}get isConnected(){return R.track(this,"isConnected"),this._isConnected}setIsConnected(e){this._isConnected=e,R.notify(this,"isConnected")}get template(){return this._template}set template(e){this._template!==e&&(this._template=e,this.needsInitialization||this.renderTemplate(e))}get styles(){return this._styles}set styles(e){this._styles!==e&&(null!==this._styles&&this.removeStyles(this._styles),this._styles=e,this.needsInitialization||null===e||this.addStyles(e))}addStyles(e){const t=_e(this.element)||this.element.getRootNode();if(e instanceof HTMLStyleElement)t.append(e);else if(!e.isAttachedTo(t)){const i=e.behaviors;e.addStylesTo(t),null!==i&&this.addBehaviors(i)}}removeStyles(e){const t=_e(this.element)||this.element.getRootNode();if(e instanceof HTMLStyleElement)t.removeChild(e);else if(e.isAttachedTo(t)){const i=e.behaviors;e.removeStylesFrom(t),null!==i&&this.removeBehaviors(i)}}addBehaviors(e){const t=this.behaviors||(this.behaviors=new Map),i=e.length,o=[];for(let r=0;r<i;++r){const i=e[r];t.has(i)?t.set(i,t.get(i)+1):(t.set(i,1),o.push(i))}if(this._isConnected){const e=this.element;for(let t=0;t<o.length;++t)o[t].bind(e,k)}}removeBehaviors(e,t=!1){const i=this.behaviors;if(null===i)return;const o=e.length,r=[];for(let n=0;n<o;++n){const o=e[n];if(i.has(o)){const e=i.get(o)-1;0===e||t?i.delete(o)&&r.push(o):i.set(o,e)}}if(this._isConnected){const e=this.element;for(let t=0;t<r.length;++t)r[t].unbind(e)}}onConnectedCallback(){if(this._isConnected)return;const e=this.element;this.needsInitialization?this.finishInitialization():null!==this.view&&this.view.bind(e,k);const t=this.behaviors;if(null!==t)for(const[i]of t)i.bind(e,k);this.setIsConnected(!0)}onDisconnectedCallback(){if(!this._isConnected)return;this.setIsConnected(!1);const e=this.view;null!==e&&e.unbind();const t=this.behaviors;if(null!==t){const e=this.element;for(const[i]of t)i.unbind(e)}}onAttributeChangedCallback(e,t,i){const o=this.definition.attributeLookup[e];void 0!==o&&o.onAttributeChangedCallback(this.element,i)}emit(e,t,i){return!!this._isConnected&&this.element.dispatchEvent(new CustomEvent(e,Object.assign(Object.assign({detail:t},Ne),i)))}finishInitialization(){const e=this.element,t=this.boundObservables;if(null!==t){const i=Object.keys(t);for(let o=0,r=i.length;o<r;++o){const r=i[o];e[r]=t[r]}this.boundObservables=null}const i=this.definition;null===this._template&&(this.element.resolveTemplate?this._template=this.element.resolveTemplate():i.template&&(this._template=i.template||null)),null!==this._template&&this.renderTemplate(this._template),null===this._styles&&(this.element.resolveStyles?this._styles=this.element.resolveStyles():i.styles&&(this._styles=i.styles||null)),null!==this._styles&&this.addStyles(this._styles),this.needsInitialization=!1}renderTemplate(e){const t=this.element,i=_e(t)||t;null!==this.view?(this.view.dispose(),this.view=null):this.needsInitialization||h.removeChildNodes(i),e&&(this.view=e.render(t,i,t))}static forCustomElement(e){const t=e.$fastController;if(void 0!==t)return t;const i=Te.forType(e.constructor);if(void 0===i)throw new Error("Missing FASTElement definition.");return e.$fastController=new Oe(e,i)}}function be(e){return class extends e{constructor(){super(),Oe.forCustomElement(this)}$emit(e,t,i){return this.$fastController.emit(e,t,i)}connectedCallback(){this.$fastController.onConnectedCallback()}disconnectedCallback(){this.$fastController.onDisconnectedCallback()}attributeChangedCallback(e,t,i){this.$fastController.onAttributeChangedCallback(e,t,i)}}}const Ie=Object.assign(be(HTMLElement),{from:e=>be(e),define:(e,t)=>new Te(e,t).define().type});function Ae(e){return function(t){new Te(t,e).define()}}const Ce="Enter",ve=" ";var Re,ye;(ye=Re||(Re={})).Sync="edge://settings/profiles/sync",ye.RoamingSync="edge://settings/profiles/sync#Roaming",ye.Payments="edge://settings/payments",ye.WalletHub="edge://wallet",ye.walletPaymentMethodsURL="edge://wallet/paymentMethods",ye.walletPasswordsURL="edge://wallet/passwords",ye.walletPersonalInfoURL="edge://wallet/personalInfo",ye.walletEtreeURL="edge://wallet/#popup=etree",ye.EnrollCardToMSALearnMore="https://aka.ms/EdgeSaveCardFAQ",ye.UseVirtualCardLearnMore="https://aka.ms/EdgeVirtualCardFAQ",ye.WalletSettings="edge://wallet/settings",ye.microsoftRewardsDashboardURL="https://go.microsoft.com/fwlink/?linkid=2222743",ye.microsoftRewardsGiveDashboardURL="https://go.microsoft.com/fwlink/?linkid=2147443",ye.microsoftRewardsRedeemURL="https://rewards.microsoft.com/redeem/?form=edgepredeem",ye.microsoftRebatesPayoutURL="https://www.bing.com/rebates/payouts?pay=1",ye.microsoftRebatesDealsURL="https://www.bing.com/rebates",ye.PCToMobileLandingPage="https://aka.ms/emw";const Se="REDACTED",we=(window.chrome,(e,t)=>{const i=JSON.stringify({eventName:e,data:t});performance.mark(i)});window.cachedMojom||(window.cachedMojom={});const{loadTimeData:ke}=window,Me=ke;function xe(e,t,i=!1){const o=(o,r)=>{if(!r)return Me?.getString(o);try{return Me.valueExists(o)?Me.getValue(o)!==r&&((e,t)=>Me.valueExists(e)&&Me.getValue(e)===t)(e,t)?(console.warn(`Different values for key ${o}: loadtimeData: ${Me.getValue(o)}\n                       default: ${r}`),r):Me.getValue(o):(i||o?.toLowerCase()?.includes("crypto")||console.warn(`Missing localized string: ${o}, will use ${r}`),r)}catch(e){console.error(`Error loading loadtimeData for ${o}: ${e}`)}return r};return{getString:o,getStringF:Me?.getStringF.bind(Me),getStringFWithDefaultValue:(e,t,...i)=>{const r=o(e,t);return r?Me?.substituteString(r,...i):""},getInteger:Me?.getInteger.bind(Me),getBoolean:Me?.getBoolean.bind(Me),valueExists:Me?.valueExists.bind(Me),getValue:Me?.getValue.bind(Me),overrideValues:Me?.overrideValues.bind(Me)}}const Pe=xe("notificationTitle","Notification"),De=e=>Pe.valueExists(e)&&Pe.getBoolean(e),Le=()=>De("isWalletNotificationContentClickable"),Fe=()=>!Pe.valueExists("EdgeWalletPageHandlerVersion")||De("isWalletHomepageNotificationEnabled"),We=Fe()?import("./edge-wallet-notification.mojom-webui.js").then((e=>e?.EdgeWalletNotificationHandler?.getRemote?.())):null,Be="object"==typeof global&&global&&global.Object===Object&&global;var Ge="object"==typeof self&&self&&self.Object===Object&&self;const Ve=(Be||Ge||Function("return this")()).Symbol;var Ue=Object.prototype,$e=Ue.hasOwnProperty,He=Ue.toString,ze=Ve?Ve.toStringTag:void 0;var je=Object.prototype.toString;var Ze=Ve?Ve.toStringTag:void 0;const Ye=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Ze&&Ze in Object(e)?function(e){var t=$e.call(e,ze),i=e[ze];try{e[ze]=void 0;var o=!0}catch(e){}var r=He.call(e);return o&&(t?e[ze]=i:delete e[ze]),r}(e):function(e){return je.call(e)}(e)};var Ke;!function(e){e.HISTOGRAM_LOG="histogram-log",e.HISTOGRAM_LINEAR="histogram-linear"}(Ke||(Ke={}));const qe=window?.chrome?.metricsPrivate;var Xe;!function(e){e.TOKENIZATION_MASTERCARD="TOKENIZATION_MASTERCARD",e.TOKENIZATION_VISA="TOKENIZATION_VISA",e.TOKENIZATION_NOTIFICATION_MASTERCARD="TOKENIZATION_NOTIFICATION_MASTERCARD",e.TOKENIZATION_NOTIFICATION_VISA="TOKENIZATION_NOTIFICATION_VISA"}(Xe||(Xe={}));const Qe=window?.chrome?.edgeWalletPrivate;var Je,et,tt;!function(e){e.visa="visa",e.mastercard="mastercard",e.amex="amex",e.discover="discover"}(Je||(Je={})),function(e){e.expired="expired",e.lastused="lastused",e.bestdeal="bestdeal",e.notsynced="notsynced",e.tokenized="tokenized",e.tokenizationEligible="tokenizationEligible",e.partialCard="partialCard"}(et||(et={})),function(e){e[e.NONE=0]="NONE",e[e.INVALID_NAME=1]="INVALID_NAME",e[e.INVALID_EXP_DATE=2]="INVALID_EXP_DATE",e[e.INVALID_CVV=4]="INVALID_CVV",e[e.INVALID_ADDRESS=8]="INVALID_ADDRESS",e[e.INVALID_NETWORK_TYPE=16]="INVALID_NETWORK_TYPE",e[e.UNKNOWN_ERROR=32]="UNKNOWN_ERROR",e[e.kMaxValue=32]="kMaxValue"}(tt||(tt={}));const it=e=>Je[e?.toLowerCase()],ot=e=>e?.slice(-4)||"";var rt,nt,at,st,lt,ct,dt,ut,pt,ht,ft;function gt(e,t){const{error:i}=e,o=i?.message?i?.message:i?.name,r=i?.stack;Dt.logErrorDetails(o?`${o}. Source: ${t}`:`no error message. Source: ${t}`,r??"no error callstack")}(ft=rt||(rt={})).WalletNotificationStatusMiniWallet="Microsoft.Wallet.NotificationStatus.MiniWallet",ft.WalletNotificationStatusMiniWalletWithAttraction="Microsoft.Wallet.NotificationStatus.MiniWallet.WithAttraction",ft.WalletNotificationStatusMiniWalletWithoutAttraction="Microsoft.Wallet.NotificationStatus.MiniWallet.WithoutAttraction",ft.WalletNotificationStatusAllApps="Microsoft.Wallet.NotificationStatus.AllApps",ft.WalletNotificationStatusAllAppsWithAttraction="Microsoft.Wallet.NotificationStatus.AllApps.WithAttraction",ft.WalletNotificationStatusAllAppsWithoutAttraction="Microsoft.Wallet.NotificationStatus.AllApps.WithoutAttraction",ft.WalletNotificationStatusHubHome="Microsoft.Wallet.NotificationStatus.HubHome",ft.WalletNotificationStatusHubHomeWithAttraction="Microsoft.Wallet.NotificationStatus.HubHome.WithAttraction",ft.WalletNotificationStatusHubHomeWithoutAttraction="Microsoft.Wallet.NotificationStatus.HubHome.WithoutAttraction",ft.WalletNotificationStatusHubHeader="Microsoft.Wallet.NotificationStatus.HubHeader",ft.WalletNotificationStatusHubHeaderWithAttraction="Microsoft.Wallet.NotificationStatus.HubHeader.WithAttraction",ft.WalletNotificationStatusHubHeaderWithoutAttraction="Microsoft.Wallet.NotificationStatus.HubHeader.WithoutAttraction",ft.WalletNotificationFirstBuildToFirstViewDuration="Microsoft.Wallet.Notification.Duration.FirstBuildToFirstViewInSeconds",ft.WalletNotificationFirstBuildToFirstEngagementDuration="Microsoft.Wallet.Notification.Duration.FirstBuildToFirstEngagementInSeconds",ft.WalletNotificationFirstViewToFirstEngagementDuration="Microsoft.Wallet.Notification.Duration.FirstViewToFirstEngagementInSeconds",ft.WalletNotificationCardExpiredDiffDaysOfEnagement="Microsoft.Wallet.Notification.DiffDaysOfEnagement.CardExpired",ft.WalletNotificationCardExpiringDiffDaysOfEnagement="Microsoft.Wallet.Notification.DiffDaysOfEnagement.CardExpiring",ft.NotificationFeaturePromotionStatus="Microsoft.Wallet.NotificationFeaturePromotionStatus",(ht=nt||(nt={}))[ht.NOTIFICATION_PROFILE_STATE_SHOWN=0]="NOTIFICATION_PROFILE_STATE_SHOWN",ht[ht.NOTIFICATION_PROFILE_STATE_ENGAGEMENT=1]="NOTIFICATION_PROFILE_STATE_ENGAGEMENT",ht[ht.NOTIFICATION_CARD_LOADED=2]="NOTIFICATION_CARD_LOADED",ht[ht.CARD_EXPIRED_IMPRESSION=3]="CARD_EXPIRED_IMPRESSION",ht[ht.CARD_EXPIRED_ENGAGEMENT=4]="CARD_EXPIRED_ENGAGEMENT",ht[ht.CARD_EXPIRED_ENGAGEMENT_SUCCESS=5]="CARD_EXPIRED_ENGAGEMENT_SUCCESS",ht[ht.CARD_EXPIRED_ENGAGEMENT_FAILURE=6]="CARD_EXPIRED_ENGAGEMENT_FAILURE",ht[ht.CARD_EXPIRED_SNOOZE=7]="CARD_EXPIRED_SNOOZE",ht[ht.CARD_EXPIRED_DISMISS_PERMANENTLY=8]="CARD_EXPIRED_DISMISS_PERMANENTLY",ht[ht.CARD_EXPIRING_SOON_IMPRESSION=9]="CARD_EXPIRING_SOON_IMPRESSION",ht[ht.CARD_EXPIRING_SOON_ENGAGEMENT=10]="CARD_EXPIRING_SOON_ENGAGEMENT",ht[ht.CARD_EXPIRING_SOON_ENGAGEMENT_SUCCESS=11]="CARD_EXPIRING_SOON_ENGAGEMENT_SUCCESS",ht[ht.CARD_EXPIRING_SOON_ENGAGEMENT_FAILURE=12]="CARD_EXPIRING_SOON_ENGAGEMENT_FAILURE",ht[ht.CARD_EXPIRING_SOON_SNOOZE=13]="CARD_EXPIRING_SOON_SNOOZE",ht[ht.CARD_EXPIRING_SOON_DISMISS_PERMANENTLY=14]="CARD_EXPIRING_SOON_DISMISS_PERMANENTLY",ht[ht.ROAM_CARD_IMPRESSION=15]="ROAM_CARD_IMPRESSION",ht[ht.ROAM_CARD_ENGAGEMENT=16]="ROAM_CARD_ENGAGEMENT",ht[ht.ROAM_CARD_ENGAGEMENT_SUCCESS=17]="ROAM_CARD_ENGAGEMENT_SUCCESS",ht[ht.ROAM_CARD_ENGAGEMENT_FAILURE=18]="ROAM_CARD_ENGAGEMENT_FAILURE",ht[ht.ROAM_CARD_SNOOZE=19]="ROAM_CARD_SNOOZE",ht[ht.ROAM_CARD_DISMISS_PERMANENTLY=20]="ROAM_CARD_DISMISS_PERMANENTLY",ht[ht.SIGN_UP_REBATES_IMPRESSION_DEPRECATED=21]="SIGN_UP_REBATES_IMPRESSION_DEPRECATED",ht[ht.SIGN_UP_REBATES_ENGAGEMENT_DEPRECATED=22]="SIGN_UP_REBATES_ENGAGEMENT_DEPRECATED",ht[ht.SIGN_UP_REBATES_ENGAGEMENT_SUCCESS_DEPRECATED=23]="SIGN_UP_REBATES_ENGAGEMENT_SUCCESS_DEPRECATED",ht[ht.SIGN_UP_REBATES_ENGAGEMENT_FAILURE_DEPRECATED=24]="SIGN_UP_REBATES_ENGAGEMENT_FAILURE_DEPRECATED",ht[ht.SIGN_UP_REBATES_SNOOZE_DEPRECATED=25]="SIGN_UP_REBATES_SNOOZE_DEPRECATED",ht[ht.SIGN_UP_REBATES_DISMISS_PERMANENTLY_DEPRECATED=26]="SIGN_UP_REBATES_DISMISS_PERMANENTLY_DEPRECATED",ht[ht.REBATES_PAYOUT_IMPRESSION_DEPRECATED=27]="REBATES_PAYOUT_IMPRESSION_DEPRECATED",ht[ht.REBATES_PAYOUT_ENGAGEMENT_DEPRECATED=28]="REBATES_PAYOUT_ENGAGEMENT_DEPRECATED",ht[ht.REBATES_PAYOUT_ENGAGEMENT_SUCCESS_DEPRECATED=29]="REBATES_PAYOUT_ENGAGEMENT_SUCCESS_DEPRECATED",ht[ht.REBATES_PAYOUT_ENGAGEMENT_FAILURE_DEPRECATED=30]="REBATES_PAYOUT_ENGAGEMENT_FAILURE_DEPRECATED",ht[ht.REBATES_PAYOUT_SNOOZE_DEPRECATED=31]="REBATES_PAYOUT_SNOOZE_DEPRECATED",ht[ht.REBATES_PAYOUT_DISMISS_PERMANENTLY_DEPRECATED=32]="REBATES_PAYOUT_DISMISS_PERMANENTLY_DEPRECATED",ht[ht.SIGN_UP_REWARDS_IMPRESSION_DEPRECATED=33]="SIGN_UP_REWARDS_IMPRESSION_DEPRECATED",ht[ht.SIGN_UP_REWARDS_ENGAGEMENT_DEPRECATED=34]="SIGN_UP_REWARDS_ENGAGEMENT_DEPRECATED",ht[ht.SIGN_UP_REWARDS_SNOOZE_DEPRECATED=35]="SIGN_UP_REWARDS_SNOOZE_DEPRECATED",ht[ht.SIGN_UP_REWARDS_DISMISS_PERMANENTLY_DEPRECATED=36]="SIGN_UP_REWARDS_DISMISS_PERMANENTLY_DEPRECATED",ht[ht.NOTIFICATION_CARD_ENGAGEMENT=37]="NOTIFICATION_CARD_ENGAGEMENT",ht[ht.CARD_TOKENIZATION_ELIGIBLE_IMPRESSION=38]="CARD_TOKENIZATION_ELIGIBLE_IMPRESSION",ht[ht.CARD_TOKENIZATION_ELIGIBLE_ENGAGEMENT=39]="CARD_TOKENIZATION_ELIGIBLE_ENGAGEMENT",ht[ht.CARD_TOKENIZATION_ELIGIBLE_SNOOZE=40]="CARD_TOKENIZATION_ELIGIBLE_SNOOZE",ht[ht.CARD_TOKENIZATION_ELIGIBLE_DISMISS_PERMANENTLY=41]="CARD_TOKENIZATION_ELIGIBLE_DISMISS_PERMANENTLY",ht[ht.UNSUPPORTED_TYPE=42]="UNSUPPORTED_TYPE",ht[ht.PERSONALIZED_OFFERS_AVAILABLE_IMPRESSION=43]="PERSONALIZED_OFFERS_AVAILABLE_IMPRESSION",ht[ht.PERSONALIZED_OFFERS_AVAILABLE_ENGAGEMENT=44]="PERSONALIZED_OFFERS_AVAILABLE_ENGAGEMENT",ht[ht.PERSONALIZED_OFFERS_AVAILABLE_SNOOZE=45]="PERSONALIZED_OFFERS_AVAILABLE_SNOOZE",ht[ht.PERSONALIZED_OFFERS_AVAILABLE_DISMISS_PERMANENTLY=46]="PERSONALIZED_OFFERS_AVAILABLE_DISMISS_PERMANENTLY",ht[ht.WALLET_NOTIFICATION_INTRODUCTION_FRE_IMPRESSION=47]="WALLET_NOTIFICATION_INTRODUCTION_FRE_IMPRESSION",ht[ht.WALLET_NOTIFICATION_INTRODUCTION_FRE_ENGAGEMENT=48]="WALLET_NOTIFICATION_INTRODUCTION_FRE_ENGAGEMENT",ht[ht.WALLET_NOTIFICATION_INTRODUCTION_FRE_SNOOZE=49]="WALLET_NOTIFICATION_INTRODUCTION_FRE_SNOOZE",ht[ht.WALLET_NOTIFICATION_INTRODUCTION_FRE_DISMISS_PERMANENTLY=50]="WALLET_NOTIFICATION_INTRODUCTION_FRE_DISMISS_PERMANENTLY",ht[ht.PASSWORD_LEAKAGE_IMPRESSION=51]="PASSWORD_LEAKAGE_IMPRESSION",ht[ht.PASSWORD_LEAKAGE_ENGAGEMENT=52]="PASSWORD_LEAKAGE_ENGAGEMENT",ht[ht.PASSWORD_LEAKAGE_SNOOZE=53]="PASSWORD_LEAKAGE_SNOOZE",ht[ht.PASSWORD_LEAKAGE_DISMISS_PERMANENTLY=54]="PASSWORD_LEAKAGE_DISMISS_PERMANENTLY",ht[ht.SIGN_UP_CRYPTOWALLET_IMPRESSION=55]="SIGN_UP_CRYPTOWALLET_IMPRESSION",ht[ht.SIGN_UP_CRYPTOWALLET_ENGAGEMENT=56]="SIGN_UP_CRYPTOWALLET_ENGAGEMENT",ht[ht.SIGN_UP_CRYPTOWALLET_SNOOZE=57]="SIGN_UP_CRYPTOWALLET_SNOOZE",ht[ht.SIGN_UP_CRYPTOWALLET_DISMISS_PERMANENTLY=58]="SIGN_UP_CRYPTOWALLET_DISMISS_PERMANENTLY",ht[ht.GIVE_WITH_BING_IMPRESSION_DEPRECATED=59]="GIVE_WITH_BING_IMPRESSION_DEPRECATED",ht[ht.GIVE_WITH_BING_ENGAGEMENT_DEPRECATED=60]="GIVE_WITH_BING_ENGAGEMENT_DEPRECATED",ht[ht.GIVE_WITH_BING_SNOOZE_DEPRECATED=61]="GIVE_WITH_BING_SNOOZE_DEPRECATED",ht[ht.GIVE_WITH_BING_DISMISS_PERMANENTLY_DEPRECATED=62]="GIVE_WITH_BING_DISMISS_PERMANENTLY_DEPRECATED",ht[ht.NOTIFICATION_INVALIDATED=63]="NOTIFICATION_INVALIDATED",ht[ht.UPCOMING_HOTEL_RESERVATION_IMPRESSION=64]="UPCOMING_HOTEL_RESERVATION_IMPRESSION",ht[ht.UPCOMING_HOTEL_RESERVATION_ENGAGEMENT=65]="UPCOMING_HOTEL_RESERVATION_ENGAGEMENT",ht[ht.UPCOMING_HOTEL_RESERVATION_SNOOZE=66]="UPCOMING_HOTEL_RESERVATION_SNOOZE",ht[ht.UPCOMING_HOTEL_RESERVATION_DISMISS_PERMANENTLY=67]="UPCOMING_HOTEL_RESERVATION_DISMISS_PERMANENTLY",ht[ht.NOTIFICATION_CARD_ENGAGEMENT_CTA=68]="NOTIFICATION_CARD_ENGAGEMENT_CTA",ht[ht.NOTIFICATION_CARD_ENGAGEMENT_DISMISS=69]="NOTIFICATION_CARD_ENGAGEMENT_DISMISS",ht[ht.NOTIFICATION_CARD_ENGAGEMENT_SNOOZE=70]="NOTIFICATION_CARD_ENGAGEMENT_SNOOZE",ht[ht.NOTIFICATION_CARD_ENGAGEMENT_FROM_CONTENT=71]="NOTIFICATION_CARD_ENGAGEMENT_FROM_CONTENT",ht[ht.DONATION_SUMMARY_IMPRESSION=72]="DONATION_SUMMARY_IMPRESSION",ht[ht.DONATION_SUMMARY_ENGAGEMENT=73]="DONATION_SUMMARY_ENGAGEMENT",ht[ht.DONATION_SUMMARY_SNOOZE=74]="DONATION_SUMMARY_SNOOZE",ht[ht.DONATION_SUMMARY_DISMISS_PERMANENTLY=75]="DONATION_SUMMARY_DISMISS_PERMANENTLY",ht[ht.FEATURE_PROMOTION_IMPRESSION=76]="FEATURE_PROMOTION_IMPRESSION",ht[ht.FEATURE_PROMOTION_ENGAGEMENT=77]="FEATURE_PROMOTION_ENGAGEMENT",ht[ht.FEATURE_PROMOTION_SNOOZE=78]="FEATURE_PROMOTION_SNOOZE",ht[ht.FEATURE_PROMOTION_DISMISS_PERMANENTLY=79]="FEATURE_PROMOTION_DISMISS_PERMANENTLY",ht[ht.PACKAGE_TRACKING_IMPRESSION=80]="PACKAGE_TRACKING_IMPRESSION",ht[ht.PACKAGE_TRACKING_SNOOZE=81]="PACKAGE_TRACKING_SNOOZE",ht[ht.PACKAGE_TRACKING_DISMISS_PERMANENTLY=82]="PACKAGE_TRACKING_DISMISS_PERMANENTLY",ht[ht.PACKAGE_TRACKING_VIEW_ORDER_ENGAGEMENT=83]="PACKAGE_TRACKING_VIEW_ORDER_ENGAGEMENT",ht[ht.PACKAGE_TRACKING_TRACK_ORDER_ENGAGEMENT=84]="PACKAGE_TRACKING_TRACK_ORDER_ENGAGEMENT",ht[ht.REBATES_IMPRESSION=85]="REBATES_IMPRESSION",ht[ht.REBATES_ENGAGEMENT=86]="REBATES_ENGAGEMENT",ht[ht.REBATES_ENGAGEMENT_SUCCESS=87]="REBATES_ENGAGEMENT_SUCCESS",ht[ht.REBATES_ENGAGEMENT_FAILURE=88]="REBATES_ENGAGEMENT_FAILURE",ht[ht.REBATES_SNOOZE=89]="REBATES_SNOOZE",ht[ht.REBATES_DISMISS_PERMANENTLY=90]="REBATES_DISMISS_PERMANENTLY",ht[ht.ETREE_CAMPAIGN_IMPRESSION=91]="ETREE_CAMPAIGN_IMPRESSION",ht[ht.ETREE_CAMPAIGN_ENGAGEMENT=92]="ETREE_CAMPAIGN_ENGAGEMENT",ht[ht.ETREE_CAMPAIGN_SNOOZE=93]="ETREE_CAMPAIGN_SNOOZE",ht[ht.ETREE_CAMPAIGN_DISMISS_PERMANENTLY=94]="ETREE_CAMPAIGN_DISMISS_PERMANENTLY",ht[ht.PWA_PROMOTION_IMPRESSION=95]="PWA_PROMOTION_IMPRESSION",ht[ht.PWA_PROMOTION_ENGAGEMENT=96]="PWA_PROMOTION_ENGAGEMENT",ht[ht.PWA_PROMOTION_SNOOZE=97]="PWA_PROMOTION_SNOOZE",ht[ht.PWA_PROMOTION_DISMISS_PERMANENTLY=98]="PWA_PROMOTION_DISMISS_PERMANENTLY",ht[ht.ETREE_NORMAL_IMPRESSION=99]="ETREE_NORMAL_IMPRESSION",ht[ht.ETREE_NORMAL_ENGAGEMENT=100]="ETREE_NORMAL_ENGAGEMENT",ht[ht.ETREE_NORMAL_SNOOZE=101]="ETREE_NORMAL_SNOOZE",ht[ht.ETREE_NORMAL_DISMISS_PERMANENTLY=102]="ETREE_NORMAL_DISMISS_PERMANENTLY",ht[ht.ETREE_NORMAL_ENROLLED_IMPRESSION=103]="ETREE_NORMAL_ENROLLED_IMPRESSION",ht[ht.ETREE_NORMAL_ENROLLED_ENGAGEMENT=104]="ETREE_NORMAL_ENROLLED_ENGAGEMENT",ht[ht.ETREE_NORMAL_ENROLLED_SNOOZE=105]="ETREE_NORMAL_ENROLLED_SNOOZE",ht[ht.ETREE_NORMAL_ENROLLED_DISMISS_PERMANENTLY=106]="ETREE_NORMAL_ENROLLED_DISMISS_PERMANENTLY",ht[ht.ETREE_NORMAL_NONENROLLED_IMPRESSION=107]="ETREE_NORMAL_NONENROLLED_IMPRESSION",ht[ht.ETREE_NORMAL_NONENROLLED_ENGAGEMENT=108]="ETREE_NORMAL_NONENROLLED_ENGAGEMENT",ht[ht.ETREE_NORMAL_NONENROLLED_SNOOZE=109]="ETREE_NORMAL_NONENROLLED_SNOOZE",ht[ht.ETREE_NORMAL_NONENROLLED_DISMISS_PERMANENTLY=110]="ETREE_NORMAL_NONENROLLED_DISMISS_PERMANENTLY",ht[ht.DONATION_TREND_NPO_IMPRESSION=111]="DONATION_TREND_NPO_IMPRESSION",ht[ht.DONATION_TREND_NPO_ENGAGEMENT=112]="DONATION_TREND_NPO_ENGAGEMENT",ht[ht.DONATION_TREND_NPO_SNOOZE=113]="DONATION_TREND_NPO_SNOOZE",ht[ht.DONATION_TREND_NPO_DISMISS_PERMANENTLY=114]="DONATION_TREND_NPO_DISMISS_PERMANENTLY",ht[ht.MAX=115]="MAX",function(e){e[e.FEATURE_PROMOTION_1_IMPRESSION_WITH_ATTRACTION=0]="FEATURE_PROMOTION_1_IMPRESSION_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_1_ENGAGEMENT_WITH_ATTRACTION=1]="FEATURE_PROMOTION_1_ENGAGEMENT_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_1_SNOOZE_WITH_ATTRACTION=2]="FEATURE_PROMOTION_1_SNOOZE_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_1_DISMISS_PERMANENTLY_WITH_ATTRACTION=3]="FEATURE_PROMOTION_1_DISMISS_PERMANENTLY_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_1_IMPRESSION_WITHOUT_ATTRACTION=4]="FEATURE_PROMOTION_1_IMPRESSION_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_1_ENGAGEMENT_WITHOUT_ATTRACTION=5]="FEATURE_PROMOTION_1_ENGAGEMENT_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_1_SNOOZE_WITHOUT_ATTRACTION=6]="FEATURE_PROMOTION_1_SNOOZE_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_1_DISMISS_PERMANENTLY_WITHOUT_ATTRACTION=7]="FEATURE_PROMOTION_1_DISMISS_PERMANENTLY_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_2_IMPRESSION_WITH_ATTRACTION=8]="FEATURE_PROMOTION_2_IMPRESSION_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_2_ENGAGEMENT_WITH_ATTRACTION=9]="FEATURE_PROMOTION_2_ENGAGEMENT_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_2_SNOOZE_WITH_ATTRACTION=10]="FEATURE_PROMOTION_2_SNOOZE_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_2_DISMISS_PERMANENTLY_WITH_ATTRACTION=11]="FEATURE_PROMOTION_2_DISMISS_PERMANENTLY_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_2_IMPRESSION_WITHOUT_ATTRACTION=12]="FEATURE_PROMOTION_2_IMPRESSION_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_2_ENGAGEMENT_WITHOUT_ATTRACTION=13]="FEATURE_PROMOTION_2_ENGAGEMENT_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_2_SNOOZE_WITHOUT_ATTRACTION=14]="FEATURE_PROMOTION_2_SNOOZE_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_2_DISMISS_PERMANENTLY_WITHOUT_ATTRACTION=15]="FEATURE_PROMOTION_2_DISMISS_PERMANENTLY_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_3_IMPRESSION_WITH_ATTRACTION=16]="FEATURE_PROMOTION_3_IMPRESSION_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_3_ENGAGEMENT_WITH_ATTRACTION=17]="FEATURE_PROMOTION_3_ENGAGEMENT_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_3_SNOOZE_WITH_ATTRACTION=18]="FEATURE_PROMOTION_3_SNOOZE_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_3_DISMISS_PERMANENTLY_WITH_ATTRACTION=19]="FEATURE_PROMOTION_3_DISMISS_PERMANENTLY_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_3_IMPRESSION_WITHOUT_ATTRACTION=20]="FEATURE_PROMOTION_3_IMPRESSION_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_3_ENGAGEMENT_WITHOUT_ATTRACTION=21]="FEATURE_PROMOTION_3_ENGAGEMENT_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_3_SNOOZE_WITHOUT_ATTRACTION=22]="FEATURE_PROMOTION_3_SNOOZE_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_3_DISMISS_PERMANENTLY_WITHOUT_ATTRACTION=23]="FEATURE_PROMOTION_3_DISMISS_PERMANENTLY_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_4_IMPRESSION_WITH_ATTRACTION=24]="FEATURE_PROMOTION_4_IMPRESSION_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_4_ENGAGEMENT_WITH_ATTRACTION=25]="FEATURE_PROMOTION_4_ENGAGEMENT_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_4_SNOOZE_WITH_ATTRACTION=26]="FEATURE_PROMOTION_4_SNOOZE_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_4_DISMISS_PERMANENTLY_WITH_ATTRACTION=27]="FEATURE_PROMOTION_4_DISMISS_PERMANENTLY_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_4_IMPRESSION_WITHOUT_ATTRACTION=28]="FEATURE_PROMOTION_4_IMPRESSION_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_4_ENGAGEMENT_WITHOUT_ATTRACTION=29]="FEATURE_PROMOTION_4_ENGAGEMENT_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_4_SNOOZE_WITHOUT_ATTRACTION=30]="FEATURE_PROMOTION_4_SNOOZE_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_4_DISMISS_PERMANENTLY_WITHOUT_ATTRACTION=31]="FEATURE_PROMOTION_4_DISMISS_PERMANENTLY_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_5_IMPRESSION_WITH_ATTRACTION=32]="FEATURE_PROMOTION_5_IMPRESSION_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_5_ENGAGEMENT_WITH_ATTRACTION=33]="FEATURE_PROMOTION_5_ENGAGEMENT_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_5_SNOOZE_WITH_ATTRACTION=34]="FEATURE_PROMOTION_5_SNOOZE_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_5_DISMISS_PERMANENTLY_WITH_ATTRACTION=35]="FEATURE_PROMOTION_5_DISMISS_PERMANENTLY_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_5_IMPRESSION_WITHOUT_ATTRACTION=36]="FEATURE_PROMOTION_5_IMPRESSION_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_5_ENGAGEMENT_WITHOUT_ATTRACTION=37]="FEATURE_PROMOTION_5_ENGAGEMENT_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_5_SNOOZE_WITHOUT_ATTRACTION=38]="FEATURE_PROMOTION_5_SNOOZE_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_5_DISMISS_PERMANENTLY_WITHOUT_ATTRACTION=39]="FEATURE_PROMOTION_5_DISMISS_PERMANENTLY_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_6_IMPRESSION_WITH_ATTRACTION=40]="FEATURE_PROMOTION_6_IMPRESSION_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_6_ENGAGEMENT_WITH_ATTRACTION=41]="FEATURE_PROMOTION_6_ENGAGEMENT_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_6_SNOOZE_WITH_ATTRACTION=42]="FEATURE_PROMOTION_6_SNOOZE_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_6_DISMISS_PERMANENTLY_WITH_ATTRACTION=43]="FEATURE_PROMOTION_6_DISMISS_PERMANENTLY_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_6_IMPRESSION_WITHOUT_ATTRACTION=44]="FEATURE_PROMOTION_6_IMPRESSION_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_6_ENGAGEMENT_WITHOUT_ATTRACTION=45]="FEATURE_PROMOTION_6_ENGAGEMENT_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_6_SNOOZE_WITHOUT_ATTRACTION=46]="FEATURE_PROMOTION_6_SNOOZE_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_6_DISMISS_PERMANENTLY_WITHOUT_ATTRACTION=47]="FEATURE_PROMOTION_6_DISMISS_PERMANENTLY_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_7_IMPRESSION_WITH_ATTRACTION=48]="FEATURE_PROMOTION_7_IMPRESSION_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_7_ENGAGEMENT_WITH_ATTRACTION=49]="FEATURE_PROMOTION_7_ENGAGEMENT_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_7_SNOOZE_WITH_ATTRACTION=50]="FEATURE_PROMOTION_7_SNOOZE_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_7_DISMISS_PERMANENTLY_WITH_ATTRACTION=51]="FEATURE_PROMOTION_7_DISMISS_PERMANENTLY_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_7_IMPRESSION_WITHOUT_ATTRACTION=52]="FEATURE_PROMOTION_7_IMPRESSION_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_7_ENGAGEMENT_WITHOUT_ATTRACTION=53]="FEATURE_PROMOTION_7_ENGAGEMENT_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_7_SNOOZE_WITHOUT_ATTRACTION=54]="FEATURE_PROMOTION_7_SNOOZE_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_7_DISMISS_PERMANENTLY_WITHOUT_ATTRACTION=55]="FEATURE_PROMOTION_7_DISMISS_PERMANENTLY_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_8_IMPRESSION_WITH_ATTRACTION=56]="FEATURE_PROMOTION_8_IMPRESSION_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_8_ENGAGEMENT_WITH_ATTRACTION=57]="FEATURE_PROMOTION_8_ENGAGEMENT_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_8_SNOOZE_WITH_ATTRACTION=58]="FEATURE_PROMOTION_8_SNOOZE_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_8_DISMISS_PERMANENTLY_WITH_ATTRACTION=59]="FEATURE_PROMOTION_8_DISMISS_PERMANENTLY_WITH_ATTRACTION",e[e.FEATURE_PROMOTION_8_IMPRESSION_WITHOUT_ATTRACTION=60]="FEATURE_PROMOTION_8_IMPRESSION_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_8_ENGAGEMENT_WITHOUT_ATTRACTION=61]="FEATURE_PROMOTION_8_ENGAGEMENT_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_8_SNOOZE_WITHOUT_ATTRACTION=62]="FEATURE_PROMOTION_8_SNOOZE_WITHOUT_ATTRACTION",e[e.FEATURE_PROMOTION_8_DISMISS_PERMANENTLY_WITHOUT_ATTRACTION=63]="FEATURE_PROMOTION_8_DISMISS_PERMANENTLY_WITHOUT_ATTRACTION",e[e.MAX=64]="MAX"}(at||(at={})),(pt=st||(st={})).CardExpired="CardExpired",pt.CardExpiring="CardExpiring",pt.RoamCard="RoamCard",pt.CardTokenizationEligible="CardTokenizationEligible",pt.PasswordLeakage="PasswordLeakage",pt.PersonalizedOffersAvailable="PersonalizedOffersAvailable",pt.UpcomingHotelReservations="UpcomingHotelReservations",pt.SignupCryptoWallet="SignupCryptoWallet",pt.DonationSummary="DonationSummary",pt.FeaturePromotion="FeaturePromotion",pt.PackageTracking="PackageTracking",pt.Rebates="Rebates",pt.EtreeCampaign="EtreeCampaign",pt.Etree="Etree",pt.PWAPromotion="PWAPromotion",pt.DonationTrendNpo="DonationTrendNpo",function(e){e[e.WalletHomeBell=0]="WalletHomeBell",e[e.WalletHomeOther=1]="WalletHomeOther"}(lt||(lt={})),(ut=ct||(ct={})).MiniWallet="MiniWallet",ut.HubHomepage="HubHomepage",ut.HubHeader="HubHeader",function(e){e.Halloween="41F3EF79-FA45-4A06-B38C-F46B514817B2",e.Christmas="40B1A79D-3455-46F8-B20E-AF2568178AEA",e.NormalEnrolled="41F3EF79-FA45-4A06-B38C-F46B514817B2",e.NormalNonEnrolled="86F10C77-0F63-40E0-B1D8-086A3181F6B5"}(dt||(dt={}));const Et=[nt.CARD_EXPIRED_ENGAGEMENT,nt.ROAM_CARD_ENGAGEMENT,nt.CARD_EXPIRING_SOON_ENGAGEMENT,nt.CARD_TOKENIZATION_ELIGIBLE_ENGAGEMENT,nt.WALLET_NOTIFICATION_INTRODUCTION_FRE_ENGAGEMENT,nt.SIGN_UP_CRYPTOWALLET_ENGAGEMENT,nt.PASSWORD_LEAKAGE_ENGAGEMENT,nt.PERSONALIZED_OFFERS_AVAILABLE_ENGAGEMENT,nt.UPCOMING_HOTEL_RESERVATION_ENGAGEMENT,nt.DONATION_SUMMARY_ENGAGEMENT,nt.FEATURE_PROMOTION_ENGAGEMENT,nt.REBATES_ENGAGEMENT,nt.ETREE_CAMPAIGN_ENGAGEMENT,nt.ETREE_NORMAL_ENGAGEMENT,nt.PWA_PROMOTION_ENGAGEMENT,nt.DONATION_TREND_NPO_ENGAGEMENT,nt.CARD_EXPIRED_DISMISS_PERMANENTLY,nt.ROAM_CARD_DISMISS_PERMANENTLY,nt.CARD_EXPIRING_SOON_DISMISS_PERMANENTLY,nt.CARD_TOKENIZATION_ELIGIBLE_DISMISS_PERMANENTLY,nt.WALLET_NOTIFICATION_INTRODUCTION_FRE_DISMISS_PERMANENTLY,nt.SIGN_UP_CRYPTOWALLET_DISMISS_PERMANENTLY,nt.PASSWORD_LEAKAGE_DISMISS_PERMANENTLY,nt.PERSONALIZED_OFFERS_AVAILABLE_DISMISS_PERMANENTLY,nt.UPCOMING_HOTEL_RESERVATION_DISMISS_PERMANENTLY,nt.DONATION_SUMMARY_DISMISS_PERMANENTLY,nt.FEATURE_PROMOTION_DISMISS_PERMANENTLY,nt.REBATES_DISMISS_PERMANENTLY,nt.ETREE_CAMPAIGN_DISMISS_PERMANENTLY,nt.ETREE_NORMAL_DISMISS_PERMANENTLY,nt.PWA_PROMOTION_DISMISS_PERMANENTLY,nt.DONATION_TREND_NPO_DISMISS_PERMANENTLY,nt.CARD_EXPIRED_SNOOZE,nt.ROAM_CARD_SNOOZE,nt.CARD_EXPIRING_SOON_SNOOZE,nt.CARD_TOKENIZATION_ELIGIBLE_SNOOZE,nt.WALLET_NOTIFICATION_INTRODUCTION_FRE_SNOOZE,nt.SIGN_UP_CRYPTOWALLET_SNOOZE,nt.PASSWORD_LEAKAGE_SNOOZE,nt.PERSONALIZED_OFFERS_AVAILABLE_SNOOZE,nt.UPCOMING_HOTEL_RESERVATION_SNOOZE,nt.DONATION_SUMMARY_SNOOZE,nt.FEATURE_PROMOTION_SNOOZE,nt.REBATES_SNOOZE,nt.ETREE_CAMPAIGN_SNOOZE,nt.ETREE_NORMAL_SNOOZE,nt.PWA_PROMOTION_SNOOZE,nt.DONATION_TREND_NPO_SNOOZE],Tt=[nt.CARD_EXPIRED_IMPRESSION,nt.ROAM_CARD_IMPRESSION,nt.SIGN_UP_CRYPTOWALLET_IMPRESSION,nt.CARD_EXPIRING_SOON_IMPRESSION,nt.CARD_TOKENIZATION_ELIGIBLE_IMPRESSION,nt.WALLET_NOTIFICATION_INTRODUCTION_FRE_IMPRESSION,nt.PASSWORD_LEAKAGE_IMPRESSION,nt.PERSONALIZED_OFFERS_AVAILABLE_IMPRESSION,nt.UPCOMING_HOTEL_RESERVATION_IMPRESSION,nt.DONATION_SUMMARY_IMPRESSION,nt.FEATURE_PROMOTION_IMPRESSION,nt.REBATES_IMPRESSION,nt.ETREE_CAMPAIGN_IMPRESSION,nt.ETREE_NORMAL_IMPRESSION,nt.PWA_PROMOTION_IMPRESSION,nt.DONATION_TREND_NPO_IMPRESSION],mt=new Map([[nt.CARD_EXPIRED_ENGAGEMENT,nt.NOTIFICATION_CARD_ENGAGEMENT_CTA],[nt.ROAM_CARD_ENGAGEMENT,nt.NOTIFICATION_CARD_ENGAGEMENT_CTA],[nt.CARD_EXPIRING_SOON_ENGAGEMENT,nt.NOTIFICATION_CARD_ENGAGEMENT_CTA],[nt.CARD_TOKENIZATION_ELIGIBLE_ENGAGEMENT,nt.NOTIFICATION_CARD_ENGAGEMENT_CTA],[nt.WALLET_NOTIFICATION_INTRODUCTION_FRE_ENGAGEMENT,nt.NOTIFICATION_CARD_ENGAGEMENT_CTA],[nt.SIGN_UP_CRYPTOWALLET_ENGAGEMENT,nt.NOTIFICATION_CARD_ENGAGEMENT_CTA],[nt.PASSWORD_LEAKAGE_ENGAGEMENT,nt.NOTIFICATION_CARD_ENGAGEMENT_CTA],[nt.PERSONALIZED_OFFERS_AVAILABLE_ENGAGEMENT,nt.NOTIFICATION_CARD_ENGAGEMENT_CTA],[nt.UPCOMING_HOTEL_RESERVATION_ENGAGEMENT,nt.NOTIFICATION_CARD_ENGAGEMENT_CTA],[nt.DONATION_SUMMARY_ENGAGEMENT,nt.NOTIFICATION_CARD_ENGAGEMENT_CTA],[nt.FEATURE_PROMOTION_ENGAGEMENT,nt.NOTIFICATION_CARD_ENGAGEMENT_CTA],[nt.REBATES_ENGAGEMENT,nt.NOTIFICATION_CARD_ENGAGEMENT_CTA],[nt.ETREE_CAMPAIGN_ENGAGEMENT,nt.NOTIFICATION_CARD_ENGAGEMENT_CTA],[nt.ETREE_NORMAL_ENGAGEMENT,nt.NOTIFICATION_CARD_ENGAGEMENT_CTA],[nt.PWA_PROMOTION_ENGAGEMENT,nt.NOTIFICATION_CARD_ENGAGEMENT_CTA],[nt.DONATION_TREND_NPO_ENGAGEMENT,nt.NOTIFICATION_CARD_ENGAGEMENT_CTA],[nt.CARD_EXPIRED_DISMISS_PERMANENTLY,nt.NOTIFICATION_CARD_ENGAGEMENT_DISMISS],[nt.ROAM_CARD_DISMISS_PERMANENTLY,nt.NOTIFICATION_CARD_ENGAGEMENT_DISMISS],[nt.CARD_EXPIRING_SOON_DISMISS_PERMANENTLY,nt.NOTIFICATION_CARD_ENGAGEMENT_DISMISS],[nt.CARD_TOKENIZATION_ELIGIBLE_DISMISS_PERMANENTLY,nt.NOTIFICATION_CARD_ENGAGEMENT_DISMISS],[nt.WALLET_NOTIFICATION_INTRODUCTION_FRE_DISMISS_PERMANENTLY,nt.NOTIFICATION_CARD_ENGAGEMENT_DISMISS],[nt.SIGN_UP_CRYPTOWALLET_DISMISS_PERMANENTLY,nt.NOTIFICATION_CARD_ENGAGEMENT_DISMISS],[nt.PASSWORD_LEAKAGE_DISMISS_PERMANENTLY,nt.NOTIFICATION_CARD_ENGAGEMENT_DISMISS],[nt.PERSONALIZED_OFFERS_AVAILABLE_DISMISS_PERMANENTLY,nt.NOTIFICATION_CARD_ENGAGEMENT_DISMISS],[nt.UPCOMING_HOTEL_RESERVATION_DISMISS_PERMANENTLY,nt.NOTIFICATION_CARD_ENGAGEMENT_DISMISS],[nt.DONATION_SUMMARY_DISMISS_PERMANENTLY,nt.NOTIFICATION_CARD_ENGAGEMENT_DISMISS],[nt.FEATURE_PROMOTION_DISMISS_PERMANENTLY,nt.NOTIFICATION_CARD_ENGAGEMENT_DISMISS],[nt.REBATES_DISMISS_PERMANENTLY,nt.NOTIFICATION_CARD_ENGAGEMENT_DISMISS],[nt.ETREE_CAMPAIGN_DISMISS_PERMANENTLY,nt.NOTIFICATION_CARD_ENGAGEMENT_DISMISS],[nt.ETREE_NORMAL_DISMISS_PERMANENTLY,nt.NOTIFICATION_CARD_ENGAGEMENT_DISMISS],[nt.PWA_PROMOTION_DISMISS_PERMANENTLY,nt.NOTIFICATION_CARD_ENGAGEMENT_DISMISS],[nt.DONATION_TREND_NPO_DISMISS_PERMANENTLY,nt.NOTIFICATION_CARD_ENGAGEMENT_DISMISS],[nt.CARD_EXPIRED_SNOOZE,nt.NOTIFICATION_CARD_ENGAGEMENT_SNOOZE],[nt.ROAM_CARD_SNOOZE,nt.NOTIFICATION_CARD_ENGAGEMENT_SNOOZE],[nt.CARD_EXPIRING_SOON_SNOOZE,nt.NOTIFICATION_CARD_ENGAGEMENT_SNOOZE],[nt.CARD_TOKENIZATION_ELIGIBLE_SNOOZE,nt.NOTIFICATION_CARD_ENGAGEMENT_SNOOZE],[nt.WALLET_NOTIFICATION_INTRODUCTION_FRE_SNOOZE,nt.NOTIFICATION_CARD_ENGAGEMENT_SNOOZE],[nt.SIGN_UP_CRYPTOWALLET_SNOOZE,nt.NOTIFICATION_CARD_ENGAGEMENT_SNOOZE],[nt.PASSWORD_LEAKAGE_SNOOZE,nt.NOTIFICATION_CARD_ENGAGEMENT_SNOOZE],[nt.PERSONALIZED_OFFERS_AVAILABLE_SNOOZE,nt.NOTIFICATION_CARD_ENGAGEMENT_SNOOZE],[nt.UPCOMING_HOTEL_RESERVATION_SNOOZE,nt.NOTIFICATION_CARD_ENGAGEMENT_SNOOZE],[nt.DONATION_SUMMARY_SNOOZE,nt.NOTIFICATION_CARD_ENGAGEMENT_SNOOZE],[nt.FEATURE_PROMOTION_SNOOZE,nt.NOTIFICATION_CARD_ENGAGEMENT_SNOOZE],[nt.REBATES_SNOOZE,nt.NOTIFICATION_CARD_ENGAGEMENT_SNOOZE],[nt.ETREE_CAMPAIGN_SNOOZE,nt.NOTIFICATION_CARD_ENGAGEMENT_SNOOZE],[nt.ETREE_NORMAL_SNOOZE,nt.NOTIFICATION_CARD_ENGAGEMENT_SNOOZE],[nt.PWA_PROMOTION_SNOOZE,nt.NOTIFICATION_CARD_ENGAGEMENT_SNOOZE],[nt.DONATION_TREND_NPO_SNOOZE,nt.NOTIFICATION_CARD_ENGAGEMENT_SNOOZE]]),Nt="walletNotificationFunnelEnumMaxValue",_t="walletNotificationUseNewHistograms";const Ot=(e,t)=>{const i=Pe.valueExists(Nt)?parseInt(Pe.getValue(Nt),10)+1:nt.MAX;if(e>i)return void console.warn("Action enum passed in is larger than known size. Please check walletNotificationFunnelEnumMaxValue.");const o=(e=>{const t=(e=>{const t="isUserTriggeredByAttration";switch(e){case ct.MiniWallet:return!!Pe.valueExists(t)&&Pe.getValue(t);case ct.HubHeader:case ct.HubHomepage:return!1}})(e),i=[];switch(e){case ct.MiniWallet:i.push(t?rt.WalletNotificationStatusMiniWalletWithAttraction:rt.WalletNotificationStatusMiniWalletWithoutAttraction),i.push(rt.WalletNotificationStatusMiniWallet);break;case ct.HubHomepage:i.push(t?rt.WalletNotificationStatusHubHomeWithAttraction:rt.WalletNotificationStatusHubHomeWithoutAttraction),i.push(rt.WalletNotificationStatusHubHome);break;case ct.HubHeader:i.push(t?rt.WalletNotificationStatusHubHeaderWithAttraction:rt.WalletNotificationStatusHubHeaderWithoutAttraction),i.push(rt.WalletNotificationStatusHubHeader)}return i.push(t?rt.WalletNotificationStatusAllAppsWithAttraction:rt.WalletNotificationStatusAllAppsWithoutAttraction),i.push(rt.WalletNotificationStatusAllApps),i})(t);o.forEach((t=>{((e,t,i)=>{try{if(qe.recordEnumerationValue(e,t,i),Et.includes(t)){qe.recordEnumerationValue(e,nt.NOTIFICATION_CARD_ENGAGEMENT,i);const o=mt.get(t);o<i&&qe.recordEnumerationValue(e,o,i)}Tt.includes(t)&&qe.recordEnumerationValue(e,nt.NOTIFICATION_CARD_LOADED,i)}catch(o){gt(o,`MetricName : ${e}, Key: ${t}, MaxValue: ${i}`)}})(t,e,i)}))},bt=(e,t)=>{Pe.valueExists(_t)&&Pe.getValue(_t)&&Ot(e,t)},It=(e,t)=>{if(Qe&&function(e){if(!function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}(e))return!1;var t=Ye(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}(Qe?.triggerExperiment))try{if(e)switch(t){case Je.mastercard:Qe.triggerExperiment(Xe.TOKENIZATION_NOTIFICATION_MASTERCARD);break;case Je.visa:Qe.triggerExperiment(Xe.TOKENIZATION_NOTIFICATION_VISA)}}catch{}};function At(e,t="profileflyoutnotification"){try{const i=new URL(e);return i.searchParams.append("source",t),i.toString()}catch(e){return console.error("Error happened when appending source, redirect to default page.",e),`edge://wallet?source=${t}`}}function Ct(e,t=""){try{const i=new URL(e);return i.searchParams.append("type",t),i.toString()}catch(e){return console.error("Error happened when appending type, redirect to default page.",e),`edge://wallet?type=${t}`}}var vt=r(265);function Rt(e,t,i=!1){const o=!!e.values&&e.values.every((e=>"string"==typeof e||"number"==typeof e));let r;if(r=i||!0!==t.valueExists(e.id)?e.defaultMessage:t.getString(e.id),!e.values)return r;try{return o?r.replace(/\$(.|$|\n)/g,(function(t){return"$$"===t?"$":Number(t[1])>0?e.values?.[Number(t[1])-1]:""})):(n=r,a=e.values,function(e,t,i,o){const r=(e.match(/(\$[1-9])|(([^$]|\$([^1-9]|$))+)/g)||[]).map((t=>{if(!t.match(/^\$[1-9]$/)){if((t.match(/\$/g)||[]).length%2!=0)throw new Error(`Unescaped $ found in localized string "${e}".`);const r=t.replace(/\$\$/g,"$");return{value:i?i(r,o):r,arg:null}}return{value:(r=parseInt(t[1],10)-1,a[r]),arg:t};var r}));return vt.createElement(vt.Fragment,null,r.map(((e,t)=>vt.createElement(vt.Fragment,{key:t},e.value))))}(n,0,s,l))}catch(t){return console.error(`Error when replacing ${JSON.stringify(e)}:\r\n ${t}`),t instanceof Error&&window?.onerror?.(t.message,"_TL_Without_LoadTimeData error",1,0,t),r}var n,a,s,l}function yt(e,t,i=!1){return o=>Rt({...e,values:o},t,i)}function St(e){return Rt(e,Pe)}function wt(e){return yt(e,Pe)}const kt={msWalletNotificationCardExpired:wt({id:"msWalletNotificationCardExpired",description:"Title of wallet notification for expired payment card",defaultMessage:"Your card has expired"}),msWalletNotificationCardExpiring:wt({id:"msWalletNotificationCardExpiring",description:"Title of wallet notification for payment card that is going to expire",defaultMessage:"Your card is expiring soon"}),msWalletNotificationUpdateExpText:wt({id:"msWalletNotificationUpdateExpText",description:"Description of expiration date update, $1 is last 4 digits of the card number",defaultMessage:"Edit your card information to continue use of your card ending in $1."}),msWalletNotificationUpdateButton:wt({id:"msWalletNotificationUpdateButton",description:"Text of wallet notification update action button",defaultMessage:"Update now"}),msWalletNotificationUpdateButton1:wt({id:"msWalletNotificationUpdateButton1",description:"Text of wallet notification update action button",defaultMessage:"Update or remove card"}),msWalletNotificationUpdateButton2:wt({id:"msWalletNotificationUpdateButton2",description:"Text of wallet notification update action button",defaultMessage:"Fix it"}),msWalletNotificationSnoozeText1:wt({id:"msWalletNotificationSnoozeText1",description:"Wallet notification snooze description",defaultMessage:"Notification snoozed for $1 days"}),msWalletNotificationUpdatedCardText:wt({id:"msWalletNotificationUpdatedCardText",description:"Description of successfully updating payment card result",defaultMessage:"Card updated successfully!"}),msWalletNotificationUpdateCardFailText:wt({id:"msWalletNotificationUpdateCardFailText",description:"Description of failed payment card update result, $1 is link",defaultMessage:"Update failed. Try again in $1"}),msWalletNotificationDismissLabel:wt({id:"msWalletNotificationDismissLabel",description:"Title text for the dismiss button",defaultMessage:"Dismiss"}),msWalletNotificationSnoozeLabel:wt({id:"msWalletNotificationSnoozeLabel",description:"Title text for the snooze button",defaultMessage:"Snooze"}),msWalletNotificationCardTokenizationEligibleTitle:wt({id:"msWalletNotificationCardTokenizationEligibleTitle",description:"Title of wallet notification for card tokenization eligible",defaultMessage:"Protect your purchases with virtual cards"}),msWalletNotificationCardTokenizationEligibleDescription:wt({id:"msWalletNotificationCardTokenizationEligibleDescription",description:"Description of wallet notification for card tokenization eligible, $1 is card number(****1234)",defaultMessage:"Virtual cards are single-use payment credentials that are randomly generated each time you make an online purchase. This helps ensure your $1 is protected."}),learnMoreLinkTextShort:wt({id:"learnMoreLinkTextShort",defaultMessage:"Learn more",description:"Text of learn more link text"}),activateVirtualCardButtonText:wt({id:"activateVirtualCardButtonText",description:"Text of Activate virtual card button text",defaultMessage:"Activate virtual card"}),msWalletNotificationCardTokenizationEligibleHeader:wt({id:"msWalletNotificationCardTokenizationEligibleHeader",description:"Header of wallet notification for card tokenization eligible V2",defaultMessage:"Protect your card details"}),msWalletNotificationCardTokenizationEligibleIntroduction:wt({id:"msWalletNotificationCardTokenizationEligibleIntroduction",description:"Introduction of wallet notification for card tokenization eligible V2",defaultMessage:"Set up a virtual card to make online shopping safer and more convenient."}),msWalletNotificationCardTokenizationEligibleIntroductionForRewards:wt({id:"msWalletNotificationCardTokenizationEligibleIntroductionForRewards",description:"Introduction of wallet notification for card tokenization eligible V2 for rewards incentive",defaultMessage:"Set up a virtual card to make online shopping safer and earn 20 Microsoft Rewards points."}),msWalletNotificationCardTokenizationEligibleSetUpText:wt({id:"msWalletNotificationCardTokenizationEligibleSetUpText",description:"Text of Set up button of wallet notification for card tokenization eligible V2",defaultMessage:"Set up for $1"}),personalizedOffersCashBackDescription:wt({id:"personalizedOffersCashBackDescription",defaultMessage:"Earn $1 cash back with $2.",description:"Text of Personalized Offers available description, $1 is cash value($20), $2 is seller name(Nike),"}),personalizedOffersOfferExpiresInText:wt({id:"personalizedOffersOfferExpiresInText",description:"Text of Personalized Offers available Offer expires in text",defaultMessage:"Offer expires in"}),msWalletNotificationCryptowalletSignupTitle:wt({id:"msWalletNotificationCryptowalletSignupTitle",description:"Title of set up crypto wallet",defaultMessage:"Set up your Crypto Wallet"}),msWalletNotificationCryptowalletSignupDescription:wt({id:"msWalletNotificationCryptowalletSignupDescription",description:"Description of set up crypto wallet",defaultMessage:"Explore Web3 securely with this non-custodial wallet"}),msWalletNotificationCryptowalletJoinButton:wt({id:"msWalletNotificationCryptowalletJoinButton",description:"Text of set up crypto wallet action button",defaultMessage:"Set up"}),msWalletNotificationHotelReservationTitle:wt({id:"msWalletNotificationHotelReservationTitle",description:"The title of upcoming hotel reservation notification",defaultMessage:"Upcoming accommodation"}),msWalletNotificationHotelReservationBody:wt({id:"msWalletNotificationHotelReservationBody",description:"The body of upcoming hotel reservation notification, $1 is hotel name, $2 is checkin date, $3 is checkout date",defaultMessage:"Your booking date of $1 from $2 to $3 is upcoming."}),msWalletNotificationHotelReservationViewDetails:wt({id:"msWalletNotificationHotelReservationViewDetails",description:"The text of link to view detials in upcoming hotel reservation notification",defaultMessage:"View details"}),personalizedOffersNoThanksText:wt({id:"personalizedOffersNoThanksText",description:"Text of Personalized Offers available no thanks text",defaultMessage:"No thanks"}),msWalletNotificationDonationSummaryTitle:wt({id:"msWalletNotificationDonationSummaryTitle",description:"Title of donation summary notification",defaultMessage:"View your entire giving summary and more"}),msWalletNotificationDonationSummaryLinkText:wt({id:"msWalletNotificationDonationSummaryLinkText",description:"Text of donation summary notification link",defaultMessage:"Check it out"}),walletTitle:wt({id:"walletTitle",description:"The title of wallet page",defaultMessage:"Wallet"}),msWalletNotificationRebatesCashoutTitle:wt({id:"msWalletNotificationRebatesCashoutTitle",description:"Title of wallet notification for rebates cashout",defaultMessage:"It's time to cash out!"}),msWalletNotificationRebatesCashoutDescription:wt({id:"msWalletNotificationRebatesCashoutDescription",description:"Description of wallet notification for rebates cashout",defaultMessage:"Know more about cashback program and cashout using PayPal."}),msWalletNotificationRebatesCashoutAmountDescription:wt({id:"msWalletNotificationRebatesCashoutAmountDescription",description:"Description of wallet notification for rebates cashout. $1 is cashback amount",defaultMessage:"Congratulations you have $1 in cashback. Cashout using PayPal."}),msWalletNotificationRebatesCashoutAction:wt({id:"msWalletNotificationRebatesCashoutAction",description:"Action text of wallet notification for rebates cashout",defaultMessage:"Transfer to Paypal"}),msWalletNotificationDismissText:wt({id:"msWalletNotificationDismissText",description:"Wallet notification dismiss permanently description",defaultMessage:"You will not see this notification again"}),msWalletNotificationSnoozeText:wt({id:"msWalletNotificationSnoozeText",description:"Wallet notification snooze description",defaultMessage:"Notification snoozed for 7 days"}),msWalletNotificationDismissPermanently:wt({id:"msWalletNotificationDismissPermanently",description:"Wallet notification dismiss permanently action text",defaultMessage:"Dismiss permanently"}),msWalletNotificationPOMessage:wt({id:"msWalletNotificationPOMessage",description:"Text of Personalized Offers available description, $1 is cash value($20)",defaultMessage:"Your selected $1 Microsoft Cashback offer expires in $2"}),personalizedOffersViewOfferText:wt({id:"personalizedOffersViewOfferText",description:"Text of Personalized Offers available view offer text",defaultMessage:"View offer"}),msWalletNotificationLeakageTitle:wt({id:"msWalletNotificationLeakageTitle",description:"The title of password leakage notification",defaultMessage:"Password leakage"}),msWalletNotificationPasswordCompromiseTitle:wt({id:"msWalletNotificationPasswordCompromiseTitle",description:"The title of password leakage or compromise notification",defaultMessage:"Password compromised"}),msWalletNotificationPasswordCompromiseBody:wt({id:"msWalletNotificationPasswordCompromiseBody",description:"The content of password compromise notification, $1 is the compromised password count",defaultMessage:"$1 passwords have appeared in a data leak."}),msWalletNotificationPasswordCompromiseBodySingular:wt({id:"msWalletNotificationPasswordCompromiseBodySingular",description:"The content of password compromise notification for one password leaked",defaultMessage:"1 password has appeared in a data leak."}),msWalletNotificationOrderStatusTitle:wt({id:"msWalletNotificationOrderStatusTitle",description:"The title of your order status change notification in package tracking notification",defaultMessage:"Order status updated"}),msWalletNotificationViewDetailLink:wt({id:"msWalletNotificationViewDetailLink",description:"The text of View details link in package tracking notification",defaultMessage:"View details"}),msWalletNotificationTrackOrderTitle:wt({id:"msWalletNotificationTrackOrderTitle",description:"The title of Track your order notification in package tracking notification",defaultMessage:"Track your order"}),msWalletNotificationTrackOrderSubTitle:wt({id:"msWalletNotificationTrackOrderSubTitle",description:"The subtitle of Track your order notification in package tracking notification",defaultMessage:"Wallet helps you track your order when you buy from certain sites."}),msWalletNotificationTrackOrderLink:wt({id:"msWalletNotificationTrackOrderLink",description:"The text of Track your order link in package tracking notification",defaultMessage:"Track order"}),msWalletNotificationPOTitle:wt({id:"msWalletNotificationPOTitle",description:"The title of personalized offers notification",defaultMessage:"Offer expiring soon"}),msWalletNotificationEtreeHalloweenTitle:wt({id:"msWalletNotificationEtreeHalloweenTitle",description:"Title of Etree Halloween event notification, E-tree is term and don't need translation",defaultMessage:"Halloween event with E-tree"}),msWalletNotificationEtreeHalloweenContent:wt({id:"msWalletNotificationEtreeHalloweenContent",description:"Content of Etree Halloween event notification",defaultMessage:"Check in daily to get a special ornament and extra water drops."}),msWalletNotificationEtreeHalloweenLinkText:wt({id:"msWalletNotificationEtreeHalloweenLinkText",description:"Text of Etree Halloween event notification action link",defaultMessage:"Join the Halloween event"}),msWalletNotificationEtreeChristmasTitle:wt({id:"msWalletNotificationEtreeChristmasTitle",description:"Title of Etree Christmas event notification",defaultMessage:"Earn holiday surprises"}),msWalletNotificationEtreeChristmasContent:wt({id:"msWalletNotificationEtreeChristmasContent",description:"Content of Etree Christmas event notification",defaultMessage:"Get daily surprises and make this holiday season more magical!"}),msWalletNotificationEtreeChristmasLinkText:wt({id:"msWalletNotificationEtreeChristmasLinkText",description:"Text of Etree Christmas event notification action link",defaultMessage:"Join now"}),msWalletNotificationEtreeNonEnrolledTitleV1:wt({id:"msWalletNotificationEtreeNonEnrolledTitleV1",description:"Title of Etree notification for non-enrolled users",defaultMessage:"Plant a tree"}),msWalletNotificationEtreeNonEnrolledDescriptionV1:wt({id:"msWalletNotificationEtreeNonEnrolledDescriptionV1",description:"Description of Etree notification for non-enrolled users",defaultMessage:"Plant a virtual tree in Wallet and we’ll plant a real tree to support reforestation efforts."}),msWalletNotificationEtreeNonEnrolledLinkTextV1:wt({id:"msWalletNotificationEtreeNonEnrolledLinkTextV1",description:"Text of Etree notification action link for non-enrolled users",defaultMessage:"Start now"}),msWalletNotificationEtreeEnrolledTitleV1:wt({id:"msWalletNotificationEtreeEnrolledTitleV1",description:"Title of Etree notification for enrolled users, $1 is water drops count for next level",defaultMessage:"$1 drops to level up"}),msWalletNotificationEtreeEnrolledDescriptionV1:wt({id:"msWalletNotificationEtreeEnrolledDescriptionV1",description:"Description of Etree notification for enrolled users, , $1 is water drops count for next level",defaultMessage:"$1 drops to level up and keep growing your tree."}),msWalletNotificationEtreeEnrolledLinkTextV1:wt({id:"msWalletNotificationEtreeEnrolledLinkTextV1",description:"Text of Etree notification action link for enrolled users",defaultMessage:"Grow my tree"}),msWalletNotificationEtreeNonEnrolledTitleV2:wt({id:"msWalletNotificationEtreeNonEnrolledTitleV2",description:"Title of Etree notification for enrolled users",defaultMessage:"Let’s grow green"}),msWalletNotificationEtreeNonEnrolledDescriptionV2:wt({id:"msWalletNotificationEtreeNonEnrolledDescriptionV2",description:"Description of Etree notification for enrolled users",defaultMessage:"Collect water drops today to grow your tree."}),msWalletNotificationEtreeNonEnrolledLinkTextV2:wt({id:"msWalletNotificationEtreeNonEnrolledLinkTextV2",description:"Text of Etree notification action link for non-enrolled users",defaultMessage:"Grow my tree"}),msWalletNotificationEtreeEnrolledTitleV2:wt({id:"msWalletNotificationEtreeEnrolledTitleV2",description:"Title of Etree notification for enrolled users, E-tree is term and don't need translation",defaultMessage:"Continue the green initiative"}),msWalletNotificationEtreeEnrolledDescriptionV2:wt({id:"msWalletNotificationEtreeEnrolledDescriptionV2",description:"Description of Etree notification for enrolled users",defaultMessage:"Collect water drops today to grow your tree. More efforts, more trees."}),msWalletNotificationEtreeEnrolledLinkTextV2:wt({id:"msWalletNotificationEtreeEnrolledLinkTextV2",description:"Text of Etree notification action link for enrolled users",defaultMessage:"Grow my tree"}),msWalletNotificationDonationTrendNpoTitle:wt({id:"msWalletNotificationDonationTrendNpoTitle",description:"Title of donation notification to recommend trending non-profit organization",defaultMessage:"Make your gift go further"}),msWalletNotificationDonationTrendNpoDescription:wt({id:"msWalletNotificationDonationTrendNpoDescription",description:"Description of donation notification to recommend trending non-profit organization, $1 is NPO name",defaultMessage:"$1 is trending, your gift will make a greater impact!"}),msWalletNotificationDonationTrendNpoLink:wt({id:"msWalletNotificationDonationTrendNpoLink",description:"Text of donation notification action link to redirect to the donation page",defaultMessage:"Give now"})},Mt={featurePromotionActionLinkText:wt({id:"featurePromotionActionLinkText",description:"Text of Wallet Notification Feature Promotion Action Link",defaultMessage:"Try it now"}),passwordsPromotionTitle:wt({id:"passwordsPromotionTitle",description:"Title of Wallet Notification Password Promotion",defaultMessage:"Manage your passwords in Wallet"}),passwordsDescription:wt({id:"passwordsDescription",description:"Text of Wallet Notification Password Promotion",defaultMessage:"Try the new management experience in Wallet."}),membershipsPromotionTitle:wt({id:"membershipsPromotionTitle",description:"Title of Wallet Notification Password Promotion",defaultMessage:"Manage your memberships in Wallet"}),membershipsDescription:wt({id:"membershipsDescription",description:"Text of Wallet Notification Password Promotion",defaultMessage:"Stay organized and in control."}),rewardsPromotionTitle:wt({id:"rewardsPromotionTitle",description:"Title of Wallet Notification Rewards Promotion",defaultMessage:"Add memberships to earn Microsoft Rewards"}),rewardsPromotionDescription:wt({id:"rewardsPromotionDescription",description:"Text of Wallet Notification Rewards Promotion",defaultMessage:"Earn up to 20 rewards points by adding new memberships to wallet"}),rewardsPromotionActionLinkText:wt({id:"rewardsPromotionActionLinkText",description:"Text of Wallet Notification Rewards Promotion Action Link",defaultMessage:"Add new membership"}),msWalletNotificationRoamCardHeader:wt({id:"msWalletNotificationRoamCardHeader",description:"Header of wallet notification for roam card",defaultMessage:"Save the card to Microsoft account"}),msWalletNotificationRoamCardHeaderV2:wt({id:"msWalletNotificationRoamCardHeaderV2",description:"Header of wallet notification for roam card",defaultMessage:"Pay faster and from different devices on future purchases"}),msWalletNotificationRoamCardHeaderV3:wt({id:"msWalletNotificationRoamCardHeaderV3",description:"Header of wallet notification for roam card",defaultMessage:"Pay faster from any device"}),msWalletNotificationRoamCardAction:wt({id:"msWalletNotificationRoamCardAction",description:"Action text of wallet notification for roam card",defaultMessage:"Save the card"}),msWalletNotificationRoamCardActionV2:wt({id:"msWalletNotificationRoamCardActionV2",description:"Action text of wallet notification for roam card",defaultMessage:"Save card to Microsoft account"})},xt=["getRewardsItem","clearCurrentNotification","getNotificationsUnviewedStatus","getMultipleNotifications","getCurrentNotification","onApply","onDismiss","onSnooze","isValidNotificationFromMultipleNotifications","isValidNotification"],Pt=["setNotificationsUnviewedStatus","buildNotifications","clearCurrentNotification","onApply","onDismiss","onSnooze","isValidNotification","isValidNotificationFromMultipleNotifications"],Dt=new class{constructor(){this.instanceBase=()=>{var e,t;return this.instanceBase_||(this.instanceBase_=Fe()?(e="EdgeWalletNotificationMojomModule",t=We,new Proxy({},{get:(i,o)=>async(...i)=>{const r=window.cachedMojom;return r[e]||(r[e]="function"==typeof t?await(t?.()):await t),r[e]?.[o]?.(...i)}})):null),this.instanceBase_},this.instanceTracingProxy_=function(e,t,i,o){const r={get:function(o,r){const n=o[r],a=new Error;return(...o)=>{const s={args:0===o.length?[]:t.includes(r)?[...o.values()]:Se,status:"Success",result:Se,stack:a.stack};try{const t=n.apply(this,o);return i.includes(r)&&(s.result=t),we(e,{apiName:r,data:s}),t}catch(t){s.status="Error",we(e,{apiName:r,data:s})}}}};return o?new Proxy(o,r):null}("WalletNotification::Mojom",xt,Pt,this.instanceBase()),this.instance=()=>this.instanceTracingProxy_,this.navigateToUrl=e=>this.instance()?.navigateToUrl?.({url:e}),this.logErrorDetails=(e,t)=>{this.instance()?.logErrorDetails?.(e,t)},this.isValidNotification=e=>this.instance()?.isValidNotification?.(e),this.isValidNotificationFromMultipleNotifications=e=>this.instance()?.isValidNotificationFromMultipleNotifications?.(e),this.getCurrentNotification=()=>this.instance()?.getCurrentNotification?.(),this.getMultipleNotifications=()=>this.instance()?.getMultipleNotifications?.(),this.setNotificationsUnviewedStatus=e=>{this.instance()?.setNotificationsUnviewedStatus(e)},this.getNotificationsUnviewedStatus=()=>this.instance()?.getNotificationsUnviewedStatus?.().then((e=>e?.status)),this.buildNotifications=e=>{this.instance()?.buildNotifications(e)},this.clearCurrentNotification=()=>{this.instance()?.clearCurrentNotification?.()},this.onApply=e=>{this.instance()?.onApply?.(e)},this.onDismiss=e=>{this.instance()?.onDismiss?.(e)},this.onSnooze=e=>{this.instance()?.onSnooze?.(e)},this.getRewardsItem=()=>this.instance()?.getRewardsItem?.(),this.writeDiagnosticLog=e=>this.instance()?.writeDiagnosticLog?.(e)}};var Lt,Ft,Wt;!function(e){e.BOOLEAN="BOOLEAN",e.NUMBER="NUMBER",e.STRING="STRING",e.URL="URL",e.LIST="LIST",e.DICTIONARY="DICTIONARY"}(Lt||(Lt={})),function(e){e.DEVICE_POLICY="DEVICE_POLICY",e.USER_POLICY="USER_POLICY",e.OWNER="OWNER",e.PRIMARY_USER="PRIMARY_USER",e.EXTENSION="EXTENSION",e.PARENT="PARENT",e.CHILD_RESTRICTION="CHILD_RESTRICTION"}(Ft||(Ft={})),function(e){e.ENFORCED="ENFORCED",e.RECOMMENDED="RECOMMENDED",e.PARENT_SUPERVISED="PARENT_SUPERVISED"}(Wt||(Wt={}));const{settingsPrivate:Bt}=window?.chrome,Gt=()=>{const e={};return function(t,i){void 0===e[t.id]&&(t.insertCSSRules(i),e[t.id]=!0)}};function Vt(e){for(var t,i=0,o=0,r=e.length;r>=4;++o,r-=4)t=1540483477*(65535&(t=255&e.charCodeAt(o)|(255&e.charCodeAt(++o))<<8|(255&e.charCodeAt(++o))<<16|(255&e.charCodeAt(++o))<<24))+(59797*(t>>>16)<<16),i=1540483477*(65535&(t^=t>>>24))+(59797*(t>>>16)<<16)^1540483477*(65535&i)+(59797*(i>>>16)<<16);switch(r){case 3:i^=(255&e.charCodeAt(o+2))<<16;case 2:i^=(255&e.charCodeAt(o+1))<<8;case 1:i=1540483477*(65535&(i^=255&e.charCodeAt(o)))+(59797*(i>>>16)<<16)}return(((i=1540483477*(65535&(i^=i>>>13))+(59797*(i>>>16)<<16))^i>>>15)>>>0).toString(36)}function Ut(e){return e.reduce((function(e,t){var i=t[0],o=t[1];return e[i]=o,e[o]=i,e}),{})}function $t(e){return"number"==typeof e}function Ht(e,t){return-1!==e.indexOf(t)}function zt(e,t,i,o){return t+(r=i,0===parseFloat(r)?r:"-"===r[0]?r.slice(1):"-"+r)+o;var r}function jt(e){return e.replace(/ +/g," ").split(" ").map((function(e){return e.trim()})).filter(Boolean).reduce((function(e,t){var i=e.list,o=e.state,r=(t.match(/\(/g)||[]).length,n=(t.match(/\)/g)||[]).length;return o.parensDepth>0?i[i.length-1]=i[i.length-1]+" "+t:i.push(t),o.parensDepth+=r-n,{list:i,state:o}}),{list:[],state:{parensDepth:0}}).list}function Zt(e){var t=jt(e);if(t.length<=3||t.length>4)return e;var i=t[0],o=t[1],r=t[2];return[i,t[3],r,o].join(" ")}var Yt={padding:function(e){var t=e.value;return $t(t)?t:Zt(t)},textShadow:function(e){return function(e){for(var t=[],i=0,o=0,r=!1;o<e.length;)r||","!==e[o]?"("===e[o]?(r=!0,o++):")"===e[o]?(r=!1,o++):o++:(t.push(e.substring(i,o).trim()),i=++o);return i!=o&&t.push(e.substring(i,o+1)),t}(e.value).map((function(e){return e.replace(/(^|\s)(-*)([.|\d]+)/,(function(e,t,i,o){return"0"===o?e:t+(""===i?"-":"")+o}))})).join(",")},borderColor:function(e){return Zt(e.value)},borderRadius:function(e){var t=e.value;if($t(t))return t;if(Ht(t,"/")){var i=t.split("/"),o=i[0],r=i[1];return Yt.borderRadius({value:o.trim()})+" / "+Yt.borderRadius({value:r.trim()})}var n=jt(t);switch(n.length){case 2:return n.reverse().join(" ");case 4:var a=n[0],s=n[1],l=n[2];return[s,a,n[3],l].join(" ");default:return t}},background:function(e){var t=e.value,i=e.valuesToConvert,o=e.isRtl,r=e.bgImgDirectionRegex,n=e.bgPosDirectionRegex;if($t(t))return t;var a=t.replace(/(url\(.*?\))|(rgba?\(.*?\))|(hsl\(.*?\))|(#[a-fA-F0-9]+)|((^| )(\D)+( |$))/g,"").trim();return t=t.replace(a,Yt.backgroundPosition({value:a,valuesToConvert:i,isRtl:o,bgPosDirectionRegex:n})),Yt.backgroundImage({value:t,valuesToConvert:i,bgImgDirectionRegex:r})},backgroundImage:function(e){var t=e.value,i=e.valuesToConvert,o=e.bgImgDirectionRegex;return Ht(t,"url(")||Ht(t,"linear-gradient(")?t.replace(o,(function(e,t,o){return e.replace(o,i[o])})):t},backgroundPosition:function(e){var t=e.value,i=e.valuesToConvert,o=e.isRtl,r=e.bgPosDirectionRegex;return t.replace(o?/^((-|\d|\.)+%)/:null,(function(e,t){return function(e){var t=e.indexOf(".");if(-1===t)e=100-parseFloat(e)+"%";else{var i=e.length-t-2;e=(e=100-parseFloat(e)).toFixed(i)+"%"}return e}(t)})).replace(r,(function(e){return i[e]}))},backgroundPositionX:function(e){var t=e.value,i=e.valuesToConvert,o=e.isRtl,r=e.bgPosDirectionRegex;return $t(t)?t:Yt.backgroundPosition({value:t,valuesToConvert:i,isRtl:o,bgPosDirectionRegex:r})},transition:function(e){var t=e.value,i=e.propertiesToConvert;return t.split(/,\s*/g).map((function(e){var t=e.split(" ");return t[0]=i[t[0]]||t[0],t.join(" ")})).join(", ")},transitionProperty:function(e){var t=e.value,i=e.propertiesToConvert;return t.split(/,\s*/g).map((function(e){return i[e]||e})).join(", ")},transform:function(e){var t=e.value,i="(?:(?:(?:\\[0-9a-f]{1,6})(?:\\r\\n|\\s)?)|\\\\[^\\r\\n\\f0-9a-f])",o="((?:-?(?:[0-9]*\\.[0-9]+|[0-9]+)(?:\\s*(?:em|ex|px|cm|mm|in|pt|pc|deg|rad|grad|ms|s|hz|khz|%)|-?(?:[_a-z]|[^\\u0020-\\u007e]|"+i+")(?:[_a-z0-9-]|[^\\u0020-\\u007e]|"+i+")*)?)|(?:inherit|auto))",r=new RegExp("(translateX\\s*\\(\\s*)"+o+"(\\s*\\))","gi"),n=new RegExp("(translate\\s*\\(\\s*)"+o+"((?:\\s*,\\s*"+o+"){0,1}\\s*\\))","gi"),a=new RegExp("(translate3d\\s*\\(\\s*)"+o+"((?:\\s*,\\s*"+o+"){0,2}\\s*\\))","gi"),s=new RegExp("(rotate[ZY]?\\s*\\(\\s*)"+o+"(\\s*\\))","gi");return t.replace(r,zt).replace(n,zt).replace(a,zt).replace(s,zt)}};Yt.objectPosition=Yt.backgroundPosition,Yt.margin=Yt.padding,Yt.borderWidth=Yt.padding,Yt.boxShadow=Yt.textShadow,Yt.webkitBoxShadow=Yt.boxShadow,Yt.mozBoxShadow=Yt.boxShadow,Yt.WebkitBoxShadow=Yt.boxShadow,Yt.MozBoxShadow=Yt.boxShadow,Yt.borderStyle=Yt.borderColor,Yt.webkitTransform=Yt.transform,Yt.mozTransform=Yt.transform,Yt.WebkitTransform=Yt.transform,Yt.MozTransform=Yt.transform,Yt.transformOrigin=Yt.backgroundPosition,Yt.webkitTransformOrigin=Yt.transformOrigin,Yt.mozTransformOrigin=Yt.transformOrigin,Yt.WebkitTransformOrigin=Yt.transformOrigin,Yt.MozTransformOrigin=Yt.transformOrigin,Yt.webkitTransition=Yt.transition,Yt.mozTransition=Yt.transition,Yt.WebkitTransition=Yt.transition,Yt.MozTransition=Yt.transition,Yt.webkitTransitionProperty=Yt.transitionProperty,Yt.mozTransitionProperty=Yt.transitionProperty,Yt.WebkitTransitionProperty=Yt.transitionProperty,Yt.MozTransitionProperty=Yt.transitionProperty,Yt["text-shadow"]=Yt.textShadow,Yt["border-color"]=Yt.borderColor,Yt["border-radius"]=Yt.borderRadius,Yt["background-image"]=Yt.backgroundImage,Yt["background-position"]=Yt.backgroundPosition,Yt["background-position-x"]=Yt.backgroundPositionX,Yt["object-position"]=Yt.objectPosition,Yt["border-width"]=Yt.padding,Yt["box-shadow"]=Yt.textShadow,Yt["-webkit-box-shadow"]=Yt.textShadow,Yt["-moz-box-shadow"]=Yt.textShadow,Yt["border-style"]=Yt.borderColor,Yt["-webkit-transform"]=Yt.transform,Yt["-moz-transform"]=Yt.transform,Yt["transform-origin"]=Yt.transformOrigin,Yt["-webkit-transform-origin"]=Yt.transformOrigin,Yt["-moz-transform-origin"]=Yt.transformOrigin,Yt["-webkit-transition"]=Yt.transition,Yt["-moz-transition"]=Yt.transition,Yt["transition-property"]=Yt.transitionProperty,Yt["-webkit-transition-property"]=Yt.transitionProperty,Yt["-moz-transition-property"]=Yt.transitionProperty;var Kt=Ut([["paddingLeft","paddingRight"],["marginLeft","marginRight"],["left","right"],["borderLeft","borderRight"],["borderLeftColor","borderRightColor"],["borderLeftStyle","borderRightStyle"],["borderLeftWidth","borderRightWidth"],["borderTopLeftRadius","borderTopRightRadius"],["borderBottomLeftRadius","borderBottomRightRadius"],["padding-left","padding-right"],["margin-left","margin-right"],["border-left","border-right"],["border-left-color","border-right-color"],["border-left-style","border-right-style"],["border-left-width","border-right-width"],["border-top-left-radius","border-top-right-radius"],["border-bottom-left-radius","border-bottom-right-radius"]]),qt=["content"],Xt=Ut([["ltr","rtl"],["left","right"],["w-resize","e-resize"],["sw-resize","se-resize"],["nw-resize","ne-resize"]]),Qt=new RegExp("(^|\\W|_)((ltr)|(rtl)|(left)|(right))(\\W|_|$)","g"),Jt=new RegExp("(left)|(right)");function ei(e){return Object.keys(e).reduce((function(t,i){var o=e[i];if("string"==typeof o&&(o=o.trim()),Ht(qt,i))return t[i]=o,t;var r=ti(i,o),n=r.key,a=r.value;return t[n]=a,t}),Array.isArray(e)?[]:{})}function ti(e,t){var i,o=/\/\*\s?@noflip\s?\*\//.test(t),r=o?e:Kt[i=e]||i,n=o?t:function(e,t){if(!function(e){return!("boolean"==typeof e||function(e){return null==e}(e))}(t))return t;if((i=t)&&"object"==typeof i)return ei(t);var i,o,r=$t(t),n=function(e){return"function"==typeof e}(t),a=r||n?t:t.replace(/ !important.*?$/,""),s=!r&&a.length!==t.length,l=Yt[e];return o=l?l({value:a,valuesToConvert:Xt,propertiesToConvert:Kt,isRtl:!0,bgImgDirectionRegex:Qt,bgPosDirectionRegex:Jt}):Xt[a]||a,s?o+" !important":o}(r,t);return{key:r,value:n}}const ii="undefined"==typeof window?r.g:window,oi="@griffel/";function ri(e,t){return ii[Symbol.for(oi+e)]||(ii[Symbol.for(oi+e)]=t),ii[Symbol.for(oi+e)]}const ni=ri("DEFINITION_LOOKUP_TABLE",{}),ai="___",si=ai.length+7,li={all:1,animation:1,background:1,backgroundPosition:1,border:1,borderBlock:1,borderBlockEnd:1,borderBlockStart:1,borderBottom:1,borderColor:1,borderImage:1,borderInline:1,borderInlineEnd:1,borderInlineStart:1,borderLeft:1,borderRadius:1,borderRight:1,borderStyle:1,borderTop:1,borderWidth:1,caret:1,columns:1,columnRule:1,containIntrinsicSize:1,container:1,flex:1,flexFlow:1,font:1,gap:1,grid:1,gridArea:1,gridColumn:1,gridRow:1,gridTemplate:1,inset:1,insetBlock:1,insetInline:1,lineClamp:1,listStyle:1,margin:1,marginBlock:1,marginInline:1,mask:1,maskBorder:1,motion:1,offset:1,outline:1,overflow:1,overscrollBehavior:1,padding:1,paddingBlock:1,paddingInline:1,placeItems:1,placeContent:1,placeSelf:1,scrollMargin:1,scrollMarginBlock:1,scrollMarginInline:1,scrollPadding:1,scrollPaddingBlock:1,scrollPaddingInline:1,scrollSnapMargin:1,scrollTimeline:1,textDecoration:1,textEmphasis:1,transition:1},ci=/[A-Z]/g,di=/^ms-/,ui={};function pi(e){return"-"+e.toLowerCase()}function hi(e){if(Object.prototype.hasOwnProperty.call(ui,e))return ui[e];if("--"===e.substr(0,2))return e;const t=e.replace(ci,pi);return ui[e]=di.test(t)?"-"+t:t}function fi(e){return"&"===e.charAt(0)?e.slice(1):e}var gi="-moz-",Ei="-webkit-",Ti="comm",mi="rule",Ni="decl",_i="@layer",Oi=Math.abs,bi=String.fromCharCode,Ii=Object.assign;function Ai(e){return e.trim()}function Ci(e,t){return(e=t.exec(e))?e[0]:e}function vi(e,t,i){return e.replace(t,i)}function Ri(e,t){return e.indexOf(t)}function yi(e,t){return 0|e.charCodeAt(t)}function Si(e,t,i){return e.slice(t,i)}function wi(e){return e.length}function ki(e){return e.length}function Mi(e,t){return t.push(e),e}function xi(e,t){for(var i="",o=0;o<e.length;o++)i+=t(e[o],o,e,t)||"";return i}function Pi(e,t,i,o){switch(e.type){case _i:if(e.children.length)break;case"@import":case Ni:return e.return=e.return||e.value;case Ti:return"";case"@keyframes":return e.return=e.value+"{"+xi(e.children,o)+"}";case mi:if(!wi(e.value=e.props.join(",")))return""}return wi(i=xi(e.children,o))?e.return=e.value+"{"+i+"}":""}var Di=1,Li=1,Fi=0,Wi=0,Bi=0,Gi="";function Vi(e,t,i,o,r,n,a,s){return{value:e,root:t,parent:i,type:o,props:r,children:n,line:Di,column:Li,length:a,return:"",siblings:s}}function Ui(e,t){return Ii(Vi("",null,null,"",null,null,0,e.siblings),e,{length:-e.length},t)}function $i(){return Bi=Wi>0?yi(Gi,--Wi):0,Li--,10===Bi&&(Li=1,Di--),Bi}function Hi(){return Bi=Wi<Fi?yi(Gi,Wi++):0,Li++,10===Bi&&(Li=1,Di++),Bi}function zi(){return yi(Gi,Wi)}function ji(){return Wi}function Zi(e,t){return Si(Gi,e,t)}function Yi(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function Ki(e){return Di=Li=1,Fi=wi(Gi=e),Wi=0,[]}function qi(e){return Gi="",e}function Xi(e){return Ai(Zi(Wi-1,eo(91===e?e+2:40===e?e+1:e)))}function Qi(e){for(;(Bi=zi())&&Bi<33;)Hi();return Yi(e)>2||Yi(Bi)>3?"":" "}function Ji(e,t){for(;--t&&Hi()&&!(Bi<48||Bi>102||Bi>57&&Bi<65||Bi>70&&Bi<97););return Zi(e,ji()+(t<6&&32==zi()&&32==Hi()))}function eo(e){for(;Hi();)switch(Bi){case e:return Wi;case 34:case 39:34!==e&&39!==e&&eo(Bi);break;case 40:41===e&&eo(e);break;case 92:Hi()}return Wi}function to(e,t){for(;Hi()&&e+Bi!==57&&(e+Bi!==84||47!==zi()););return"/*"+Zi(t,Wi-1)+"*"+bi(47===e?e:Hi())}function io(e){for(;!Yi(zi());)Hi();return Zi(e,Wi)}function oo(e){return qi(ro("",null,null,null,[""],e=Ki(e),0,[0],e))}function ro(e,t,i,o,r,n,a,s,l){for(var c=0,d=0,u=a,p=0,h=0,f=0,g=1,E=1,T=1,m=0,N="",_=r,O=n,b=o,I=N;E;)switch(f=m,m=Hi()){case 40:if(108!=f&&58==yi(I,u-1)){-1!=Ri(I+=vi(Xi(m),"&","&\f"),"&\f")&&(T=-1);break}case 34:case 39:case 91:I+=Xi(m);break;case 9:case 10:case 13:case 32:I+=Qi(f);break;case 92:I+=Ji(ji()-1,7);continue;case 47:switch(zi()){case 42:case 47:Mi(ao(to(Hi(),ji()),t,i,l),l);break;default:I+="/"}break;case 123*g:s[c++]=wi(I)*T;case 125*g:case 59:case 0:switch(m){case 0:case 125:E=0;case 59+d:-1==T&&(I=vi(I,/\f/g,"")),h>0&&wi(I)-u&&Mi(h>32?so(I+";",o,i,u-1,l):so(vi(I," ","")+";",o,i,u-2,l),l);break;case 59:I+=";";default:if(Mi(b=no(I,t,i,c,d,r,s,N,_=[],O=[],u,n),n),123===m)if(0===d)ro(I,t,b,b,_,n,u,s,O);else switch(99===p&&110===yi(I,3)?100:p){case 100:case 108:case 109:case 115:ro(e,b,b,o&&Mi(no(e,b,b,0,0,r,s,N,r,_=[],u,O),O),r,O,u,s,o?_:O);break;default:ro(I,b,b,b,[""],O,0,s,O)}}c=d=h=0,g=T=1,N=I="",u=a;break;case 58:u=1+wi(I),h=f;default:if(g<1)if(123==m)--g;else if(125==m&&0==g++&&125==$i())continue;switch(I+=bi(m),m*g){case 38:T=d>0?1:(I+="\f",-1);break;case 44:s[c++]=(wi(I)-1)*T,T=1;break;case 64:45===zi()&&(I+=Xi(Hi())),p=zi(),d=u=wi(N=I+=io(ji())),m++;break;case 45:45===f&&2==wi(I)&&(g=0)}}return n}function no(e,t,i,o,r,n,a,s,l,c,d,u){for(var p=r-1,h=0===r?n:[""],f=ki(h),g=0,E=0,T=0;g<o;++g)for(var m=0,N=Si(e,p+1,p=Oi(E=a[g])),_=e;m<f;++m)(_=Ai(E>0?h[m]+" "+N:vi(N,/&\f/g,h[m])))&&(l[T++]=_);return Vi(e,t,i,0===r?mi:s,l,c,d,u)}function ao(e,t,i,o){return Vi(e,t,i,Ti,bi(Bi),Si(e,2,-2),0,o)}function so(e,t,i,o,r){return Vi(e,t,i,Ni,Si(e,0,o),Si(e,o+1,-1),o,r)}function lo(e){var t=ki(e);return function(i,o,r,n){for(var a="",s=0;s<t;s++)a+=e[s](i,o,r,n)||"";return a}}function co(e){return function(t){t.root||(t=t.return)&&e(t)}}const uo=e=>{if(e.type===mi){if("string"==typeof e.props)return;e.props=e.props.map((e=>-1===e.indexOf(":global(")?e:function(e){return qi(function(e){for(;Hi();)switch(Yi(Bi)){case 0:Mi(io(Wi-1),e);break;case 2:Mi(Xi(Bi),e);break;default:Mi(bi(Bi),e)}return e}(Ki(e)))}(e).reduce(((e,t,i,o)=>{if(""===t)return e;if(":"===t&&"global"===o[i+1]){const t=o[i+2].slice(1,-1)+" ";return e.unshift(t),o[i+1]="",o[i+2]="",e}return e.push(t),e}),[]).join("")))}};function po(e,t,i){switch(function(e,t){return 45^yi(e,0)?(((t<<2^yi(e,0))<<2^yi(e,1))<<2^yi(e,2))<<2^yi(e,3):0}(e,t)){case 5103:return"-webkit-print-"+e+e;case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:return Ei+e+e;case 4789:return gi+e+e;case 5349:case 4246:case 6968:return Ei+e+gi+e+"-ms-"+e+e;case 6187:if(!Ci(e,/grab/))return vi(vi(vi(e,/(zoom-|grab)/,"-webkit-$1"),/(image-set)/,"-webkit-$1"),e,"")+e;case 5495:case 3959:return vi(e,/(image-set\([^]*)/,"-webkit-$1$`$1");case 4095:case 3583:case 4068:case 2532:return vi(e,/(.+)-inline(.+)/,"-webkit-$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(wi(e)-1-t>6)switch(yi(e,t+1)){case 102:if(108===yi(e,t+3))return vi(e,/(.+:)(.+)-([^]+)/,"$1-webkit-$2-$3$1-moz-"+(108==yi(e,t+3)?"$3":"$2-$3"))+e;case 115:return~Ri(e,"stretch")?po(vi(e,"stretch","fill-available"),t)+e:e}}return e}function ho(e,t,i,o){if(e.length>-1&&!e.return)switch(e.type){case Ni:return void(e.return=po(e.value,e.length));case mi:if(e.length)return function(e,t){return e.map(t).join("")}(e.props,(function(t){switch(Ci(t,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return xi([Ui(e,{props:[vi(t,/:(read-\w+)/,":-moz-$1")]})],o);case"::placeholder":return xi([Ui(e,{props:[vi(t,/:(plac\w+)/,":-webkit-input-$1")]}),Ui(e,{props:[vi(t,/:(plac\w+)/,":-moz-$1")]}),Ui(e,{props:[vi(t,/:(plac\w+)/,"-ms-input-$1")]})],o)}return""}))}}const fo=e=>{(function(e){switch(e.type){case"@container":case"@media":case"@supports":case _i:return!0}return!1})(e)&&Array.isArray(e.children)&&e.children.sort(((e,t)=>e.props[0]>t.props[0]?1:-1))};const go=/,( *[^ &])/g;function Eo(e,t,i){let o=t;return i.length>0&&(o=i.reduceRight(((e,t)=>{return`${i=t,"&"+fi(i.replace(go,",&$1"))} { ${e} }`;var i}),t)),`${e}{${o}}`}function To(e){const{className:t,media:i,layer:o,selectors:r,support:n,property:a,rtlClassName:s,rtlProperty:l,rtlValue:c,value:d,container:u}=e;let p=Eo(`.${t}`,Array.isArray(d)?`${d.map((e=>`${hi(a)}: ${e}`)).join(";")};`:`${hi(a)}: ${d};`,r);return l&&s&&(p+=Eo(`.${s}`,Array.isArray(c)?`${c.map((e=>`${hi(l)}: ${e}`)).join(";")};`:`${hi(l)}: ${c};`,r)),i&&(p=`@media ${i} { ${p} }`),o&&(p=`@layer ${o} { ${p} }`),n&&(p=`@supports ${n} { ${p} }`),u&&(p=`@container ${u} { ${p} }`),function(e,t){const i=[];return xi(oo(e),lo([uo,fo,ho,Pi,co((e=>i.push(e)))])),i}(p)}function mo(e){let t="";for(const i in e){const o=e[i];"string"!=typeof o&&"number"!=typeof o||(t+=hi(i)+":"+o+";")}return t}function No(e){let t="";for(const i in e)t+=`${i}{${mo(e[i])}}`;return t}function _o(e,t){const i=[];return xi(oo(`@keyframes ${e} {${t}}`),lo([Pi,ho,co((e=>i.push(e)))])),i}function Oo(e,t){return 0===e.length?t:`${e} and ${t}`}function bo(e){return"@media"===e.substr(0,6)}function Io(e){return"@layer"===e.substr(0,6)}const Ao=/^(:|\[|>|&)/;function Co(e){return Ao.test(e)}function vo(e){return"@supports"===e.substr(0,9)}function Ro(e){return"@container"===e.substring(0,10)}const yo={"us-w":"w","us-v":"i",nk:"l",si:"v",cu:"f",ve:"h",ti:"a"};function So(e,t,i,o,r){if(i)return"m";if(t||o)return"t";if(r)return"c";if(e.length>0){const t=e[0].trim();if(58===t.charCodeAt(0))return yo[t.slice(4,8)]||yo[t.slice(3,5)]||"d"}return"d"}function wo({container:e,media:t,layer:i,property:o,selector:r,support:n,value:a}){return"f"+Vt(r+e+t+i+n+o+a.trim())}function ko(e,t,i,o,r){const n=Vt(e+t+i+o+r),a=n.charCodeAt(0);return a>=48&&a<=57?String.fromCharCode(a+17)+n.slice(1):n}function Mo(e){return e.replace(/>\s+/g,">")}function xo(e,t){const i=JSON.stringify(t,null,2),o=["@griffel/react: A rule was not resolved to CSS properly. Please check your `makeStyles` or `makeResetStyles` calls for following:"," ".repeat(2)+"makeStyles({"," ".repeat(4)+"[slot]: {"," ".repeat(6)+`"${e}": ${i.split("\n").map(((e,t)=>" ".repeat(0===t?0:6)+e)).join("\n")}`," ".repeat(4)+"}"," ".repeat(2)+"})",""];-1===e.indexOf("&")?(o.push("It looks that you're are using a nested selector, but it is missing an ampersand placeholder where the generated class name should be injected."),o.push(`Try to update a property to include it i.e "${e}" => "&${e}".`)):(o.push(""),o.push("If it's not obvious what triggers a problem, please report an issue at https://github.com/microsoft/griffel/issues")),o.join("\n")}function Po(e,t){[`@griffel/react: You are using unsupported shorthand CSS property "${e}". Please check your "makeStyles" calls, there *should not* be following:`," ".repeat(2)+"makeStyles({"," ".repeat(4)+`[slot]: { ${e}: "${t}" }`," ".repeat(2)+"})","","Learn why CSS shorthands are not supported: https://aka.ms/griffel-css-shorthands"].join("\n")}var Do=Object.defineProperty,Lo=Object.getOwnPropertySymbols,Fo=Object.prototype.hasOwnProperty,Wo=Object.prototype.propertyIsEnumerable,Bo=(e,t,i)=>t in e?Do(e,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[t]=i,Go=(e,t)=>{for(var i in t||(t={}))Fo.call(t,i)&&Bo(e,i,t[i]);if(Lo)for(var i of Lo(t))Wo.call(t,i)&&Bo(e,i,t[i]);return e};function Vo(e,t,i,o){e[t]=o?[i,o]:i}function Uo(e,t){return t?[e,t]:e}function $o(e,t,i,o,r){var n;let a;"m"===t&&r&&(a={m:r}),null!==(n=e[t])&&void 0!==n||(e[t]=[]),i&&e[t].push(Uo(i,a)),o&&e[t].push(Uo(o,a))}function Ho(e,t=[],i="",o="",r="",n="",a={},s={},l){for(const d in e){if(li.hasOwnProperty(d)){Po(d,e[d]);continue}const u=e[d];if(null!=u)if("string"==typeof u||"number"==typeof u){const e=Mo(t.join("")),c=ko(e,n,i,r,d),p=wo({container:n,media:i,layer:o,value:u.toString(),support:r,selector:e,property:d}),h=l&&{key:d,value:l}||ti(d,u),f=h.key!==d||h.value!==u,g=f?wo({container:n,value:h.value.toString(),property:h.key,selector:e,media:i,layer:o,support:r}):void 0,E=f?{rtlClassName:g,rtlProperty:h.key,rtlValue:h.value}:void 0,T=So(t,o,i,r,n),[m,N]=To(Go({className:p,media:i,layer:o,selectors:t,property:d,support:r,container:n,value:u},E));Vo(a,c,p,g),$o(s,T,m,N,i)}else if("animationName"===d){const e=Array.isArray(u)?u:[u],l=[],c=[];for(const t of e){const e=No(t),o=No(ei(t)),r="f"+Vt(e);let n;const a=_o(r,e);let d=[];e===o?n=r:(n="f"+Vt(o),d=_o(n,o));for(let e=0;e<a.length;e++)$o(s,"k",a[e],d[e],i);l.push(r),c.push(n)}Ho({animationName:l.join(", ")},t,i,o,r,n,a,s,c.join(", "))}else if(Array.isArray(u)){if(0===u.length)continue;const e=Mo(t.join("")),l=ko(e,n,i,r,d),c=wo({container:n,media:i,layer:o,value:u.map((e=>(null!=e?e:"").toString())).join(";"),support:r,selector:e,property:d}),p=u.map((e=>ti(d,e)));if(p.some((e=>e.key!==p[0].key)))continue;const h=p[0].key!==d||p.some(((e,t)=>e.value!==u[t])),f=h?wo({container:n,value:p.map((e=>{var t;return(null!==(t=null==e?void 0:e.value)&&void 0!==t?t:"").toString()})).join(";"),property:p[0].key,selector:e,layer:o,media:i,support:r}):void 0,g=h?{rtlClassName:f,rtlProperty:p[0].key,rtlValue:p.map((e=>e.value))}:void 0,E=So(t,o,i,r,n),[T,m]=To(Go({className:c,media:i,layer:o,selectors:t,property:d,support:r,container:n,value:u},g));Vo(a,l,c,f),$o(s,E,T,m,i)}else if(null!=(c=u)&&"object"==typeof c&&!1===Array.isArray(c))if(Co(d))Ho(u,t.concat(fi(d)),i,o,r,n,a,s);else if(bo(d)){const e=Oo(i,d.slice(6).trim());Ho(u,t,e,o,r,n,a,s)}else if(Io(d)){const e=(o?`${o}.`:"")+d.slice(6).trim();Ho(u,t,i,e,r,n,a,s)}else if(vo(d)){const e=Oo(r,d.slice(9).trim());Ho(u,t,i,o,e,n,a,s)}else if(Ro(d)){const e=d.slice(10).trim();Ho(u,t,i,o,r,e,a,s)}else xo(d,u)}var c;return[a,s]}function zo(e,t,i=[]){return ai+function(e){const t=e.length;if(7===t)return e;for(let i=t;i<7;i++)e+="0";return e}(Vt(e+t))}function jo(e,t){let i="";for(const o in e){const r=e[o];if(r){const e=Array.isArray(r);i+="rtl"===t?(e?r[1]:r)+" ":(e?r[0]:r)+" "}}return i.slice(0,-1)}function Zo(e,t){const i={};for(const o in e){const r=jo(e[o],t);if(""===r){i[o]="";continue}const n=zo(r,t),a=n+" "+r;ni[n]=[e[o],t],i[o]=a}return i}const Yo=vt.useInsertionEffect?vt.useInsertionEffect:void 0,Ko=()=>{const e={};return function(t,i){Yo&&"undefined"!=typeof window&&window.document&&window.document.createElement?Yo((()=>{t.insertCSSRules(i)}),[t,i]):void 0===e[t.id]&&(t.insertCSSRules(i),e[t.id]=!0)}};var qo=Object.defineProperty,Xo=Object.getOwnPropertySymbols,Qo=Object.prototype.hasOwnProperty,Jo=Object.prototype.propertyIsEnumerable,er=(e,t,i)=>t in e?qo(e,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[t]=i,tr=(e,t)=>{for(var i in t||(t={}))Qo.call(t,i)&&er(e,i,t[i]);if(Xo)for(var i of Xo(t))Jo.call(t,i)&&er(e,i,t[i]);return e};const ir=["r","d","l","v","w","f","i","h","a","s","k","t","m","c"].reduce(((e,t,i)=>(e[t]=i,e)),{});function or(e,t,i,o,r={}){const n="m"===e,a=n?e+r.m:e;if(!o.stylesheets[a]){const s=t&&t.createElement("style"),l=function(e,t,i){const o=[];if(i["data-make-styles-bucket"]=t,e)for(const t in i)e.setAttribute(t,i[t]);return{elementAttributes:i,insertRule:function(t){return(null==e?void 0:e.sheet)?e.sheet.insertRule(t,e.sheet.cssRules.length):o.push(t)},element:e,bucketName:t,cssRules:()=>(null==e?void 0:e.sheet)?Array.from(e.sheet.cssRules).map((e=>e.cssText)):o}}(s,e,tr(tr({},o.styleElementAttributes),n&&{media:r.m}));o.stylesheets[a]=l,t&&s&&t.head.insertBefore(s,function(e,t,i,o,r){const n=ir[i];let a=e=>n-ir[e.getAttribute("data-make-styles-bucket")],s=e.head.querySelectorAll("[data-make-styles-bucket]");if("m"===i&&r){const t=e.head.querySelectorAll(`[data-make-styles-bucket="${i}"]`);t.length&&(s=t,a=e=>o.compareMediaQueries(r.m,e.media))}const l=s.length;let c=l-1;for(;c>=0;){const e=s.item(c);if(a(e)>0)return e.nextSibling;c--}return l>0?s.item(0):t?t.nextSibling:null}(t,i,e,o,r))}return o.stylesheets[a]}function rr(e,t){try{e.insertRule(t)}catch(e){}}let nr=0;const ar=(e,t)=>e<t?-1:e>t?1:0;function sr(e=("undefined"==typeof document?void 0:document),t={}){const{unstable_filterCSSRule:i,insertionPoint:o,styleElementAttributes:r,compareMediaQueries:n=ar}=t,a={insertionCache:{},stylesheets:{},styleElementAttributes:Object.freeze(r),compareMediaQueries:n,id:"d"+nr++,insertCSSRules(t){for(const n in t){const s=t[n];for(let t=0,l=s.length;t<l;t++){const[l,c]=(r=s[t],Array.isArray(r)?r:[r]),d=or(n,e,o||null,a,c);a.insertionCache[l]||(a.insertionCache[l]=n,i?i(l)&&rr(d,l):rr(d,l))}}var r}};return a}const lr=vt.createContext(sr());function cr(){return vt.useContext(lr)}const dr=vt.createContext("ltr");function ur(){return vt.useContext(dr)}function pr(e){const t=function(e,t=Gt){const i=t();let o=null,r=null,n=null,a=null;return function(t){const{dir:s,renderer:l}=t;null===o&&([o,r]=function(e){const t={},i={};for(const o in e){const r=e[o],[n,a]=Ho(r);t[o]=n,Object.keys(a).forEach((e=>{i[e]=(i[e]||[]).concat(a[e])}))}return[t,i]}(e));const c="ltr"===s;return c?null===n&&(n=Zo(o,s)):null===a&&(a=Zo(o,s)),i(l,r),c?n:a}}(e,Ko);return function(){const e=ur(),i=cr();return t({dir:e,renderer:i})}}var hr;(hr||(hr={})).isTokenizationEnrollRewardsEnabled="isTokenizationEnrollRewardsEnabled";const fr={showContent:!0,setAppState:()=>{},appName:ct.MiniWallet},gr=(vt.createContext(fr),pr({container:{}}),["Top","Right","Bottom","Left"]);function Er(e,t,...i){const[o,r=o,n=o,a=r]=i,s=[o,r,n,a],l={};for(let i=0;i<s.length;i+=1)(s[i]||0===s[i])&&(l[e+gr[i]+t]=s[i]);return l}function Tr(...e){return Er("border","Width",...e)}function mr(...e){return Er("border","Style",...e)}function Nr(...e){return Er("border","Color",...e)}const _r=["none","hidden","dotted","dashed","solid","double","groove","ridge","inset","outset"];function Or(e){return _r.includes(e)}var br=Object.defineProperty,Ir=Object.getOwnPropertySymbols,Ar=Object.prototype.hasOwnProperty,Cr=Object.prototype.propertyIsEnumerable,vr=(e,t,i)=>t in e?br(e,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[t]=i,Rr=(e,t)=>{for(var i in t||(t={}))Ar.call(t,i)&&vr(e,i,t[i]);if(Ir)for(var i of Ir(t))Cr.call(t,i)&&vr(e,i,t[i]);return e},yr=Object.defineProperty,Sr=Object.getOwnPropertySymbols,wr=Object.prototype.hasOwnProperty,kr=Object.prototype.propertyIsEnumerable,Mr=(e,t,i)=>t in e?yr(e,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[t]=i,xr=(e,t)=>{for(var i in t||(t={}))wr.call(t,i)&&Mr(e,i,t[i]);if(Sr)for(var i of Sr(t))kr.call(t,i)&&Mr(e,i,t[i]);return e},Pr=Object.defineProperty,Dr=Object.getOwnPropertySymbols,Lr=Object.prototype.hasOwnProperty,Fr=Object.prototype.propertyIsEnumerable,Wr=(e,t,i)=>t in e?Pr(e,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[t]=i,Br=(e,t)=>{for(var i in t||(t={}))Lr.call(t,i)&&Wr(e,i,t[i]);if(Dr)for(var i of Dr(t))Fr.call(t,i)&&Wr(e,i,t[i]);return e},Gr=Object.defineProperty,Vr=Object.getOwnPropertySymbols,Ur=Object.prototype.hasOwnProperty,$r=Object.prototype.propertyIsEnumerable,Hr=(e,t,i)=>t in e?Gr(e,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[t]=i,zr=(e,t)=>{for(var i in t||(t={}))Ur.call(t,i)&&Hr(e,i,t[i]);if(Vr)for(var i of Vr(t))$r.call(t,i)&&Hr(e,i,t[i]);return e},jr=Object.defineProperty,Zr=Object.getOwnPropertySymbols,Yr=Object.prototype.hasOwnProperty,Kr=Object.prototype.propertyIsEnumerable,qr=(e,t,i)=>t in e?jr(e,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[t]=i,Xr=(e,t)=>{for(var i in t||(t={}))Yr.call(t,i)&&qr(e,i,t[i]);if(Zr)for(var i of Zr(t))Kr.call(t,i)&&qr(e,i,t[i]);return e};const Qr=e=>"number"==typeof e&&!Number.isNaN(e),Jr=e=>"auto"===e,en=["content","fit-content","max-content","min-content"],tn=e=>en.some((t=>e===t))||(e=>"string"==typeof e&&/(\d+(\w+|%))/.test(e))(e),on=/var\(.*\)/gi,rn=/^[a-zA-Z0-9\-_\\#;]+$/,nn=/^-moz-initial$|^auto$|^initial$|^inherit$|^revert$|^unset$|^span \d+$|\d.*/;function an(e){return void 0!==e&&"string"==typeof e&&rn.test(e)&&!nn.test(e)}var sn=Object.defineProperty,ln=Object.getOwnPropertySymbols,cn=Object.prototype.hasOwnProperty,dn=Object.prototype.propertyIsEnumerable,un=(e,t,i)=>t in e?sn(e,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[t]=i,pn=(e,t)=>{for(var i in t||(t={}))cn.call(t,i)&&un(e,i,t[i]);if(ln)for(var i of ln(t))dn.call(t,i)&&un(e,i,t[i]);return e};const hn=["-moz-initial","inherit","initial","revert","unset"];var fn=Object.defineProperty,gn=Object.getOwnPropertySymbols,En=Object.prototype.hasOwnProperty,Tn=Object.prototype.propertyIsEnumerable,mn=(e,t,i)=>t in e?fn(e,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[t]=i,Nn=(e,t)=>{for(var i in t||(t={}))En.call(t,i)&&mn(e,i,t[i]);if(gn)for(var i of gn(t))Tn.call(t,i)&&mn(e,i,t[i]);return e};const _n=["dashed","dotted","double","solid","wavy"],On={border:function(...e){return Or(e[0])?Rr(Rr(Rr({},mr(e[0])),e[1]&&Tr(e[1])),e[2]&&Nr(e[2])):Rr(Rr(Rr({},Tr(e[0])),e[1]&&mr(e[1])),e[2]&&Nr(e[2]))},borderLeft:function(...e){return Or(e[0])?xr(xr({borderLeftStyle:e[0]},e[1]&&{borderLeftWidth:e[1]}),e[2]&&{borderLeftColor:e[2]}):xr(xr({borderLeftWidth:e[0]},e[1]&&{borderLeftStyle:e[1]}),e[2]&&{borderLeftColor:e[2]})},borderBottom:function(...e){return Or(e[0])?Br(Br({borderBottomStyle:e[0]},e[1]&&{borderBottomWidth:e[1]}),e[2]&&{borderBottomColor:e[2]}):Br(Br({borderBottomWidth:e[0]},e[1]&&{borderBottomStyle:e[1]}),e[2]&&{borderBottomColor:e[2]})},borderRight:function(...e){return Or(e[0])?zr(zr({borderRightStyle:e[0]},e[1]&&{borderRightWidth:e[1]}),e[2]&&{borderRightColor:e[2]}):zr(zr({borderRightWidth:e[0]},e[1]&&{borderRightStyle:e[1]}),e[2]&&{borderRightColor:e[2]})},borderTop:function(...e){return Or(e[0])?Xr(Xr({borderTopStyle:e[0]},e[1]&&{borderTopWidth:e[1]}),e[2]&&{borderTopColor:e[2]}):Xr(Xr({borderTopWidth:e[0]},e[1]&&{borderTopStyle:e[1]}),e[2]&&{borderTopColor:e[2]})},borderColor:Nr,borderStyle:mr,borderRadius:function(e,t=e,i=e,o=t){return{borderBottomRightRadius:i,borderBottomLeftRadius:o,borderTopRightRadius:t,borderTopLeftRadius:e}},borderWidth:Tr,flex:function(...e){const t=1===e.length,i=2===e.length,o=3===e.length;if(t){const[t]=e;if("initial"===t)return{flexGrow:0,flexShrink:1,flexBasis:"auto"};if(Jr(t))return{flexGrow:1,flexShrink:1,flexBasis:"auto"};if("none"===t)return{flexGrow:0,flexShrink:0,flexBasis:"auto"};if(Qr(t))return{flexGrow:t,flexShrink:1,flexBasis:0};if(tn(t))return{flexGrow:1,flexShrink:1,flexBasis:t}}if(i){const[t,i]=e;if(Qr(i))return{flexGrow:t,flexShrink:i,flexBasis:0};if(tn(i))return{flexGrow:t,flexShrink:1,flexBasis:i}}if(o){const[t,i,o]=e;if(Qr(t)&&Qr(i)&&(Jr(o)||tn(o)))return{flexGrow:t,flexShrink:i,flexBasis:o}}return{}},gap:function(e,t=e){return{columnGap:e,rowGap:t}},gridArea:function(...e){if(e.some((e=>!function(e){return void 0===e||"number"==typeof e||"string"==typeof e&&!on.test(e)}(e))))return{};const t=void 0!==e[0]?e[0]:"auto",i=void 0!==e[1]?e[1]:an(t)?t:"auto";return{gridRowStart:t,gridColumnStart:i,gridRowEnd:void 0!==e[2]?e[2]:an(t)?t:"auto",gridColumnEnd:void 0!==e[3]?e[3]:an(i)?i:"auto"}},margin:function(...e){return Er("margin","",...e)},marginBlock:function(e,t=e){return{marginBlockStart:e,marginBlockEnd:t}},marginInline:function(e,t=e){return{marginInlineStart:e,marginInlineEnd:t}},padding:function(...e){return Er("padding","",...e)},paddingBlock:function(e,t=e){return{paddingBlockStart:e,paddingBlockEnd:t}},paddingInline:function(e,t=e){return{paddingInlineStart:e,paddingInlineEnd:t}},overflow:function(e,t=e){return{overflowX:e,overflowY:t}},inset:function(...e){const[t,i=t,o=t,r=i]=e;return{top:t,right:i,bottom:o,left:r}},outline:function(e,t,i){return pn(pn({outlineWidth:e},t&&{outlineStyle:t}),i&&{outlineColor:i})},transition:function(...e){return function(e){return 1===e.length&&hn.includes(e[0])}(e)?{transitionDelay:e[0],transitionDuration:e[0],transitionProperty:e[0],transitionTimingFunction:e[0]}:(t=e,1===t.length&&Array.isArray(t[0])?t[0]:[t]).reduce(((e,[t,i="0s",o="0s",r="ease"],n)=>(0===n?(e.transitionProperty=t,e.transitionDuration=i,e.transitionDelay=o,e.transitionTimingFunction=r):(e.transitionProperty+=`, ${t}`,e.transitionDuration+=`, ${i}`,e.transitionDelay+=`, ${o}`,e.transitionTimingFunction+=`, ${r}`),e)),{});var t},textDecoration:function(e,...t){if(0===t.length)return function(e){return _n.includes(e)}(e)?{textDecorationStyle:e}:{textDecorationLine:e};const[i,o,r]=t;return Nn(Nn(Nn({textDecorationLine:e},i&&{textDecorationStyle:i}),o&&{textDecorationColor:o}),r&&{textDecorationThickness:r})}},bn={darkGreen2DarkBlue:"linear-gradient(113.02deg, rgba(17, 70, 47, 0.534) 0%, rgba(32, 74, 222, 0.6) 87.93%),                        linear-gradient(111.95deg, rgba(93, 231, 189, 0.95) -23.05%, rgba(6, 44, 120, 0.95) 118.51%)",lightGreen2LightBlue:"linear-gradient(75.54deg, #A7FBC3 9.26%, #AEE4FF 83.12%)",xLightGreeen2xLightBlue:"linear-gradient(79.5deg, rgba(235, 255, 242, 0.6) 7.26%, rgba(227, 246, 255, 0.6) 94.85%)",darkModexLightGreen2xLightBlue:"linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)), linear-gradient(79.89deg, rgba(12, 243, 91, 0.1) 13.08%, rgba(26, 144, 255, 0.1) 87.46%)",green2Blue:"linear-gradient(112.65deg, #0CF35B -3.38%, #0072DC 105.35%)",orange2Yellow:"linear-gradient(236.16deg, #FE7002 6.29%, #FF6388 77.23%, #FFD98E 115.25%, #FFD98E 115.25%)",get darkRed2Yellow(){return`linear-gradient(97.07deg, rgba(114, 18, 35, 0.4) 6.18%, rgba(71, 10, 10, 0.368) 98.82%), ${this.orange2Yellow}`},blue2Green:"linear-gradient(180deg, rgba(3, 163, 214, 0.5) 0%, rgba(12, 51, 28, 0.43) 116.67%),                linear-gradient(173.61deg, #0072DC 1.09%, #0CF35B 103.87%)",darkModeDarkGreen2DarkBlue:"linear-gradient(86.44deg, #00C16E 5.86%, #07C5D9 50.32%, #46A6FF 92.97%);",white2red:"linear-gradient(236.16deg, #FE7002 6.29%, #FF6388 77.23%, #FFD98E 115.25%, #FFD98E 115.25%)",lightWhite2red:"linear-gradient(236.16deg, rgba(254, 112, 2, 0.2) 6.29%, rgba(255, 99, 136, 0.2) 77.23%, rgba(255, 217, 142, 0.2) 115.25%, rgba(255, 217, 142, 0.2) 115.25%)",xLightBlue2LightBlue:"linear-gradient(106.79deg, #EFF8FF 7.25%, #E1F1FF 92.37%)",purple2green:"linear-gradient(89.79deg, #6517CB 0.19%, #3251C4 47.92%, #499FB0 99.81%)"},In="#1E4061",An=(bn.darkGreen2DarkBlue,bn.orange2Yellow,bn.darkModeDarkGreen2DarkBlue,bn.lightGreen2LightBlue,bn.green2Blue,bn.darkRed2Yellow,bn.white2red,bn.lightWhite2red,bn.green2Blue,bn.lightGreen2LightBlue,bn.blue2Green,bn.green2Blue,bn.green2Blue,bn.green2Blue,bn.green2Blue,bn.darkRed2Yellow,bn.xLightBlue2LightBlue,bn.orange2Yellow,bn.xLightGreeen2xLightBlue,bn.darkModexLightGreen2xLightBlue,bn.purple2green,{whiteBackground:"#FFFFFF",darkBackground:"#4A4A4A",darkMiniWalletBackground:"#303030",darkNotificationBackground:"#303030",blackBackground:"#000000",listItemBackground:"#F7F7F7",darkModeListItemBackground:"#575757",normalText:"#344555",darkModeNormalText:"#FFFFFF",darModeHyperText:"#D1D1D1",normalTextDarkMode:"#FFFFFF",accentTextSecondary:"#0066B4",accentTextTertiary:"#057665",textSecondary:"#44596C",textSecondaryDarkMode:"#D1D1D1",accentTextSecondaryDarkMode:"#46A6FF",accentTextTertiaryDarkMode:"#00BAAE",textDisabled:"#A19F9D",textDisabledStrokeDarkMode:"#6E6E6E",grayScale300:"#ACACAC",grayScale100:"#E1E1E1",grayScale96:"#606060",emphasizeText:In,alertCloseButtonHover:"#F2F2F2",alertCloseButtonActive:"#F7F7F7",buttonFocusBorder:"#838383",buttonText:"#0066B4",buttonTextHover:"#0060A9",buttonBackgroundHover:"#EAEAEA",darkModeButtonBackgroundHover:"#404040",buttonBackgroundDoubleHover:"#CACACA",darkButtonBackgroundDoubleHover:"#5B5B5B",darkModeButtonText:"#46A6FF",darkModeButtonTextHover:"#68AFE5",buttonTextDarkMode:"linear-gradient(86.44deg, #00C16E 5.86%, #07C5D9 50.32%, #1A90FF 92.97%)",buttonHover:In,buttonDisabledBackground:"#E5E5E5",buttonDisabledText:"#919191",errorColor:"#A3341D",errorColorDarkMode:"#D13438",toggleColor:"#20C68A",toggleUnselectIndicator:"#44596C",toggleUnselectedBorder:"#5D6873",formFieldBackground:"rgba(255, 255, 255, 0.7)",darkModeformFieldBackground:"rgba(255, 255, 255, 0.02)",formFieldHoverBackground:"rgb(247, 247, 247)",formFieldBorder:"rgba(12, 127, 192, 0.3)",formFieldHoverBorder:"rgb(144, 144, 144)",selectOption:"#262626",darkModeSelectOption:"rgba(255, 255, 255, 0.02)",selectToggleGlyph:"#262626",selectOptionSelected:"rgba(174, 212, 232, 0.3)",darkModeSelectOptionSelected:"#244666",darkModeSelectOptionDropdownBackground:"#151D24",sectionBackground:"rgba(255, 255, 255, 0.7)",darkModeSectionBackground:"rgba(21, 29, 36, 0.8)",sectionBorder:"rgba(12, 127, 192, 0.1)",toggleUnselectedBackground:"#EFEFEF",accentText:"#0A7D80",accentTextDark:"#00BAAE",hotAccentText:"#C14821",hotAccentTextDark:"#FF9D7C",lightModeTextDisabled:"#A19F9D",normalControl:"#0078D4",normalControlHover:"#006CBE",darkModeNormalControl:"#63ADE5",iconFillColorLight:"#212121",darkModeNormalBackground:"#151d24",bottomBorderGray:"#D3D3D3",highContrastDarkMode:"#1AEBFF",highContrastLightMode:"#37006E",textDescriptionHighContrastLightModeColor:"#000000",textDescriptionHighContrastDarkModeColor:"#FFFFFF",separator:"#EFEFEF",separatorDark:"#FFFFFF",dialogText:"#262626",modalBackgroundDark:"#3B3B3B",typePrimary:"#323130",typeSecondary:"#605E5C",typeDisabled:"#A19F9D",typePrimaryDarkMode:"#FFFFFF",isReskinMVPNormalText:"#1A1A1A",criticalPrimary:"#C42B1C",criticalDarkMode:"#BC2F2F",YellowAlertPrimary:"#817400",YellowAlertIconDarkMode:"#feee66",RedAlertIconDarkMode:"#D73333",redForeground:"#BC2F32",iconBackplateBackgroud:"rgba(4, 115, 206, 0.08)",iconBackplate:"rgba(3, 106, 196, 1)",iconBackplateBackgroundDarkMode:"#4C5963",iconBackplateDarkMode:"#FFFFFF",miniWalletDividerLight:"#EFEFEF",miniWalletDividerDarkMode:"#525252",packageTrackingIconLight:"#004377",packageTrackingIconDark:"#a9d3f2",compoundBrandForegroundDark:"#479ef5",compoundBrandForegroundLight:"#0f6cbd",donationIconBackgroundDark:"#3f1011",donationIconBackgroundLight:"#fdf6f6",personalizedOfferTextColorDark:"#e37d80"}),Cn="@media (prefers-color-scheme: dark)",vn=e=>`@media only screen and (min-width: ${e}px)`,Rn=(vn(1),vn(375),vn(480),vn(640),vn(1036),vn(1367),vn(1920),{paddingInlineStart:"16px",paddingInlineEnd:"16px",fontSize:"12px",lineHeight:"16px",userSelect:"none"}),yn=On.borderColor(An.errorColor),Sn={...yn,"&:hover:enabled":{...yn},"&:active:enabled":{...yn},"&:focus:enabled":{...yn}},wn=Le()?{[Cn]:{backgroundColor:An.darkNotificationBackground,":hover,:focus":{backgroundColor:An.darkModeButtonBackgroundHover}},"@media screen and (-ms-high-contrast: active)":{":hover,:focus":{"forced-color-adjust":"none",backgroundColor:"Highlight"}},":hover,:focus":{backgroundColor:An.buttonBackgroundHover},":hover .CTA":{"text-decoration":"underline"},":focus .CTA":{"text-decoration":"underline"},":focus-visible":{outlineWidth:"0px"},marginBlockEnd:"1px"}:{},kn={container:{width:"100%",backgroundColor:An.whiteBackground,[Cn]:{backgroundColor:An.darkNotificationBackground},userSelect:"none",textAlign:"start",...wn},header:{paddingBlockStart:"16px",paddingBlockEnd:"16px",paddingInlineStart:"18px",width:"252px",fontWeight:"600",wordBreak:"break-word"},image:{width:"100%"},text:{...Rn,paddingBlockStart:"12px",paddingBlockEnd:"12px"},textCompact:{...Rn},action:{paddingInlineStart:"16px",paddingBlockEnd:"16px",width:"fit-content"},actionButton:{fontWeight:"400"},agreement:{paddingInlineStart:"16px",paddingInlineEnd:"16px",fontSize:"10px",lineHeight:"13px",paddingBlockEnd:"16px"},agreementLarge:{...Rn,paddingBlockEnd:"16px"},agreementSmall:{...Rn,paddingBlockEnd:"12px"},link:{},linkLarge:{},inlineError:{color:An.errorColor,fontSize:"12px",lineHeight:"16px",fontWeight:"600"}},Mn={width:"126px",textAlign:"start",display:"grid",gridTemplateColumns:"88px 12px"};var xn,Pn,Dn,Ln,Fn,Wn,Bn,Gn,Vn,Un,$n;pr({...kn,icon:{display:"inline-block",paddingInlineStart:"18px",paddingBlockStart:"14px",color:An.normalControl},header:{...kn.header,paddingBlockStart:"12px",paddingBlockEnd:"0px",width:"220px"},title:{fontWeight:"600",lineHeight:"20px",fontSize:"14px"},text:{...kn.text,fontSize:"12px",paddingInlineStart:"18px",paddingBlockStart:"8px",paddingBlockEnd:"0px",lineHeight:"16px",width:"220px"},expDate:{paddingBlockEnd:"12px",paddingInlineStart:"16px",width:"fit-content"},expDateSelector:{width:"126px",paddingInlineEnd:"12px",display:"inline-block"},label:{paddingBlockEnd:"4px",display:"inline-block"},menuButton:Mn,menuPopover:{minWidth:"116px",height:"64px",overflowY:"scroll"},errorMenuButton:{...Sn,...Mn},iconContainer:{display:"inline-block",verticalAlign:"top"},contentContainer:{display:"inline-block"},action:{...kn.action,width:"fit-content",height:"20px",paddingInlineStart:"18px",paddingBlockStart:"8px",paddingBlockEnd:"8px"},actionButton:{display:"inline-block",...kn.actionButton,fontSize:"12px"}}),function(e){e.ClientErrors="Microsoft.Wallet.ClientError",e.TokenizationEnrollActions="Microsoft.Wallet.Tokenization.MainFunnel"}(xn||(xn={})),function(e){e[e.ExpressCheckout=0]="ExpressCheckout",e[e.WalletHub=1]="WalletHub",e[e.BNPL=2]="BNPL",e[e.MAX=3]="MAX"}(Pn||(Pn={})),($n=Dn||(Dn={})).Enroll="Enroll",$n.Autofill="Autofill",$n.EnrollInline="EnrollInline",$n.AutofillInline="AutofillInline",(Un=Ln||(Ln={})).DropdownList="DropdownList",Un.Settings="Settings",Un.EditCard="EditCard",Un.WalletCheckout="WalletCheckout",Un.PaymentList="PaymentList",Un.ProfileNotification="ProfileNotification",function(e){e.Local="Local",e.Server="Server",e.Partial="Partial",e.Unknown="Unknown"}(Fn||(Fn={})),function(e){e.VISA="Visa",e.MASTERCARD="MasterCard",e.UNKNOWN="Unknown"}(Wn||(Wn={})),(Vn=Bn||(Bn={}))[Vn.CARD_ELIGIBLE=0]="CARD_ELIGIBLE",Vn[Vn.CARD_TOKENIZED=1]="CARD_TOKENIZED",Vn[Vn.USE_TOKENIZED_CARD_TOGGLE_ON=2]="USE_TOKENIZED_CARD_TOGGLE_ON",Vn[Vn.USE_TOKENIZED_CARD_TOGGLE_OFF=3]="USE_TOKENIZED_CARD_TOGGLE_OFF",Vn[Vn.SELECT_TOKENIZED_CARD_AUTOFILL=4]="SELECT_TOKENIZED_CARD_AUTOFILL",Vn[Vn.SELECT_ORIGINAL_CARD_AUTOFILL=5]="SELECT_ORIGINAL_CARD_AUTOFILL",Vn[Vn.CLICK_ENROLL_TOGGLE=6]="CLICK_ENROLL_TOGGLE",Vn[Vn.OPEN_ENROLL_DIALOG=7]="OPEN_ENROLL_DIALOG",Vn[Vn.CLCIK_ENROALL_DIALOG_LEARN_MORE_LINK=8]="CLCIK_ENROALL_DIALOG_LEARN_MORE_LINK",Vn[Vn.CLICK_ENROLL_NOW=9]="CLICK_ENROLL_NOW",Vn[Vn.CLOSE_ENROLL_DIALOG=10]="CLOSE_ENROLL_DIALOG",Vn[Vn.OPEN_CONTACTING_BANK_DIALOG=11]="OPEN_CONTACTING_BANK_DIALOG",Vn[Vn.CLOSE_CONTACTING_BANK_DIALOG=12]="CLOSE_CONTACTING_BANK_DIALOG",Vn[Vn.REQUEST_TOKEN_STARTED=13]="REQUEST_TOKEN_STARTED",Vn[Vn.REQUEST_TOKEN_GET_HBI_FAILED=14]="REQUEST_TOKEN_GET_HBI_FAILED",Vn[Vn.REQUEST_TOKEN_SUCCESS=15]="REQUEST_TOKEN_SUCCESS",Vn[Vn.DEVICE_BINDING_REQUIRED=16]="DEVICE_BINDING_REQUIRED",Vn[Vn.DEVICE_BINDING_NOT_REQUIRED=17]="DEVICE_BINDING_NOT_REQUIRED",Vn[Vn.VERIFICATION_SUCCESS=18]="VERIFICATION_SUCCESS",Vn[Vn.DEVICE_ENROLL_REQUIRED=19]="DEVICE_ENROLL_REQUIRED",Vn[Vn.DEVICE_ENROLL_NOT_REQUIRED=20]="DEVICE_ENROLL_NOT_REQUIRED",Vn[Vn.DEVICE_ENROLL_STARTED=21]="DEVICE_ENROLL_STARTED",Vn[Vn.DEVICE_ENROLL_SUCCESS=22]="DEVICE_ENROLL_SUCCESS",Vn[Vn.REQUEST_DEVICE_BINGDING_STARTED=23]="REQUEST_DEVICE_BINGDING_STARTED",Vn[Vn.REQUEST_DEVICE_BINGDING_GET_HBI_FAILED=24]="REQUEST_DEVICE_BINGDING_GET_HBI_FAILED",Vn[Vn.REQUEST_DEVICE_BINGDING_SUCCESS=25]="REQUEST_DEVICE_BINGDING_SUCCESS",Vn[Vn.CHALLENGE_APPROVED=26]="CHALLENGE_APPROVED",Vn[Vn.CHALLENGE_REQUIRED=27]="CHALLENGE_REQUIRED",Vn[Vn.GET_CRENDETAILS_STARTED=28]="GET_CRENDETAILS_STARTED",Vn[Vn.GET_CRENDETAILS_GET_HBI_FAILED=29]="GET_CRENDETAILS_GET_HBI_FAILED",Vn[Vn.GET_CRENDETAILS_SUCCESS=30]="GET_CRENDETAILS_SUCCESS",Vn[Vn.DECRYPT_TOKEN_SUCCESS=31]="DECRYPT_TOKEN_SUCCESS",Vn[Vn.GET_TOKEN_METADATA_SUCCESS=32]="GET_TOKEN_METADATA_SUCCESS",Vn[Vn.FETCH_AUTOFILL_DATA_SUCCESS=33]="FETCH_AUTOFILL_DATA_SUCCESS",Vn[Vn.OPEN_OTP_DIALOG=34]="OPEN_OTP_DIALOG",Vn[Vn.CLOSE_OTP_DIALOG=35]="CLOSE_OTP_DIALOG",Vn[Vn.CHANGE_OTP_METHOD=36]="CHANGE_OTP_METHOD",Vn[Vn.GET_OTP_SUCCESS=37]="GET_OTP_SUCCESS",Vn[Vn.CLICK_VERIFY_OTP_BUTTON=38]="CLICK_VERIFY_OTP_BUTTON",Vn[Vn.OPEN_VERIFIED_SUCCESS_DIALOG=39]="OPEN_VERIFIED_SUCCESS_DIALOG",Vn[Vn.CLOSE_VERIFIED_SUCCESS_DIALOG=40]="CLOSE_VERIFIED_SUCCESS_DIALOG",Vn[Vn.OPEN_GENERATING_CARD_DETAILS_DIALOG=41]="OPEN_GENERATING_CARD_DETAILS_DIALOG",Vn[Vn.CLOSE_GENERATING_CARD_DETAILS_DIALOG_FROM_UI=42]="CLOSE_GENERATING_CARD_DETAILS_DIALOG_FROM_UI",Vn[Vn.CLOSE_GENERATING_CARD_DETAILS_DIALOG_FROM_NATIVE=43]="CLOSE_GENERATING_CARD_DETAILS_DIALOG_FROM_NATIVE",Vn[Vn.AUTOFILL_SUCCESS=44]="AUTOFILL_SUCCESS",Vn[Vn.SHOW_VIRTUAL_CARD_READY_FLYOUT=45]="SHOW_VIRTUAL_CARD_READY_FLYOUT",Vn[Vn.OPEN_ERROR_DIALOG=46]="OPEN_ERROR_DIALOG",Vn[Vn.GET_LOCAL_META_DATA_SUCCESS=47]="GET_LOCAL_META_DATA_SUCCESS",Vn[Vn.CLICK_ELIGIBLE_CARD=48]="CLICK_ELIGIBLE_CARD",Vn[Vn.CLICK_ELIGIBLE_CARD_TAG=49]="CLICK_ELIGIBLE_CARD_TAG",Vn[Vn.SAVE_ELIGIBLE_CARD_WITH_TOKENIZATION_CONSENT=50]="SAVE_ELIGIBLE_CARD_WITH_TOKENIZATION_CONSENT",Vn[Vn.TOKEN_WILL_BE_SUBMITTED=51]="TOKEN_WILL_BE_SUBMITTED",Vn[Vn.TOKEN_SUBMITTED_FOR_TRANSACTION=52]="TOKEN_SUBMITTED_FOR_TRANSACTION",Vn[Vn.CARD_ELIGIBLE_SHOWN_ONCE=53]="CARD_ELIGIBLE_SHOWN_ONCE",Vn[Vn.CARD_TOKENIZED_SHOWN_ONCE=54]="CARD_TOKENIZED_SHOWN_ONCE",Vn[Vn.OPEN_VERIFY_CVV_DIALOG=55]="OPEN_VERIFY_CVV_DIALOG",Vn[Vn.CLICK_VERIFY_CVV_BUTTON=56]="CLICK_VERIFY_CVV_BUTTON",Vn[Vn.CLOSE_VERIFY_CVV_DIALOG=57]="CLOSE_VERIFY_CVV_DIALOG",Vn[Vn.CLICK_ERROR_DIALOG_TRY_AGAIN_BUTTON=58]="CLICK_ERROR_DIALOG_TRY_AGAIN_BUTTON",Vn[Vn.OPEN_EDIT_CARD_PAGE=59]="OPEN_EDIT_CARD_PAGE",Vn[Vn.MAX=59]="MAX",function(e){e.impression="impression",e.engagement="engagement",e.snooze="snooze",e.dismiss="dismiss"}(Gn||(Gn={}));const Hn={[st.CardExpiring]:{[Gn.impression]:nt.CARD_EXPIRING_SOON_IMPRESSION,[Gn.engagement]:nt.CARD_EXPIRING_SOON_ENGAGEMENT,[Gn.snooze]:nt.CARD_EXPIRING_SOON_SNOOZE,[Gn.dismiss]:nt.CARD_EXPIRING_SOON_DISMISS_PERMANENTLY},[st.CardExpired]:{[Gn.impression]:nt.CARD_EXPIRED_IMPRESSION,[Gn.engagement]:nt.CARD_EXPIRED_ENGAGEMENT,[Gn.snooze]:nt.CARD_EXPIRED_SNOOZE,[Gn.dismiss]:nt.CARD_EXPIRED_DISMISS_PERMANENTLY},[st.CardTokenizationEligible]:{[Gn.impression]:nt.CARD_TOKENIZATION_ELIGIBLE_IMPRESSION,[Gn.engagement]:nt.CARD_TOKENIZATION_ELIGIBLE_ENGAGEMENT,[Gn.snooze]:nt.CARD_TOKENIZATION_ELIGIBLE_SNOOZE,[Gn.dismiss]:nt.CARD_TOKENIZATION_ELIGIBLE_DISMISS_PERMANENTLY},[st.SignupCryptoWallet]:{[Gn.impression]:nt.SIGN_UP_CRYPTOWALLET_IMPRESSION,[Gn.engagement]:nt.SIGN_UP_CRYPTOWALLET_ENGAGEMENT,[Gn.snooze]:nt.SIGN_UP_CRYPTOWALLET_SNOOZE,[Gn.dismiss]:nt.SIGN_UP_CRYPTOWALLET_DISMISS_PERMANENTLY},[st.PasswordLeakage]:{[Gn.impression]:nt.PASSWORD_LEAKAGE_IMPRESSION,[Gn.engagement]:nt.PASSWORD_LEAKAGE_ENGAGEMENT,[Gn.snooze]:nt.PASSWORD_LEAKAGE_SNOOZE,[Gn.dismiss]:nt.PASSWORD_LEAKAGE_DISMISS_PERMANENTLY},[st.UpcomingHotelReservations]:{[Gn.impression]:nt.UPCOMING_HOTEL_RESERVATION_IMPRESSION,[Gn.engagement]:nt.UPCOMING_HOTEL_RESERVATION_ENGAGEMENT,[Gn.snooze]:nt.UPCOMING_HOTEL_RESERVATION_SNOOZE,[Gn.dismiss]:nt.UPCOMING_HOTEL_RESERVATION_DISMISS_PERMANENTLY},[st.PersonalizedOffersAvailable]:{[Gn.impression]:nt.PERSONALIZED_OFFERS_AVAILABLE_IMPRESSION,[Gn.engagement]:nt.PERSONALIZED_OFFERS_AVAILABLE_ENGAGEMENT,[Gn.snooze]:nt.PERSONALIZED_OFFERS_AVAILABLE_SNOOZE,[Gn.dismiss]:nt.PERSONALIZED_OFFERS_AVAILABLE_DISMISS_PERMANENTLY},[st.RoamCard]:{[Gn.impression]:nt.ROAM_CARD_IMPRESSION,[Gn.engagement]:nt.ROAM_CARD_ENGAGEMENT,[Gn.snooze]:nt.ROAM_CARD_SNOOZE,[Gn.dismiss]:nt.ROAM_CARD_DISMISS_PERMANENTLY},[st.DonationSummary]:{[Gn.impression]:nt.DONATION_SUMMARY_IMPRESSION,[Gn.engagement]:nt.DONATION_SUMMARY_ENGAGEMENT,[Gn.snooze]:nt.DONATION_SUMMARY_SNOOZE,[Gn.dismiss]:nt.DONATION_SUMMARY_DISMISS_PERMANENTLY},[st.PackageTracking]:{[Gn.impression]:nt.PACKAGE_TRACKING_IMPRESSION,[Gn.engagement]:nt.MAX,[Gn.snooze]:nt.PACKAGE_TRACKING_SNOOZE,[Gn.dismiss]:nt.PACKAGE_TRACKING_DISMISS_PERMANENTLY},[st.Rebates]:{[Gn.impression]:nt.REBATES_IMPRESSION,[Gn.engagement]:nt.REBATES_ENGAGEMENT,[Gn.snooze]:nt.REBATES_SNOOZE,[Gn.dismiss]:nt.REBATES_DISMISS_PERMANENTLY},[st.PWAPromotion]:{[Gn.impression]:nt.PWA_PROMOTION_IMPRESSION,[Gn.engagement]:nt.PWA_PROMOTION_ENGAGEMENT,[Gn.snooze]:nt.PWA_PROMOTION_SNOOZE,[Gn.dismiss]:nt.PWA_PROMOTION_DISMISS_PERMANENTLY},[st.DonationTrendNpo]:{[Gn.impression]:nt.DONATION_TREND_NPO_IMPRESSION,[Gn.engagement]:nt.DONATION_TREND_NPO_ENGAGEMENT,[Gn.snooze]:nt.DONATION_TREND_NPO_SNOOZE,[Gn.dismiss]:nt.DONATION_TREND_NPO_DISMISS_PERMANENTLY}};pr({...kn,header:{...kn.header,display:"inline-block",paddingInlineStart:"16px",paddingBlockStart:"12px",paddingBlockEnd:"0px",width:"195px",fontWeight:"600",lineHeight:"20px",fontSize:"14px"},text:{...kn.text,paddingInlineStart:"16px",paddingBlockStart:"8px",paddingBlockEnd:"0px",lineHeight:"16px",width:"206px",fontSize:"12px"},action:{...kn.action,fontSize:"12px",width:"fit-content",paddingInlineStart:"16px",paddingBlockStart:"8px",paddingBlockEnd:"8px"},actionButton:{display:"inline-block",...kn.actionButton,fontSize:"12px"},iconContainer:{display:"inline-block",verticalAlign:"top"},icon:{paddingInlineStart:"20px",paddingBlockStart:"12px"},contentContainer:{display:"inline-block"}}),Ln.ProfileNotification;const zn=(pr({container:{...kn.container,paddingBlockEnd:"10px"},header:{paddingBlockStart:"10px",paddingBlockEnd:"8px",width:"252px",fontWeight:"600",wordBreak:"break-word",display:"flex",paddingInlineStart:"0px"},text:{...kn.text,paddingInlineStart:"62px",paddingInlineEnd:"36px",paddingBlockStart:"0px",paddingBlockEnd:"8px"},linkLarge:{...kn.linkLarge,fontSize:"12px",paddingInlineStart:"62px !important"},icon:{color:"var(--colorPaletteDarkOrangeForeground1)",[Cn]:{color:An.RedAlertIconDarkMode},paddingInlineStart:"24px"}}),pr({...kn,container__header_container:{display:"flex",paddingBlockStart:"12px",paddingInlineStart:"16px",paddingInlineEnd:"12px"},imageContainer:{width:"36px"},image:{paddingBlockStart:"5px",display:"block",maxWidth:"36px",maxHeight:"36px",width:"auto",height:"auto",objectFit:"contain"},container__header_text:{maxWidth:"198px",paddingInlineStart:"12px"},expirationTitle:{...kn.header,maxWidth:"182px",maxHeight:"50px",paddingInlineStart:"0px",paddingBlockStart:"0px",paddingBlockEnd:"0px",fontSize:"16px",lineHeight:"22px",overflowY:"hidden",textOverflow:"ellipsis",display:"-webkit-box","-webkit-line-clamp":"2","-webkit-box-orient":"vertical"},expirationContainer:{display:"flex",flexDirection:"row",fontSize:"12px",fontWeight:"400",lineHeight:"16px"},expiresInText:{overflowY:"hidden",textOverflow:"ellipsis",maxWidth:"198px",paddingInlineEnd:"6px"},countDownTimerText:{display:"inline-block",color:"var(--colorPaletteRedForeground1)"},action:{...kn.link,display:"flex",flexDirection:"row",justifyContent:"space-between",paddingBlockStart:"8px",width:"unset",paddingBlockEnd:"8px"},actionButton:{display:"inline-block",...kn.actionButton,fontSize:"12px",overflowY:"hidden",textOverflow:"ellipsis",maxWidth:"198px",minWidth:"132px",lineHeight:"16px",color:"#036AC4"},header:{...kn.header,paddingBlockStart:"0px",paddingInlineStart:"0px",paddingBlockEnd:"0px",width:"200px"},title:{fontWeight:"600",lineHeight:"20px",fontSize:"14px"}}),pr({...kn,header:{...kn.header,paddingInlineStart:"0px",display:"inline-block",paddingBlockStart:"12px",paddingBlockEnd:"0px",width:"195px",fontWeight:"600",lineHeight:"20px",fontSize:"14px"},textContainer:{...kn.text,paddingInlineStart:"0px",paddingBlockStart:"8px",paddingBlockEnd:"0px",display:"flex",columnGap:"8px",alignItems:"center",width:"206px"},text:{lineHeight:"18px",fontSize:"14px"},action:{...kn.action,paddingInlineStart:"0px",fontSize:"12px",width:"fit-content",paddingBlockStart:"8px",paddingBlockEnd:"8px"},actionButton:{display:"inline-block",...kn.actionButton,fontSize:"12px"},iconContainer:{display:"inline-block",verticalAlign:"top"},icon:{paddingInlineStart:"24px",paddingBlockStart:"14px",[Cn]:{fill:"white","& path":{fill:"white"}}},contentContainer:{paddingInlineStart:"18px",display:"inline-block",boxSizing:"border-box"},cardLogo:{display:"block",width:"32px",height:"auto"}}),pr({container:{...kn.container,paddingBlockEnd:"8px"},header:{display:"flex",marginInlineStart:"24px",paddingBlockStart:"12px",paddingBlockEnd:"0px",width:"240px"},icon:{color:"var(--colorCompoundBrandForeground1)",lineHeight:"20px"},headerText:{width:"220px",fontWeight:"600",wordBreak:"break-word",paddingInlineStart:"18px",lineHeight:"20px"},text:{fontSize:"12px",lineHeight:"16px",marginInlineStart:"62px",marginInlineEnd:"16px",paddingBlockStart:"8px",paddingBlockEnd:"0px",width:"200px"},action:{paddingBlockStart:"8px",marginInlineStart:"62px",width:"fit-content"},actionLink:{fontSize:"12px",lineHeight:"16px"}}),pr({container:{...kn.container,paddingBlockEnd:"8px"},header:{display:"flex",marginInlineStart:"16px",marginInlineEnd:"40px",paddingBlockStart:"12px"},icon:{width:"20px",height:"20px",...On.paddingBlock("8px"),...On.paddingInline("8px"),backgroundColor:"var(--colorPaletteRedBackground1)",color:"var(--colorPaletteRedBackground3)",...On.borderRadius("50%")},text:{fontWeight:"600",wordBreak:"break-word",marginInlineStart:"12px",lineHeight:"20px"},action:{height:"20px",marginBlockStart:"16px",paddingBlockStart:"8px",paddingBlockEnd:"4px",marginInlineStart:"64px",width:"fit-content"},actionButton:{...kn.actionButton,fontSize:"12px",lineHeight:"20px"}}),pr({container:{...kn.container,display:"flex"},iconContainer:{paddingBlockStart:"8px",paddingInlineStart:"16px",paddingInlineEnd:"12px",flexShrink:0,flexGrow:0},icon:{lineHeight:"36px",width:"36px",height:"36px"},contentContainer:{display:"flex",flexDirection:"column",marginInlineEnd:"44px"},header:{fontWeight:"600",wordBreak:"break-word",lineHeight:"20px",paddingBlockStart:"8px",paddingBlockEnd:"8px"},description:{fontSize:"12px",lineHeight:"16px"},action:{height:"20px",paddingBlockStart:"8px",paddingBlockEnd:"12px"},actionButton:{...kn.actionButton,fontSize:"12px",lineHeight:"20px"}}),Symbol("fui.slotRenderFunction")),jn=Symbol("fui.slotElementType");var Zn=Object.defineProperty,Yn=Object.defineProperties,Kn=Object.getOwnPropertyDescriptors,qn=Object.getOwnPropertySymbols,Xn=Object.prototype.hasOwnProperty,Qn=Object.prototype.propertyIsEnumerable,Jn=(e,t,i)=>t in e?Zn(e,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[t]=i,ea=(e,t)=>{for(var i in t||(t={}))Xn.call(t,i)&&Jn(e,i,t[i]);if(qn)for(var i of qn(t))Qn.call(t,i)&&Jn(e,i,t[i]);return e};function ta(e,t){const{defaultProps:i,elementType:o}=t,r=function(e){return"string"==typeof e||"number"==typeof e||Array.isArray(e)||vt.isValidElement(e)?{children:e}:e}(e),n=(a=ea(ea({},i),r),Yn(a,Kn({[jn]:o})));var a;return r&&"function"==typeof r.children&&(n[zn]=r.children,n.children=null==i?void 0:i.children),n}const ia=(...e)=>{const t={};for(const i of e){const e=Array.isArray(i)?i:Object.keys(i);for(const i of e)t[i]=1}return t},oa=ia(["onAuxClick","onAnimationEnd","onAnimationStart","onCopy","onCut","onPaste","onCompositionEnd","onCompositionStart","onCompositionUpdate","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onInput","onSubmit","onLoad","onError","onKeyDown","onKeyDownCapture","onKeyPress","onKeyUp","onAbort","onCanPlay","onCanPlayThrough","onDurationChange","onEmptied","onEncrypted","onEnded","onLoadedData","onLoadedMetadata","onLoadStart","onPause","onPlay","onPlaying","onProgress","onRateChange","onSeeked","onSeeking","onStalled","onSuspend","onTimeUpdate","onVolumeChange","onWaiting","onClick","onClickCapture","onContextMenu","onDoubleClick","onDrag","onDragEnd","onDragEnter","onDragExit","onDragLeave","onDragOver","onDragStart","onDrop","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseOut","onMouseOver","onMouseUp","onMouseUpCapture","onSelect","onTouchCancel","onTouchEnd","onTouchMove","onTouchStart","onScroll","onWheel","onPointerCancel","onPointerDown","onPointerEnter","onPointerLeave","onPointerMove","onPointerOut","onPointerOver","onPointerUp","onGotPointerCapture","onLostPointerCapture"]),ra=ia(["accessKey","children","className","contentEditable","dir","draggable","hidden","htmlFor","id","lang","ref","role","style","tabIndex","title","translate","spellCheck","name"]),na=ia(["itemID","itemProp","itemRef","itemScope","itemType"]),aa=ia(ra,oa,na),sa=ia(aa,["form"]),la=ia(aa,["height","loop","muted","preload","src","width"]),ca=ia(la,["poster"]),da=ia(aa,["start"]),ua=ia(aa,["value"]),pa=ia(aa,["download","href","hrefLang","media","rel","target","type"]),ha=ia(aa,["dateTime"]),fa=ia(aa,["autoFocus","disabled","form","formAction","formEncType","formMethod","formNoValidate","formTarget","type","value"]),ga={label:sa,audio:la,video:ca,ol:da,li:ua,a:pa,button:fa,input:ia(fa,["accept","alt","autoCapitalize","autoComplete","checked","dirname","form","height","inputMode","list","max","maxLength","min","multiple","pattern","placeholder","readOnly","required","src","step","size","type","value","width"]),textarea:ia(fa,["autoCapitalize","cols","dirname","form","maxLength","placeholder","readOnly","required","rows","wrap"]),select:ia(fa,["form","multiple","required"]),option:ia(aa,["selected","value"]),table:ia(aa,["cellPadding","cellSpacing"]),tr:aa,th:ia(aa,["colSpan","rowSpan","scope"]),td:ia(aa,["colSpan","headers","rowSpan","scope"]),colGroup:ia(aa,["span"]),col:ia(aa,["span"]),fieldset:ia(aa,["disabled","form"]),form:ia(aa,["acceptCharset","action","encType","encType","method","noValidate","target"]),iframe:ia(aa,["allow","allowFullScreen","allowPaymentRequest","allowTransparency","csp","height","importance","referrerPolicy","sandbox","src","srcDoc","width"]),img:ia(aa,["alt","crossOrigin","height","src","srcSet","useMap","width"]),time:ha,dialog:ia(aa,["open","onCancel","onClose"])};const Ea=(e,t,i)=>{var o;return function(e,t,i){const o=e&&ga[e]||aa;return o.as=1,function(e,t,i){const o=Array.isArray(t),r={},n=Object.keys(e);for(const a of n)!(!o&&t[a]||o&&t.indexOf(a)>=0||0===a.indexOf("data-")||0===a.indexOf("aria-"))||i&&-1!==(null==i?void 0:i.indexOf(a))||(r[a]=e[a]);return r}(t,o,i)}(null!==(o=t.as)&&void 0!==o?o:e,t,i)};var Ta=Object.defineProperty,ma=Object.getOwnPropertySymbols,Na=Object.prototype.hasOwnProperty,_a=Object.prototype.propertyIsEnumerable,Oa=(e,t,i)=>t in e?Ta(e,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[t]=i,ba=(e,t)=>{for(var i in t||(t={}))Na.call(t,i)&&Oa(e,i,t[i]);if(ma)for(var i of ma(t))_a.call(t,i)&&Oa(e,i,t[i]);return e};function Ia(e){return Boolean(null==e?void 0:e.hasOwnProperty(jn))}var Aa=Object.defineProperty,Ca=Object.defineProperties,va=Object.getOwnPropertyDescriptors,Ra=Object.getOwnPropertySymbols,ya=Object.prototype.hasOwnProperty,Sa=Object.prototype.propertyIsEnumerable,wa=(e,t,i)=>t in e?Aa(e,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[t]=i;function ka(e,t){return function(i,o,r,n,a){return Ia(o)?t(function(e,t){return i=((e,t)=>{for(var i in t||(t={}))ya.call(t,i)&&wa(e,i,t[i]);if(Ra)for(var i of Ra(t))Sa.call(t,i)&&wa(e,i,t[i]);return e})({},t),Ca(i,va({[jn]:e}));var i}(i,o),null,r,n,a):Ia(i)?t(i,o,r,n,a):e(i,o,r,n,a)}}r(732);var Ma=Object.getOwnPropertySymbols,xa=Object.prototype.hasOwnProperty,Pa=Object.prototype.propertyIsEnumerable,Da=e=>"symbol"==typeof e?e:e+"";function La(e){var t,i;const o=e,{as:r,[t=jn]:n,[i=zn]:a}=o,s=((e,t)=>{var i={};for(var o in e)xa.call(e,o)&&t.indexOf(o)<0&&(i[o]=e[o]);if(null!=e&&Ma)for(var o of Ma(e))t.indexOf(o)<0&&Pa.call(e,o)&&(i[o]=e[o]);return i})(o,["as",Da(t),Da(i)]),l=s,c="string"==typeof n&&null!=r?r:n;return"string"!=typeof c&&r&&(l.as=r),{elementType:c,props:l,renderFunction:a}}var Fa=r(668);const Wa=r.t(Fa,2);var Ba=Object.defineProperty,Ga=Object.getOwnPropertySymbols,Va=Object.prototype.hasOwnProperty,Ua=Object.prototype.propertyIsEnumerable,$a=(e,t,i)=>t in e?Ba(e,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[t]=i,Ha=(e,t)=>{for(var i in t||(t={}))Va.call(t,i)&&$a(e,i,t[i]);if(Ga)for(var i of Ga(t))Ua.call(t,i)&&$a(e,i,t[i]);return e},za=Object.defineProperty,ja=Object.defineProperties,Za=Object.getOwnPropertyDescriptors,Ya=Object.getOwnPropertySymbols,Ka=Object.prototype.hasOwnProperty,qa=Object.prototype.propertyIsEnumerable,Xa=(e,t,i)=>t in e?za(e,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[t]=i,Qa=(e,t)=>{for(var i in t||(t={}))Ka.call(t,i)&&Xa(e,i,t[i]);if(Ya)for(var i of Ya(t))qa.call(t,i)&&Xa(e,i,t[i]);return e};const Ja=ka(Wa.jsx,((e,t,i)=>{const{elementType:o,renderFunction:r,props:n}=La(e),a=Ha(Ha({},n),t);return r?Wa.jsx(vt.Fragment,{children:r(o,a)},i):Wa.jsx(o,a,i)}));function es(e,t){const i=function(e,t,i=Gt){const o=i();let r=null,n=null;return function(i){const{dir:a,renderer:s}=i,l="ltr"===a;return l?null===r&&(r=Zo(e,a)):null===n&&(n=Zo(e,a)),o(s,t),l?r:n}}(e,t,Ko);return function(){const e=ur(),t=cr();return i({dir:e,renderer:t})}}ka(Wa.jsxs,((e,t,i)=>{const{elementType:o,renderFunction:r,props:n}=La(e),a=Qa(Qa({},n),t);return r?Wa.jsx(vt.Fragment,{children:r(o,(s=Qa({},a),l={children:Wa.jsxs(vt.Fragment,{children:a.children},void 0)},ja(s,Za(l))))},i):Wa.jsxs(o,a,i);var s,l}));const ts={};function is(){let e=null,t="",i="";const o=new Array(arguments.length);for(let e=0;e<arguments.length;e++){const r=arguments[e];if("string"==typeof r&&""!==r){const n=r.indexOf(ai);if(-1===n)t+=r+" ";else{const a=r.substr(n,si);n>0&&(t+=r.slice(0,n)),i+=a,o[e]=a}}}if(""===i)return t.slice(0,-1);const r=ts[i];if(void 0!==r)return t+r;const n=[];for(let t=0;t<arguments.length;t++){const i=o[t];if(i){const t=ni[i];t&&(n.push(t[0]),e=t[1])}}const a=Object.assign.apply(Object,[{}].concat(n));let s=jo(a,e);const l=zo(s,e,o);return s=l+" "+s,ts[i]=s,ni[l]=[a,e],t+s}const os=es({root:{Bahqtrf:"fk6fouc",Be2twd7:"fkhj508",Bg96gwp:"f1i3iumi",Bhrd7zp:"figsok6",fsow6f:"fpgzoln",mc9l5x:"f1w7gpdv",Huce71:"f6juhto",B68tc82:"f1mtd64y",Bmxbyg5:"f1y7q3j9",ygn44y:"f2jf649"},nowrap:{Huce71:"fz5stix",B68tc82:"f1p9o1ba",Bmxbyg5:"f1sil6mw"},truncate:{ygn44y:"f1cmbuwj"},block:{mc9l5x:"ftgm304"},italic:{B80ckks:"f1j4dglz"},underline:{w71qe1:"f13mvf36"},strikethrough:{w71qe1:"fv5q2k7"},strikethroughUnderline:{w71qe1:"f1drk4o6"},base100:{Be2twd7:"f13mqy1h",Bg96gwp:"fcpl73t"},base200:{Be2twd7:"fy9rknc",Bg96gwp:"fwrc4pm"},base400:{Be2twd7:"fod5ikn",Bg96gwp:"faaz57k"},base500:{Be2twd7:"f1pp30po",Bg96gwp:"f106mvju"},base600:{Be2twd7:"f1x0m3f5",Bg96gwp:"fb86gi6"},hero700:{Be2twd7:"fojgt09",Bg96gwp:"fcen8rp"},hero800:{Be2twd7:"fccw675",Bg96gwp:"f1ebx5kk"},hero900:{Be2twd7:"f15afnhw",Bg96gwp:"fr3w3wp"},hero1000:{Be2twd7:"fpyltcb",Bg96gwp:"f1ivgwrt"},monospace:{Bahqtrf:"f1fedwem"},numeric:{Bahqtrf:"f1uq0ln5"},weightMedium:{Bhrd7zp:"fdj6btp"},weightSemibold:{Bhrd7zp:"fl43uef"},weightBold:{Bhrd7zp:"flh3ekv"},alignCenter:{fsow6f:"f17mccla"},alignEnd:{fsow6f:"f12ymhq5"},alignJustify:{fsow6f:"f1j59e10"}},{d:[".fk6fouc{font-family:var(--fontFamilyBase);}",".fkhj508{font-size:var(--fontSizeBase300);}",".f1i3iumi{line-height:var(--lineHeightBase300);}",".figsok6{font-weight:var(--fontWeightRegular);}",".fpgzoln{text-align:start;}",".f1w7gpdv{display:inline;}",".f6juhto{white-space:normal;}",".f1mtd64y{overflow-x:visible;}",".f1y7q3j9{overflow-y:visible;}",".f2jf649{text-overflow:clip;}",".fz5stix{white-space:nowrap;}",".f1p9o1ba{overflow-x:hidden;}",".f1sil6mw{overflow-y:hidden;}",".f1cmbuwj{text-overflow:ellipsis;}",".ftgm304{display:block;}",".f1j4dglz{font-style:italic;}",".f13mvf36{text-decoration-line:underline;}",".fv5q2k7{text-decoration-line:line-through;}",".f1drk4o6{text-decoration-line:line-through underline;}",".f13mqy1h{font-size:var(--fontSizeBase100);}",".fcpl73t{line-height:var(--lineHeightBase100);}",".fy9rknc{font-size:var(--fontSizeBase200);}",".fwrc4pm{line-height:var(--lineHeightBase200);}",".fod5ikn{font-size:var(--fontSizeBase400);}",".faaz57k{line-height:var(--lineHeightBase400);}",".f1pp30po{font-size:var(--fontSizeBase500);}",".f106mvju{line-height:var(--lineHeightBase500);}",".f1x0m3f5{font-size:var(--fontSizeBase600);}",".fb86gi6{line-height:var(--lineHeightBase600);}",".fojgt09{font-size:var(--fontSizeHero700);}",".fcen8rp{line-height:var(--lineHeightHero700);}",".fccw675{font-size:var(--fontSizeHero800);}",".f1ebx5kk{line-height:var(--lineHeightHero800);}",".f15afnhw{font-size:var(--fontSizeHero900);}",".fr3w3wp{line-height:var(--lineHeightHero900);}",".fpyltcb{font-size:var(--fontSizeHero1000);}",".f1ivgwrt{line-height:var(--lineHeightHero1000);}",".f1fedwem{font-family:var(--fontFamilyMonospace);}",".f1uq0ln5{font-family:var(--fontFamilyNumeric);}",".fdj6btp{font-weight:var(--fontWeightMedium);}",".fl43uef{font-weight:var(--fontWeightSemibold);}",".flh3ekv{font-weight:var(--fontWeightBold);}",".f17mccla{text-align:center;}",".f12ymhq5{text-align:end;}",".f1j59e10{text-align:justify;}"]}),rs=vt.createContext(void 0),ns=()=>{},as=(rs.Provider,vt.forwardRef(((e,t)=>{const i=((e,t)=>{const{wrap:i,truncate:o,block:r,italic:n,underline:a,strikethrough:s,size:l,font:c,weight:d,align:u}=e;return{align:null!=u?u:"start",block:null!=r&&r,font:null!=c?c:"base",italic:null!=n&&n,size:null!=l?l:300,strikethrough:null!=s&&s,truncate:null!=o&&o,underline:null!=a&&a,weight:null!=d?d:"regular",wrap:null==i||i,components:{root:"span"},root:ta(Ea("span",ba({ref:t},e)),{elementType:"span"})}})(e,t);var o,r;return(e=>{const t=os();e.root.className=is("fui-Text",t.root,!1===e.wrap&&t.nowrap,e.truncate&&t.truncate,e.block&&t.block,e.italic&&t.italic,e.underline&&t.underline,e.strikethrough&&t.strikethrough,e.underline&&e.strikethrough&&t.strikethroughUnderline,100===e.size&&t.base100,200===e.size&&t.base200,400===e.size&&t.base400,500===e.size&&t.base500,600===e.size&&t.base600,700===e.size&&t.hero700,800===e.size&&t.hero800,900===e.size&&t.hero900,1e3===e.size&&t.hero1000,"monospace"===e.font&&t.monospace,"numeric"===e.font&&t.numeric,"medium"===e.weight&&t.weightMedium,"semibold"===e.weight&&t.weightSemibold,"bold"===e.weight&&t.weightBold,"center"===e.align&&t.alignCenter,"end"===e.align&&t.alignEnd,"justify"===e.align&&t.alignJustify,e.root.className)})(i),("useTextStyles_unstable",null!==(r=null===(o=vt.useContext(rs))||void 0===o?void 0:o.useTextStyles_unstable)&&void 0!==r?r:ns)(i),(e=>Ja(e.root,{}))(i)})));as.displayName="Text";const ss=vt.createContext(void 0);ss.Provider;var ls=Object.defineProperty,cs=Object.defineProperties,ds=Object.getOwnPropertyDescriptors,us=Object.getOwnPropertySymbols,ps=Object.prototype.hasOwnProperty,hs=Object.prototype.propertyIsEnumerable,fs=(e,t,i)=>t in e?ls(e,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[t]=i,gs=(e,t)=>{for(var i in t||(t={}))ps.call(t,i)&&fs(e,i,t[i]);if(us)for(var i of us(t))hs.call(t,i)&&fs(e,i,t[i]);return e};const Es=es({focusIndicator:{Bttzg6e:"fhgqx19",B3uz8dt:"f1olyrje",B6ihwck:"f1p93eir",g9k6zt:"f1nev41a"},root:{B486eqv:"f2hkw1w",De3pzq:"f3rmtva",B7ck84d:"f1ewtqcl",sj55zd:"fyind8e",Bceei9c:"f1k6fduh",mc9l5x:"f1w7gpdv",Bahqtrf:"fk6fouc",Be2twd7:"fkhj508",Bhrd7zp:"figsok6",B6of3ja:"f1hu3pq6",t21cq0:["f11qmguv","f1tyq0we"],jrapky:"f19f4twv",Frg6f3:["f1tyq0we","f11qmguv"],z8tnut:"f1g0x7ka",z189sj:["fhxju0i","f1cnd47f"],Byoj8tv:"f1qch9an",uwmqm3:["f1cnd47f","fhxju0i"],B68tc82:"fqv5qza",Bmxbyg5:"f1vmzxwi",fsow6f:["f1o700av","fes3tcz"],w71qe1:"f1iuv45f",Bkioxbp:"f1cmlufx",ygn44y:"f9n3di6",famaaq:"f1ids18y",Bde5pd6:"f1tx3yz7",Bi91k9c:"f1deo86v",i089h6:"f1eh06m1",lj723h:"f1iescvh"},button:{icvyot:"f1ern45e",vrafjx:["f1n71otn","f1deefiw"],oivjwe:"f1h8hb77",wvpqe5:["f1deefiw","f1n71otn"]},href:{Be2twd7:"fjoy568"},subtle:{sj55zd:"fkfq4zb",Bde5pd6:"f1tx3yz7",Bi91k9c:"fnwyq0v",i089h6:"f1eh06m1",lj723h:"flvvhsy"},inline:{w71qe1:"f13mvf36"},disabled:{w71qe1:"f1iuv45f",sj55zd:"f1s2aq7o",Bceei9c:"fdrzuqr",Bde5pd6:"fbnuktb",Bi91k9c:"fvgxktp",i089h6:"fljg2da",lj723h:"f19wldhg"},inverted:{sj55zd:"f1qz2gb0",Bi91k9c:"f1mlt8il",lj723h:"f1hsd4st"}},{d:[".fhgqx19[data-fui-focus-visible]{text-decoration-color:var(--colorStrokeFocus2);}",".f1olyrje[data-fui-focus-visible]{text-decoration-line:underline;}",".f1p93eir[data-fui-focus-visible]{text-decoration-style:double;}",".f1nev41a[data-fui-focus-visible]{outline-style:none;}",".f3rmtva{background-color:transparent;}",".f1ewtqcl{box-sizing:border-box;}",".fyind8e{color:var(--colorBrandForegroundLink);}",".f1k6fduh{cursor:pointer;}",".f1w7gpdv{display:inline;}",".fk6fouc{font-family:var(--fontFamilyBase);}",".fkhj508{font-size:var(--fontSizeBase300);}",".figsok6{font-weight:var(--fontWeightRegular);}",".f1hu3pq6{margin-top:0;}",".f11qmguv{margin-right:0;}",".f1tyq0we{margin-left:0;}",".f19f4twv{margin-bottom:0;}",".f1g0x7ka{padding-top:0;}",".fhxju0i{padding-right:0;}",".f1cnd47f{padding-left:0;}",".f1qch9an{padding-bottom:0;}",".fqv5qza{overflow-x:inherit;}",".f1vmzxwi{overflow-y:inherit;}",".f1o700av{text-align:left;}",".fes3tcz{text-align:right;}",".f1iuv45f{text-decoration-line:none;}",".f1cmlufx{text-decoration-thickness:var(--strokeWidthThin);}",".f9n3di6{text-overflow:inherit;}",".f1ids18y{-webkit-user-select:text;-moz-user-select:text;-ms-user-select:text;user-select:text;}",".f1ern45e{border-top-style:none;}",".f1n71otn{border-right-style:none;}",".f1deefiw{border-left-style:none;}",".f1h8hb77{border-bottom-style:none;}",".fjoy568{font-size:inherit;}",".fkfq4zb{color:var(--colorNeutralForeground2);}",".f13mvf36{text-decoration-line:underline;}",".f1s2aq7o{color:var(--colorNeutralForegroundDisabled);}",".fdrzuqr{cursor:not-allowed;}",".f1qz2gb0{color:var(--colorBrandForegroundInverted);}"],i:[".f2hkw1w:focus-visible{outline-style:none;}"],h:[".f1tx3yz7:hover{text-decoration-line:underline;}",".f1deo86v:hover{color:var(--colorBrandForegroundLinkHover);}",".fnwyq0v:hover{color:var(--colorNeutralForeground2Hover);}",".fbnuktb:hover{text-decoration-line:none;}",".fvgxktp:hover{color:var(--colorNeutralForegroundDisabled);}",".f1mlt8il:hover{color:var(--colorBrandForegroundInvertedHover);}"],a:[".f1eh06m1:active{text-decoration-line:underline;}",".f1iescvh:active{color:var(--colorBrandForegroundLinkPressed);}",".flvvhsy:active{color:var(--colorNeutralForeground2Pressed);}",".fljg2da:active{text-decoration-line:none;}",".f19wldhg:active{color:var(--colorNeutralForegroundDisabled);}",".f1hsd4st:active{color:var(--colorBrandForegroundInvertedPressed);}"]}),Ts=vt.forwardRef(((e,t)=>{const i=((e,t)=>{const i=vt.useContext(ss),{appearance:o="default",disabled:r=!1,disabledFocusable:n=!1,inline:a=!1}=e,s=e.as||(e.href?"a":"button"),l=(c=gs({role:"span"===s?"button":void 0,type:"button"===s?"button":void 0},e),cs(c,ds({as:s})));var c;const d={appearance:o,disabled:r,disabledFocusable:n,inline:a,components:{root:s},root:ta(Ea(s,gs({ref:t},l)),{elementType:s}),backgroundAppearance:i};return(e=>{const{disabled:t,disabledFocusable:i}=e,{onClick:o,onKeyDown:r,role:n,tabIndex:a}=e.root;"a"===e.root.as&&(e.root.href=t?void 0:e.root.href,(t||i)&&(e.root.role=n||"link")),"a"!==e.root.as&&"span"!==e.root.as||(e.root.tabIndex=null!=a?a:t&&!i?void 0:0),e.root.onClick=e=>{t||i?e.preventDefault():null==o||o(e)},e.root.onKeyDown=e=>{!t&&!i||e.key!==Ce&&e.key!==ve?null==r||r(e):(e.preventDefault(),e.stopPropagation())},e.disabled=t||i,e.root["aria-disabled"]=t||i||void 0,"button"===e.root.as&&(e.root.disabled=t&&!i)})(d),d})(e,t);return(e=>{const t=Es(),{appearance:i,disabled:o,inline:r,root:n,backgroundAppearance:a}=e;e.root.className=is("fui-Link",t.root,t.focusIndicator,"a"===n.as&&n.href&&t.href,"button"===n.as&&t.button,"subtle"===i&&t.subtle,"inverted"===a&&t.inverted,r&&t.inline,o&&t.disabled,e.root.className)})(i),(e=>Ja(e.root,{}))(i)}));Ts.displayName="Link";pr({container:{...kn.container},header:{display:"flex",marginInlineStart:"22px",paddingBlockStart:"12px",paddingBlockEnd:"0px",width:"240px"},headerText:{width:"220px",fontWeight:"600",wordBreak:"break-word",marginInlineStart:"22px",lineHeight:"20px"},action:{...On.paddingBlock("8px"),marginInlineStart:"62px",width:"fit-content"},actionLink:{fontSize:"12px",lineHeight:"16px"}}),Array.isArray;var ms=Ve?Ve.prototype:void 0;ms&&ms.toString;pr({icon:{lineHeight:"20px",width:"20px",height:"19px",paddingBlockStart:"4px"}});(e=>{if(at.MAX/8<1)throw new Error("Invalid index 1, index should not be larger than "+at.MAX/8);at.FEATURE_PROMOTION_1_IMPRESSION_WITH_ATTRACTION,at.FEATURE_PROMOTION_1_ENGAGEMENT_WITH_ATTRACTION,at.FEATURE_PROMOTION_1_SNOOZE_WITH_ATTRACTION,at.FEATURE_PROMOTION_1_DISMISS_PERMANENTLY_WITH_ATTRACTION,at.FEATURE_PROMOTION_1_IMPRESSION_WITHOUT_ATTRACTION,at.FEATURE_PROMOTION_1_ENGAGEMENT_WITHOUT_ATTRACTION,at.FEATURE_PROMOTION_1_SNOOZE_WITHOUT_ATTRACTION,at.FEATURE_PROMOTION_1_DISMISS_PERMANENTLY_WITHOUT_ATTRACTION})(),Mt.rewardsPromotionTitle(),pr({container:{...kn.container,paddingBlockEnd:"4px",marginInlineEnd:"40px",display:"flex"},iconContainer:{paddingInlineStart:"14px",paddingInlineEnd:"12px",paddingBlockStart:"10px",flexShrink:0,flexGrow:0},icon:{width:"36px",height:"30px"},contentContainer:{display:"flex",flexDirection:"column",marginInlineEnd:"40px"},header:{paddingBlockStart:"12px",paddingBlockEnd:"0px"},headerText:{fontWeight:"600",lineHeight:"20px",fontSize:"14px"},description:{fontSize:"12px",lineHeight:"16px",paddingBlockStart:"8px",paddingBlockEnd:"8px"},action:{paddingBlockEnd:"4px"},actionButton:{...kn.actionButton,fontSize:"12px",lineHeight:"16px"}}),pr({container:{...kn.container,display:"flex"},iconContainer:{paddingBlockStart:"8px",paddingInlineStart:"24px",paddingInlineEnd:"20px",flexShrink:0,flexGrow:0},icon:{lineHeight:"20px",width:"20px",height:"20px"},contentContainer:{display:"flex",flexDirection:"column",marginInlineEnd:"44px"},header:{fontWeight:"600",wordBreak:"break-word",lineHeight:"20px",paddingBlockStart:"8px",paddingBlockEnd:"8px"},description:{fontSize:"12px",lineHeight:"16px"},action:{height:"20px",paddingBlockStart:"8px",paddingBlockEnd:"12px"},actionButton:{...kn.actionButton,fontSize:"12px",lineHeight:"20px"}}),pr({container:{...kn.container,display:"flex"},iconContainer:{paddingBlockStart:"8px",paddingInlineStart:"20px",paddingInlineEnd:"16px",flexShrink:0,flexGrow:0},iconBackground:{backgroundColor:"var(--colorPaletteGreenBackground1)",display:"flex",alignItems:"center",justifyContent:"center",height:"28px",width:"28px",...On.borderRadius("200px")},icon:{lineHeight:"20px",width:"20px",height:"20px",color:"var(--colorPaletteGreenForeground1)"},contentContainer:{display:"flex",flexDirection:"column",marginInlineEnd:"44px"},header:{fontWeight:"600",wordBreak:"break-word",lineHeight:"20px",paddingBlockStart:"8px",paddingBlockEnd:"8px"},description:{fontSize:"12px",lineHeight:"16px"},action:{minHeight:"20px",paddingBlockStart:"8px",paddingBlockEnd:"12px"},actionButton:{...kn.actionButton,fontSize:"12px",lineHeight:"20px"}}),pr({container:{...kn.container,display:"flex"},iconContainer:{paddingBlockStart:"8px",paddingInlineStart:"16px",paddingInlineEnd:"12px",flexShrink:0,flexGrow:0},icon:{lineHeight:"36px",width:"36px",height:"36px"},contentContainer:{height:"108px",display:"flex",flexDirection:"column",justifyContent:"space-between",marginInlineEnd:"44px"},header:{fontWeight:"600",wordBreak:"break-word",lineHeight:"20px",paddingBlockStart:"8px"},description:{fontSize:"12px",lineHeight:"16px",display:"-webkit-box",WebkitBoxOrient:"vertical",WebkitLineClamp:3,color:"var(--colorNeutralForeground3)",...On.overflow("hidden")},action:{height:"16px",paddingBlockEnd:"12px"},actionButton:{...kn.actionButton,fontSize:"12px",lineHeight:"16px",textDecorationLine:"none"}}),st.CardExpired,nt.CARD_EXPIRED_SNOOZE,st.CardExpiring,nt.CARD_EXPIRING_SOON_SNOOZE,st.RoamCard,nt.ROAM_CARD_SNOOZE,st.SignupCryptoWallet,nt.SIGN_UP_CRYPTOWALLET_SNOOZE,st.CardTokenizationEligible,nt.CARD_TOKENIZATION_ELIGIBLE_SNOOZE,st.PasswordLeakage,nt.PASSWORD_LEAKAGE_SNOOZE,st.PersonalizedOffersAvailable,nt.PERSONALIZED_OFFERS_AVAILABLE_SNOOZE,st.UpcomingHotelReservations,nt.UPCOMING_HOTEL_RESERVATION_SNOOZE,st.DonationSummary,nt.DONATION_SUMMARY_SNOOZE,st.FeaturePromotion,nt.FEATURE_PROMOTION_SNOOZE,st.PackageTracking,nt.PACKAGE_TRACKING_SNOOZE,st.Rebates,nt.REBATES_SNOOZE,st.EtreeCampaign,nt.ETREE_CAMPAIGN_SNOOZE,st.Etree,nt.ETREE_NORMAL_SNOOZE,st.PWAPromotion,nt.PWA_PROMOTION_SNOOZE,st.DonationTrendNpo,nt.DONATION_TREND_NPO_SNOOZE,st.CardExpired,nt.CARD_EXPIRED_DISMISS_PERMANENTLY,st.CardExpiring,nt.CARD_EXPIRING_SOON_DISMISS_PERMANENTLY,st.RoamCard,nt.ROAM_CARD_DISMISS_PERMANENTLY,st.SignupCryptoWallet,nt.SIGN_UP_CRYPTOWALLET_DISMISS_PERMANENTLY,st.CardTokenizationEligible,nt.CARD_TOKENIZATION_ELIGIBLE_DISMISS_PERMANENTLY,st.PasswordLeakage,nt.PASSWORD_LEAKAGE_DISMISS_PERMANENTLY,st.PersonalizedOffersAvailable,nt.PERSONALIZED_OFFERS_AVAILABLE_DISMISS_PERMANENTLY,st.UpcomingHotelReservations,nt.UPCOMING_HOTEL_RESERVATION_DISMISS_PERMANENTLY,st.DonationSummary,nt.DONATION_SUMMARY_DISMISS_PERMANENTLY,st.FeaturePromotion,nt.FEATURE_PROMOTION_DISMISS_PERMANENTLY,st.PackageTracking,nt.PACKAGE_TRACKING_DISMISS_PERMANENTLY,st.Rebates,nt.REBATES_DISMISS_PERMANENTLY,st.EtreeCampaign,nt.ETREE_CAMPAIGN_DISMISS_PERMANENTLY,st.Etree,nt.ETREE_NORMAL_DISMISS_PERMANENTLY,st.PWAPromotion,nt.PWA_PROMOTION_DISMISS_PERMANENTLY,st.DonationTrendNpo,nt.DONATION_TREND_NPO_DISMISS_PERMANENTLY,pr({dismiss:{zIndex:"1",position:"absolute",height:"20px",top:"16px",minWidth:"20px",width:"20px",right:"8px",...On.borderWidth("0"),...On.borderRadius("0"),...On.padding("0"),backgroundColor:"transparent","&:focus-visible":{...On.borderColor("var(--colorTransparentStroke)"),...On.borderRadius("var(--borderRadiusMedium)"),outlineColor:"var(--colorTransparentStroke)",outlineStyle:"solid",outlineWidth:"var(--strokeWidthThick)",boxShadow:"var(--shadow4),0 0 0 2px var(--colorStrokeFocus2)",zIndex:"1"}},topV2:{top:"11px"}}),pr({container:{width:"100%",backgroundColor:An.whiteBackground,[Cn]:{backgroundColor:An.darkNotificationBackground},userSelect:"none",textAlign:"start",paddingBlockStart:"36px",paddingBlockEnd:"28px"},content:{textAlign:"center"},icon:{paddingInlineEnd:"8px",verticalAlign:"bottom"},text:{display:"inline-block"},link:{textAlign:"center",paddingBlockStart:"8px"}});const Ns={width:"126px",textAlign:"start",display:"grid",gridTemplateColumns:"88px 12px"};pr({...kn,icon:{display:"inline-block",paddingInlineStart:"24px",paddingBlockStart:"14px"},errorIcon:{color:An.criticalPrimary,[Cn]:{color:An.RedAlertIconDarkMode}},warningIcon:{color:An.YellowAlertPrimary,[Cn]:{color:An.YellowAlertIconDarkMode}},header:{...kn.header,paddingBlockStart:"12px",paddingBlockEnd:"0px",width:"200px"},title:{fontWeight:"600",lineHeight:"20px",fontSize:"14px"},text:{...kn.text,fontSize:"12px",paddingInlineStart:"18px",paddingBlockStart:"8px",paddingBlockEnd:"0px",lineHeight:"16px",width:"200px"},expDate:{paddingBlockEnd:"12px",paddingInlineStart:"16px",width:"fit-content"},expDateSelector:{width:"126px",paddingInlineEnd:"12px",display:"inline-block"},label:{paddingBlockEnd:"4px",display:"inline-block"},menuButton:Ns,menuPopover:{minWidth:"116px",height:"64px",overflowY:"scroll"},errorMenuButton:{...Sn,...Ns},iconContainer:{display:"inline-block",verticalAlign:"top"},contentContainer:{display:"inline-block"},action:{...kn.action,width:"fit-content",paddingInlineStart:"18px",paddingBlockStart:"8px",paddingBlockEnd:"8px"},actionButton:{display:"inline-block",...kn.actionButton,fontSize:"12px"}}),pr({container:{...kn.container,display:"flex",flexDirection:"column",...On.paddingBlock("8px"),...On.gap("8px")},header:{...kn.header,display:"flex",paddingInlineStart:"0px",...On.paddingBlock("0px")},text:{...kn.text,paddingInlineStart:"62px",paddingInlineEnd:"36px",...On.paddingBlock("0px !important")},linkLarge:{...kn.linkLarge,fontSize:"12px",...On.paddingBlock("0px"),paddingInlineStart:"62px !important"},icon:{width:"62px",color:"var(--colorPaletteBlueForeground2)"},title:{width:"195px",fontWeight:"var(--fontWeightSemibold)"}}),vt.Component,pr({notificationDivider:{...On.borderTop("1px","solid","var(--colorNeutralStroke2)")}});const _s=xe("notificationTitle","Notification");function Os(e){return yt(e,_s)}const bs={miniWalletRewardsRowRewardsPointsBalance:Os({id:"miniWalletRewardsRowRewardsPointsBalance",description:"Message to indicate the Rewards points",defaultMessage:"$1 points"}),miniWalletHeaderPaymentMethodIconTooltip:Os({id:"miniWalletHeaderPaymentMethodIconTooltip",description:"Tooltip for the payment methods icon in the mini wallet header",defaultMessage:"Open payment methods"}),miniWalletHeaderPasswordsIconTooltip:Os({id:"miniWalletHeaderPasswordsIconTooltip",description:"Tooltip for the passwords icon in the mini wallet header",defaultMessage:"Open passwords"}),miniWalletHeaderPersonalInfoIconTooltip:Os({id:"miniWalletHeaderPersonalInfoIconTooltip",description:"Tooltip for the personal info icon in the mini wallet header",defaultMessage:"Open personal info"})};var Is;!function(e){e[e.kWalletCardImpression=0]="kWalletCardImpression",e[e.kWalletCardHeaderImpression=1]="kWalletCardHeaderImpression",e[e.kWalletCardHeaderClicked=2]="kWalletCardHeaderClicked",e[e.kWalletCardPaymentMethodsIconImpression=3]="kWalletCardPaymentMethodsIconImpression",e[e.kWalletCardPaymentMethodsIconClicked=4]="kWalletCardPaymentMethodsIconClicked",e[e.kWalletCardPasswordsIconImpression=5]="kWalletCardPasswordsIconImpression",e[e.kWalletCardPasswordsIconClicked=6]="kWalletCardPasswordsIconClicked",e[e.kWalletCardPersonalInfoIconImpression=7]="kWalletCardPersonalInfoIconImpression",e[e.kWalletCardPersonalInfoIconClicked=8]="kWalletCardPersonalInfoIconClicked",e[e.kWalletCardRewardsImpression=9]="kWalletCardRewardsImpression",e[e.kWalletCardRewardsImpression_FromCache=10]="kWalletCardRewardsImpression_FromCache",e[e.kWalletCardRewardsImpression_NoCache=11]="kWalletCardRewardsImpression_NoCache",e[e.kWalletCardRewardsClicked=12]="kWalletCardRewardsClicked",e[e.kWalletCardRewardsEarnLinkImpression=13]="kWalletCardRewardsEarnLinkImpression",e[e.kWalletCardRewardsRedeemLinkImpression=14]="kWalletCardRewardsRedeemLinkImpression",e[e.kWalletCardRewardsEarnLinkClicked=15]="kWalletCardRewardsEarnLinkClicked",e[e.kWalletCardRewardsRedeemLinkClicked=16]="kWalletCardRewardsRedeemLinkClicked",e[e.kWalletCardRewardsImpression_GiveMode=17]="kWalletCardRewardsImpression_GiveMode",e[e.kWalletCardRewardsClicked_GiveMode=18]="kWalletCardRewardsClicked_GiveMode",e[e.kWalletCardRewardsImpression_NonGiveMode=19]="kWalletCardRewardsImpression_NonGiveMode",e[e.kWalletCardRewardsClicked_NonGiveMode=20]="kWalletCardRewardsClicked_NonGiveMode",e[e.kWalletCardRewardsImpression_GiveWithOrg=21]="kWalletCardRewardsImpression_GiveWithOrg",e[e.kWalletCardRewardsClicked_GiveWithOrg=22]="kWalletCardRewardsClicked_GiveWithOrg",e[e.kWalletCardRewardsImpression_GiveWithoutOrg=23]="kWalletCardRewardsImpression_GiveWithoutOrg",e[e.kWalletCardRewardsClicked_GiveWithoutOrg=24]="kWalletCardRewardsClicked_GiveWithoutOrg",e[e.kWalletCardRebatesImpression=25]="kWalletCardRebatesImpression",e[e.kWalletCardRebatesImpression_FromCache=26]="kWalletCardRebatesImpression_FromCache",e[e.kWalletCardRebatesImpression_NoCache=27]="kWalletCardRebatesImpression_NoCache",e[e.kWalletCardRebatesValueClicked=28]="kWalletCardRebatesValueClicked",e[e.kWalletCardFallbackBundleUsed=29]="kWalletCardFallbackBundleUsed",e[e.kWalletCardWalletComponentBundleUsed=30]="kWalletCardWalletComponentBundleUsed",e[e.kWalletCardEtreeImpression=31]="kWalletCardEtreeImpression",e[e.kWalletCardEtreeImpression_FromCache=32]="kWalletCardEtreeImpression_FromCache",e[e.kWalletCardEtreeImpression_NoCache=33]="kWalletCardEtreeImpression_NoCache",e[e.kWalletCardEtreeLevelLinkImpression=34]="kWalletCardEtreeLevelLinkImpression",e[e.kWalletCardEtreeCertificateLinkImpression=35]="kWalletCardEtreeCertificateLinkImpression",e[e.kWalletCardEtreeEnrollLinkImpression=36]="kWalletCardEtreeEnrollLinkImpression",e[e.kWalletCardEtreeClicked=37]="kWalletCardEtreeClicked",e[e.kWalletCardEtreeClicked_Level=38]="kWalletCardEtreeClicked_Level",e[e.kWalletCardEtreeClicked_Certificate=39]="kWalletCardEtreeClicked_Certificate",e[e.kWalletCardEtreeClicked_Enroll=40]="kWalletCardEtreeClicked_Enroll",e[e.kMaxValue=41]="kMaxValue"}(Is||(Is={}));const As=e=>{const t=_s.valueExists("walletMiniWalletUsageEnumMaxValue")?parseInt(_s.getValue("walletMiniWalletUsageEnumMaxValue"),10)+1:Is.kMaxValue;e>=t&&console.warn("MiniWalletUsage enum passed in is larger than known size. Please check walletMiniWalletUsageEnumMaxValue."),e>=0&&e<t&&qe.recordEnumerationValue("Microsoft.Wallet.MiniWalletUsage",e,t)};var Cs;!function(e){e[e.WalletIcon=1]="WalletIcon",e[e.Rewards=2]="Rewards",e[e.Crypto=4]="Crypto",e[e.Notification=8]="Notification",e[e.PaymentMethods=16]="PaymentMethods",e[e.Passwords=32]="Passwords",e[e.PersonalInfo=64]="PersonalInfo",e[e.Rebates=128]="Rebates",e[e.Etree=256]="Etree",e[e.ALL=-1]="ALL"}(Cs||(Cs={}));const vs=e=>_s.valueExists(e)&&_s.getBoolean(e),Rs=()=>vs("isMiniWalletCashbackEnhancementEnabled");var ys;function Ss(e){return e===ys.WalletFirstTimeShow}function ws(){return _s.valueExists("mini_wallet_data")?_s.getValue("mini_wallet_data"):null}function ks(e){return null!=e&&!isNaN(e?.rewards_points)}function Ms(e){return!isNaN(e?.available_balance)&&(e?.available_balance>0||vs("isMiniWalletCashbackAlwaysOnEnabled"))}function xs(e){return null!=e}function Ps(e){return!0===e?.is_etree_user}function Ds(e){return e?.etree_level>=10}function Ls(e){return null!=e?{event_type:e.event_type,data:e.rewards_data?.rewards_data}:null}function Fs(e){return null!=e?{event_type:e.event_type,data:e.rewards_data?.rebates_data}:null}function Ws(e){return null!=e?{event_type:e.event_type,data:e?.etree_data}:null}function Bs(){return _s.valueExists("isTest")&&_s.getBoolean("isTest")}!function(e){e.WalletFirstTimeShow="mini_wallet_first_time_show",e.WalletContentsDataUpdate="mini_wallet_contents_data_update"}(ys||(ys={}));var Gs;!function(e){e[e.paymentMethods=0]="paymentMethods",e[e.passwords=1]="passwords",e[e.personalInfo=2]="personalInfo"}(Gs||(Gs={}));const Vs=I`
  .walletHeaderRow {
    width: 100%;
    height: 42px;
    padding-inline: 14px;
    box-sizing: border-box;
    display: grid;
    column-gap: 2px;
    grid-template-columns: 1fr auto auto auto;
    grid-template-areas: 'label payments-icon passwords-icon personal-info-icon';
  }

  .walletHeaderRow:hover,
  :focus {
    background-color: ${An.buttonBackgroundHover};
    outline: 0px none transparent !important;
  }

  .walletEntryIcon {
    padding: 4px;
    justify-self: center;
    align-self: center;
    border: none;
    background-color: transparent;
    color: inherit;
  }

  .walletEntryIcon:focus {
    outline: 2px solid #80868B !important;
    border-radius: 4px;
  }

  .walletEntryIcon:hover {
    background-color: ${An.buttonBackgroundDoubleHover};
    outline: 0 !important;
    border-radius: 4px;
  }

  @media (prefers-color-scheme: dark) {
    .walletHeaderRow:hover,
    :focus {
      background-color: ${An.darkModeButtonBackgroundHover};
      outline: 0px none transparent !important;
    }
    .walletEntryIcon:focus {
      outline: 2px solid background-color: ${An.darkButtonBackgroundDoubleHover} !important;
    }
    .walletEntryIcon:hover {
      background-color: ${An.darkButtonBackgroundDoubleHover};
    }
  }

  @media screen and (-ms-high-contrast: active) {
    .walletHeaderRow:hover,
    :focus {
      background-color: highlight;
      outline: 0;
    }
    .walletEntryIcon:hover,
    :focus {
      outline: 0;
      border: 2px solid buttonHighlight;
    }
  }

  .walletHeaderLabel {
    font-weight: 600;
    font-size: 14px;
    grid-area: label;
    justify-self: start;
    align-self: center;
  }

  .walletHeaderPaymentsIcon {
    grid-area: payments-icon;
  }

  .walletHeaderPasswordsIcon {
    grid-area: passwords-icon;
  }

  .walletHeaderPersonalInfoIcon {
    grid-area: personal-info-icon;
  }
`,Us=re`
  <div
    class="walletHeaderRow"
    tabindex="0"
    title="${_s.getString("miniWalletOpenWalletTooltip","Open Wallet")}"
    aria-label="${_s.getString("miniWalletOpenWalletTooltip","Open Wallet")}"
    role="button"
    @click=${e=>e.onHeaderClicked()}
    @keydown=${(e,t)=>e.onHeaderKeyDown(t.event)}
  >
    <label class="walletHeaderLabel">${_s.getString("miniWalletHeaderLabel","Wallet")}</label>
    ${le((e=>e.isPaymentMethodsIconVisbile),re`
        <button
          class="walletHeaderPaymentsIcon walletEntryIcon"
          title="${bs.miniWalletHeaderPaymentMethodIconTooltip()}"
          aria-label="${bs.miniWalletHeaderPaymentMethodIconTooltip()}"
          @click=${(e,t)=>e.onEntryIconClicked(Gs.paymentMethods,t.event)}
        >
          <svg width="20" height="20" viewBox="0 0 20 20">
            <path fillRule="nonzero" fill="currentColor" d="M13.5 13a.5.5 0 000 1h2a.5.5 0 000-1h-2z"></path>
            <path
              fillRule="nonzero"
              fill="currentColor"
              d="M2 6.5A2.5 2.5 0 014.5 4h11A2.5 2.5 0 0118 6.5v7a2.5 2.5 0 01-2.5 2.5h-11A2.5 2.5 0 012 13.5v-7zm1 7c0 .83.67 1.5 1.5 1.5h11c.83 0 1.5-.67 1.5-1.5V9H3v4.5zm14-7c0-.83-.67-1.5-1.5-1.5h-11C3.67 5 3 5.67 3 6.5V8h14V6.5z"
            ></path>
          </svg>
        </button>
      `)}
    ${le((e=>e.isPasswardIconVisbile),re`
        <button
          class="walletHeaderPasswordsIcon walletEntryIcon"
          title="${bs.miniWalletHeaderPasswordsIconTooltip()}"
          aria-label="${bs.miniWalletHeaderPasswordsIconTooltip()}"
          @click=${(e,t)=>e.onEntryIconClicked(Gs.passwords,t.event)}
        >
          <svg width="20" height="20" viewBox="0 0 20 20">
            <path fillRule="nonzero" fill="currentColor" d="M15 6a1 1 0 11-2 0 1 1 0 012 0z"></path>
            <path
              fillRule="nonzero"
              fill="currentColor"
              d="M12.5 2a5.45 5.45 0 00-5.38 6.67c.06.27 0 .5-.14.64l-4.54 4.54A1.5 1.5 0 002 14.91v1.59c0 .83.67 1.5 1.5 1.5h2c.83 0 1.5-.67 1.5-1.5V16h1a1 1 0 001-1v-1h1a1 1 0 001-1v-.18c.5.13 1 .18 1.5.18 3.08 0 5.5-2.42 5.5-5.5S15.58 2 12.5 2zM8 7.5C8 4.98 9.98 3 12.5 3S17 4.98 17 7.5 15.02 12 12.5 12c-.66 0-1.27-.1-1.78-.35a.5.5 0 00-.72.45v.9H9a1 1 0 00-1 1v1H7a1 1 0 00-1 1v.5a.5.5 0 01-.5.5h-2a.5.5 0 01-.5-.5v-1.59a.5.5 0 01.15-.35l4.54-4.54c.43-.43.52-1.04.4-1.56-.06-.3-.09-.63-.09-.96z"
            ></path>
          </svg>
        </button>
      `)}
    ${le((e=>e.isPersonalInfoVisbile),re`
        <button
          class="walletHeaderPersonalInfoIcon walletEntryIcon"
          title="${bs.miniWalletHeaderPersonalInfoIconTooltip()}"
          aria-label="${bs.miniWalletHeaderPersonalInfoIconTooltip()}"
          @click=${(e,t)=>e.onEntryIconClicked(Gs.personalInfo,t.event)}
        >
          <svg width="20" height="20" viewBox="0 0 24 24">
            <path
              fillRule="nonzero"
              fill="currentColor"
              d="M11 15c0-.35.06-.687.17-1H4.253a2.249 2.249 0 00-2.249 2.249v.578c0 .892.319 1.756.899 2.435 1.566 1.834 3.952 2.74 7.098 2.74.397 0 .783-.015 1.156-.044A2.998 2.998 0 0111 21v-.535c-.321.024-.655.036-1 .036-2.738 0-4.704-.746-5.958-2.213a2.25 2.25 0 01-.539-1.462v-.577c0-.414.336-.75.75-.75H11V15zM10 2.005a5 5 0 110 10 5 5 0 010-10zm0 1.5a3.5 3.5 0 100 7 3.5 3.5 0 000-7zM12 15a2 2 0 012-2h7a2 2 0 012 2v6a2 2 0 01-2 2h-7a2 2 0 01-2-2v-6zm2.5 1a.5.5 0 000 1h6a.5.5 0 000-1h-6zm0 3a.5.5 0 000 1h6a.5.5 0 000-1h-6z"
            ></path>
          </svg>
        </button>
      `)}
  </div>
`;let $s=class extends Ie{constructor(){super(),Bs()&&(this.content_flags=ws()?.content_flags),As(Is.kWalletCardHeaderImpression)}connectedCallback(){super.connectedCallback(),this.isPaymentMethodsIconVisbile&&As(Is.kWalletCardPaymentMethodsIconImpression),this.isPasswardIconVisbile&&As(Is.kWalletCardPasswordsIconImpression),this.isPersonalInfoVisbile&&As(Is.kWalletCardPersonalInfoIconImpression)}set content_flags(e){this.isPasswardIconVisbile=(e&Cs.Passwords)>0,this.isPaymentMethodsIconVisbile=(e&Cs.PaymentMethods)>0,this.isPersonalInfoVisbile=(e&Cs.PersonalInfo)>0}get isPasswardIconVisbile(){return R.track(this,"isPasswardIconVisbile"),this._isPasswardIconVisbile}set isPasswardIconVisbile(e){this._isPasswardIconVisbile=e,R.notify(this,"isPasswardIconVisbile")}get isPaymentMethodsIconVisbile(){return R.track(this,"isPaymentMethodsIconVisbile"),this._isPaymentMethodsIconVisbile}set isPaymentMethodsIconVisbile(e){this._isPaymentMethodsIconVisbile=e,R.notify(this,"isPaymentMethodsIconVisbile")}get isPersonalInfoVisbile(){return R.track(this,"isPersonalInfoVisbile"),this._isPersonalInfoVisbile}set isPersonalInfoVisbile(e){this._isPersonalInfoVisbile=e,R.notify(this,"isPersonalInfoVisbile")}onHeaderClicked(){As(Is.kWalletCardHeaderClicked),Dt.navigateToUrl(Ct(At(Re.WalletHub,"profileflyout"),"MiniWalletHeader"))}onHeaderKeyDown(e){return e.key!==Ce&&e.key!==ve||this.onHeaderClicked(),!0}onEntryIconClicked(e,t){switch(e){case Gs.paymentMethods:As(Is.kWalletCardPaymentMethodsIconClicked),Dt.navigateToUrl(Ct(At(Re.walletPaymentMethodsURL,"profileflyout"),"MiniWalletPaymentIcon"));break;case Gs.passwords:As(Is.kWalletCardPasswordsIconClicked),Dt.navigateToUrl(Ct(At(Re.walletPasswordsURL,"profileflyout"),"MiniWalletPasswordIcon"));break;case Gs.personalInfo:As(Is.kWalletCardPersonalInfoIconClicked),Dt.navigateToUrl(Ct(At(Re.walletPersonalInfoURL,"profileflyout"),"MiniWalletPersonalInfoIcon"))}As(Is.kWalletCardHeaderClicked),t.stopPropagation()}};$s=function(e,t,i,o){var r,n=arguments.length,a=n<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,i):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,i,o);else for(var s=e.length-1;s>=0;s--)(r=e[s])&&(a=(n<3?r(a):n>3?r(t,i,a):r(t,i))||a);return n>3&&a&&Object.defineProperty(t,i,a),a}([Ae({name:"wallet-header",template:Us,styles:Vs})],$s);const Hs=function(){};var zs=function(e,t,i,o){var r,n=arguments.length,a=n<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,i):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,i,o);else for(var s=e.length-1;s>=0;s--)(r=e[s])&&(a=(n<3?r(a):n>3?r(t,i,a):r(t,i))||a);return n>3&&a&&Object.defineProperty(t,i,a),a};const js=I`
  .dismissNotificationStyle {
    padding: 10px 14px;
    display: grid;
    grid-template-rows: auto auto;
    grid-template-areas:
      'icon'
      'content';
  }

  .dismissText {
    font-size: 12px;
    line-height: 16px;
  }

  .actionLink {
    font-weight: 400;
    color: #115ea3;
    text-decoration: none;
  }

  @media (prefers-color-scheme: dark) {
    .actionLink {
      color: #479ef5;
    }
  }

  .actionLink:hover,
  :focus {
    text-decoration: underline;
  }

  .dismiss {
    color: inherit;
    background-color: transparent;
    border: none;
    justify-self: center;
    align-self: center;
    padding: 4px;
  }

  .dismiss:hover,
  :focus {
    background-color: ${An.buttonBackgroundDoubleHover};
    border-radius: 4px;
  }

  @media (prefers-color-scheme: dark) {
    .dismiss:hover,
    :focus {
      background-color: ${An.darkButtonBackgroundDoubleHover};
    }
  }
`,Zs=re`
  <svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
    <path
      fill="currentColor"
      d="M5 11.5V8.05a5 5 0 016.36-4.87.5.5 0 10.27-.96A6 6 0 004 8v3.4l-.92 2.22A1 1 0 004 15h3.5a2.5 2.5 0 005 0H16a1 1 0 00.92-1.38L16 11.4V10a.5.5 0 00-1 0v1.5c0 .07.01.13.04.2L16 14H4l.96-2.3a.5.5 0 00.04-.2zM8.5 15h3a1.5 1.5 0 01-3 0zM14 2h3.5c.38 0 .6.4.45.71l-.04.08L14.96 7h2.54a.5.5 0 01.09 1H14a.5.5 0 01-.45-.71l.04-.08L16.54 3H14a.5.5 0 01-.09-1H14zM9.5 6H12c.4 0 .62.43.43.75l-.04.07L10.57 9H12a.5.5 0 01.1 1H9.5a.5.5 0 01-.43-.75l.05-.07L10.93 7H9.5a.5.5 0 01-.09-1h.1z"
    />
  </svg>
`,Ys=re`
  <svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
    <path
      fill="currentColor"
      d="M4 7.57c.04-.82.24-1.59.58-2.28L2.15 2.85a.5.5 0 11.7-.7l15 15a.5.5 0 01-.7.7L14.3 15h-1.8v.17a2.5 2.5 0 01-5 0V15H4a1 1 0 01-.26-.03l-.13-.04a1 1 0 01-.6-1.05l.02-.13.05-.13L4 11.4V7.57zM13.3 14L5.34 6.05a4.6 4.6 0 00-.32 1.33L5 7.6V11.5l-.04.2L4 14h9.3zm-1.8 1h-3v.14a1.5 1.5 0 001.36 1.34l.14.01c.78 0 1.42-.6 1.5-1.36V15zm3.54-3.32l.87 2.1.86.85c.15-.17.23-.4.23-.64v-.13l-.02-.08a1 1 0 00-.06-.17L16 11.4V7.58l-.02-.22A5.92 5.92 0 0010 2a6.1 6.1 0 00-4.21 1.66l.7.71A5.1 5.1 0 0110 3a4.9 4.9 0 015 4.6l.01.21v3.69l.04.2z"
    />
  </svg>
`,Ks=re`
  <div class="dismissNotificationStyle" role="group">
    <div style="grid-area:icon; text-align: right;">
      <button class="dismiss" @click=${e=>e.onCloseClick()}>
        <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
          <path
            fill="currentColor"
            d="M2.59 2.72l.06-.07a.5.5 0 01.63-.06l.07.06L8 7.29l4.65-4.64a.5.5 0 01.7.7L8.71 8l4.64 4.65c.18.17.2.44.06.63l-.06.07a.5.5 0 01-.63.06l-.07-.06L8 8.71l-4.65 4.64a.5.5 0 01-.7-.7L7.29 8 2.65 3.35a.5.5 0 01-.06-.63l.06-.07-.06.07z"
          />
        </svg>
      </button>
    </div>
    <div style="grid-area:content; text-align: center; margin-bottom: 20px;">
      <div>
        ${e=>e.dismissedPermanently?Ys:Zs}
        <label style="vertical-align: top; padding-left: 4px;">
          ${e=>e.dismissedPermanently?kt.msWalletNotificationDismissText():kt.msWalletNotificationSnoozeText()}
        </label>
      </div>
      ${le((e=>!e.dismissedPermanently),re`
          <div>
            <a href="#" class="actionLink" @click=${e=>e.onDismissClick()} data-test-id="close-permanently-link">
              ${kt.msWalletNotificationDismissPermanently()}
            </a>
          </div>
        `)}
    </div>
  </div>
`;let qs=class extends Ie{constructor(){super(),this._dismissedPermanently=!1,this.notificationType=this.getAttribute("notificationType"),this.dataKey=this.getAttribute("dataKey"),this.appName=this.getAttribute("appName")}get dismissedPermanently(){return R.track(this,"dismissedPermanently"),this._dismissedPermanently}set dismissedPermanently(e){this._dismissedPermanently=e,R.notify(this,"dismissedPermanently")}onDismissClick(){var e;this.dismissedPermanently=!0,e=this.dataKey,Dt.onDismiss(e),bt(Hn[this.notificationType][Gn.dismiss],this.appName)}onCloseClick(){this.shadowRoot.querySelector(".dismissNotificationStyle").remove()}};zs([he],qs.prototype,"notificationType",void 0),zs([he],qs.prototype,"dataKey",void 0),zs([he],qs.prototype,"appName",void 0),qs=zs([Ae({name:"dismiss-notification",template:Ks,styles:js})],qs);var Xs=function(e,t,i,o){var r,n=arguments.length,a=n<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,i):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,i,o);else for(var s=e.length-1;s>=0;s--)(r=e[s])&&(a=(n<3?r(a):n>3?r(t,i,a):r(t,i))||a);return n>3&&a&&Object.defineProperty(t,i,a),a};const Qs=I`
  .notificationRowStyle {
    padding: 10px 14px;
    display: grid;
    grid-template-columns: 36px 198px 16px;
    grid-template-rows: auto auto;
    grid-template-areas:
      'icon title dismiss'
      'icon content dismiss';
    column-gap: 10px;
  }

  .notificationRowStyle:hover,
  .notificationRowStyle:focus {
    background-color: ${An.buttonBackgroundHover};
    outline: 0;
    text-decoration: none !important;
  }

  @media (prefers-color-scheme: dark) {
    .notificationRowStyle:hover,
    :focus {
      background-color: ${An.darkModeButtonBackgroundHover};
      outline: 0;
    }
  }

  @media screen and (-ms-high-contrast: active) {
    .notificationRowStyle:hover,
    :focus {
      background-color: highlight;
    }
  }

  .noitificationIcon {
    grid-area: icon;
    display: inline-grid;
    padding-top: 2px;
    justify-content: center;
  }

  .errorIcon {
    fill: ${An.criticalPrimary};
  }

  @media (prefers-color-scheme: dark) {
    .errorIcon {
      fill: ${An.RedAlertIconDarkMode};
    }
  }

  .warningIcon {
    fill: ${An.YellowAlertPrimary};
  }

  @media (prefers-color-scheme: dark) {
    .warningIcon {
      fill: ${An.YellowAlertIconDarkMode};
    }
  }

  .blueIcon {
    fill: ${An.compoundBrandForegroundLight};
  }

  @media (prefers-color-scheme: dark) {
    .blueIcon {
      fill: ${An.compoundBrandForegroundDark};
    }
  }

  .notificationTitle {
    font-weight: 600;
    line-height: 20px;
    font-size: 14px;
    color: inherit;
  }

  .notificationText {
    font-size: 12px;
    line-height: 16px;
    padding-top: 8px;
    padding-bottom: 8px;
  }

  .actionLink {
    font-size: 12px;
    font-weight: 400;
    color: #115ea3;
    text-decoration: none;
  }

  @media (prefers-color-scheme: dark) {
    .actionLink {
      color: #479ef5;
    }
  }

  .actionLink:hover,
  .actionLink:focus {
    outline: 0;
    text-decoration: underline !important;
  }

  .dismiss {
    color: inherit;
    background-color: transparent;
    border: none;
    justify-self: center;
    align-self: center;
    padding: 4px;
  }

  .dismiss:focus {
    outline: 2px solid #80868b !important;
    border-radius: 4px;
  }

  .dismiss:hover {
    background-color: ${An.buttonBackgroundDoubleHover};
    border-radius: 4px;
    outline: 0;
  }

  @media (prefers-color-scheme: dark) {
    .dismiss:focus {
      outline: 2px solid background-color: ${An.darkButtonBackgroundDoubleHover} !important;
    }
    .dismiss:hover {
      background-color: ${An.darkButtonBackgroundDoubleHover};
    }
  }
`,Js=re`
  ${le((e=>!e.dismiss),re`
      <div
        class="notificationRowStyle"
        tabindex="0"
        @click=${(e,t)=>e.onActionAreaClick(t.event)}
        @keydown=${(e,t)=>e.onActionAreaKeyDown(t.event)}
      >
        <div class="${e=>(e.iconClassName||" ")+" noitificationIcon"}">
          <slot name="icon"></slot>
        </div>
        <div style="grid-area: title">
          <label class="notificationTitle">${e=>e.headerTitle}</label>
        </div>
        <div style="grid-area: content">
          <slot name="content">
            <div class="notificationText" id="content">${e=>e.content}</div>
          </slot>
          <div>
            <a class="actionLink" @click=${(e,t)=>e.onActionLinkClick(t.event)} href="#">${e=>e.actionText}</a>
          </div>
        </div>
        <div style="grid-area:dismiss; text-align: right;">
          <button
            class="dismiss"
            title="${kt.msWalletNotificationSnoozeLabel()}"
            aria-label="${kt.msWalletNotificationSnoozeLabel()}"
            @click=${(e,t)=>e.onDismissClick(t.event)}
          >
            <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
              <path
                fill="currentColor"
                d="M2.59 2.72l.06-.07a.5.5 0 01.63-.06l.07.06L8 7.29l4.65-4.64a.5.5 0 01.7.7L8.71 8l4.64 4.65c.18.17.2.44.06.63l-.06.07a.5.5 0 01-.63.06l-.07-.06L8 8.71l-4.65 4.64a.5.5 0 01-.7-.7L7.29 8 2.65 3.35a.5.5 0 01-.06-.63l.06-.07-.06.07z"
              />
            </svg>
          </button>
        </div>
      </div>
    `)}
  ${le((e=>e.dismiss),re`
      <dismiss-notification
        notificationType="${e=>e.notificationType}"
        dataKey="${e=>e.dataKey}"
      ></dismiss-notification>
    `)}
`;let el=class extends Ie{get dismiss(){return R.track(this,"dismiss"),this._dismiss}set dismiss(e){this._dismiss=e,R.notify(this,"dismiss")}constructor(){super(),this.onActionLinkClickExtended=Hs,this.notificationType=this.getAttribute("notificationType"),this.dataKey=this.getAttribute("dataKey"),this.headerTitle=this.getAttribute("headerTitle"),this.content=this.getAttribute("content"),this.actionUrl=this.getAttribute("actionUrl"),this.actionText=this.getAttribute("actionText"),this.appName=this.getAttribute("appName"),function(e,...t){["onActionLinkClickExtended"].forEach((t=>{let i=e[t];delete e[t],Object.defineProperty(e,t,{get:()=>(R.track(e,t),i),set(o){const r=e[t];i=o,R.notify(e,t);const n=e[t+"Changed"];n&&n.call(e,o,r)}})}))}(this)}connectedCallback(){super.connectedCallback(),bt(Hn[this.notificationType][Gn.impression],this.appName)}onActionAreaClick(e){bt(nt.NOTIFICATION_CARD_ENGAGEMENT_FROM_CONTENT,this.appName),this.onActionLinkClick(e)}onActionAreaKeyDown(e){return e.key!==Ce&&e.key!==ve||this.onActionAreaClick(e),!0}onActionLinkClick(e){var t;bt(Hn[this.notificationType][Gn.engagement],this.appName),t=this.dataKey,Dt.onApply(t),this.actionUrl&&Dt.navigateToUrl(this.actionUrl),this.onActionLinkClickExtended(),e.stopPropagation()}onDismissClick(e){var t;bt(Hn[this.notificationType][Gn.snooze],this.appName),t=this.dataKey,Dt.onSnooze(t),this.dismiss=!0,e.stopPropagation()}};Xs([he],el.prototype,"iconClassName",void 0),Xs([he],el.prototype,"notificationType",void 0),Xs([he],el.prototype,"dataKey",void 0),Xs([he],el.prototype,"headerTitle",void 0),Xs([he],el.prototype,"content",void 0),Xs([he],el.prototype,"actionUrl",void 0),Xs([he],el.prototype,"actionText",void 0),Xs([he],el.prototype,"appName",void 0),el=Xs([Ae({name:"base-notification",template:Js,styles:Qs})],el);const tl=re`
  <svg slot="icon" fill="inherit" width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M10 2a8 8 0 110 16 8 8 0 010-16zm0 10.5a.75.75 0 100 ********* 0 000-1.5zM10 6a.5.5 0 00-.5.41v4.68a.5.5 0 001 0V6.41A.5.5 0 0010 6z"
    />
  </svg>
`,il=re`
  <svg slot="icon" fill="inherit" width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M2.62235 18.5C2.40213 18.5 2.1932 18.4576 1.99556 18.3729C1.80358 18.2882 1.63135 18.1753 1.47889 18.0341C1.33207 17.8873 1.21349 17.7179 1.12315 17.5259C1.03844 17.3283 0.996094 17.1194 0.996094 16.8992C0.996094 16.6394 1.05256 16.3909 1.1655 16.1538L7.54347 3.39783C7.68464 3.11549 7.8851 2.89527 8.14485 2.73716C8.4046 2.57905 8.68976 2.5 9.00033 2.5C9.3109 2.5 9.59606 2.57905 9.85581 2.73716C10.1156 2.89527 10.316 3.11549 10.4572 3.39783L16.8352 16.1538C16.9481 16.3909 17.0046 16.6394 17.0046 16.8992C17.0046 17.1194 16.9594 17.3283 16.869 17.5259C16.7843 17.7179 16.6658 17.8873 16.5133 18.0341C16.3665 18.1753 16.1943 18.2882 15.9966 18.3729C15.8046 18.4576 15.5985 18.5 15.3783 18.5H2.62235ZM9.54241 12.6726V8.33589C9.54241 8.18908 9.48877 8.06203 9.38148 7.95474C9.27419 7.84745 9.14714 7.79381 9.00033 7.79381C8.85351 7.79381 8.72646 7.84745 8.61917 7.95474C8.51189 8.06203 8.45824 8.18908 8.45824 8.33589V12.6726C8.45824 12.825 8.50906 12.9549 8.6107 13.0622C8.71799 13.1638 8.84787 13.2147 9.00033 13.2147C9.15279 13.2147 9.27984 13.1638 9.38148 13.0622C9.48877 12.9549 9.54241 12.825 9.54241 12.6726ZM9.81346 14.8409C9.81346 14.7336 9.79087 14.632 9.7457 14.536C9.70617 14.4344 9.64688 14.3468 9.56782 14.2734C9.49442 14.1944 9.40689 14.1351 9.30525 14.0956C9.20926 14.0504 9.10762 14.0278 9.00033 14.0278C8.88739 14.0278 8.78011 14.0504 8.67846 14.0956C8.58247 14.1407 8.49777 14.2 8.42436 14.2734C8.35095 14.3468 8.29166 14.4344 8.24649 14.536C8.20696 14.6376 8.1872 14.7421 8.1872 14.8494C8.1872 14.9623 8.20696 15.0696 8.24649 15.1713C8.29166 15.2672 8.35095 15.3519 8.42436 15.4254C8.50342 15.4931 8.59094 15.5496 8.68693 15.5948C8.78293 15.6343 8.88739 15.654 9.00033 15.654C9.2262 15.654 9.41819 15.575 9.57629 15.4169C9.7344 15.2588 9.81346 15.0668 9.81346 14.8409Z"
    />
  </svg>
`;var ol,rl,nl,al;!function(e){e[e.PROGRAMMATIC=0]="PROGRAMMATIC",e[e.USER_ACTION=1]="USER_ACTION"}(ol||(ol={})),(al=rl||(rl={})).Tokenization="Tokenization",al.RoamCard="RoamCard",al.ErrorHandling="ErrorHandling",al.Unknown="Unknown",function(e){e.isWalletBNPLAffirmAdaptiveCheckoutEnabled="isWalletBNPLAffirmAdaptiveCheckoutEnabled",e.isTokenizationEnrollRewardsEnabled="isTokenizationEnrollRewardsEnabled",e.isWalletTokenizedAutofillEnabled="isWalletTokenizedAutofillEnabled",e.creditCardUploadEnabled="creditCardUploadEnabled",e.creditCardGlobalizationEnabled="creditCardGlobalizationEnabled",e.creditCardBetterStateTranslationEnabled="creditCardBetterStateTranslationEnabled",e.creditCardSyncCardEnabled="creditCardSyncCardEnabled",e.cardUploadErrorHandling="cardUploadErrorHandling",e.isCardRoamingLocalSaveCvvEnabled="isCardRoamingLocalSaveCvvV2Enabled",e.isCardRoamingAlwaysVerifyEnabled="isCardRoamingAlwaysVerifyEnabled",e.isCreditCardSilentUploadEnabled="creditCardSilentUpload",e.isCreditCardAutoSaveEnabled="creditCardAutoSaveEnabled",e.isWalletPartialCardEnabled="isWalletPartialCardEnabled",e.isWalletHubUXReskinEnabled="isWalletHubUXReskinEnabled",e.isWalletHubLayoutRefreshEnabled="isWalletHubLayoutRefreshEnabled",e.isWalletTokenizationCardMetadataEnabled="isWalletTokenizationCardMetadataEnabled",e.isBNPLFeedbackEnabled="isBNPLFeedbackEnabled",e.isCardRoamingSupportCrossRegionV2Enabled="isCardRoamingSupportCrossRegionV2Enabled",e.isCardRoamingSupportCrossRegionV3Enabled="isCardRoamingSupportCrossRegionV3Enabled"}(nl||(nl={}));const sl=xe("walletTitle","Wallet"),ll=(e,t,i,o,r=Dn.Enroll)=>{if(n=nl.isWalletTokenizedAutofillEnabled,!sl.valueExists(n)||!sl.getBoolean(n))return;var n;if(!(e&&t&&r&&i))return;const a=sl.valueExists("tokenizationFunnelEnumMaxValue")?parseInt(sl.getValue("tokenizationFunnelEnumMaxValue"),10)+1:-1;if(null!==o&&o>=0&&o<=a&&!isNaN(a)){const n=r===Dn.Autofill?`${xn.TokenizationEnrollActions}.${r}.${e}.${i}`:`${xn.TokenizationEnrollActions}.${r}.${e}.${i}.${t}`;qe.recordEnumerationValue(n,o,a)}};function cl(e,t){const i=e.data,o=!0===e.previewData?.isLocal||!0===i?.metadata?.isLocal,r=e.previewData?.guid??i?.guid,n=kt.msWalletNotificationCardTokenizationEligibleHeader(),a=kt.msWalletNotificationCardTokenizationEligibleIntroduction(),s=((e,t)=>Ct(At(`edge://wallet${((e,t)=>e?`/paymentMethods/cardDetails/edit#paymentInstrumentId=${t}&from=${Ln.ProfileNotification}&usage=${rl.Tokenization}`:`/paymentMethods#paymentInstrumentId=${t}&from=${Ln.ProfileNotification}&usage=${rl.Tokenization}`)(e,t)}`),"CardTokenizationEligible"))(o,r),l=function(e){const t=`${(e=>e.cardType??e.network)(e)} ****${ot(e?.cardNumber)}`;return kt.msWalletNotificationCardTokenizationEligibleSetUpText([t])}(e.previewData);var c;return ll(Ln.ProfileNotification,(e=>e?(e=>!0===e.metadata?.isLocal)(e)?Fn.Local:(e=>!0===e?.metadata?.isPartialCard)(e)?Fn.Partial:(e=>!1===e?.metadata?.isLocal)(e)?Fn.Server:Fn.Unknown:Fn.Unknown)(i),(c=i?.network,Wn[c?.toUpperCase()]??Wn.UNKNOWN),Bn.CARD_ELIGIBLE,Dn.Enroll),re`<base-notification
    notificationType="${e.type}"
    dataKey="${e.dataKey}"
    headerTitle="${n}"
    content="${a}"
    actionUrl="${s}"
    actionText="${l}"
    appName="${t}"
  >
    <svg slot="icon" width="28" height="28" viewBox="0 0 28 28" fill="none">
      <rect width="28" height="28" rx="14" fill="#FDE300"></rect>
      <path
        fill-rule="evenodd"
        clip-path="evenodd"
        d="M6.53325 9.13302C6.23875 9.13302 6 9.37177 6 9.66627V13.3991C6 16.9549 8.10284 19.5675 12.2036 21.183C12.3292 21.2325 12.4689 21.2325 12.5945 21.183C13.2001 20.9444 13.7622 20.6841 14.2803 20.4022C13.5496 19.5225 13.1103 18.3922 13.1103 17.1593C13.1103 14.3545 15.3841 12.0807 18.1889 12.0807C18.3951 12.0807 18.5984 12.093 18.7981 12.1169V9.66627C18.7981 9.37177 18.5594 9.13302 18.2649 9.13302C16.3712 9.13302 14.5264 8.4622 12.719 7.10665C12.5294 6.96445 12.2687 6.96445 12.0791 7.10665C10.2717 8.4622 8.42695 9.13302 6.53325 9.13302Z"
        fill="black"
      ></path>
      <path
        fill-rule="evenodd"
        clip-path="evenodd"
        d="M22.2494 17.1589C22.2494 14.9154 20.4307 13.0967 18.1872 13.0967C15.9437 13.0967 14.125 14.9154 14.125 17.1589C14.125 19.4024 15.9437 21.2211 18.1872 21.2211C20.4307 21.2211 22.2494 19.4024 22.2494 17.1589ZM21.0296 17.1583C21.0296 16.7657 20.7113 16.4474 20.3186 16.4474L16.0531 16.4474C15.6605 16.4474 15.3422 16.7657 15.3422 17.1583C15.3422 17.5509 15.6605 17.8692 16.0531 17.8692L20.3186 17.8692C20.7113 17.8692 21.0296 17.5509 21.0296 17.1583Z"
        fill="black"
      ></path>
    </svg>
  </base-notification>`}var dl=function(e,t,i,o){var r,n=arguments.length,a=n<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,i):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,i,o);else for(var s=e.length-1;s>=0;s--)(r=e[s])&&(a=(n<3?r(a):n>3?r(t,i,a):r(t,i))||a);return n>3&&a&&Object.defineProperty(t,i,a),a};const ul=I`
  .image {
    max-width: 36px;
    max-height: 36px;
    width: auto;
    height: auto;
    object-fit: contain;
  }

  .cashbackShape {
    height: 20px;
    display: inline-block;
    padding: 0px;
    position: relative;
    background: ${"#3267FA"};
    border-radius: 4px;
    margin-inline-end: 10px;
    margin-block-start: 2px;
    color: ${An.whiteBackground};
  }

  .notificationText {
    font-size: 12px;
    padding-bottom: 8px;
  }

  .cashbackShape::before {
    top: 10%;
    right: -6px;
    width: 16px;
    height: 16px;
    content: '';
    position: absolute;
    transform: rotate(-45deg);
    background: inherit;
    border-radius: 4px;
  }

  .cashbackPadding {
    padding-inline-end: 5px;
    padding-inline-start: 5px;
    padding-block-start: 1px;
    position: relative;
  }

  .expireText {
    display: inline-block;
    color: ${An.redForeground};
  }

  @media (prefers-color-scheme: dark) {
    .expireText {
      color: ${An.personalizedOfferTextColorDark};
    }
  }
`,pl=re`
  <base-notification
    notificationType="${e=>e.notificationType}"
    dataKey="${e=>e.dataKey}"
    content="${e=>e.content}"
    actionUrl="${e=>e.actionUrl}"
    actionText="${e=>e.actionText}"
    appName="${e=>e.appName}"
  >
    <img slot="icon" class="image" src="${e=>`edge://image?${e.imageUrl}`}" />
    <div slot="content" class="notificationText">
      ${e=>re`${kt.msWalletNotificationPOMessage([`<div class="cashbackShape">\n            <div class="cashbackPadding">\n              <span>$</span>\n              <span>${e.rebateValue}</span>\n            </div>\n          </div>`,`<div class="expireText">${e.expireStr}</div>`])} `}
    </div>
  </base-notification>
`;let hl=class extends Ie{padNumber(e){return e<10?`0${e}`:`${e}`}connectedCallback(){super.connectedCallback(),this.notificationType=this.getAttribute("notificationType");const e=Date.now()/1e3,t=this.expiration-e;if(t>0){const e=Math.floor(t/3600),i=Math.floor((t-3600*e)/60);this.expireStr=`${this.padNumber(e)}h:${this.padNumber(i)}m`}else this.expireStr="00h:00m"}};dl([y],hl.prototype,"dismiss",void 0),dl([he],hl.prototype,"notificationType",void 0),dl([he],hl.prototype,"dataKey",void 0),dl([he],hl.prototype,"actionUrl",void 0),dl([he],hl.prototype,"actionText",void 0),dl([he],hl.prototype,"expiration",void 0),dl([he],hl.prototype,"rebateValue",void 0),dl([he],hl.prototype,"imageUrl",void 0),dl([he],hl.prototype,"appName",void 0),dl([y],hl.prototype,"expireStr",void 0),hl=dl([Ae({name:"personalized-offers-notification",template:pl,styles:ul})],hl);const fl=I`
  .icon-background {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: ${An.donationIconBackgroundLight};
    padding-block-start: 10px;
    padding-block-end: 6px;
    padding-inline-start: 5px;
    padding-inline-end: 6px;
  }

  .icon-container {
    padding-inline-start: 3px;
    padding-block-start: 3px;
  }

  @media (prefers-color-scheme: dark) {
    .icon-background {
      background-color: ${An.donationIconBackgroundDark};
    }
  }
`,gl=re`
  <span class="icon-background">
    <svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg" class="icon-container">
      <path
        d="M10 6.5V2H5.5C4.67 2 4 2.67 4 3.5V9c.7.03 1.4.25 2 .66a3.85 3.85 0 014.88 5.91L8.45 18h6.05c.83 0 1.5-.67 1.5-1.5V8h-4.5A1.5 1.5 0 0110 6.5zm1 0V2.25L15.75 7H11.5a.5.5 0 01-.5-.5zm-4.86 4.33a2.85 2.85 0 114.03 4.04l-3.82 3.81a.5.5 0 01-.7 0l-3.82-3.81a2.85 2.85 0 114.03-4.04l.14.14.14-.14zm4.03 4.04l-.36-.36z"
      />
    </svg>
  </span>
`;let El=class extends Ie{};var Tl,ml,Nl;El=function(e,t,i,o){var r,n=arguments.length,a=n<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,i):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,i,o);else for(var s=e.length-1;s>=0;s--)(r=e[s])&&(a=(n<3?r(a):n>3?r(t,i,a):r(t,i))||a);return n>3&&a&&Object.defineProperty(t,i,a),a}([Ae({name:"donation-icon",template:gl,styles:fl})],El),(Nl=Tl||(Tl={})).AddCreditCardPref="autofill.credit_card_enabled",Nl.TokenizationNumRewardsUsed="autofill.tokenization_number_rewards_used",Nl.MSPayCreditCardEnabled="autofill.mspay_credit_card_enabled",Nl.AutofillOfferToSyncCardEnabled="autofill.offer_to_sync_card_enabled",Nl.ProfileEnabled="autofill.profile_enabled",Nl.AutofillInfoEnabled="autofill.autostuff_enabled",Nl.SaveCreditCardCvvLocally="autofill.save_credit_card_cvv_locally",Nl.RoamingCardSilentlyEnabled="autofill.roaming_card_silently_enabled",Nl.MultiFactorAuthToFill="edge_wallet.multi_factor_auth_to_fill",Nl.MembershipsAutofillEnabled="autofill.memberships_enabled",Nl.PCToMobileSectionDismissed="edge_wallet.pc_to_mobile_section_dismissed",Nl.EnabledFeedbackPref="edge.feedback_allowed",Nl.ShowRewards="edge_rewards.show",Nl.EdgeWalletBNPLEnabled="edge_wallet_bnpl_enabled",Nl.ShowWalletIntroPref="edge_wallet.show_wallet_intro_section",Nl.WalletCheckoutEnabled="edge_wallet.wallet_checkout_enabled",Nl.ShoppingAssistantPref="edge_shopping_assistant_enabled",Nl.ShowOffersIntroPref="edge_wallet.show_wallet_offers_intro",Nl.ShowNewInTickets="edge_wallet.show_new_in_tickets",Nl.ShowTickets="edge_wallet.show_wallet_tickets",Nl.EdgeTippingAssistantEnabled="edge_tipping_assistant_enabled",Nl.ShoppingPackageTrackingUserConsent="shopping.package_tracking_user_consented",Nl.CredentialsEnableService="credentials_enable_service",Nl.CredentialsEnableAutofillPasswords="credentials_enable_autofill_passwords",Nl.CredentialsEnableBreachDetection="credentials_enable_breachdetection",Nl.PasswordBreachScanned="profile.password_breach_scanned",Nl.PasswordBreachLastScannedTime="profile.password_breach_last_scanned_time",Nl.PasswordBreachScanTriggeredCount="profile.password_breach_scan_triggered_count",Nl.PasswordBreachScanTriggeredPasswordCount="profile.password_breach_scan_triggered_password_count",Nl.PasswordRestrictLength="profile.edge_password_restrict_length",Nl.CredentialsEnablePasswordGeneration="credentials_enable_passwordgeneration",Nl.CredentialsEnablePasswordReveal="credentials_enable_passwordreveal",Nl.MasterPasswordAuthCriteria="profile.master_password_auth_criteria",Nl.MasterPasswordAuthFrequency="profile.master_password_auth_frequency",Nl.CredentailsEnableAutoSave="credentials_enable_autosave",Nl.CustomPrimaryPasswordHash="profile.custom_primary_password_hash",Nl.FeaturePromotionDict="edge_wallet.notification.featurePromotion.promotionDict",Nl.SettingsMigration="edge_wallet.use_as_default_t2",Nl.SettingsMigrationDismissed="edge_wallet.use_as_default_t2_dismissed",Nl.ShowWalletETree="edge_wallet.show_wallet_e_tree",Nl.WalletETreeEnabled="edge_wallet.wallet_etree_enabled",Nl.EtreeNotificationLastShow="edge_wallet.etree_notification_last_show",Nl.IsSignedInProfile="edge_wallet.isSignedInProfile",Nl.HomeFreDismissed="edge_wallet.home.fre.dismissed",Nl.HomeFrePaymentsStepCompleted="edge_wallet.home.fre.payments_step_completed",Nl.HomeFreMembershipsStepCompleted="edge_wallet.home.fre.memberships_step_completed",Nl.HomeFrePasswordsStepCompleted="edge_wallet.home.fre.passwords_step_completed",Nl.HomeFreRewardsStepCompleted="edge_wallet.home.fre.rewards_step_completed",Nl.HomeFreSavingsStepCompleted="edge_wallet.home.fre.savings_step_completed",Nl.HomeFreSigninStepCompleted="edge_wallet.home.fre.signin_step_completed",Nl.HomeFreAutosaveStepCompleted="edge_wallet.home.fre.autosave_step_completed",Nl.CryptoWalletUserEnabled="edge_wallet.crypto_wallet_user_enabled",Nl.CryptoUserFREState="edge_wallet.crypto_user_fre_state",Nl.CryptoWalletSuspended="edge_wallet.a0b1b27987ba331be1a8f2cd0d74c615c3d35ec3f298ad69506bbe21d5cb71d7",Nl.PWAPromotionDialogShownTimes="edge_wallet.pwa_promotion.dialog_shown_times",Nl.PWAPromotionDialogNextTime="edge_wallet.pwa_promotion.dialog_next_time",Nl.PWAPromotionRewardsLastReportTime="edge_wallet.pwa_promotion.rewards_last_report_time",Nl.PWAPromotionNotificationCompleted="edge_wallet.notification.pwa_promotion_completed",function(e){e.history="import_dialog_history",e.favorites="import_dialog_bookmarks",e.passwords="import_dialog_saved_passwords",e.search="import_dialog_search_engine",e.autofillFormData="import_dialog_autofill_form_data",e.payments="import_dialog_payment_info",e.cookies="import_dialog_cookies",e.homePage="import_dialog_homepage",e.settings="import_dialog_browser_settings",e.openTabs="import_dialog_open_tabs",e.extensions="import_dialog_browser_extensions"}(ml||(ml={}));var _l,Ol=function(e,t,i,o){var r,n=arguments.length,a=n<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,i):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,i,o);else for(var s=e.length-1;s>=0;s--)(r=e[s])&&(a=(n<3?r(a):n>3?r(t,i,a):r(t,i))||a);return n>3&&a&&Object.defineProperty(t,i,a),a};!function(e){e.TrackOrder="TrackOrder",e.ViewOrder="ViewOrder"}(_l||(_l={}));const bl=I`
  .packageTrackingIcon {
    fill: ${An.packageTrackingIconLight};
  }

  @media (prefers-color-scheme: dark) {
    .packageTrackingIcon {
      fill: ${An.packageTrackingIconDark};
    }
  }
`,Il=re`<base-notification
  notificationType="${e=>e.notificationType}"
  dataKey="${e=>e.dataKey}"
  headerTitle="${e=>e.headerTitle}"
  content="${e=>e.content}"
  actionUrl="${e=>e.actionUrl}"
  actionText="${e=>e.actionText}"
  appName="${e=>e.appName}"
  :onActionLinkClickExtended=${e=>e.onActionLinkClickExtended}
>
  <svg
    slot="icon"
    class="packageTrackingIcon"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M1.75 4.5a.75.75 0 0 0-.75.75v6c0 1.24 1 2.25 2.25 2.25h5c1.24 0 2.25-1 2.25-2.25v-6a.75.75 0 0 0-.75-.75H9.5v-1a2.5 2.5 0 0 0-3.75-2.17A2.5 2.5 0 0 0 2 3.5v1h-.25ZM7 4.5v-1c0-.35-.07-.68-.2-.98A1 1 0 0 1 8 3.5v1H7Zm-1.5-1v1h-2v-1a1 1 0 0 1 2 0ZM2 17.25v-3c.38.16.8.25 1.25.25h.25v2.75c0 .39.3.7.67.75a3 3 0 0 1 5.66 0h2.34A3 3 0 0 1 15 16V5.25a.75.75 0 0 0-.75-.75h-2.92a1.76 1.76 0 0 0-.83-.83V3.5c0-.17-.01-.34-.04-.5h3.79c1.24 0 2.25 1 2.25 2.25V6h1.55c.87 0 1.66.5 2.03 1.29l1.7 3.58c.15.3.22.63.22.97v5.41c0 1.24-1 2.25-2.25 2.25h-1.8a3 3 0 0 1-5.9 0h-2.1a3 3 0 0 1-5.91-.01A2.25 2.25 0 0 1 2 17.25Zm15.83.75h1.92c.41 0 .75-.34.75-.75V17h-1.25a.75.75 0 0 1 0-1.5h1.25v-3h-4v3.9c.61.36 1.09.92 1.33 1.6ZM16.5 7.5V11h3.68l-1.45-3.07a.75.75 0 0 0-.68-.43H16.5ZM7 20.5a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Zm9.5-1.5a1.5 1.5 0 1 0-3 0 1.5 1.5 0 0 0 3 0ZM3.5 3.5a1 1 0 0 1 .08-.4Z"
    ></path>
  </svg>
</base-notification>`;let Al=class extends Ie{constructor(){super(...arguments),this.onActionLinkClickExtended=()=>{var e;this.subType===_l.ViewOrder?bt(nt.PACKAGE_TRACKING_VIEW_ORDER_ENGAGEMENT,this.appName):this.subType===_l.TrackOrder&&(bt(nt.PACKAGE_TRACKING_TRACK_ORDER_ENGAGEMENT,this.appName),e=Tl.ShoppingPackageTrackingUserConsent,!0,Bt.setPref(e,true))}}};function Cl(e){return yt(e,sl)}Ol([he],Al.prototype,"notificationType",void 0),Ol([he],Al.prototype,"dataKey",void 0),Ol([he],Al.prototype,"headerTitle",void 0),Ol([he],Al.prototype,"content",void 0),Ol([he],Al.prototype,"actionUrl",void 0),Ol([he],Al.prototype,"actionText",void 0),Ol([he],Al.prototype,"subType",void 0),Ol([he],Al.prototype,"appName",void 0),Al=Ol([Ae({name:"package-tracking",styles:bl,template:Il})],Al);const vl={cancel:Cl({id:"cancel",defaultMessage:"Cancel",description:"Text of the cancel button"}),dismiss:Cl({id:"dismiss",defaultMessage:"Dismiss",description:"Text of the dismiss button"}),learnMore:Cl({id:"learnMore",defaultMessage:"Learn more",description:"Text of the learn more button"}),close:Cl({id:"close",defaultMessage:"Close",description:"Text of the Close button"}),textOfCreateButton:Cl({id:"textOfCreateButton",description:"Text for create button",defaultMessage:"Create"}),textOfChangeButton:Cl({id:"textOfChangeButton",description:"Text for change button",defaultMessage:"Change"}),tokenizeCardEnterCode:Cl({id:"tokenizeCardEnterCode",defaultMessage:"Enter code",description:"Label for enter verification code field"}),tokenizeCardVerify:Cl({id:"tokenizeCardVerify",defaultMessage:"Verify",description:"Label for the verify button"}),tokenizeCardChallengeMethodAPPToAPP:Cl({id:"tokenizeCardChallengeMethodAPPToAPP",defaultMessage:"Mobile banking app",description:"Simple description for the verify via banking app option"}),tokenizeCardChallengeMethodCustomerService:Cl({id:"tokenizeCardChallengeMethodCustomerService",defaultMessage:"Bank customer service",description:"Simple description for the verify via customer service option"}),tokenizeCardChallengeMethodEmail:Cl({id:"tokenizeCardChallengeMethodEmail",defaultMessage:"Email",description:"Simple description for the verify via email option"}),tokenizeCardChallengeMethodOnlineBanking:Cl({id:"tokenizeCardChallengeMethodOnlineBanking",defaultMessage:"Bank account",description:"Simple description for the verify via online banking option"}),tokenizeCardChallengeMethodOutboundCall:Cl({id:"tokenizeCardChallengeMethodOutboundCall",defaultMessage:"Phone call",description:"Simple description for the verify via phone call option"}),tokenizeCardChallengeMethodSMS:Cl({id:"tokenizeCardChallengeMethodSMS",defaultMessage:"Text message",description:"Simple description for the verify via SMS option"}),tokenizeCardSelectMethodDescription:Cl({id:"tokenizeCardSelectMethodDescription",defaultMessage:"Choose one of the following verification methods:",description:"Description for choose a new method"}),tokenizeCardBack:Cl({id:"tokenizeCardBack",defaultMessage:"Back",description:"Label for the Back button"}),tokenizeCardNext:Cl({id:"tokenizeCardNext",defaultMessage:"Next",description:"Label for the Next button"}),tokenizeCardEnterCodeStepTitle:Cl({id:"tokenizeCardEnterCodeStepTitle",defaultMessage:"Let's get you verified",description:"Title for the enter verification code step"}),tokenizeCardSelectMethodStepTitle:Cl({id:"tokenizeCardSelectMethodStepTitle",defaultMessage:"Let's get you verified",description:"Title for the select method code step"}),tokenizeCardErrorStepTitle:Cl({id:"tokenizeCardErrorStepTitle",defaultMessage:"Something went wrong",description:"Title for the error step"}),tokenizeCardLoadingTitle:Cl({id:"tokenizeCardLoadingTitle",defaultMessage:"Contacting your bank…",description:"Title for the loading step"}),tokenizeCardEnterCvvStepDescription:Cl({id:"tokenizeCardEnterCvvStepDescription",defaultMessage:"Your bank requires you to verify the CVV security code associated with your $1 ending in $2.",description:"Description shows in CVV verification dialog"}),tokenizeCardCvvInvalidError:Cl({id:"tokenizeCardCvvInvalidError",defaultMessage:"Please verify your security code and try again.",description:"Error message shows when cvv is invalid"}),tokenizeCardGeneralError:Cl({id:"tokenizeCardGeneralError",defaultMessage:"Your card issuer is having issues at the moment, please try again later.",description:"Error message for the generate card error"}),tokenizationEnroll:Cl({id:"tokenizationEnroll",description:"Label for the confirmation button in verification dialog",defaultMessage:"Enroll"}),tokenizationEnrollConfirmDialogTitle:Cl({id:"tokenizationEnrollConfirmDialogTitle",defaultMessage:"Pay safely with a virtual card",description:"Title for the enroll confirmation dialog"}),tokenizationEnrollConfirmDialogTitleForRewards:Cl({id:"tokenizationEnrollConfirmDialogTitleForRewards",defaultMessage:"Set up a virtual card and earn 20 Microsoft Rewards points",description:"Title for the enroll confirmation dialog for rewards incentive"}),tokenizationEnrollConfirmModalDescription:Cl({id:"tokenizationEnrollConfirmModalDescription",description:"Description for the enroll confirm dialog",defaultMessage:"A virtual card hides your card details from merchants when you shop online. If any of them experiences a data breach, your details remain protected. $1"}),tokenizationEnrollConfirmFooterDescription:Cl({id:"tokenizationEnrollConfirmFooterDescription",description:"Description for the enroll confirm footer",defaultMessage:"Your card issuer may send a verification code or request the card's security code to verify it's you."}),tokenizationTerms:Cl({id:"tokenizationTerms",defaultMessage:"By continuing, you agree to the $1.",description:"Terms description shows in tokenization dialog"}),tokenizeCardFetchCodeError:Cl({id:"tokenizeCardFetchCodeError",defaultMessage:"Your card issuer had an issue sending the code. Request another code or use a different verification method.",description:"Error message shows when sending verification code failed"}),tokenizeCardFetchCodeErrorCustomerService:Cl({id:"tokenizeCardFetchCodeErrorCustomerService",defaultMessage:"Your card issuer had an issue generating the code. Please try a different verification method or try again later.",description:"Error message shows when issuer cannot generate verification code"}),manageYourPaymentMethodsLinkText:Cl({id:"manageYourPaymentMethodsLinkText",defaultMessage:"Manage your payment methods",description:"the link text for wallet page"}),walletDrawerLabelShowCardNumber:Cl({id:"walletDrawerLabelShowCardNumber",defaultMessage:"Show card number",description:"The label for the button to show card number"}),walletDrawerLabelCopyCardNumber:Cl({id:"walletDrawerLabelCopyCardNumber",defaultMessage:"Copy card number",description:"The label for the button to copy card number"}),walletDrawerLabelShowExpirationDate:Cl({id:"walletDrawerLabelShowExpirationDate",defaultMessage:"Copy expiration date",description:"The label for the button to copy expiration date"}),walletDrawerLabelShowCvc:Cl({id:"walletDrawerLabelShowCvc",defaultMessage:"Show CVC",description:"The label for the button to show CVC"}),walletDrawerLabelCopyCvc:Cl({id:"walletDrawerLabelCopyCvc",defaultMessage:"Copy CVC",description:"The label for the button to copy CVC"}),walletDrawerLabelCopyNameAddress:Cl({id:"walletDrawerLabelCopyNameAddress",defaultMessage:"Copy name and address",description:"The label for the button to copy name and address"}),walletFeedbackThankYouText:Cl({id:"walletFeedbackThankYouText",defaultMessage:"Thank you for your feedback. It will help us improve Wallet.",description:"Than you text after users provide feedback"}),walletMicrofeedbackPrompt:Cl({id:"walletMicrofeedbackPrompt",defaultMessage:"Satisfied with Wallet?",description:"Text to ask users to provide feedback"}),feedbackOptionAutofillDontWork:Cl({id:"feedbackOptionAutofillDontWork",defaultMessage:"Autofill did not work",description:"The string used as the first dislike option for wallet BNPL feedback."}),feedbackOptionTookTooMuchTime:Cl({id:"feedbackOptionTookTooMuchTime",defaultMessage:"Took too much time",description:"The string used as the second dislike option for wallet BNPL feedback."}),feedbackOptionOthers:Cl({id:"feedbackOptionOthers",defaultMessage:"Other",description:"The string used as the other dislike option for wallet feedback."}),walletDrawerLinkErrorMessage:Cl({id:"walletDrawerLinkErrorMessage",defaultMessage:"We are unable to link your Microsoft account to $1. Please try again later.",description:"Text of failed link account notification in Wallet EC BNPL"}),walletDrawerPayWithVirtualCard:Cl({id:"walletDrawerPayWithVirtualCard",defaultMessage:"Pay with your $1 $2 card",description:"The description to pay with your virtual card",examples:[{name:"INSTALLMENT_NAME",value:"ZIP"},{name:"CARD_BRAND",value:"Visa"}]}),walletDrawerSystemErrorHeader:Cl({id:"walletDrawerSystemErrorHeader",defaultMessage:"Uh oh",description:"Text of system failure view title"}),walletDrawerAutoFillMessage:Cl({id:"walletDrawerAutoFillMessage",defaultMessage:"We've automatically filled your card information from $1.",description:"The message when autofill succeed"}),walletDrawerAutoFillMessageWithBillingAddress:Cl({id:"walletDrawerAutoFillMessageWithBillingAddress",defaultMessage:"Your payment details from $1 have been filled in. If not, click the card and address details below to copy.",description:"The message when autofill succeed which has billing address"}),walletDrawerAutoFillMessageWithoutBillingAddress:Cl({id:"walletDrawerAutoFillMessageWithoutBillingAddress",defaultMessage:"Your payment details from $1 have been filled in. If not, click the card details below to copy. To complete this purchase, please fill in your home billing address.",description:"The message when autofill succeed which does not have billing address"}),walletDrawerAutoFillErrorMessage:Cl({id:"walletDrawerAutoFillErrorMessage",defaultMessage:"We are unable to autofill your card information from $1. Please manually enter your $1 card information during checkout.",description:"The error message when autofill fails"}),commonSubtitle:Cl({id:"commonSubtitle",defaultMessage:"See if you qualify to pay over time",description:"The common sub title of the total amount dialog"}),msPayAddCardAgreement:Cl({id:"msPayAddCardAgreement",defaultMessage:"By continuing, you agree to the $1, $2, and $3 regarding how your data is handled.",description:"Privacy statement at the save card action when the card is to be saved by uploading it to Microsoft Payments and also saved locally, $1, $2 and $3 are links"}),paymentServiceTermsLinkText:Cl({id:"paymentServiceTermsLinkText",defaultMessage:"Payment Service Terms",description:"Text for Edge terms of servcie link for the Autofill save card prompt when the card is to be saved by uploading it to Microsoft Payments and also saved locally. The prompt can be either a bubble or an infobar."}),previousButtonAriaLabel:Cl({id:"previousButtonAriaLabel",defaultMessage:"Go to previous page",description:"label for the previous button in the pagination mechanism. Clicking this button will take the user back to the previous page in the pagination."}),nextButtonAriaLabel:Cl({id:"nextButtonAriaLabel",defaultMessage:"Go to next page",description:"label for the next button in the pagination mechanism. Clicking this button will take the user to the next page in the pagination."}),paginationButtonAriaLabel:Cl({id:"paginationButtonAriaLabel",defaultMessage:"Go to page $1",description:"label for the pagination button. Clicking this button will take the user to the specific page in the pagination."}),aboutTermsOfUseLinkText:Cl({id:"aboutTermsOfUseLinkText",description:"The label of the link used to direct a user to the terms of use webpage.",defaultMessage:"Microsoft Edge Terms of Use"}),aboutPrivacyStatementLinkText:Cl({id:"aboutPrivacyStatementLinkText",description:"The label of the link used to direct a user to the privacy statement webpage",defaultMessage:"Microsoft Privacy Statement"}),walletCommonAccountCount:Cl({id:"walletCommonAccountCount",description:"For each group indicating the count of accounts, $1 is the count of accounts",defaultMessage:"$1 accounts"}),walletSeeDetailsAriaLabel:Cl({id:"walletSeeDetailsAriaLabel",description:"Aria-label for buttons that go to detail page",defaultMessage:"see details"}),walletPWAPromoTitle:Cl({id:"walletPWAPromoTitle",description:"Title of promo Wallet PWA (progressive web app) installation",defaultMessage:"Add a shortcut to Wallet"}),walletPWAPromoContent:Cl({id:"walletPWAPromoContent",description:"Content of promo Wallet PWA (progressive web app) installation, $1 is the number of reward points",defaultMessage:"Install now and earn $1 Microsoft Rewards points.",examples:[{name:"NUMBER_OF_POINTS",value:"30"}]}),walletPWACommonPromoContent:Cl({id:"walletPWACommonPromoContent",description:"Content of promo Wallet PWA (progressive web app) installation for all type od users",defaultMessage:"Install now for faster access to Wallet."}),walletPWAAddShortcut:Cl({id:"walletPWAAddShortcut",description:"Wallet PWA (progressive web app) installation button text",defaultMessage:"Add shortcut"}),walletNotInterestedText:Cl({id:"walletNotInterestedText",description:"Text for not interested action/feedback, especially for promotion",defaultMessage:"Not interested"}),feedbackLike:Cl({id:"feedbackLike",description:"Like button for mini feedback",defaultMessage:"Like"}),feedbackDislike:Cl({id:"feedbackDislike",description:"Dislike button for mini feedback",defaultMessage:"Dislike"})};function Rl(e,t){switch(e.type){case st.CardExpired:case st.CardExpiring:return function(e,t){const{guid:i,network:o,cardNumber:r}={guid:e.previewData?.guid,network:e.previewData?.network,cardNumber:e.previewData?.cardNumber},n=e.type==st.CardExpired,a=n?kt.msWalletNotificationCardExpired():kt.msWalletNotificationCardExpiring(),s=function(e,t){const i=new URL(`${Re.walletPaymentMethodsURL}/cardDetails/edit`);return e?.length>0&&(i.hash=`#paymentInstrumentId=${e}`),Ct(At(i.toString()),t?"CardExpired":"CardExpiring")}(i,n),l=kt.msWalletNotificationUpdateButton1(),c=function(e,t){return it(e)?Pe.valueExists("msWalletNotificationUpdateExpTextV2")?Pe.getStringF("msWalletNotificationUpdateExpTextV2",e,`****${ot(t)}`):kt.msWalletNotificationUpdateExpText([`****${ot(t)}`]):St({id:"msWalletNotificationUpdateExpTextV2MissingIssuer",description:"Update your card ending in $1 to continue using it.",defaultMessage:"Update your card ending in $1 to continue using it.",values:[`****${ot(t)}`]})}(o,r);return re`<base-notification
    iconClassName="${n?"errorIcon":"warningIcon"}"
    notificationType="${e.type}"
    dataKey="${e.dataKey}"
    headerTitle="${a}"
    content="${c}"
    actionUrl="${s}"
    actionText="${l}"
    appName="${t}"
  >
    ${n?tl:il}
  </base-notification>`}(e,t);case st.SignupCryptoWallet:return function(e,t){const i=kt.msWalletNotificationCryptowalletSignupTitle(),o=kt.msWalletNotificationCryptowalletSignupDescription(),r=Ct(At("edge://wallet/crypto/onboard?from=notification"),"CryptoSignup"),n=kt.msWalletNotificationCryptowalletJoinButton();return re`<base-notification
    notificationType="${e.type}"
    dataKey="${e.dataKey}"
    headerTitle="${i}"
    content="${o}"
    actionUrl="${r}"
    actionText="${n}"
    appName="${t}"
  >
    <svg
      slot="icon"
      fill="${An.normalControl}"
      width="20"
      height="20"
      viewBox="0 0 20 20"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14.16 1.03c.**********.31.63l-.5 1.5a.5.5 0 01-.94-.32l.5-1.5a.5.5 0 01.63-.31zm3.7 1.82a.5.5 0 10-.71-.7l-2 2a.5.5 0 00.7.7l2-2zm-10.25.3a2 2 0 013.26-.52l6.58 6.98a2 2 0 01-.64 3.2l-3.94 1.74a3.5 3.5 0 01-6.34 2.8l-1.2.52a1.5 1.5 0 01-1.67-.3l-1.22-1.22a1.5 1.5 0 01-.3-1.7l5.47-11.5zm-.16 13.78a2.5 2.5 0 004.5-1.97l-4.5 1.97zM17 6a.5.5 0 000 1h1.5a.5.5 0 100-1H17z"
      />
    </svg>
  </base-notification>`}(e,t);case st.CardTokenizationEligible:return cl(e,t);case st.PasswordLeakage:return function(e,t){const{LeakedPasswordCount:i}=e.previewData,o=kt.msWalletNotificationPasswordCompromiseTitle(),r=1==i?kt.msWalletNotificationPasswordCompromiseBodySingular():kt.msWalletNotificationPasswordCompromiseBody([i]),n=new URL(ke.getString("msWalletPageURL")+"/passwords/");n.hash="#filter=Leaked";const a=Ct(At(n.toString()),"PasswordLeakage"),s=St({id:"msWalletNotificationFixLeakage",description:"The text of view details link in password leakage notification",defaultMessage:"View details"});return re`<base-notification
    iconClassName="errorIcon"
    notificationType="${e.type}"
    dataKey="${e.dataKey}"
    headerTitle="${o}"
    content="${r}"
    actionUrl="${a}"
    actionText="${s}"
    appName="${t}"
  >
    <svg slot="icon" fill="inherit" width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M10 2a8 8 0 110 16 8 8 0 010-16zm0 10.5a.75.75 0 100 ********* 0 000-1.5zM10 6a.5.5 0 00-.5.41v4.68a.5.5 0 001 0V6.41A.5.5 0 0010 6z"
      />
    </svg>
  </base-notification>`}(e,t);case st.UpcomingHotelReservations:return function(e,t){const{HotelName:i,CheckinDate:o,CheckoutDate:r}=e.previewData,n=new Date(o).toLocaleDateString(void 0,{month:"short",day:"numeric"}),a=new Date(r).toLocaleDateString(void 0,{month:"short",day:"numeric"}),s=kt.msWalletNotificationHotelReservationTitle(),l=kt.msWalletNotificationHotelReservationBody([i,n,a]),c=new URL(ke.getString("msWalletPageURL")+"/tickets/");c.hash="#tab=travelnotification";const d=Ct(At(c.toString()),"HotelReservation"),u=kt.msWalletNotificationHotelReservationViewDetails();return re`<base-notification
    iconClassName="blueIcon"
    notificationType="${e.type}"
    dataKey="${e.dataKey}"
    headerTitle="${s}"
    content="${l}"
    actionUrl="${d}"
    actionText="${u}"
    appName="${t}"
  >
    <svg slot="icon" width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M18 10a8 8 0 10-16 0 8 8 0 0016 0zM9.5 8.91a.5.5 0 011 0V13.6a.5.5 0 01-1 0V8.9zm-.25-2.16a.75.75 0 111.5 0 .75.75 0 01-1.5 0z"
      />
    </svg>
  </base-notification>`}(e,t);case st.PersonalizedOffersAvailable:return function(e,t){const i=e.previewData?.contentUrl,o=e.previewData?.contentImageUrl,r=e.previewData?.contentRebateValue,n=e.previewData?.expireEpochTime,a=kt.personalizedOffersViewOfferText();return re` <personalized-offers-notification
    notificationType="${e.type}"
    dataKey="${e.dataKey}"
    actionUrl="${i}"
    actionText="${a}"
    expiration="${n}"
    rebateValue="${r}"
    imageUrl="${o}"
    appName="${t}"
  >
  </personalized-offers-notification>`}(e,t);case st.RoamCard:return function(e,t){const i=e.previewData?.guid,o=e.previewData?.cardNumber,r=Mt.msWalletNotificationRoamCardHeaderV3(),n=Ct(At(`edge://wallet/paymentMethods/cardDetails/edit#paymentInstrumentId=${i}&usage=${rl.RoamCard}`),"CardRoaming"),a=Mt.msWalletNotificationRoamCardActionV2(),s=`•••• •••• •••• ${ot(o)}`;return re`<base-notification
    notificationType="${e.type}"
    dataKey="${e.dataKey}"
    headerTitle="${r}"
    content="${s}"
    actionUrl="${n}"
    actionText="${a}"
    appName="${t}"
  >
    <svg slot="icon" width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M4.5 4C3.11929 4 2 5.11929 2 6.5V13.5C2 14.8807 3.11929 16 4.5 16H15.5C16.8807 16 18 14.8807 18 13.5V6.5C18 5.11929 16.8807 4 15.5 4H4.5ZM3 6.5C3 5.67157 3.67157 5 4.5 5H15.5C16.3284 5 17 5.67157 17 6.5V13.5C17 14.3284 16.3284 15 15.5 15H4.5C3.67157 15 3 14.3284 3 13.5V6.5ZM4.5 6C4.22386 6 4 6.22386 4 6.5C4 6.77614 4.22386 7 4.5 7H9.5C9.77614 7 10 6.77614 10 6.5C10 6.22386 9.77614 6 9.5 6H4.5ZM4.5 8C4.22386 8 4 8.22386 4 8.5C4 8.77614 4.22386 9 4.5 9H12.5C12.7761 9 13 8.77614 13 8.5C13 8.22386 12.7761 8 12.5 8H4.5ZM5 11C4.44772 11 4 11.4477 4 12V13C4 13.5523 4.44772 14 5 14H9C9.55228 14 10 13.5523 10 13V12C10 11.4477 9.55228 11 9 11H5Z"
        fill="#115EA3"
      />
    </svg>
  </base-notification>`}(e,t);case st.DonationSummary:return function(e,t){const i=kt.msWalletNotificationDonationSummaryTitle(),o=new URL(Pe.getString("msWalletPageURL")+"/donation/");o.hash="#tab=overviewfromnotification";const r=Ct(At(o.toString()),"DonationSummary"),n=kt.msWalletNotificationDonationSummaryLinkText();return re`<base-notification
    iconClassName="errorIcon"
    notificationType="${e.type}"
    dataKey="${e.dataKey}"
    headerTitle="${i}"
    actionUrl="${r}"
    actionText="${n}"
    appName="${t}"
  >
    <donation-icon slot="icon" />
  </base-notification>`}(e,t);case st.PackageTracking:return function(e,t){const{IsShoppingPackageTrackingUserConsent:i}=e.previewData,o=i?_l.ViewOrder:_l.TrackOrder,r=o===_l.ViewOrder?kt.msWalletNotificationOrderStatusTitle():kt.msWalletNotificationTrackOrderTitle(),n=o===_l.ViewOrder?"":kt.msWalletNotificationTrackOrderSubTitle(),a=Ct(At(new URL(Pe.getString("msWalletPageURL")+"/orders/").toString()),"PackageTracking"),s=o===_l.ViewOrder?kt.msWalletNotificationViewDetailLink():kt.msWalletNotificationTrackOrderLink();return re`<package-tracking
    notificationType="${e.type}"
    dataKey="${e.dataKey}"
    headerTitle="${r}"
    content="${n}"
    actionUrl="${a}"
    actionText="${s}"
    appName="${t}"
    subType="${o}"
  >
    <svg slot="icon" width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M10 6.5V2H5.5C4.67 2 4 2.67 4 3.5V9c.7.03 1.4.25 2 .66a3.85 3.85 0 014.88 5.91L8.45 18h6.05c.83 0 1.5-.67 1.5-1.5V8h-4.5A1.5 1.5 0 0110 6.5zm1 0V2.25L15.75 7H11.5a.5.5 0 01-.5-.5zm-4.86 4.33a2.85 2.85 0 114.03 4.04l-3.82 3.81a.5.5 0 01-.7 0l-3.82-3.81a2.85 2.85 0 114.03-4.04l.14.14.14-.14zm4.03 4.04l-.36-.36z"
      />
    </svg>
  </package-tracking>`}(e,t);case st.Rebates:return function(e,t){const i=kt.msWalletNotificationRebatesCashoutTitle(),{balance:o=0}=e.previewData,r=5*Math.floor(o/5),n=o<5?kt.msWalletNotificationRebatesCashoutDescription():kt.msWalletNotificationRebatesCashoutAmountDescription([`$${r}`]),a=Re.microsoftRebatesPayoutURL,s=kt.msWalletNotificationRebatesCashoutAction();return re`<base-notification
    notificationType="${e.type}"
    dataKey="${e.dataKey}"
    headerTitle="${i}"
    content="${n}"
    actionUrl="${a}"
    actionText="${s}"
    appName="${t}"
  >
    <img
      slot="icon"
      height="30"
      width="36"
      src="data:image/png;base64,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"
    />
  </base-notification>`}(e,t);case st.PWAPromotion:return function(e,t){const i=vl.walletPWAPromoTitle(),o=vl.walletPWACommonPromoContent(),r=Ct(At(new URL(ke.getString("msWalletPageURL")).toString()),"PWAPromotion"),n=vl.walletPWAAddShortcut();return re`<base-notification
    notificationType="${e.type}"
    dataKey="${e.dataKey}"
    headerTitle="${i}"
    content="${o}"
    actionUrl="${r}"
    actionText="${n}"
    appName="${t}"
  >
    <svg slot="icon" width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="28" height="28" rx="14" fill="#F1FAF1" />
      <path
        d="M16.8712 7.01503C17.1391 7.08197 17.302 7.35342 17.2351 7.62132L16.9852 8.6214C16.9183 8.8893 16.6468 9.05222 16.3789 8.98528C16.111 8.91834 15.9481 8.64689 16.015 8.37899L16.2649 7.37891C16.3319 7.11101 16.6033 6.94809 16.8712 7.01503ZM19.8536 8.14659C20.0488 8.34186 20.0488 8.65844 19.8536 8.8537L18.8536 9.8537C18.6583 10.049 18.3417 10.049 18.1464 9.8537C17.9512 9.65844 17.9512 9.34186 18.1464 9.14659L19.1464 8.14659C19.3417 7.95133 19.6583 7.95133 19.8536 8.14659ZM12.2836 19.0325C12.7966 19.886 13.8774 20.2425 14.8102 19.8271C15.7399 19.4132 16.1982 18.3773 15.9133 17.4277L12.2836 19.0325ZM11.3642 19.439L10.3655 19.8806C9.83756 20.1141 9.21978 19.9996 8.81088 19.5919L8.41013 19.1923C8.00252 18.7858 7.88618 18.1711 8.11785 17.644L11.9891 8.83553C12.3847 7.9354 13.5625 7.71525 14.258 8.40875L19.5897 13.725C20.2853 14.4186 20.0653 15.5919 19.1678 15.9888L16.8328 17.0212C17.3372 18.4773 16.6508 20.1023 15.217 20.7407C13.7784 21.3812 12.1051 20.798 11.3642 19.439ZM20.5 11.0001H19.5C19.2239 11.0001 19 11.224 19 11.5001C19 11.7763 19.2239 12.0001 19.5 12.0001H20.5C20.7761 12.0001 21 11.7763 21 11.5001C21 11.224 20.7761 11.0001 20.5 11.0001Z"
        fill="#0E700E"
      />
    </svg>
  </base-notification>`}(e,t);case st.DonationTrendNpo:return function(e,t){const i=e.data?.causeName,o=kt.msWalletNotificationDonationTrendNpoTitle(),r=kt.msWalletNotificationDonationTrendNpoDescription([i]),n=new URL(ke.getString("msWalletPageURL")+"/donation/");n.hash=i?`#tab=explore&npo=${i}`:"#tab=explore";const a=Ct(At(n.toString()),"PWAPromotion"),s=kt.msWalletNotificationDonationTrendNpoLink();return re`<base-notification
    notificationType="${e.type}"
    dataKey="${e.dataKey}"
    headerTitle="${o}"
    content="${r}"
    actionUrl="${a}"
    actionText="${s}"
    appName="${t}"
  >
    <img
      slot="icon"
      height="36"
      width="36"
      src="data:image/png;base64,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"
    />
  </base-notification>`}(e,t);default:return bt(nt.UNSUPPORTED_TYPE,t),null}}const yl=I``,Sl=re`
  ${le((e=>e.notification),re`<div>${e=>Rl(e.notification,e.appName)}</div> `)}
`;let wl=class extends Ie{get notification(){return R.track(this,"notification"),this._notification}set notification(e){this._notification=e,R.notify(this,"notification")}get appName(){return R.track(this,"appName"),this._appName}set appName(e){this._appName=e,R.notify(this,"appName")}};function kl(e,t,i,o){var r,n=arguments.length,a=n<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,i):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,i,o);else for(var s=e.length-1;s>=0;s--)(r=e[s])&&(a=(n<3?r(a):n>3?r(t,i,a):r(t,i))||a);return n>3&&a&&Object.defineProperty(t,i,a),a}wl=function(e,t,i,o){var r,n=arguments.length,a=n<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,i):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,i,o);else for(var s=e.length-1;s>=0;s--)(r=e[s])&&(a=(n<3?r(a):n>3?r(t,i,a):r(t,i))||a);return n>3&&a&&Object.defineProperty(t,i,a),a}([Ae({name:"notification-container",template:Sl,styles:yl})],wl);const Ml=new Map;"metadata"in Reflect||(Reflect.metadata=function(e,t){return function(i){Reflect.defineMetadata(e,t,i)}},Reflect.defineMetadata=function(e,t,i){let o=Ml.get(i);void 0===o&&Ml.set(i,o=new Map),o.set(e,t)},Reflect.getOwnMetadata=function(e,t){const i=Ml.get(t);if(void 0!==i)return i.get(e)});class xl{constructor(e,t){this.container=e,this.key=t}instance(e){return this.registerResolver(0,e)}singleton(e){return this.registerResolver(1,e)}transient(e){return this.registerResolver(2,e)}callback(e){return this.registerResolver(3,e)}cachedCallback(e){return this.registerResolver(3,rc(e))}aliasTo(e){return this.registerResolver(5,e)}registerResolver(e,t){const{container:i,key:o}=this;return this.container=this.key=void 0,i.registerResolver(o,new jl(o,e,t))}}function Pl(e){const t=e.slice(),i=Object.keys(e),o=i.length;let r;for(let n=0;n<o;++n)r=i[n],pc(r)||(t[r]=e[r]);return t}const Dl=Object.freeze({none(e){throw Error(`${e.toString()} not registered, did you forget to add @singleton()?`)},singleton:e=>new jl(e,1,e),transient:e=>new jl(e,2,e)}),Ll=Object.freeze({default:Object.freeze({parentLocator:()=>null,responsibleForOwnerRequests:!1,defaultResolver:Dl.singleton})}),Fl=new Map;function Wl(e){return t=>Reflect.getOwnMetadata(e,t)}let Bl=null;const Gl=Object.freeze({createContainer:e=>new ic(null,Object.assign({},Ll.default,e)),findResponsibleContainer(e){const t=e.$$container$$;return t&&t.responsibleForOwnerRequests?t:Gl.findParentContainer(e)},findParentContainer(e){const t=new CustomEvent(ec,{bubbles:!0,composed:!0,cancelable:!0,detail:{container:void 0}});return e.dispatchEvent(t),t.detail.container||Gl.getOrCreateDOMContainer()},getOrCreateDOMContainer:(e,t)=>e?e.$$container$$||new ic(e,Object.assign({},Ll.default,t,{parentLocator:Gl.findParentContainer})):Bl||(Bl=new ic(null,Object.assign({},Ll.default,t,{parentLocator:()=>null}))),getDesignParamtypes:Wl("design:paramtypes"),getAnnotationParamtypes:Wl("di:paramtypes"),getOrCreateAnnotationParamTypes(e){let t=this.getAnnotationParamtypes(e);return void 0===t&&Reflect.defineMetadata("di:paramtypes",t=[],e),t},getDependencies(e){let t=Fl.get(e);if(void 0===t){const i=e.inject;if(void 0===i){const i=Gl.getDesignParamtypes(e),o=Gl.getAnnotationParamtypes(e);if(void 0===i)if(void 0===o){const i=Object.getPrototypeOf(e);t="function"==typeof i&&i!==Function.prototype?Pl(Gl.getDependencies(i)):[]}else t=Pl(o);else if(void 0===o)t=Pl(i);else{t=Pl(i);let e,r=o.length;for(let i=0;i<r;++i)e=o[i],void 0!==e&&(t[i]=e);const n=Object.keys(o);let a;r=n.length;for(let e=0;e<r;++e)a=n[e],pc(a)||(t[a]=o[a])}}else t=Pl(i);Fl.set(e,t)}return t},defineProperty(e,t,i,o=!1){const r=`$di_${t}`;Reflect.defineProperty(e,t,{get:function(){let e=this[r];if(void 0===e){const n=this instanceof HTMLElement?Gl.findResponsibleContainer(this):Gl.getOrCreateDOMContainer();if(e=n.get(i),this[r]=e,o&&this instanceof Ie){const o=this.$fastController,n=()=>{Gl.findResponsibleContainer(this).get(i)!==this[r]&&(this[r]=e,o.notify(t))};o.subscribe({handleChange:n},"isConnected")}}return e}})},createInterface(e,t){const i="function"==typeof e?e:t,o="string"==typeof e?e:e&&"friendlyName"in e&&e.friendlyName||lc,r="string"!=typeof e&&(e&&"respectConnection"in e&&e.respectConnection||!1),n=function(e,t,i){if(null==e||void 0!==new.target)throw new Error(`No registration for interface: '${n.friendlyName}'`);t?Gl.defineProperty(e,t,n,r):Gl.getOrCreateAnnotationParamTypes(e)[i]=n};return n.$isInterface=!0,n.friendlyName=null==o?"(anonymous)":o,null!=i&&(n.register=function(e,t){return i(new xl(e,null!=t?t:n))}),n.toString=function(){return`InterfaceSymbol<${n.friendlyName}>`},n},inject:(...e)=>function(t,i,o){if("number"==typeof o){const i=Gl.getOrCreateAnnotationParamTypes(t),r=e[0];void 0!==r&&(i[o]=r)}else if(i)Gl.defineProperty(t,i,e[0]);else{const i=o?Gl.getOrCreateAnnotationParamTypes(o.value):Gl.getOrCreateAnnotationParamTypes(t);let r;for(let t=0;t<e.length;++t)r=e[t],void 0!==r&&(i[t]=r)}},transient:e=>(e.register=function(t){return nc.transient(e,e).register(t)},e.registerInRequestor=!1,e),singleton:(e,t=$l)=>(e.register=function(t){return nc.singleton(e,e).register(t)},e.registerInRequestor=t.scoped,e)}),Vl=Gl.createInterface("Container");function Ul(e){return function(t){const i=function(e,t,o){Gl.inject(i)(e,t,o)};return i.$isResolver=!0,i.resolve=function(i,o){return e(t,i,o)},i}}Gl.inject;const $l={scoped:!1};function Hl(e,t,i){Gl.inject(Hl)(e,t,i)}function zl(e,t){return t.getFactory(e).construct(t)}Ul(((e,t,i)=>()=>i.get(e))),Ul(((e,t,i)=>i.has(e,!0)?i.get(e):void 0)),Hl.$isResolver=!0,Hl.resolve=()=>{},Ul(((e,t,i)=>{const o=zl(e,t),r=new jl(e,0,o);return i.registerResolver(e,r),o})),Ul(((e,t,i)=>zl(e,t)));class jl{constructor(e,t,i){this.key=e,this.strategy=t,this.state=i,this.resolving=!1}get $isResolver(){return!0}register(e){return e.registerResolver(this.key,this)}resolve(e,t){switch(this.strategy){case 0:return this.state;case 1:if(this.resolving)throw new Error(`Cyclic dependency found: ${this.state.name}`);return this.resolving=!0,this.state=e.getFactory(this.state).construct(t),this.strategy=0,this.resolving=!1,this.state;case 2:{const i=e.getFactory(this.state);if(null===i)throw new Error(`Resolver for ${String(this.key)} returned a null factory`);return i.construct(t)}case 3:return this.state(e,t,this);case 4:return this.state[0].resolve(e,t);case 5:return t.get(this.state);default:throw new Error(`Invalid resolver strategy specified: ${this.strategy}.`)}}getFactory(e){var t,i,o;switch(this.strategy){case 1:case 2:return e.getFactory(this.state);case 5:return null!==(o=null===(i=null===(t=e.getResolver(this.state))||void 0===t?void 0:t.getFactory)||void 0===i?void 0:i.call(t,e))&&void 0!==o?o:null;default:return null}}}function Zl(e){return this.get(e)}function Yl(e,t){return t(e)}class Kl{constructor(e,t){this.Type=e,this.dependencies=t,this.transformers=null}construct(e,t){let i;return i=void 0===t?new this.Type(...this.dependencies.map(Zl,e)):new this.Type(...this.dependencies.map(Zl,e),...t),null==this.transformers?i:this.transformers.reduce(Yl,i)}registerTransformer(e){(this.transformers||(this.transformers=[])).push(e)}}const ql={$isResolver:!0,resolve:(e,t)=>t};function Xl(e){return"function"==typeof e.register}function Ql(e){return function(e){return Xl(e)&&"boolean"==typeof e.registerInRequestor}(e)&&e.registerInRequestor}const Jl=new Set(["Array","ArrayBuffer","Boolean","DataView","Date","Error","EvalError","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Number","Object","Promise","RangeError","ReferenceError","RegExp","Set","SharedArrayBuffer","String","SyntaxError","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","URIError","WeakMap","WeakSet"]),ec="__DI_LOCATE_PARENT__",tc=new Map;class ic{constructor(e,t){this.owner=e,this.config=t,this._parent=void 0,this.registerDepth=0,this.context=null,null!==e&&(e.$$container$$=this),this.resolvers=new Map,this.resolvers.set(Vl,ql),e instanceof Node&&e.addEventListener(ec,(e=>{e.composedPath()[0]!==this.owner&&(e.detail.container=this,e.stopImmediatePropagation())}))}get parent(){return void 0===this._parent&&(this._parent=this.config.parentLocator(this.owner)),this._parent}get depth(){return null===this.parent?0:this.parent.depth+1}get responsibleForOwnerRequests(){return this.config.responsibleForOwnerRequests}registerWithContext(e,...t){return this.context=e,this.register(...t),this.context=null,this}register(...e){if(100==++this.registerDepth)throw new Error("Unable to autoregister dependency");let t,i,o,r,n;const a=this.context;for(let s=0,l=e.length;s<l;++s)if(t=e[s],cc(t))if(Xl(t))t.register(this,a);else if(void 0!==t.prototype)nc.singleton(t,t).register(this);else for(i=Object.keys(t),r=0,n=i.length;r<n;++r)o=t[i[r]],cc(o)&&(Xl(o)?o.register(this,a):this.register(o));return--this.registerDepth,this}registerResolver(e,t){ac(e);const i=this.resolvers,o=i.get(e);return null==o?i.set(e,t):o instanceof jl&&4===o.strategy?o.state.push(t):i.set(e,new jl(e,4,[o,t])),t}registerTransformer(e,t){const i=this.getResolver(e);if(null==i)return!1;if(i.getFactory){const e=i.getFactory(this);return null!=e&&(e.registerTransformer(t),!0)}return!1}getResolver(e,t=!0){if(ac(e),void 0!==e.resolve)return e;let i,o=this;for(;null!=o;){if(i=o.resolvers.get(e),null!=i)return i;if(null==o.parent){const i=Ql(e)?this:o;return t?this.jitRegister(e,i):null}o=o.parent}return null}has(e,t=!1){return!!this.resolvers.has(e)||!(!t||null==this.parent)&&this.parent.has(e,!0)}get(e){if(ac(e),e.$isResolver)return e.resolve(this,this);let t,i=this;for(;null!=i;){if(t=i.resolvers.get(e),null!=t)return t.resolve(i,this);if(null==i.parent){const o=Ql(e)?this:i;return t=this.jitRegister(e,o),t.resolve(i,this)}i=i.parent}throw new Error(`Unable to resolve key: ${e}`)}getAll(e,t=!1){ac(e);const i=this;let o,r=i;if(t){let t=n;for(;null!=r;)o=r.resolvers.get(e),null!=o&&(t=t.concat(sc(o,r,i))),r=r.parent;return t}for(;null!=r;){if(o=r.resolvers.get(e),null!=o)return sc(o,r,i);if(r=r.parent,null==r)return n}return n}getFactory(e){let t=tc.get(e);if(void 0===t){if(dc(e))throw new Error(`${e.name} is a native function and therefore cannot be safely constructed by DI. If this is intentional, please use a callback or cachedCallback resolver.`);tc.set(e,t=new Kl(e,Gl.getDependencies(e)))}return t}registerFactory(e,t){tc.set(e,t)}createChild(e){return new ic(null,Object.assign({},this.config,e,{parentLocator:()=>this}))}jitRegister(e,t){if("function"!=typeof e)throw new Error(`Attempted to jitRegister something that is not a constructor: '${e}'. Did you forget to register this dependency?`);if(Jl.has(e.name))throw new Error(`Attempted to jitRegister an intrinsic type: ${e.name}. Did you forget to add @inject(Key)`);if(Xl(e)){const i=e.register(t);if(!(i instanceof Object)||null==i.resolve){const i=t.resolvers.get(e);if(null!=i)return i;throw new Error("A valid resolver was not returned from the static register method")}return i}if(e.$isInterface)throw new Error(`Attempted to jitRegister an interface: ${e.friendlyName}`);{const i=this.config.defaultResolver(e,t);return t.resolvers.set(e,i),i}}}const oc=new WeakMap;function rc(e){return function(t,i,o){if(oc.has(o))return oc.get(o);const r=e(t,i,o);return oc.set(o,r),r}}const nc=Object.freeze({instance:(e,t)=>new jl(e,0,t),singleton:(e,t)=>new jl(e,1,t),transient:(e,t)=>new jl(e,2,t),callback:(e,t)=>new jl(e,3,t),cachedCallback:(e,t)=>new jl(e,3,rc(t)),aliasTo:(e,t)=>new jl(t,5,e)});function ac(e){if(null==e)throw new Error("key/value cannot be null or undefined. Are you trying to inject/register something that doesn't exist with DI?")}function sc(e,t,i){if(e instanceof jl&&4===e.strategy){const o=e.state;let r=o.length;const n=new Array(r);for(;r--;)n[r]=o[r].resolve(t,i);return n}return[e.resolve(t,i)]}const lc="(anonymous)";function cc(e){return"object"==typeof e&&null!==e||"function"==typeof e}const dc=function(){const e=new WeakMap;let t=!1,i="",o=0;return function(r){return t=e.get(r),void 0===t&&(i=r.toString(),o=i.length,t=o>=29&&o<=100&&125===i.charCodeAt(o-1)&&i.charCodeAt(o-2)<=32&&93===i.charCodeAt(o-3)&&101===i.charCodeAt(o-4)&&100===i.charCodeAt(o-5)&&111===i.charCodeAt(o-6)&&99===i.charCodeAt(o-7)&&32===i.charCodeAt(o-8)&&101===i.charCodeAt(o-9)&&118===i.charCodeAt(o-10)&&105===i.charCodeAt(o-11)&&116===i.charCodeAt(o-12)&&97===i.charCodeAt(o-13)&&110===i.charCodeAt(o-14)&&88===i.charCodeAt(o-15),e.set(r,t)),t}}(),uc={};function pc(e){switch(typeof e){case"number":return e>=0&&(0|e)===e;case"string":{const t=uc[e];if(void 0!==t)return t;const i=e.length;if(0===i)return uc[e]=!1;let o=0;for(let t=0;t<i;++t)if(o=e.charCodeAt(t),0===t&&48===o&&i>1||o<48||o>57)return uc[e]=!1;return uc[e]=!0}default:return!1}}function hc(e){return`${e.toLowerCase()}:presentation`}const fc=new Map,gc=Object.freeze({define(e,t,i){const o=hc(e);void 0===fc.get(o)?fc.set(o,t):fc.set(o,!1),i.register(nc.instance(o,t))},forTag(e,t){const i=hc(e),o=fc.get(i);return!1===o?Gl.findResponsibleContainer(t).get(i):o||null}});class Ec{constructor(e,t){this.template=e||null,this.styles=void 0===t?null:Array.isArray(t)?f.create(t):t instanceof f?t:f.create([t])}applyTo(e){const t=e.$fastController;null===t.template&&(t.template=this.template),null===t.styles&&(t.styles=this.styles)}}class Tc extends Ie{constructor(){super(...arguments),this._presentation=void 0}get $presentation(){return void 0===this._presentation&&(this._presentation=gc.forTag(this.tagName,this)),this._presentation}templateChanged(){void 0!==this.template&&(this.$fastController.template=this.template)}stylesChanged(){void 0!==this.styles&&(this.$fastController.styles=this.styles)}connectedCallback(){null!==this.$presentation&&this.$presentation.applyTo(this),super.connectedCallback()}static compose(e){return(t={})=>new Nc(this===Tc?class extends Tc{}:this,e,t)}}function mc(e,t,i){return"function"==typeof e?e(t,i):e}kl([y],Tc.prototype,"template",void 0),kl([y],Tc.prototype,"styles",void 0);class Nc{constructor(e,t,i){this.type=e,this.elementDefinition=t,this.overrideDefinition=i,this.definition=Object.assign(Object.assign({},this.elementDefinition),this.overrideDefinition)}register(e,t){const i=this.definition,o=this.overrideDefinition,r=`${i.prefix||t.elementPrefix}-${i.baseName}`;t.tryDefineElement({name:r,type:this.type,baseClass:this.elementDefinition.baseClass,callback:e=>{const t=new Ec(mc(i.template,e,i),mc(i.styles,e,i));e.definePresentation(t);let r=mc(i.shadowOptions,e,i);e.shadowRootMode&&(r?o.shadowOptions||(r.mode=e.shadowRootMode):null!==r&&(r={mode:e.shadowRootMode})),e.defineElement({elementOptions:mc(i.elementOptions,e,i),shadowOptions:r,attributes:mc(i.attributes,e,i)})}})}}const _c=new Set(["children","localName","ref","style","className"]),Oc=Object.freeze(Object.create(null));function bc(e,t){if(!t.name){const i=Te.forType(e);if(!i)throw new Error("React wrappers must wrap a FASTElement or be configured with a name.");t.name=i.name}return t.name}function Ic(e){return e.events||(e.events={})}function Ac(e,t,i){return!_c.has(i)||(console.warn(`${bc(e,t)} contains property ${i} which is a React reserved property. It will be used by React and not set on the element.`),!1)}(function(e,t){let i=[];return{wrap:function(t,o={}){t instanceof Nc&&(i.push(t),t=t.type);class r extends e.Component{constructor(){super(...arguments),this._element=null}_updateElement(e){const t=this._element;if(null===t)return;const i=this.props,r=e||Oc,n=Ic(o);for(const e in this._elementProps){const o=i[e],a=n[e];if(void 0===a)t[e]=o;else{const i=r[e];if(o===i)continue;void 0!==i&&t.removeEventListener(a,i),void 0!==o&&t.addEventListener(a,o)}}}componentDidMount(){this._updateElement()}componentDidUpdate(e){this._updateElement(e)}render(){const i=this.props.__forwardedRef;void 0!==this._ref&&this._userRef===i||(this._ref=e=>{null===this._element&&(this._element=e),null!==i&&function(e,t){"function"==typeof e?e(t):e.current=t}(i,e),this._userRef=i});const r={ref:this._ref},n=this._elementProps={},a=function(e,t){if(!t.keys)if(t.properties)t.keys=new Set(t.properties.concat(Object.keys(Ic(t))));else{const i=new Set(Object.keys(Ic(t))),o=R.getAccessors(e.prototype);if(o.length>0)for(const r of o)Ac(e,t,r.name)&&i.add(r.name);else for(const o in e.prototype)!(o in HTMLElement.prototype)&&Ac(e,t,o)&&i.add(o);t.keys=i}return t.keys}(t,o),s=this.props;for(const e in s){const t=s[e];a.has(e)?n[e]=t:r["className"===e?"class":e]=t}return e.createElement(bc(t,o),r)}}return e.forwardRef(((t,i)=>e.createElement(r,Object.assign(Object.assign({},t),{__forwardedRef:i}),null==t?void 0:t.children)))},registry:{register(e,...t){i.forEach((i=>i.register(e,...t))),i=[]}}}})(vt).wrap(wl,{properties:["notification","appName"]});let Cc=1;const vc={},Rc={};function yc(e){const t=e.parentElement;if(t)return t;{const t=e.getRootNode();if(t.host instanceof HTMLElement)return t.host}return null}window.cr=window.cr||{},window.cr.webUIResponse=function(e,t,i){const o=vc[e];delete vc[e],t?o.resolve(i):o.reject(i)},window.cr.webUIListenerCallback=function(e,t){const i=Rc[e];if(!i)return;const o=Array.prototype.slice.call(arguments,1);for(const e in i)i[e].apply(null,o)};const Sc=document.createElement("div");class wc{setProperty(e,t){h.queueUpdate((()=>this.target.setProperty(e,t)))}removeProperty(e){h.queueUpdate((()=>this.target.removeProperty(e)))}}class kc extends wc{constructor(){super();const e=new CSSStyleSheet;this.target=e.cssRules[e.insertRule(":root{}")].style,document.adoptedStyleSheets=[...document.adoptedStyleSheets,e]}}class Mc extends wc{constructor(){super(),this.style=document.createElement("style"),document.head.appendChild(this.style);const{sheet:e}=this.style;if(e){const t=e.insertRule(":root{}",e.cssRules.length);this.target=e.cssRules[t].style}}}class xc{constructor(e){this.store=new Map,this.target=null;const t=e.$fastController;this.style=document.createElement("style"),t.addStyles(this.style),R.getNotifier(t).subscribe(this,"isConnected"),this.handleChange(t,"isConnected")}targetChanged(){if(null!==this.target)for(const[e,t]of this.store.entries())this.target.setProperty(e,t)}setProperty(e,t){this.store.set(e,t),h.queueUpdate((()=>{null!==this.target&&this.target.setProperty(e,t)}))}removeProperty(e){this.store.delete(e),h.queueUpdate((()=>{null!==this.target&&this.target.removeProperty(e)}))}handleChange(e,t){const{sheet:i}=this.style;if(i){const e=i.insertRule(":host{}",i.cssRules.length);this.target=i.cssRules[e].style}else this.target=null}}kl([y],xc.prototype,"target",void 0);class Pc{constructor(e){this.target=e.style}setProperty(e,t){h.queueUpdate((()=>this.target.setProperty(e,t)))}removeProperty(e){h.queueUpdate((()=>this.target.removeProperty(e)))}}class Dc{setProperty(e,t){Dc.properties[e]=t;for(const i of Dc.roots.values())Wc.getOrCreate(Dc.normalizeRoot(i)).setProperty(e,t)}removeProperty(e){delete Dc.properties[e];for(const t of Dc.roots.values())Wc.getOrCreate(Dc.normalizeRoot(t)).removeProperty(e)}static registerRoot(e){const{roots:t}=Dc;if(!t.has(e)){t.add(e);const i=Wc.getOrCreate(this.normalizeRoot(e));for(const e in Dc.properties)i.setProperty(e,Dc.properties[e])}}static unregisterRoot(e){const{roots:t}=Dc;if(t.has(e)){t.delete(e);const i=Wc.getOrCreate(Dc.normalizeRoot(e));for(const e in Dc.properties)i.removeProperty(e)}}static normalizeRoot(e){return e===Sc?document:e}}Dc.roots=new Set,Dc.properties={};const Lc=new WeakMap,Fc=h.supportsAdoptedStyleSheets?class extends wc{constructor(e){super();const t=new CSSStyleSheet;this.target=t.cssRules[t.insertRule(":host{}")].style,e.$fastController.addStyles(f.create([t]))}}:xc,Wc=Object.freeze({getOrCreate(e){if(Lc.has(e))return Lc.get(e);let t;return t=e===Sc?new Dc:e instanceof Document?h.supportsAdoptedStyleSheets?new kc:new Mc:e instanceof Ie?new Fc(e):new Pc(e),Lc.set(e,t),t}});class Bc extends e{constructor(e){super(),this.subscribers=new WeakMap,this._appliedTo=new Set,this.name=e.name,null!==e.cssCustomPropertyName&&(this.cssCustomProperty=`--${e.cssCustomPropertyName}`,this.cssVar=`var(${this.cssCustomProperty})`),this.id=Bc.uniqueId(),Bc.tokensById.set(this.id,this)}get appliedTo(){return[...this._appliedTo]}static from(e){return new Bc({name:"string"==typeof e?e:e.name,cssCustomPropertyName:"string"==typeof e?e:void 0===e.cssCustomPropertyName?e.name:e.cssCustomPropertyName})}static isCSSDesignToken(e){return"string"==typeof e.cssCustomProperty}static isDerivedDesignTokenValue(e){return"function"==typeof e}static getTokenById(e){return Bc.tokensById.get(e)}getOrCreateSubscriberSet(e=this){return this.subscribers.get(e)||this.subscribers.set(e,new Set)&&this.subscribers.get(e)}createCSS(){return this.cssVar||""}getValueFor(e){const t=Hc.getOrCreate(e).get(this);if(void 0!==t)return t;throw new Error(`Value could not be retrieved for token named "${this.name}". Ensure the value is set for ${e} or an ancestor of ${e}.`)}setValueFor(e,t){return this._appliedTo.add(e),t instanceof Bc&&(t=this.alias(t)),Hc.getOrCreate(e).set(this,t),this}deleteValueFor(e){return this._appliedTo.delete(e),Hc.existsFor(e)&&Hc.getOrCreate(e).delete(this),this}withDefault(e){return this.setValueFor(Sc,e),this}subscribe(e,t){const i=this.getOrCreateSubscriberSet(t);t&&!Hc.existsFor(t)&&Hc.getOrCreate(t),i.has(e)||i.add(e)}unsubscribe(e,t){const i=this.subscribers.get(t||this);i&&i.has(e)&&i.delete(e)}notify(e){const t=Object.freeze({token:this,target:e});this.subscribers.has(this)&&this.subscribers.get(this).forEach((e=>e.handleChange(t))),this.subscribers.has(e)&&this.subscribers.get(e).forEach((e=>e.handleChange(t)))}alias(e){return t=>e.getValueFor(t)}}Bc.uniqueId=(()=>{let e=0;return()=>(e++,e.toString(16))})(),Bc.tokensById=new Map;class Gc{constructor(e,t,i){this.source=e,this.token=t,this.node=i,this.dependencies=new Set,this.observer=R.binding(e,this,!1),this.observer.handleChange=this.observer.call,this.handleChange()}disconnect(){this.observer.disconnect()}handleChange(){this.node.store.set(this.token,this.observer.observe(this.node.target,k))}}class Vc{constructor(){this.values=new Map}set(e,t){this.values.get(e)!==t&&(this.values.set(e,t),R.getNotifier(this).notify(e.id))}get(e){return R.track(this,e.id),this.values.get(e)}delete(e){this.values.delete(e)}all(){return this.values.entries()}}const Uc=new WeakMap,$c=new WeakMap;class Hc{constructor(e){this.target=e,this.store=new Vc,this.children=[],this.assignedValues=new Map,this.reflecting=new Set,this.bindingObservers=new Map,this.tokenValueChangeHandler={handleChange:(e,t)=>{const i=Bc.getTokenById(t);if(i&&(i.notify(this.target),Bc.isCSSDesignToken(i))){const t=this.parent,o=this.isReflecting(i);if(t){const r=t.get(i),n=e.get(i);r===n||o?r===n&&o&&this.stopReflectToCSS(i):this.reflectToCSS(i)}else o||this.reflectToCSS(i)}}},Uc.set(e,this),R.getNotifier(this.store).subscribe(this.tokenValueChangeHandler),e instanceof Ie?e.$fastController.addBehaviors([this]):e.isConnected&&this.bind()}static getOrCreate(e){return Uc.get(e)||new Hc(e)}static existsFor(e){return Uc.has(e)}static findParent(e){if(Sc!==e.target){let t=yc(e.target);for(;null!==t;){if(Uc.has(t))return Uc.get(t);t=yc(t)}return Hc.getOrCreate(Sc)}return null}static findClosestAssignedNode(e,t){let i=t;do{if(i.has(e))return i;i=i.parent?i.parent:i.target!==Sc?Hc.getOrCreate(Sc):null}while(null!==i);return null}get parent(){return $c.get(this)||null}has(e){return this.assignedValues.has(e)}get(e){const t=this.store.get(e);if(void 0!==t)return t;const i=this.getRaw(e);return void 0!==i?(this.hydrate(e,i),this.get(e)):void 0}getRaw(e){var t;return this.assignedValues.has(e)?this.assignedValues.get(e):null===(t=Hc.findClosestAssignedNode(e,this))||void 0===t?void 0:t.getRaw(e)}set(e,t){Bc.isDerivedDesignTokenValue(this.assignedValues.get(e))&&this.tearDownBindingObserver(e),this.assignedValues.set(e,t),Bc.isDerivedDesignTokenValue(t)?this.setupBindingObserver(e,t):this.store.set(e,t)}delete(e){this.assignedValues.delete(e),this.tearDownBindingObserver(e);const t=this.getRaw(e);t?this.hydrate(e,t):this.store.delete(e)}bind(){const e=Hc.findParent(this);e&&e.appendChild(this);for(const e of this.assignedValues.keys())e.notify(this.target)}unbind(){this.parent&&$c.get(this).removeChild(this)}appendChild(e){e.parent&&$c.get(e).removeChild(e);const t=this.children.filter((t=>e.contains(t)));$c.set(e,this),this.children.push(e),t.forEach((t=>e.appendChild(t))),R.getNotifier(this.store).subscribe(e);for(const[t,i]of this.store.all())e.hydrate(t,this.bindingObservers.has(t)?this.getRaw(t):i)}removeChild(e){const t=this.children.indexOf(e);return-1!==t&&this.children.splice(t,1),R.getNotifier(this.store).unsubscribe(e),e.parent===this&&$c.delete(e)}contains(e){return function(e,t){let i=t;for(;null!==i;){if(i===e)return!0;i=yc(i)}return!1}(this.target,e.target)}reflectToCSS(e){this.isReflecting(e)||(this.reflecting.add(e),Hc.cssCustomPropertyReflector.startReflection(e,this.target))}stopReflectToCSS(e){this.isReflecting(e)&&(this.reflecting.delete(e),Hc.cssCustomPropertyReflector.stopReflection(e,this.target))}isReflecting(e){return this.reflecting.has(e)}handleChange(e,t){const i=Bc.getTokenById(t);i&&this.hydrate(i,this.getRaw(i))}hydrate(e,t){if(!this.has(e)){const i=this.bindingObservers.get(e);Bc.isDerivedDesignTokenValue(t)?i?i.source!==t&&(this.tearDownBindingObserver(e),this.setupBindingObserver(e,t)):this.setupBindingObserver(e,t):(i&&this.tearDownBindingObserver(e),this.store.set(e,t))}}setupBindingObserver(e,t){const i=new Gc(t,e,this);return this.bindingObservers.set(e,i),i}tearDownBindingObserver(e){return!!this.bindingObservers.has(e)&&(this.bindingObservers.get(e).disconnect(),this.bindingObservers.delete(e),!0)}}Hc.cssCustomPropertyReflector=new class{startReflection(e,t){e.subscribe(this,t),this.handleChange({token:e,target:t})}stopReflection(e,t){e.unsubscribe(this,t),this.remove(e,t)}handleChange(e){const{token:t,target:i}=e;this.add(t,i)}add(e,t){Wc.getOrCreate(t).setProperty(e.cssCustomProperty,this.resolveCSSValue(Hc.getOrCreate(t).get(e)))}remove(e,t){Wc.getOrCreate(t).removeProperty(e.cssCustomProperty)}resolveCSSValue(e){return e&&"function"==typeof e.createCSS?e.createCSS():e}},kl([y],Hc.prototype,"children",void 0);const zc=Object.freeze({create:function(e){return Bc.from(e)},notifyConnection:e=>!(!e.isConnected||!Hc.existsFor(e)||(Hc.getOrCreate(e).bind(),0)),notifyDisconnection:e=>!(e.isConnected||!Hc.existsFor(e)||(Hc.getOrCreate(e).unbind(),0)),registerRoot(e=Sc){Dc.registerRoot(e)},unregisterRoot(e=Sc){Dc.unregisterRoot(e)}}),jc=Object.freeze({definitionCallbackOnly:null,ignoreDuplicate:Symbol()}),Zc=new Map,Yc=new Map;let Kc=null;const qc=Gl.createInterface((e=>e.cachedCallback((e=>(null===Kc&&(Kc=new Qc(null,e)),Kc))))),Xc=Object.freeze({tagFor:e=>Yc.get(e),responsibleFor(e){const t=e.$$designSystem$$;return t||Gl.findResponsibleContainer(e).get(qc)},getOrCreate(e){if(!e)return null===Kc&&(Kc=Gl.getOrCreateDOMContainer().get(qc)),Kc;const t=e.$$designSystem$$;if(t)return t;const i=Gl.getOrCreateDOMContainer(e);if(i.has(qc,!1))return i.get(qc);{const t=new Qc(e,i);return i.register(nc.instance(qc,t)),t}}});class Qc{constructor(e,t){this.owner=e,this.container=t,this.designTokensInitialized=!1,this.prefix="fast",this.shadowRootMode=void 0,this.disambiguate=()=>jc.definitionCallbackOnly,null!==e&&(e.$$designSystem$$=this)}withPrefix(e){return this.prefix=e,this}withShadowRootMode(e){return this.shadowRootMode=e,this}withElementDisambiguation(e){return this.disambiguate=e,this}withDesignTokenRoot(e){return this.designTokenRoot=e,this}register(...e){const t=this.container,i=[],o=this.disambiguate,r=this.shadowRootMode,n={elementPrefix:this.prefix,tryDefineElement(e,n,a){const s=function(e,t,i){return"string"==typeof e?{name:e,type:t,callback:i}:e}(e,n,a),{name:l,callback:c,baseClass:d}=s;let{type:u}=s,p=l,h=Zc.get(p),f=!0;for(;h;){const e=o(p,u,h);switch(e){case jc.ignoreDuplicate:return;case jc.definitionCallbackOnly:f=!1,h=void 0;break;default:p=e,h=Zc.get(p)}}f&&((Yc.has(u)||u===Tc)&&(u=class extends u{}),Zc.set(p,u),Yc.set(u,p),d&&Yc.set(d,p)),i.push(new Jc(t,p,u,r,c,f))}};this.designTokensInitialized||(this.designTokensInitialized=!0,null!==this.designTokenRoot&&zc.registerRoot(this.designTokenRoot)),t.registerWithContext(n,...e);for(const e of i)e.callback(e),e.willDefine&&null!==e.definition&&e.definition.define();return this}}class Jc{constructor(e,t,i,o,r,n){this.container=e,this.name=t,this.type=i,this.shadowRootMode=o,this.callback=r,this.willDefine=n,this.definition=null}definePresentation(e){gc.define(this.name,e,this.container)}defineElement(e){this.definition=new Te(this.type,Object.assign(Object.assign({},e),{name:this.name}))}tagFor(e){return Xc.tagFor(e)}}function ed(e,t,i,o){var r,n=arguments.length,a=n<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,i):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,i,o);else for(var s=e.length-1;s>=0;s--)(r=e[s])&&(a=(n<3?r(a):n>3?r(t,i,a):r(t,i))||a);return n>3&&a&&Object.defineProperty(t,i,a),a}function td(e,t,i){return isNaN(e)||e<=t?t:e>=i?i:e}function id(e,t,i){return isNaN(e)||e<=t?0:e>=i?1:e/(i-t)}function od(e,t,i){return isNaN(e)?t:t+e*(i-t)}function rd(e){return e*(Math.PI/180)}function nd(e,t,i){return isNaN(e)||e<=0?t:e>=1?i:t+e*(i-t)}function ad(e,t,i){if(e<=0)return t%360;if(e>=1)return i%360;const o=(t-i+360)%360;return o<=(i-t+360)%360?(t-o*e+360)%360:(t+o*e+360)%360}function sd(e,t){const i=Math.pow(10,t);return Math.round(e*i)/i}Math.PI;class ld{constructor(e,t,i,o){this.r=e,this.g=t,this.b=i,this.a="number"!=typeof o||isNaN(o)?1:o}static fromObject(e){return!e||isNaN(e.r)||isNaN(e.g)||isNaN(e.b)?null:new ld(e.r,e.g,e.b,e.a)}equalValue(e){return this.r===e.r&&this.g===e.g&&this.b===e.b&&this.a===e.a}toStringHexRGB(){return"#"+[this.r,this.g,this.b].map(this.formatHexValue).join("")}toStringHexRGBA(){return this.toStringHexRGB()+this.formatHexValue(this.a)}toStringHexARGB(){return"#"+[this.a,this.r,this.g,this.b].map(this.formatHexValue).join("")}toStringWebRGB(){return`rgb(${Math.round(od(this.r,0,255))},${Math.round(od(this.g,0,255))},${Math.round(od(this.b,0,255))})`}toStringWebRGBA(){return`rgba(${Math.round(od(this.r,0,255))},${Math.round(od(this.g,0,255))},${Math.round(od(this.b,0,255))},${td(this.a,0,1)})`}roundToPrecision(e){return new ld(sd(this.r,e),sd(this.g,e),sd(this.b,e),sd(this.a,e))}clamp(){return new ld(td(this.r,0,1),td(this.g,0,1),td(this.b,0,1),td(this.a,0,1))}toObject(){return{r:this.r,g:this.g,b:this.b,a:this.a}}formatHexValue(e){return function(e){const t=Math.round(td(e,0,255)).toString(16);return 1===t.length?"0"+t:t}(od(e,0,255))}}const cd=/^#((?:[0-9a-f]{6}|[0-9a-f]{3}))$/i;function dd(e){const t=cd.exec(e);if(null===t)return null;let i=t[1];if(3===i.length){const e=i.charAt(0),t=i.charAt(1),o=i.charAt(2);i=e.concat(e,t,t,o,o)}const o=parseInt(i,16);return isNaN(o)?null:new ld(id((16711680&o)>>>16,0,255),id((65280&o)>>>8,0,255),id(255&o,0,255),1)}class ud extends class{constructor(e){this.listenerCache=new WeakMap,this.query=e}bind(e){const{query:t}=this,i=this.constructListener(e);i.bind(t)(),t.addListener(i),this.listenerCache.set(e,i)}unbind(e){const t=this.listenerCache.get(e);t&&(this.query.removeListener(t),this.listenerCache.delete(e))}}{constructor(e,t){super(e),this.styles=t}static with(e){return t=>new ud(e,t)}constructListener(e){let t=!1;const i=this.styles;return function(){const{matches:o}=this;o&&!t?(e.$fastController.addStyles(i),t=o):!o&&t&&(e.$fastController.removeStyles(i),t=o)}}unbind(e){super.unbind(e),e.$fastController.removeStyles(this.styles)}}const pd=ud.with(window.matchMedia("(forced-colors)"));function hd(e){return`:host([hidden]){display:none}:host{display:${e}}`}var fd,gd;ud.with(window.matchMedia("(prefers-color-scheme: dark)")),ud.with(window.matchMedia("(prefers-color-scheme: light)")),(gd=fd||(fd={})).Canvas="Canvas",gd.CanvasText="CanvasText",gd.LinkText="LinkText",gd.VisitedText="VisitedText",gd.ActiveText="ActiveText",gd.ButtonFace="ButtonFace",gd.ButtonText="ButtonText",gd.Field="Field",gd.FieldText="FieldText",gd.Highlight="Highlight",gd.HighlightText="HighlightText",gd.GrayText="GrayText";class Ed{constructor(e,t,i){this.h=e,this.s=t,this.l=i}static fromObject(e){return!e||isNaN(e.h)||isNaN(e.s)||isNaN(e.l)?null:new Ed(e.h,e.s,e.l)}equalValue(e){return this.h===e.h&&this.s===e.s&&this.l===e.l}roundToPrecision(e){return new Ed(sd(this.h,e),sd(this.s,e),sd(this.l,e))}toObject(){return{h:this.h,s:this.s,l:this.l}}}class Td{constructor(e,t,i){this.h=e,this.s=t,this.v=i}static fromObject(e){return!e||isNaN(e.h)||isNaN(e.s)||isNaN(e.v)?null:new Td(e.h,e.s,e.v)}equalValue(e){return this.h===e.h&&this.s===e.s&&this.v===e.v}roundToPrecision(e){return new Td(sd(this.h,e),sd(this.s,e),sd(this.v,e))}toObject(){return{h:this.h,s:this.s,v:this.v}}}class md{constructor(e,t,i){this.l=e,this.a=t,this.b=i}static fromObject(e){return!e||isNaN(e.l)||isNaN(e.a)||isNaN(e.b)?null:new md(e.l,e.a,e.b)}equalValue(e){return this.l===e.l&&this.a===e.a&&this.b===e.b}roundToPrecision(e){return new md(sd(this.l,e),sd(this.a,e),sd(this.b,e))}toObject(){return{l:this.l,a:this.a,b:this.b}}}md.epsilon=216/24389,md.kappa=24389/27;class Nd{constructor(e,t,i){this.l=e,this.c=t,this.h=i}static fromObject(e){return!e||isNaN(e.l)||isNaN(e.c)||isNaN(e.h)?null:new Nd(e.l,e.c,e.h)}equalValue(e){return this.l===e.l&&this.c===e.c&&this.h===e.h}roundToPrecision(e){return new Nd(sd(this.l,e),sd(this.c,e),sd(this.h,e))}toObject(){return{l:this.l,c:this.c,h:this.h}}}class _d{constructor(e,t,i){this.x=e,this.y=t,this.z=i}static fromObject(e){return!e||isNaN(e.x)||isNaN(e.y)||isNaN(e.z)?null:new _d(e.x,e.y,e.z)}equalValue(e){return this.x===e.x&&this.y===e.y&&this.z===e.z}roundToPrecision(e){return new _d(sd(this.x,e),sd(this.y,e),sd(this.z,e))}toObject(){return{x:this.x,y:this.y,z:this.z}}}function Od(e){return.2126*e.r+.7152*e.g+.0722*e.b}function bd(e){function t(e){return e<=.03928?e/12.92:Math.pow((e+.055)/1.055,2.4)}return Od(new ld(t(e.r),t(e.g),t(e.b),1))}_d.whitePoint=new _d(.95047,1,1.08883);const Id=(e,t)=>(e+.05)/(t+.05);function Ad(e,t){const i=bd(e),o=bd(t);return i>o?Id(i,o):Id(o,i)}function Cd(e){const t=Math.max(e.r,e.g,e.b),i=Math.min(e.r,e.g,e.b),o=t-i;let r=0;0!==o&&(r=t===e.r?(e.g-e.b)/o%6*60:t===e.g?60*((e.b-e.r)/o+2):60*((e.r-e.g)/o+4)),r<0&&(r+=360);const n=(t+i)/2;let a=0;return 0!==o&&(a=o/(1-Math.abs(2*n-1))),new Ed(r,a,n)}function vd(e,t=1){const i=(1-Math.abs(2*e.l-1))*e.s,o=i*(1-Math.abs(e.h/60%2-1)),r=e.l-i/2;let n=0,a=0,s=0;return e.h<60?(n=i,a=o,s=0):e.h<120?(n=o,a=i,s=0):e.h<180?(n=0,a=i,s=o):e.h<240?(n=0,a=o,s=i):e.h<300?(n=o,a=0,s=i):e.h<360&&(n=i,a=0,s=o),new ld(n+r,a+r,s+r,t)}function Rd(e){const t=Math.max(e.r,e.g,e.b),i=t-Math.min(e.r,e.g,e.b);let o=0;0!==i&&(o=t===e.r?(e.g-e.b)/i%6*60:t===e.g?60*((e.b-e.r)/i+2):60*((e.r-e.g)/i+4)),o<0&&(o+=360);let r=0;return 0!==t&&(r=i/t),new Td(o,r,t)}function yd(e){function t(e){return e<=.04045?e/12.92:Math.pow((e+.055)/1.055,2.4)}const i=t(e.r),o=t(e.g),r=t(e.b);return new _d(.4124564*i+.3575761*o+.1804375*r,.2126729*i+.7151522*o+.072175*r,.0193339*i+.119192*o+.9503041*r)}function Sd(e,t=1){function i(e){return e<=.0031308?12.92*e:1.055*Math.pow(e,1/2.4)-.055}const o=i(3.2404542*e.x-1.5371385*e.y-.4985314*e.z),r=i(-.969266*e.x+1.8760108*e.y+.041556*e.z),n=i(.0556434*e.x-.2040259*e.y+1.0572252*e.z);return new ld(o,r,n,t)}function wd(e){return function(e){function t(e){return e>md.epsilon?Math.pow(e,1/3):(md.kappa*e+16)/116}const i=t(e.x/_d.whitePoint.x),o=t(e.y/_d.whitePoint.y),r=t(e.z/_d.whitePoint.z);return new md(116*o-16,500*(i-o),200*(o-r))}(yd(e))}function kd(e,t=1){return Sd(function(e){const t=(e.l+16)/116,i=t+e.a/500,o=t-e.b/200,r=Math.pow(i,3),n=Math.pow(t,3),a=Math.pow(o,3);let s=0;s=r>md.epsilon?r:(116*i-16)/md.kappa;let l=0;l=e.l>md.epsilon*md.kappa?n:e.l/md.kappa;let c=0;return c=a>md.epsilon?a:(116*o-16)/md.kappa,s=_d.whitePoint.x*s,l=_d.whitePoint.y*l,c=_d.whitePoint.z*c,new _d(s,l,c)}(e),t)}function Md(e){return function(e){let t=0;(Math.abs(e.b)>.001||Math.abs(e.a)>.001)&&(t=Math.atan2(e.b,e.a)*(180/Math.PI)),t<0&&(t+=360);const i=Math.sqrt(e.a*e.a+e.b*e.b);return new Nd(e.l,i,t)}(wd(e))}function xd(e,t=1){return kd(function(e){let t=0,i=0;return 0!==e.h&&(t=Math.cos(rd(e.h))*e.c,i=Math.sin(rd(e.h))*e.c),new md(e.l,t,i)}(e),t)}function Pd(e,t,i=18){const o=Md(e);let r=o.c+t*i;return r<0&&(r=0),xd(new Nd(o.l,r,o.h))}function Dd(e,t){return e*t}function Ld(e,t){return new ld(Dd(e.r,t.r),Dd(e.g,t.g),Dd(e.b,t.b),1)}function Fd(e,t){return td(e<.5?2*t*e:1-2*(1-t)*(1-e),0,1)}function Wd(e,t){return new ld(Fd(e.r,t.r),Fd(e.g,t.g),Fd(e.b,t.b),1)}var Bd,Gd,Vd,Ud;function $d(e,t,i,o){if(isNaN(e)||e<=0)return i;if(e>=1)return o;switch(t){case Vd.HSL:return vd(function(e,t,i){return isNaN(e)||e<=0?t:e>=1?i:new Ed(ad(e,t.h,i.h),nd(e,t.s,i.s),nd(e,t.l,i.l))}(e,Cd(i),Cd(o)));case Vd.HSV:return function(e,t=1){const i=e.s*e.v,o=i*(1-Math.abs(e.h/60%2-1)),r=e.v-i;let n=0,a=0,s=0;return e.h<60?(n=i,a=o,s=0):e.h<120?(n=o,a=i,s=0):e.h<180?(n=0,a=i,s=o):e.h<240?(n=0,a=o,s=i):e.h<300?(n=o,a=0,s=i):e.h<360&&(n=i,a=0,s=o),new ld(n+r,a+r,s+r,t)}(function(e,t,i){return isNaN(e)||e<=0?t:e>=1?i:new Td(ad(e,t.h,i.h),nd(e,t.s,i.s),nd(e,t.v,i.v))}(e,Rd(i),Rd(o)));case Vd.XYZ:return Sd(function(e,t,i){return isNaN(e)||e<=0?t:e>=1?i:new _d(nd(e,t.x,i.x),nd(e,t.y,i.y),nd(e,t.z,i.z))}(e,yd(i),yd(o)));case Vd.LAB:return kd(function(e,t,i){return isNaN(e)||e<=0?t:e>=1?i:new md(nd(e,t.l,i.l),nd(e,t.a,i.a),nd(e,t.b,i.b))}(e,wd(i),wd(o)));case Vd.LCH:return xd(function(e,t,i){return isNaN(e)||e<=0?t:e>=1?i:new Nd(nd(e,t.l,i.l),nd(e,t.c,i.c),ad(e,t.h,i.h))}(e,Md(i),Md(o)));default:return function(e,t,i){return isNaN(e)||e<=0?t:e>=1?i:new ld(nd(e,t.r,i.r),nd(e,t.g,i.g),nd(e,t.b,i.b),nd(e,t.a,i.a))}(e,i,o)}}(Gd=Bd||(Bd={}))[Gd.Burn=0]="Burn",Gd[Gd.Color=1]="Color",Gd[Gd.Darken=2]="Darken",Gd[Gd.Dodge=3]="Dodge",Gd[Gd.Lighten=4]="Lighten",Gd[Gd.Multiply=5]="Multiply",Gd[Gd.Overlay=6]="Overlay",Gd[Gd.Screen=7]="Screen",(Ud=Vd||(Vd={}))[Ud.RGB=0]="RGB",Ud[Ud.HSL=1]="HSL",Ud[Ud.HSV=2]="HSV",Ud[Ud.XYZ=3]="XYZ",Ud[Ud.LAB=4]="LAB",Ud[Ud.LCH=5]="LCH";class Hd{constructor(e){if(null==e||0===e.length)throw new Error("The stops argument must be non-empty");this.stops=this.sortColorScaleStops(e)}static createBalancedColorScale(e){if(null==e||0===e.length)throw new Error("The colors argument must be non-empty");const t=new Array(e.length);for(let i=0;i<e.length;i++)0===i?t[i]={color:e[i],position:0}:i===e.length-1?t[i]={color:e[i],position:1}:t[i]={color:e[i],position:i*(1/(e.length-1))};return new Hd(t)}getColor(e,t=Vd.RGB){if(1===this.stops.length)return this.stops[0].color;if(e<=0)return this.stops[0].color;if(e>=1)return this.stops[this.stops.length-1].color;let i=0;for(let t=0;t<this.stops.length;t++)this.stops[t].position<=e&&(i=t);let o=i+1;return o>=this.stops.length&&(o=this.stops.length-1),$d((e-this.stops[i].position)*(1/(this.stops[o].position-this.stops[i].position)),t,this.stops[i].color,this.stops[o].color)}trim(e,t,i=Vd.RGB){if(e<0||t>1||t<e)throw new Error("Invalid bounds");if(e===t)return new Hd([{color:this.getColor(e,i),position:0}]);const o=[];for(let i=0;i<this.stops.length;i++)this.stops[i].position>=e&&this.stops[i].position<=t&&o.push(this.stops[i]);if(0===o.length)return new Hd([{color:this.getColor(e),position:e},{color:this.getColor(t),position:t}]);o[0].position!==e&&o.unshift({color:this.getColor(e),position:e}),o[o.length-1].position!==t&&o.push({color:this.getColor(t),position:t});const r=t-e,n=new Array(o.length);for(let t=0;t<o.length;t++)n[t]={color:o[t].color,position:(o[t].position-e)/r};return new Hd(n)}findNextColor(e,t,i=!1,o=Vd.RGB,r=.005,n=32){isNaN(e)||e<=0?e=0:e>=1&&(e=1);const a=this.getColor(e,o),s=i?0:1;if(Ad(a,this.getColor(s,o))<=t)return s;let l=i?0:e,c=i?e:0,d=s,u=0;for(;u<=n;){d=Math.abs(c-l)/2+l;const e=Ad(a,this.getColor(d,o));if(Math.abs(e-t)<=r)return d;e>t?i?l=d:c=d:i?c=d:l=d,u++}return d}clone(){const e=new Array(this.stops.length);for(let t=0;t<e.length;t++)e[t]={color:this.stops[t].color,position:this.stops[t].position};return new Hd(e)}sortColorScaleStops(e){return e.sort(((e,t)=>{const i=e.position,o=t.position;return i<o?-1:i>o?1:0}))}}class zd{constructor(e){this.config=Object.assign({},zd.defaultPaletteConfig,e),this.palette=[],this.updatePaletteColors()}updatePaletteGenerationValues(e){let t=!1;for(const i in e)this.config[i]&&(this.config[i].equalValue?this.config[i].equalValue(e[i])||(this.config[i]=e[i],t=!0):e[i]!==this.config[i]&&(this.config[i]=e[i],t=!0));return t&&this.updatePaletteColors(),t}updatePaletteColors(){const e=this.generatePaletteColorScale();for(let t=0;t<this.config.steps;t++)this.palette[t]=e.getColor(t/(this.config.steps-1),this.config.interpolationMode)}generatePaletteColorScale(){const e=Cd(this.config.baseColor),t=new Hd([{position:0,color:this.config.scaleColorLight},{position:.5,color:this.config.baseColor},{position:1,color:this.config.scaleColorDark}]).trim(this.config.clipLight,1-this.config.clipDark);let i=t.getColor(0),o=t.getColor(1);if(e.s>=this.config.saturationAdjustmentCutoff&&(i=Pd(i,this.config.saturationLight),o=Pd(o,this.config.saturationDark)),0!==this.config.multiplyLight){const e=Ld(this.config.baseColor,i);i=$d(this.config.multiplyLight,this.config.interpolationMode,i,e)}if(0!==this.config.multiplyDark){const e=Ld(this.config.baseColor,o);o=$d(this.config.multiplyDark,this.config.interpolationMode,o,e)}if(0!==this.config.overlayLight){const e=Wd(this.config.baseColor,i);i=$d(this.config.overlayLight,this.config.interpolationMode,i,e)}if(0!==this.config.overlayDark){const e=Wd(this.config.baseColor,o);o=$d(this.config.overlayDark,this.config.interpolationMode,o,e)}return this.config.baseScalePosition?this.config.baseScalePosition<=0?new Hd([{position:0,color:this.config.baseColor},{position:1,color:o.clamp()}]):this.config.baseScalePosition>=1?new Hd([{position:0,color:i.clamp()},{position:1,color:this.config.baseColor}]):new Hd([{position:0,color:i.clamp()},{position:this.config.baseScalePosition,color:this.config.baseColor},{position:1,color:o.clamp()}]):new Hd([{position:0,color:i.clamp()},{position:.5,color:this.config.baseColor},{position:1,color:o.clamp()}])}}zd.defaultPaletteConfig={baseColor:dd("#808080"),steps:11,interpolationMode:Vd.RGB,scaleColorLight:new ld(1,1,1,1),scaleColorDark:new ld(0,0,0,1),clipLight:.185,clipDark:.16,saturationAdjustmentCutoff:.05,saturationLight:.35,saturationDark:1.25,overlayLight:0,overlayDark:.25,multiplyLight:0,multiplyDark:0,baseScalePosition:.5},zd.greyscalePaletteConfig={baseColor:dd("#808080"),steps:11,interpolationMode:Vd.RGB,scaleColorLight:new ld(1,1,1,1),scaleColorDark:new ld(0,0,0,1),clipLight:0,clipDark:0,saturationAdjustmentCutoff:0,saturationLight:0,saturationDark:0,overlayLight:0,overlayDark:0,multiplyLight:0,multiplyDark:0,baseScalePosition:.5},zd.defaultPaletteConfig.scaleColorLight,zd.defaultPaletteConfig.scaleColorDark;class jd{constructor(e){this.palette=[],this.config=Object.assign({},jd.defaultPaletteConfig,e),this.regenPalettes()}regenPalettes(){let e=this.config.steps;(isNaN(e)||e<3)&&(e=3);const t=.14,i=new ld(t,t,t,1),o=new zd(Object.assign(Object.assign({},zd.greyscalePaletteConfig),{baseColor:i,baseScalePosition:86/94,steps:e})).palette,r=(Od(this.config.baseColor)+Cd(this.config.baseColor).l)/2,n=this.matchRelativeLuminanceIndex(r,o)/(e-1),a=this.matchRelativeLuminanceIndex(t,o)/(e-1),s=Cd(this.config.baseColor),l=vd(Ed.fromObject({h:s.h,s:s.s,l:t})),c=vd(Ed.fromObject({h:s.h,s:s.s,l:.06})),d=new Array(5);d[0]={position:0,color:new ld(1,1,1,1)},d[1]={position:n,color:this.config.baseColor},d[2]={position:a,color:l},d[3]={position:.99,color:c},d[4]={position:1,color:new ld(0,0,0,1)};const u=new Hd(d);this.palette=new Array(e);for(let t=0;t<e;t++){const i=u.getColor(t/(e-1),Vd.RGB);this.palette[t]=i}}matchRelativeLuminanceIndex(e,t){let i=Number.MAX_VALUE,o=0,r=0;const n=t.length;for(;r<n;r++){const n=Math.abs(Od(t[r])-e);n<i&&(i=n,o=r)}return o}}function Zd(e,t){const i=e.relativeLuminance>t.relativeLuminance?e:t,o=e.relativeLuminance>t.relativeLuminance?t:e;return(i.relativeLuminance+.05)/(o.relativeLuminance+.05)}jd.defaultPaletteConfig={baseColor:dd("#808080"),steps:94};const Yd=Object.freeze({create:(e,t,i)=>new Kd(e,t,i),from:e=>new Kd(e.r,e.g,e.b)});class Kd extends ld{constructor(e,t,i){super(e,t,i,1),this.toColorString=this.toStringHexRGB,this.contrast=Zd.bind(null,this),this.createCSS=this.toColorString,this.relativeLuminance=bd(this)}static fromObject(e){return new Kd(e.r,e.g,e.b)}}function qd(e,t,i=0,o=e.length-1){if(o===i)return e[i];const r=Math.floor((o-i)/2)+i;return t(e[r])?qd(e,t,i,r):qd(e,t,r+1,o)}const Xd=(-.1+Math.sqrt(.21))/2;function Qd(e){return function(e){return e.relativeLuminance<=Xd}(e)?-1:1}const Jd=Object.freeze({create:e=>eu.from(e)});class eu{constructor(e,t){this.closestIndexCache=new Map,this.source=e,this.swatches=t,this.reversedSwatches=Object.freeze([...this.swatches].reverse()),this.lastIndex=this.swatches.length-1}colorContrast(e,t,i,o){void 0===i&&(i=this.closestIndexOf(e));let r=this.swatches;const n=this.lastIndex;let a=i;return void 0===o&&(o=Qd(e)),-1===o&&(r=this.reversedSwatches,a=n-a),qd(r,(i=>Zd(e,i)>=t),a,n)}get(e){return this.swatches[e]||this.swatches[td(e,0,this.lastIndex)]}closestIndexOf(e){if(this.closestIndexCache.has(e.relativeLuminance))return this.closestIndexCache.get(e.relativeLuminance);let t=this.swatches.indexOf(e);if(-1!==t)return this.closestIndexCache.set(e.relativeLuminance,t),t;const i=this.swatches.reduce(((t,i)=>Math.abs(i.relativeLuminance-e.relativeLuminance)<Math.abs(t.relativeLuminance-e.relativeLuminance)?i:t));return t=this.swatches.indexOf(i),this.closestIndexCache.set(e.relativeLuminance,t),t}static from(e){return new eu(e,Object.freeze(new jd({baseColor:ld.fromObject(e)}).palette.map((e=>{const t=dd(e.toStringHexRGB());return Yd.create(t.r,t.g,t.b)}))))}}var tu,iu;(iu=tu||(tu={})).ltr="ltr",iu.rtl="rtl";const ou=Yd.create(1,1,1),ru=Yd.create(0,0,0),nu=Yd.create(.5,.5,.5),au=dd("#0078D4"),su=Yd.create(au.r,au.g,au.b);function lu(e){return Yd.create(e,e,e)}var cu,du;function uu(e,t,i,o,r,n){return Math.max(e.closestIndexOf(lu(t))+i,o,r,n)}(du=cu||(cu={}))[du.LightMode=1]="LightMode",du[du.DarkMode=.23]="DarkMode";const{create:pu}=zc,hu=pu("direction").withDefault(tu.ltr),fu=pu("disabled-opacity").withDefault(.3),gu=pu("base-height-multiplier").withDefault(8),Eu=pu("base-horizontal-spacing-multiplier").withDefault(3),Tu=pu("density").withDefault(0),mu=pu("design-unit").withDefault(4),Nu=pu("control-corner-radius").withDefault(4),_u=(pu("layer-corner-radius").withDefault(4),pu("stroke-width").withDefault(1)),Ou=pu("focus-stroke-width").withDefault(2),bu=pu("body-font").withDefault("Segoe UI, sans-serif"),Iu=pu("type-ramp-base-font-size").withDefault("14px"),Au=pu("type-ramp-base-line-height").withDefault("20px"),Cu=pu("type-ramp-minus-1-font-size").withDefault("12px"),vu=pu("type-ramp-minus-1-line-height").withDefault("16px"),Ru=pu("type-ramp-minus-2-font-size").withDefault("10px"),yu=pu("type-ramp-minus-2-line-height").withDefault("14px"),Su=pu("type-ramp-plus-1-font-size").withDefault("16px"),wu=pu("type-ramp-plus-1-line-height").withDefault("22px"),ku=pu("type-ramp-plus-2-font-size").withDefault("20px"),Mu=pu("type-ramp-plus-2-line-height").withDefault("28px"),xu=pu("type-ramp-plus-3-font-size").withDefault("24px"),Pu=pu("type-ramp-plus-3-line-height").withDefault("32px"),Du=pu("type-ramp-plus-4-font-size").withDefault("28px"),Lu=pu("type-ramp-plus-4-line-height").withDefault("36px"),Fu=pu("type-ramp-plus-5-font-size").withDefault("32px"),Wu=pu("type-ramp-plus-5-line-height").withDefault("40px"),Bu=pu("type-ramp-plus-6-font-size").withDefault("40px"),Gu=pu("type-ramp-plus-6-line-height").withDefault("52px"),Vu=pu("base-layer-luminance").withDefault(cu.LightMode),Uu=pu("accent-fill-rest-delta").withDefault(0),$u=pu("accent-fill-hover-delta").withDefault(4),Hu=pu("accent-fill-active-delta").withDefault(-5),zu=pu("accent-fill-focus-delta").withDefault(0),ju=pu("accent-foreground-rest-delta").withDefault(0),Zu=pu("accent-foreground-hover-delta").withDefault(6),Yu=pu("accent-foreground-active-delta").withDefault(-4),Ku=pu("accent-foreground-focus-delta").withDefault(0),qu=pu("neutral-fill-rest-delta").withDefault(7),Xu=pu("neutral-fill-hover-delta").withDefault(10),Qu=pu("neutral-fill-active-delta").withDefault(5),Ju=pu("neutral-fill-focus-delta").withDefault(0),ep=pu("neutral-fill-input-rest-delta").withDefault(0),tp=pu("neutral-fill-input-hover-delta").withDefault(0),ip=pu("neutral-fill-input-active-delta").withDefault(0),op=pu("neutral-fill-input-focus-delta").withDefault(0),rp=pu("neutral-fill-inverse-rest-delta").withDefault(0),np=pu("neutral-fill-inverse-hover-delta").withDefault(-3),ap=pu("neutral-fill-inverse-active-delta").withDefault(7),sp=pu("neutral-fill-inverse-focus-delta").withDefault(0),lp=pu("neutral-fill-layer-rest-delta").withDefault(3),cp=pu("neutral-fill-stealth-rest-delta").withDefault(0),dp=pu("neutral-fill-stealth-hover-delta").withDefault(5),up=pu("neutral-fill-stealth-active-delta").withDefault(3),pp=pu("neutral-fill-stealth-focus-delta").withDefault(0),hp=pu("neutral-fill-strong-rest-delta").withDefault(0),fp=pu("neutral-fill-strong-hover-delta").withDefault(8),gp=pu("neutral-fill-strong-active-delta").withDefault(-5),Ep=pu("neutral-fill-strong-focus-delta").withDefault(0),Tp=pu("neutral-stroke-rest-delta").withDefault(25),mp=pu("neutral-stroke-hover-delta").withDefault(40),Np=pu("neutral-stroke-active-delta").withDefault(16),_p=pu("neutral-stroke-focus-delta").withDefault(25),Op=pu("neutral-stroke-divider-rest-delta").withDefault(8),bp=pu("neutral-stroke-strong-hover-delta").withDefault(40),Ip=pu("neutral-stroke-strong-active-delta").withDefault(16),Ap=pu("neutral-stroke-strong-focus-delta").withDefault(25),Cp=pu({name:"neutral-palette",cssCustomPropertyName:null}).withDefault(Jd.create(nu)),vp=pu({name:"accent-palette",cssCustomPropertyName:null}).withDefault(Jd.create(su)),Rp=pu({name:"neutral-layer-card-container-recipe",cssCustomPropertyName:null}).withDefault({evaluate:e=>function(e,t,i){const o=td(e.closestIndexOf(lu(t))-i,0,e.swatches.length-1);return e.get(o+i)}(Cp.getValueFor(e),Vu.getValueFor(e),lp.getValueFor(e))}),yp=(pu("neutral-layer-card-container").withDefault((e=>Rp.getValueFor(e).evaluate(e))),pu({name:"neutral-layer-floating-recipe",cssCustomPropertyName:null}).withDefault({evaluate:e=>function(e,t,i){const o=e.closestIndexOf(lu(t))-i;return e.get(o-i)}(Cp.getValueFor(e),Vu.getValueFor(e),lp.getValueFor(e))})),Sp=(pu("neutral-layer-floating").withDefault((e=>yp.getValueFor(e).evaluate(e))),pu({name:"neutral-layer-1-recipe",cssCustomPropertyName:null}).withDefault({evaluate:e=>function(e,t){return e.get(e.closestIndexOf(lu(t)))}(Cp.getValueFor(e),Vu.getValueFor(e))})),wp=pu("neutral-layer-1").withDefault((e=>Sp.getValueFor(e).evaluate(e))),kp=pu({name:"neutral-layer-2-recipe",cssCustomPropertyName:null}).withDefault({evaluate:e=>{return t=Cp.getValueFor(e),i=Vu.getValueFor(e),o=lp.getValueFor(e),r=qu.getValueFor(e),n=Xu.getValueFor(e),a=Qu.getValueFor(e),t.get(uu(t,i,o,r,n,a));var t,i,o,r,n,a}}),Mp=(pu("neutral-layer-2").withDefault((e=>kp.getValueFor(e).evaluate(e))),pu({name:"neutral-layer-3-recipe",cssCustomPropertyName:null}).withDefault({evaluate:e=>{return t=Cp.getValueFor(e),i=Vu.getValueFor(e),o=lp.getValueFor(e),r=qu.getValueFor(e),n=Xu.getValueFor(e),a=Qu.getValueFor(e),t.get(uu(t,i,o,r,n,a)+o);var t,i,o,r,n,a}})),xp=(pu("neutral-layer-3").withDefault((e=>Mp.getValueFor(e).evaluate(e))),pu({name:"neutral-layer-4-recipe",cssCustomPropertyName:null}).withDefault({evaluate:e=>{return t=Cp.getValueFor(e),i=Vu.getValueFor(e),o=lp.getValueFor(e),r=qu.getValueFor(e),n=Xu.getValueFor(e),a=Qu.getValueFor(e),t.get(uu(t,i,o,r,n,a)+2*o);var t,i,o,r,n,a}})),Pp=(pu("neutral-layer-4").withDefault((e=>xp.getValueFor(e).evaluate(e))),pu("fill-color").withDefault((e=>wp.getValueFor(e))));var Dp,Lp;(Lp=Dp||(Dp={}))[Lp.normal=4.5]="normal",Lp[Lp.large=7]="large";const Fp=pu({name:"accent-fill-recipe",cssCustomPropertyName:null}).withDefault({evaluate:(e,t)=>(Dp.normal,(e,t)=>function(e,t,i,o,r,n,a,s,l){const c=e.source,d=e.closestIndexOf(c),u=d+1*o,p=d+1*r,h=d+1*n;return{rest:e.get(d),hover:e.get(u),active:e.get(p),focus:e.get(h)}}(vp.getValueFor(e),Cp.getValueFor(e),t||Pp.getValueFor(e),$u.getValueFor(e),Hu.getValueFor(e),zu.getValueFor(e),qu.getValueFor(e),Xu.getValueFor(e),Qu.getValueFor(e)))(e,t)}),Wp=pu("accent-fill-rest").withDefault((e=>Fp.getValueFor(e).evaluate(e).rest)),Bp=pu("accent-fill-hover").withDefault((e=>Fp.getValueFor(e).evaluate(e).hover)),Gp=pu("accent-fill-active").withDefault((e=>Fp.getValueFor(e).evaluate(e).active)),Vp=pu("accent-fill-focus").withDefault((e=>Fp.getValueFor(e).evaluate(e).focus)),Up=e=>(t,i)=>function(e,t){return e.contrast(ou)>=t?ou:ru}(i||Wp.getValueFor(t),e),$p=pu({name:"foreground-on-accent-recipe",cssCustomPropertyName:null}).withDefault({evaluate:(e,t)=>Up(Dp.normal)(e,t)}),Hp=pu("foreground-on-accent-rest").withDefault((e=>$p.getValueFor(e).evaluate(e,Wp.getValueFor(e)))),zp=pu("foreground-on-accent-hover").withDefault((e=>$p.getValueFor(e).evaluate(e,Bp.getValueFor(e)))),jp=pu("foreground-on-accent-active").withDefault((e=>$p.getValueFor(e).evaluate(e,Gp.getValueFor(e)))),Zp=(pu("foreground-on-accent-focus").withDefault((e=>$p.getValueFor(e).evaluate(e,Vp.getValueFor(e)))),pu({name:"foreground-on-accent-large-recipe",cssCustomPropertyName:null}).withDefault({evaluate:(e,t)=>Up(Dp.large)(e,t)})),Yp=(pu("foreground-on-accent-rest-large").withDefault((e=>Zp.getValueFor(e).evaluate(e))),pu("foreground-on-accent-hover-large").withDefault((e=>Zp.getValueFor(e).evaluate(e,Bp.getValueFor(e)))),pu("foreground-on-accent-active-large").withDefault((e=>Zp.getValueFor(e).evaluate(e,Gp.getValueFor(e)))),pu("foreground-on-accent-focus-large").withDefault((e=>Zp.getValueFor(e).evaluate(e,Vp.getValueFor(e)))),e=>(t,i)=>function(e,t,i,o,r,n,a){const s=e.source,l=e.closestIndexOf(s),c=Qd(t),d=l+(1===c?Math.min(o,r):Math.max(c*o,c*r)),u=e.colorContrast(t,i,d,c),p=e.closestIndexOf(u),h=p+c*Math.abs(o-r);let f,g;return(1===c?o<r:c*o>c*r)?(f=p,g=h):(f=h,g=p),{rest:e.get(f),hover:e.get(g),active:e.get(f+c*n),focus:e.get(f+c*a)}}(vp.getValueFor(t),i||Pp.getValueFor(t),e,ju.getValueFor(t),Zu.getValueFor(t),Yu.getValueFor(t),Ku.getValueFor(t))),Kp=pu({name:"accent-foreground-recipe",cssCustomPropertyName:null}).withDefault({evaluate:(e,t)=>Yp(Dp.normal)(e,t)}),qp=pu("accent-foreground-rest").withDefault((e=>Kp.getValueFor(e).evaluate(e).rest)),Xp=pu("accent-foreground-hover").withDefault((e=>Kp.getValueFor(e).evaluate(e).hover)),Qp=pu("accent-foreground-active").withDefault((e=>Kp.getValueFor(e).evaluate(e).active)),Jp=(pu("accent-foreground-focus").withDefault((e=>Kp.getValueFor(e).evaluate(e).focus)),pu({name:"neutral-fill-recipe",cssCustomPropertyName:null}).withDefault({evaluate:(e,t)=>function(e,t,i,o,r,n){const a=e.closestIndexOf(t),s=a>=Math.max(i,o,r,n)?-1:1;return{rest:e.get(a+s*i),hover:e.get(a+s*o),active:e.get(a+s*r),focus:e.get(a+s*n)}}(Cp.getValueFor(e),t||Pp.getValueFor(e),qu.getValueFor(e),Xu.getValueFor(e),Qu.getValueFor(e),Ju.getValueFor(e))})),eh=pu("neutral-fill-rest").withDefault((e=>Jp.getValueFor(e).evaluate(e).rest)),th=pu("neutral-fill-hover").withDefault((e=>Jp.getValueFor(e).evaluate(e).hover)),ih=pu("neutral-fill-active").withDefault((e=>Jp.getValueFor(e).evaluate(e).active)),oh=(pu("neutral-fill-focus").withDefault((e=>Jp.getValueFor(e).evaluate(e).focus)),pu({name:"neutral-fill-input-recipe",cssCustomPropertyName:null}).withDefault({evaluate:(e,t)=>function(e,t,i,o,r,n){const a=Qd(t),s=e.closestIndexOf(t);return{rest:e.get(s-a*i),hover:e.get(s-a*o),active:e.get(s-a*r),focus:e.get(s-a*n)}}(Cp.getValueFor(e),t||Pp.getValueFor(e),ep.getValueFor(e),tp.getValueFor(e),ip.getValueFor(e),op.getValueFor(e))})),rh=(pu("neutral-fill-input-rest").withDefault((e=>oh.getValueFor(e).evaluate(e).rest)),pu("neutral-fill-input-hover").withDefault((e=>oh.getValueFor(e).evaluate(e).hover)),pu("neutral-fill-input-focus").withDefault((e=>oh.getValueFor(e).evaluate(e).focus)),pu("neutral-fill-input-active").withDefault((e=>oh.getValueFor(e).evaluate(e).active)),pu({name:"neutral-fill-inverse-recipe",cssCustomPropertyName:null}).withDefault({evaluate:(e,t)=>function(e,t,i,o,r,n){const a=Qd(t),s=e.closestIndexOf(e.colorContrast(t,14)),l=s+a*Math.abs(i-o);let c,d;return(1===a?i<o:a*i>a*o)?(c=s,d=l):(c=l,d=s),{rest:e.get(c),hover:e.get(d),active:e.get(c+a*r),focus:e.get(c+a*n)}}(Cp.getValueFor(e),t||Pp.getValueFor(e),rp.getValueFor(e),np.getValueFor(e),ap.getValueFor(e),sp.getValueFor(e))})),nh=(pu("neutral-fill-inverse-rest").withDefault((e=>rh.getValueFor(e).evaluate(e).rest)),pu("neutral-fill-inverse-hover").withDefault((e=>rh.getValueFor(e).evaluate(e).hover)),pu("neutral-fill-inverse-active").withDefault((e=>rh.getValueFor(e).evaluate(e).active)),pu("neutral-fill-inverse-focus").withDefault((e=>rh.getValueFor(e).evaluate(e).focus)),pu({name:"neutral-fill-layer-recipe",cssCustomPropertyName:null}).withDefault({evaluate:(e,t)=>function(e,t,i){const o=e.closestIndexOf(t);return e.get(o-i)}(Cp.getValueFor(e),t||Pp.getValueFor(e),lp.getValueFor(e))})),ah=(pu("neutral-fill-layer-rest").withDefault((e=>nh.getValueFor(e).evaluate(e))),pu({name:"neutral-fill-stealth-recipe",cssCustomPropertyName:null}).withDefault({evaluate:(e,t)=>function(e,t,i,o,r,n,a,s,l,c){const d=Math.max(i,o,r,n,a,s,l,c),u=e.closestIndexOf(t),p=u>=d?-1:1;return{rest:e.get(u+p*i),hover:e.get(u+p*o),active:e.get(u+p*r),focus:e.get(u+p*n)}}(Cp.getValueFor(e),t||Pp.getValueFor(e),cp.getValueFor(e),dp.getValueFor(e),up.getValueFor(e),pp.getValueFor(e),qu.getValueFor(e),Xu.getValueFor(e),Qu.getValueFor(e),Ju.getValueFor(e))})),sh=pu("neutral-fill-stealth-rest").withDefault((e=>ah.getValueFor(e).evaluate(e).rest)),lh=pu("neutral-fill-stealth-hover").withDefault((e=>ah.getValueFor(e).evaluate(e).hover)),ch=pu("neutral-fill-stealth-active").withDefault((e=>ah.getValueFor(e).evaluate(e).active)),dh=(pu("neutral-fill-stealth-focus").withDefault((e=>ah.getValueFor(e).evaluate(e).focus)),pu({name:"neutral-fill-strong-recipe",cssCustomPropertyName:null}).withDefault({evaluate:(e,t)=>function(e,t,i,o,r,n){const a=Qd(t),s=e.closestIndexOf(e.colorContrast(t,4.5)),l=s+a*Math.abs(i-o);let c,d;return(1===a?i<o:a*i>a*o)?(c=s,d=l):(c=l,d=s),{rest:e.get(c),hover:e.get(d),active:e.get(c+a*r),focus:e.get(c+a*n)}}(Cp.getValueFor(e),t||Pp.getValueFor(e),hp.getValueFor(e),fp.getValueFor(e),gp.getValueFor(e),Ep.getValueFor(e))})),uh=(pu("neutral-fill-strong-rest").withDefault((e=>dh.getValueFor(e).evaluate(e).rest)),pu("neutral-fill-strong-hover").withDefault((e=>dh.getValueFor(e).evaluate(e).hover)),pu("neutral-fill-strong-active").withDefault((e=>dh.getValueFor(e).evaluate(e).active)),pu("neutral-fill-strong-focus").withDefault((e=>dh.getValueFor(e).evaluate(e).focus)),pu({name:"neutral-stroke-recipe",cssCustomPropertyName:null}).withDefault({evaluate:e=>function(e,t,i,o,r,n){const a=e.closestIndexOf(t),s=Qd(t),l=a+s*i,c=l+s*(o-i),d=l+s*(r-i),u=l+s*(n-i);return{rest:e.get(l),hover:e.get(c),active:e.get(d),focus:e.get(u)}}(Cp.getValueFor(e),Pp.getValueFor(e),Tp.getValueFor(e),mp.getValueFor(e),Np.getValueFor(e),_p.getValueFor(e))})),ph=pu("neutral-stroke-rest").withDefault((e=>uh.getValueFor(e).evaluate(e).rest)),hh=pu("neutral-stroke-hover").withDefault((e=>uh.getValueFor(e).evaluate(e).hover)),fh=pu("neutral-stroke-active").withDefault((e=>uh.getValueFor(e).evaluate(e).active)),gh=(pu("neutral-stroke-focus").withDefault((e=>uh.getValueFor(e).evaluate(e).focus)),pu({name:"neutral-stroke-divider-recipe",cssCustomPropertyName:null}).withDefault({evaluate:(e,t)=>function(e,t,i){return e.get(e.closestIndexOf(t)+Qd(t)*i)}(Cp.getValueFor(e),t||Pp.getValueFor(e),Op.getValueFor(e))})),Eh=pu("neutral-stroke-divider-rest").withDefault((e=>gh.getValueFor(e).evaluate(e))),Th=pu({name:"neutral-stroke-strong-recipe",cssCustomPropertyName:null}).withDefault({evaluate:e=>function(e,t,i,o,r,n){const a=Qd(t),s=e.colorContrast(t,3),l=e.closestIndexOf(s);return{rest:s,hover:e.get(l+a*o),active:e.get(l+a*r),focus:e.get(l+a*n)}}(Cp.getValueFor(e),Pp.getValueFor(e),0,bp.getValueFor(e),Ip.getValueFor(e),Ap.getValueFor(e))}),mh=(pu("neutral-stroke-strong-rest").withDefault((e=>Th.getValueFor(e).evaluate(e).rest)),pu("neutral-stroke-strong-hover").withDefault((e=>Th.getValueFor(e).evaluate(e).hover)),pu("neutral-stroke-strong-active").withDefault((e=>Th.getValueFor(e).evaluate(e).active)),pu("neutral-stroke-strong-focus").withDefault((e=>Th.getValueFor(e).evaluate(e).focus)),pu({name:"focus-stroke-outer-recipe",cssCustomPropertyName:null}).withDefault({evaluate:e=>{return t=Cp.getValueFor(e),i=Pp.getValueFor(e),t.colorContrast(i,3.5);var t,i}})),Nh=pu("focus-stroke-outer").withDefault((e=>mh.getValueFor(e).evaluate(e))),_h=pu({name:"focus-stroke-inner-recipe",cssCustomPropertyName:null}).withDefault({evaluate:e=>{return t=vp.getValueFor(e),i=Pp.getValueFor(e),o=Nh.getValueFor(e),t.colorContrast(o,3.5,t.closestIndexOf(t.source),-1*Qd(i));var t,i,o}}),Oh=pu("focus-stroke-inner").withDefault((e=>_h.getValueFor(e).evaluate(e))),bh=pu({name:"neutral-foreground-recipe",cssCustomPropertyName:null}).withDefault({evaluate:e=>{return t=Cp.getValueFor(e),i=Pp.getValueFor(e),t.colorContrast(i,14);var t,i}}),Ih=pu("neutral-foreground-rest").withDefault((e=>bh.getValueFor(e).evaluate(e))),Ah=pu({name:"neutral-foreground-hint-recipe",cssCustomPropertyName:null}).withDefault({evaluate:e=>{return t=Cp.getValueFor(e),i=Pp.getValueFor(e),t.colorContrast(i,4.5);var t,i}}),Ch=(pu("neutral-foreground-hint").withDefault((e=>Ah.getValueFor(e).evaluate(e))),{toView(e){var t;return null==e?null:null===(t=e)||void 0===t?void 0:t.toColorString()},fromView(e){if(null==e)return null;const t=dd(e);return t?Yd.create(t.r,t.g,t.b):null}}),vh=I`
  :host {
    background-color: ${Pp};
    color: ${Ih};
  }
`.withBehaviors(pd(I`
      :host {
        background-color: ${fd.Canvas};
        box-shadow: 0 0 0 1px ${fd.CanvasText};
        color: ${fd.CanvasText};
      }
    `));function Rh(e){return(t,i)=>{t[i+"Changed"]=function(t,i){null!=i?e.setValueFor(this,i):e.deleteValueFor(this)}}}class yh extends Tc{constructor(){super(),this.noPaint=!1,R.getNotifier(this).subscribe({handleChange:this.noPaintChanged.bind(this)},"fillColor")}noPaintChanged(){this.noPaint||void 0===this.fillColor?this.$fastController.removeStyles(vh):this.$fastController.addStyles(vh)}accentBaseColorChanged(e,t){null!=t?vp.setValueFor(this,Jd.create(t)):vp.deleteValueFor(this)}neutralBaseColorChanged(e,t){null!=t?Cp.setValueFor(this,Jd.create(t)):Cp.deleteValueFor(this)}}ed([he({attribute:"no-paint",mode:"boolean"})],yh.prototype,"noPaint",void 0),ed([he({attribute:"fill-color",converter:Ch}),Rh(Pp)],yh.prototype,"fillColor",void 0),ed([he({attribute:"accent-base-color",converter:Ch,mode:"fromView"})],yh.prototype,"accentBaseColor",void 0),ed([he({attribute:"neutral-base-color",converter:Ch,mode:"fromView"})],yh.prototype,"neutralBaseColor",void 0),ed([y,Rh(Cp)],yh.prototype,"neutralPalette",void 0),ed([y,Rh(vp)],yh.prototype,"accentPalette",void 0),ed([he({converter:ue}),Rh(Tu)],yh.prototype,"density",void 0),ed([he({attribute:"design-unit",converter:ue}),Rh(mu)],yh.prototype,"designUnit",void 0),ed([he({attribute:"direction"}),Rh(hu)],yh.prototype,"direction",void 0),ed([he({attribute:"base-height-multiplier",converter:ue}),Rh(gu)],yh.prototype,"baseHeightMultiplier",void 0),ed([he({attribute:"base-horizontal-spacing-multiplier",converter:ue}),Rh(Eu)],yh.prototype,"baseHorizontalSpacingMultiplier",void 0),ed([he({attribute:"control-corner-radius",converter:ue}),Rh(Nu)],yh.prototype,"controlCornerRadius",void 0),ed([he({attribute:"stroke-width",converter:ue}),Rh(_u)],yh.prototype,"strokeWidth",void 0),ed([he({attribute:"focus-stroke-width",converter:ue}),Rh(Ou)],yh.prototype,"focusStrokeWidth",void 0),ed([he({attribute:"disabled-opacity",converter:ue}),Rh(fu)],yh.prototype,"disabledOpacity",void 0),ed([he({attribute:"type-ramp-minus-2-font-size"}),Rh(Ru)],yh.prototype,"typeRampMinus2FontSize",void 0),ed([he({attribute:"type-ramp-minus-2-line-height"}),Rh(yu)],yh.prototype,"typeRampMinus2LineHeight",void 0),ed([he({attribute:"type-ramp-minus-1-font-size"}),Rh(Cu)],yh.prototype,"typeRampMinus1FontSize",void 0),ed([he({attribute:"type-ramp-minus-1-line-height"}),Rh(vu)],yh.prototype,"typeRampMinus1LineHeight",void 0),ed([he({attribute:"type-ramp-base-font-size"}),Rh(Iu)],yh.prototype,"typeRampBaseFontSize",void 0),ed([he({attribute:"type-ramp-base-line-height"}),Rh(Au)],yh.prototype,"typeRampBaseLineHeight",void 0),ed([he({attribute:"type-ramp-plus-1-font-size"}),Rh(Su)],yh.prototype,"typeRampPlus1FontSize",void 0),ed([he({attribute:"type-ramp-plus-1-line-height"}),Rh(wu)],yh.prototype,"typeRampPlus1LineHeight",void 0),ed([he({attribute:"type-ramp-plus-2-font-size"}),Rh(ku)],yh.prototype,"typeRampPlus2FontSize",void 0),ed([he({attribute:"type-ramp-plus-2-line-height"}),Rh(Mu)],yh.prototype,"typeRampPlus2LineHeight",void 0),ed([he({attribute:"type-ramp-plus-3-font-size"}),Rh(xu)],yh.prototype,"typeRampPlus3FontSize",void 0),ed([he({attribute:"type-ramp-plus-3-line-height"}),Rh(Pu)],yh.prototype,"typeRampPlus3LineHeight",void 0),ed([he({attribute:"type-ramp-plus-4-font-size"}),Rh(Du)],yh.prototype,"typeRampPlus4FontSize",void 0),ed([he({attribute:"type-ramp-plus-4-line-height"}),Rh(Lu)],yh.prototype,"typeRampPlus4LineHeight",void 0),ed([he({attribute:"type-ramp-plus-5-font-size"}),Rh(Fu)],yh.prototype,"typeRampPlus5FontSize",void 0),ed([he({attribute:"type-ramp-plus-5-line-height"}),Rh(Wu)],yh.prototype,"typeRampPlus5LineHeight",void 0),ed([he({attribute:"type-ramp-plus-6-font-size"}),Rh(Bu)],yh.prototype,"typeRampPlus6FontSize",void 0),ed([he({attribute:"type-ramp-plus-6-line-height"}),Rh(Gu)],yh.prototype,"typeRampPlus6LineHeight",void 0),ed([he({attribute:"accent-fill-rest-delta",converter:ue}),Rh(Uu)],yh.prototype,"accentFillRestDelta",void 0),ed([he({attribute:"accent-fill-hover-delta",converter:ue}),Rh($u)],yh.prototype,"accentFillHoverDelta",void 0),ed([he({attribute:"accent-fill-active-delta",converter:ue}),Rh(Hu)],yh.prototype,"accentFillActiveDelta",void 0),ed([he({attribute:"accent-fill-focus-delta",converter:ue}),Rh(zu)],yh.prototype,"accentFillFocusDelta",void 0),ed([he({attribute:"accent-foreground-rest-delta",converter:ue}),Rh(ju)],yh.prototype,"accentForegroundRestDelta",void 0),ed([he({attribute:"accent-foreground-hover-delta",converter:ue}),Rh(Zu)],yh.prototype,"accentForegroundHoverDelta",void 0),ed([he({attribute:"accent-foreground-active-delta",converter:ue}),Rh(Yu)],yh.prototype,"accentForegroundActiveDelta",void 0),ed([he({attribute:"accent-foreground-focus-delta",converter:ue}),Rh(Ku)],yh.prototype,"accentForegroundFocusDelta",void 0),ed([he({attribute:"neutral-fill-rest-delta",converter:ue}),Rh(qu)],yh.prototype,"neutralFillRestDelta",void 0),ed([he({attribute:"neutral-fill-hover-delta",converter:ue}),Rh(Xu)],yh.prototype,"neutralFillHoverDelta",void 0),ed([he({attribute:"neutral-fill-active-delta",converter:ue}),Rh(Qu)],yh.prototype,"neutralFillActiveDelta",void 0),ed([he({attribute:"neutral-fill-focus-delta",converter:ue}),Rh(Ju)],yh.prototype,"neutralFillFocusDelta",void 0),ed([he({attribute:"neutral-fill-input-rest-delta",converter:ue}),Rh(ep)],yh.prototype,"neutralFillInputRestDelta",void 0),ed([he({attribute:"neutral-fill-input-hover-delta",converter:ue}),Rh(tp)],yh.prototype,"neutralFillInputHoverDelta",void 0),ed([he({attribute:"neutral-fill-input-active-delta",converter:ue}),Rh(ip)],yh.prototype,"neutralFillInputActiveDelta",void 0),ed([he({attribute:"neutral-fill-input-focus-delta",converter:ue}),Rh(op)],yh.prototype,"neutralFillInputFocusDelta",void 0),ed([he({attribute:"neutral-fill-layer-rest-delta",converter:ue}),Rh(lp)],yh.prototype,"neutralFillLayerRestDelta",void 0),ed([he({attribute:"neutral-fill-stealth-rest-delta",converter:ue}),Rh(cp)],yh.prototype,"neutralFillStealthRestDelta",void 0),ed([he({attribute:"neutral-fill-stealth-hover-delta",converter:ue}),Rh(dp)],yh.prototype,"neutralFillStealthHoverDelta",void 0),ed([he({attribute:"neutral-fill-stealth-active-delta",converter:ue}),Rh(up)],yh.prototype,"neutralFillStealthActiveDelta",void 0),ed([he({attribute:"neutral-fill-stealth-focus-delta",converter:ue}),Rh(pp)],yh.prototype,"neutralFillStealthFocusDelta",void 0),ed([he({attribute:"neutral-fill-strong-hover-delta",converter:ue}),Rh(fp)],yh.prototype,"neutralFillStrongHoverDelta",void 0),ed([he({attribute:"neutral-fill-strong-active-delta",converter:ue}),Rh(gp)],yh.prototype,"neutralFillStrongActiveDelta",void 0),ed([he({attribute:"neutral-fill-strong-focus-delta",converter:ue}),Rh(Ep)],yh.prototype,"neutralFillStrongFocusDelta",void 0),ed([he({attribute:"base-layer-luminance",converter:ue}),Rh(Vu)],yh.prototype,"baseLayerLuminance",void 0),ed([he({attribute:"neutral-stroke-divider-rest-delta",converter:ue}),Rh(Op)],yh.prototype,"neutralStrokeDividerRestDelta",void 0),ed([he({attribute:"neutral-stroke-rest-delta",converter:ue}),Rh(Tp)],yh.prototype,"neutralStrokeRestDelta",void 0),ed([he({attribute:"neutral-stroke-hover-delta",converter:ue}),Rh(mp)],yh.prototype,"neutralStrokeHoverDelta",void 0),ed([he({attribute:"neutral-stroke-active-delta",converter:ue}),Rh(Np)],yh.prototype,"neutralStrokeActiveDelta",void 0),ed([he({attribute:"neutral-stroke-focus-delta",converter:ue}),Rh(_p)],yh.prototype,"neutralStrokeFocusDelta",void 0);const Sh=yh.compose({baseName:"design-system-provider",template:re` <slot></slot> `,styles:I`
    ${hd("block")}
  `});class wh{}kl([he({attribute:"aria-atomic"})],wh.prototype,"ariaAtomic",void 0),kl([he({attribute:"aria-busy"})],wh.prototype,"ariaBusy",void 0),kl([he({attribute:"aria-controls"})],wh.prototype,"ariaControls",void 0),kl([he({attribute:"aria-current"})],wh.prototype,"ariaCurrent",void 0),kl([he({attribute:"aria-describedby"})],wh.prototype,"ariaDescribedby",void 0),kl([he({attribute:"aria-details"})],wh.prototype,"ariaDetails",void 0),kl([he({attribute:"aria-disabled"})],wh.prototype,"ariaDisabled",void 0),kl([he({attribute:"aria-errormessage"})],wh.prototype,"ariaErrormessage",void 0),kl([he({attribute:"aria-flowto"})],wh.prototype,"ariaFlowto",void 0),kl([he({attribute:"aria-haspopup"})],wh.prototype,"ariaHaspopup",void 0),kl([he({attribute:"aria-hidden"})],wh.prototype,"ariaHidden",void 0),kl([he({attribute:"aria-invalid"})],wh.prototype,"ariaInvalid",void 0),kl([he({attribute:"aria-keyshortcuts"})],wh.prototype,"ariaKeyshortcuts",void 0),kl([he({attribute:"aria-label"})],wh.prototype,"ariaLabel",void 0),kl([he({attribute:"aria-labelledby"})],wh.prototype,"ariaLabelledby",void 0),kl([he({attribute:"aria-live"})],wh.prototype,"ariaLive",void 0),kl([he({attribute:"aria-owns"})],wh.prototype,"ariaOwns",void 0),kl([he({attribute:"aria-relevant"})],wh.prototype,"ariaRelevant",void 0),kl([he({attribute:"aria-roledescription"})],wh.prototype,"ariaRoledescription",void 0);class kh{constructor(e,t){this.target=e,this.propertyName=t}bind(e){e[this.propertyName]=this.target}unbind(){}}function Mh(e){return new P("fast-ref",kh,e)}class xh{handleStartContentChange(){this.startContainer.classList.toggle("start",this.start.assignedNodes().length>0)}handleEndContentChange(){this.endContainer.classList.toggle("end",this.end.assignedNodes().length>0)}}const Ph=(e,t)=>re`
    <span
        part="end"
        ${Mh("endContainer")}
        class=${e=>t.end?"end":void 0}
    >
        <slot name="end" ${Mh("end")} @slotchange="${e=>e.handleEndContentChange()}">
            ${t.end||""}
        </slot>
    </span>
`,Dh=(e,t)=>re`
    <span
        part="start"
        ${Mh("startContainer")}
        class="${e=>t.start?"start":void 0}"
    >
        <slot
            name="start"
            ${Mh("start")}
            @slotchange="${e=>e.handleStartContentChange()}"
        >
            ${t.start||""}
        </slot>
    </span>
`;function Lh(e,...t){const i=ce.locate(e);t.forEach((t=>{Object.getOwnPropertyNames(t.prototype).forEach((i=>{"constructor"!==i&&Object.defineProperty(e.prototype,i,Object.getOwnPropertyDescriptor(t.prototype,i))})),ce.locate(t).forEach((e=>i.push(e)))}))}re`
    <span part="end" ${Mh("endContainer")}>
        <slot
            name="end"
            ${Mh("end")}
            @slotchange="${e=>e.handleEndContentChange()}"
        ></slot>
    </span>
`,re`
    <span part="start" ${Mh("startContainer")}>
        <slot
            name="start"
            ${Mh("start")}
            @slotchange="${e=>e.handleStartContentChange()}"
        ></slot>
    </span>
`;class Fh extends Tc{constructor(){super(...arguments),this.handleUnsupportedDelegatesFocus=()=>{var e;window.ShadowRoot&&!window.ShadowRoot.prototype.hasOwnProperty("delegatesFocus")&&(null===(e=this.$fastController.definition.shadowOptions)||void 0===e?void 0:e.delegatesFocus)&&(this.focus=()=>{var e;null===(e=this.control)||void 0===e||e.focus()})}}connectedCallback(){super.connectedCallback(),this.handleUnsupportedDelegatesFocus()}}kl([he],Fh.prototype,"download",void 0),kl([he],Fh.prototype,"href",void 0),kl([he],Fh.prototype,"hreflang",void 0),kl([he],Fh.prototype,"ping",void 0),kl([he],Fh.prototype,"referrerpolicy",void 0),kl([he],Fh.prototype,"rel",void 0),kl([he],Fh.prototype,"target",void 0),kl([he],Fh.prototype,"type",void 0),kl([y],Fh.prototype,"defaultSlottedContent",void 0);class Wh{}kl([he({attribute:"aria-expanded"})],Wh.prototype,"ariaExpanded",void 0),Lh(Wh,wh),Lh(Fh,xh,Wh);class Bh extends class{constructor(e,t){this.target=e,this.options=t,this.source=null}bind(e){const t=this.options.property;this.shouldUpdate=R.getAccessors(e).some((e=>e.name===t)),this.source=e,this.updateTarget(this.computeNodes()),this.shouldUpdate&&this.observe()}unbind(){this.updateTarget(n),this.source=null,this.shouldUpdate&&this.disconnect()}handleEvent(){this.updateTarget(this.computeNodes())}computeNodes(){let e=this.getNodes();return void 0!==this.options.filter&&(e=e.filter(this.options.filter)),e}updateTarget(e){this.source[this.options.property]=e}}{constructor(e,t){super(e,t)}observe(){this.target.addEventListener("slotchange",this)}disconnect(){this.target.removeEventListener("slotchange",this)}getNodes(){return this.target.assignedNodes(this.options)}}function Gh(e){return"string"==typeof e&&(e={property:e}),new P("fast-slotted",Bh,e)}let Vh;const Uh=function(){if("boolean"==typeof Vh)return Vh;if("undefined"==typeof window||!window.document||!window.document.createElement)return Vh=!1,Vh;const e=document.createElement("style"),t=function(){const e=document.querySelector('meta[property="csp-nonce"]');return e?e.getAttribute("content"):null}();null!==t&&e.setAttribute("nonce",t),document.head.appendChild(e);try{e.sheet.insertRule("foo:focus-visible {color:inherit}",0),Vh=!0}catch(e){Vh=!1}finally{document.head.removeChild(e)}return Vh}()?"focus-visible":"focus",$h=(function(e,...t){const{styles:i,behaviors:o}=b(e,t);return new A(i,o)})`(${gu} + ${Tu}) * ${mu}`,Hh=(e,t)=>I`
    ${hd("inline-flex")} :host {
      font-family: ${bu};
      outline: none;
      font-size: ${Iu};
      line-height: ${Au};
      height: calc(${$h} * 1px);
      min-width: calc(${$h} * 1px);
      background-color: ${eh};
      color: ${Ih};
      border-radius: calc(${Nu} * 1px);
      fill: currentcolor;
      cursor: pointer;
    }

    .control {
      background: transparent;
      height: inherit;
      flex-grow: 1;
      box-sizing: border-box;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      padding: 0 calc((10 + (${mu} * 2 * ${Tu})) * 1px);
      white-space: nowrap;
      outline: none;
      text-decoration: none;
      border: calc(${_u} * 1px) solid transparent;
      color: inherit;
      border-radius: inherit;
      fill: inherit;
      cursor: inherit;
      font-family: inherit;
    }

    .control,
    .end,
    .start {
      font: inherit;
    }

    .control.icon-only {
      padding: 0;
      line-height: 0;
    }

    :host(:hover) {
      background-color: ${th};
    }

    :host(:active) {
      background-color: ${ih};
    }

    .control:${Uh} {
      border: calc(${_u} * 1px) solid ${Nh};
      box-shadow: 0 0 0 calc((${Ou} - ${_u}) * 1px) ${Nh};
    }

    .control::-moz-focus-inner {
      border: 0;
    }

    .content {
      pointer-events: none;
    }

    .start,
    .end {
      display: flex;
      pointer-events: none;
    }

    ::slotted(svg) {
      ${""} width: 16px;
      height: 16px;
      pointer-events: none;
    }

    .start {
      margin-inline-end: 11px;
    }

    .end {
      margin-inline-start: 11px;
    }
  `.withBehaviors(pd(I`
        :host,
        :host([appearance="neutral"]) .control {
          background-color: ${fd.ButtonFace};
          border-color: ${fd.ButtonText};
          color: ${fd.ButtonText};
          fill: currentcolor;
        }

        :host(:not([disabled][href]):hover),
        :host([appearance="neutral"]:not([disabled]):hover) .control {
          forced-color-adjust: none;
          background-color: ${fd.Highlight};
          color: ${fd.HighlightText};
        }

        .control:${Uh},
        :host([appearance="outline"]) .control:${Uh},
        :host([appearance="neutral"]:${Uh}) .control {
          forced-color-adjust: none;
          background-color: ${fd.Highlight};
          border-color: ${fd.ButtonText};
          box-shadow: 0 0 0 calc((${Ou} - ${_u}) * 1px) ${fd.ButtonText};
          color: ${fd.HighlightText};
        }

        .control:not([disabled]):hover,
        :host([appearance="outline"]) .control:hover {
          border-color: ${fd.ButtonText};
        }

        :host([href]) .control {
          border-color: ${fd.LinkText};
          color: ${fd.LinkText};
        }

        :host([href]) .control:hover,
        :host(.neutral[href]) .control:hover,
        :host(.outline[href]) .control:hover,
        :host([href]) .control:${Uh}{
          forced-color-adjust: none;
          background: ${fd.ButtonFace};
          border-color: ${fd.LinkText};
          box-shadow: 0 0 0 1px ${fd.LinkText} inset;
          color: ${fd.LinkText};
          fill: currentcolor;
        }
    `)),zh=I`
  :host([appearance='accent']) {
    background: ${Wp};
    color: ${Hp};
  }

  :host([appearance='accent']:hover) {
    background: ${Bp};
    color: ${zp};
  }

  :host([appearance='accent']:active) .control:active {
    background: ${Gp};
    color: ${jp};
  }

  :host([appearance="accent"]) .control:${Uh} {
    box-shadow: 0 0 0 calc(${Ou} * 1px) inset ${Oh},
      0 0 0 calc((${Ou} - ${_u}) * 1px) ${Nh};
  }
`.withBehaviors(pd(I`
      :host([appearance='accent']) .control {
        forced-color-adjust: none;
        background: ${fd.Highlight};
        color: ${fd.HighlightText};
      }

      :host([appearance='accent']) .control:hover,
      :host([appearance='accent']:active) .control:active {
        background: ${fd.HighlightText};
        border-color: ${fd.Highlight};
        color: ${fd.Highlight};
      }

      :host([appearance="accent"]) .control:${Uh} {
        border-color: ${fd.ButtonText};
        box-shadow: 0 0 0 2px ${fd.HighlightText} inset;
      }

      :host([appearance='accent'][href]) .control {
        background: ${fd.LinkText};
        color: ${fd.HighlightText};
      }

      :host([appearance='accent'][href]) .control:hover {
        background: ${fd.ButtonFace};
        border-color: ${fd.LinkText};
        box-shadow: none;
        color: ${fd.LinkText};
        fill: currentcolor;
      }

      :host([appearance="accent"][href]) .control:${Uh} {
        border-color: ${fd.LinkText};
        box-shadow: 0 0 0 2px ${fd.HighlightText} inset;
      }
    `)),jh=I`
  :host([appearance='hypertext']) {
    height: auto;
    font-size: inherit;
    line-height: inherit;
    background: transparent;
    min-width: 0;
  }

  :host([appearance='hypertext']) .control {
    display: inline;
    padding: 0;
    border: none;
    box-shadow: none;
    border-radius: 0;
    line-height: 1;
  }
  :host a.control:not(:link) {
    background-color: transparent;
    cursor: default;
  }
  :host([appearance='hypertext']) .control:link,
  :host([appearance='hypertext']) .control:visited {
    background: transparent;
    color: ${qp};
    border-bottom: calc(${_u} * 1px) solid ${qp};
  }
  :host([appearance='hypertext']) .control:hover {
    border-bottom-color: ${Xp};
  }
  :host([appearance='hypertext']) .control:active {
    border-bottom-color: ${Qp};
  }
  :host([appearance="hypertext"]) .control:${Uh} {
    border-bottom: calc(${Ou} * 1px) solid ${Nh};
    margin-bottom: calc(calc(${_u} - ${Ou}) * 1px);
  }
`.withBehaviors(pd(I`
      :host([appearance="hypertext"]) .control:${Uh} {
        color: ${fd.LinkText};
        border-bottom-color: ${fd.LinkText};
      }
    `)),Zh=I`
  :host([appearance='lightweight']) {
    background: transparent;
    color: ${qp};
  }

  :host([appearance='lightweight']) .control {
    padding: 0;
    height: initial;
    border: none;
    box-shadow: none;
    border-radius: 0;
  }

  :host([appearance='lightweight']:hover) {
    color: ${Xp};
  }

  :host([appearance='lightweight']:active) {
    color: ${Qp};
  }

  :host([appearance='lightweight']) .content {
    position: relative;
  }

  :host([appearance='lightweight']) .content::before {
    content: '';
    display: block;
    height: calc(${_u} * 1px);
    position: absolute;
    top: calc(1em + 3px);
    width: 100%;
  }

  :host([appearance='lightweight']:hover) .content::before {
    background: ${Xp};
  }

  :host([appearance='lightweight']:active) .content::before {
    background: ${Qp};
  }

  :host([appearance="lightweight"]) .control:${Uh} .content::before {
    background: ${Ih};
    height: calc(${Ou} * 1px);
  }
`.withBehaviors(pd(I`
      :host([appearance='lightweight']) {
        color: ${fd.ButtonText};
      }
      :host([appearance="lightweight"]) .control:hover,
        :host([appearance="lightweight"]) .control:${Uh} {
        forced-color-adjust: none;
        background: ${fd.ButtonFace};
        color: ${fd.Highlight};
      }
      :host([appearance="lightweight"]) .control:hover .content::before,
        :host([appearance="lightweight"]) .control:${Uh} .content::before {
        background: ${fd.Highlight};
      }

      :host([appearance="lightweight"][href]) .control:hover,
        :host([appearance="lightweight"][href]) .control:${Uh} {
        background: ${fd.ButtonFace};
        box-shadow: none;
        color: ${fd.LinkText};
      }

      :host([appearance="lightweight"][href]) .control:hover .content::before,
        :host([appearance="lightweight"][href]) .control:${Uh} .content::before {
        background: ${fd.LinkText};
      }
    `)),Yh=I`
  :host([appearance='outline']) {
    background: transparent;
    border-color: ${ph};
  }

  :host([appearance='outline']:hover) {
    border-color: ${hh};
  }

  :host([appearance='outline']:active) {
    border-color: ${fh};
  }

  :host([appearance='outline']) .control {
    border-color: inherit;
  }

  :host([appearance="outline"]) .control:${Uh} {
    box-shadow: 0 0 0 calc((${Ou} - ${_u}) * 1px) ${Nh};
    border-color: ${Nh};
  }
`.withBehaviors(pd(I`
      :host([appearance='outline']) {
        border-color: ${fd.ButtonText};
      }
      :host([appearance='outline'][href]) {
        border-color: ${fd.LinkText};
      }
    `)),Kh=I`
  :host([appearance='stealth']) {
    background: ${sh};
  }

  :host([appearance='stealth']:hover) {
    background: ${lh};
  }

  :host([appearance='stealth']:active) {
    background: ${ch};
  }
`.withBehaviors(pd(I`
      :host([appearance='stealth']),
      :host([appearance='stealth']) .control {
        forced-color-adjust: none;
        background: ${fd.ButtonFace};
        border-color: transparent;
        color: ${fd.ButtonText};
        fill: currentcolor;
      }

      :host([appearance='stealth']:hover) .control {
        background: ${fd.Highlight};
        border-color: ${fd.Highlight};
        color: ${fd.HighlightText};
        fill: currentcolor;
      }

      :host([appearance="stealth"]:${Uh}) .control {
        background: ${fd.Highlight};
        box-shadow: 0 0 0 1px ${fd.Highlight};
        color: ${fd.HighlightText};
        fill: currentcolor;
      }

      :host([appearance='stealth'][href]) .control {
        color: ${fd.LinkText};
      }

      :host([appearance="stealth"]:hover[href]) .control,
        :host([appearance="stealth"]:${Uh}[href]) .control {
        background: ${fd.LinkText};
        border-color: ${fd.LinkText};
        color: ${fd.HighlightText};
        fill: currentcolor;
      }

      :host([appearance="stealth"]:${Uh}[href]) .control {
        box-shadow: 0 0 0 1px ${fd.LinkText};
      }
    `));class qh{constructor(e,t,i){this.propertyName=e,this.value=t,this.styles=i}bind(e){R.getNotifier(e).subscribe(this,this.propertyName),this.handleChange(e,this.propertyName)}unbind(e){R.getNotifier(e).unsubscribe(this,this.propertyName),e.$fastController.removeStyles(this.styles)}handleChange(e,t){e[t]===this.value?e.$fastController.addStyles(this.styles):e.$fastController.removeStyles(this.styles)}}function Xh(e,t){return new qh("appearance",e,t)}class Qh extends Fh{appearanceChanged(e,t){e!==t&&(this.classList.add(t),this.classList.remove(e))}connectedCallback(){super.connectedCallback(),this.appearance||(this.appearance="neutral")}defaultSlottedContentChanged(){const e=this.defaultSlottedContent.filter((e=>e.nodeType===Node.ELEMENT_NODE));1===e.length&&e[0]instanceof SVGElement?this.control.classList.add("icon-only"):this.control.classList.remove("icon-only")}}ed([he],Qh.prototype,"appearance",void 0);const Jh=Qh.compose({baseName:"anchor",baseClass:Fh,template:(e,t)=>re`
    <a
        class="control"
        part="control"
        download="${e=>e.download}"
        href="${e=>e.href}"
        hreflang="${e=>e.hreflang}"
        ping="${e=>e.ping}"
        referrerpolicy="${e=>e.referrerpolicy}"
        rel="${e=>e.rel}"
        target="${e=>e.target}"
        type="${e=>e.type}"
        aria-atomic="${e=>e.ariaAtomic}"
        aria-busy="${e=>e.ariaBusy}"
        aria-controls="${e=>e.ariaControls}"
        aria-current="${e=>e.ariaCurrent}"
        aria-describedby="${e=>e.ariaDescribedby}"
        aria-details="${e=>e.ariaDetails}"
        aria-disabled="${e=>e.ariaDisabled}"
        aria-errormessage="${e=>e.ariaErrormessage}"
        aria-expanded="${e=>e.ariaExpanded}"
        aria-flowto="${e=>e.ariaFlowto}"
        aria-haspopup="${e=>e.ariaHaspopup}"
        aria-hidden="${e=>e.ariaHidden}"
        aria-invalid="${e=>e.ariaInvalid}"
        aria-keyshortcuts="${e=>e.ariaKeyshortcuts}"
        aria-label="${e=>e.ariaLabel}"
        aria-labelledby="${e=>e.ariaLabelledby}"
        aria-live="${e=>e.ariaLive}"
        aria-owns="${e=>e.ariaOwns}"
        aria-relevant="${e=>e.ariaRelevant}"
        aria-roledescription="${e=>e.ariaRoledescription}"
        ${Mh("control")}
    >
        ${Dh(0,t)}
        <span class="content" part="content">
            <slot ${Gh("defaultSlottedContent")}></slot>
        </span>
        ${Ph(0,t)}
    </a>
`,styles:(e,t)=>I`
    ${Hh()}
  `.withBehaviors(Xh("accent",zh),Xh("hypertext",jh),Xh("lightweight",Zh),Xh("outline",Yh),Xh("stealth",Kh)),shadowOptions:{delegatesFocus:!0}});class ef extends Tc{constructor(){super(...arguments),this.role="separator",this.orientation="horizontal"}}kl([he],ef.prototype,"role",void 0),kl([he],ef.prototype,"orientation",void 0);const tf=ef.compose({baseName:"divider",template:(e,t)=>re`
    <template role="${e=>e.role}" aria-orientation="${e=>e.orientation}"></template>
`,styles:(e,t)=>I`
    ${hd("block")} :host {
      box-sizing: content-box;
      height: 0;
      margin: calc(${mu} * 1px) 0;
      border: none;
      border-top: calc(${_u} * 1px) solid ${Eh};
    }
  `});var of,rf;(rf=of||(of={}))[rf.alt=18]="alt",rf[rf.arrowDown=40]="arrowDown",rf[rf.arrowLeft=37]="arrowLeft",rf[rf.arrowRight=39]="arrowRight",rf[rf.arrowUp=38]="arrowUp",rf[rf.back=8]="back",rf[rf.backSlash=220]="backSlash",rf[rf.break=19]="break",rf[rf.capsLock=20]="capsLock",rf[rf.closeBracket=221]="closeBracket",rf[rf.colon=186]="colon",rf[rf.colon2=59]="colon2",rf[rf.comma=188]="comma",rf[rf.ctrl=17]="ctrl",rf[rf.delete=46]="delete",rf[rf.end=35]="end",rf[rf.enter=13]="enter",rf[rf.equals=187]="equals",rf[rf.equals2=61]="equals2",rf[rf.equals3=107]="equals3",rf[rf.escape=27]="escape",rf[rf.forwardSlash=191]="forwardSlash",rf[rf.function1=112]="function1",rf[rf.function10=121]="function10",rf[rf.function11=122]="function11",rf[rf.function12=123]="function12",rf[rf.function2=113]="function2",rf[rf.function3=114]="function3",rf[rf.function4=115]="function4",rf[rf.function5=116]="function5",rf[rf.function6=117]="function6",rf[rf.function7=118]="function7",rf[rf.function8=119]="function8",rf[rf.function9=120]="function9",rf[rf.home=36]="home",rf[rf.insert=45]="insert",rf[rf.menu=93]="menu",rf[rf.minus=189]="minus",rf[rf.minus2=109]="minus2",rf[rf.numLock=144]="numLock",rf[rf.numPad0=96]="numPad0",rf[rf.numPad1=97]="numPad1",rf[rf.numPad2=98]="numPad2",rf[rf.numPad3=99]="numPad3",rf[rf.numPad4=100]="numPad4",rf[rf.numPad5=101]="numPad5",rf[rf.numPad6=102]="numPad6",rf[rf.numPad7=103]="numPad7",rf[rf.numPad8=104]="numPad8",rf[rf.numPad9=105]="numPad9",rf[rf.numPadDivide=111]="numPadDivide",rf[rf.numPadDot=110]="numPadDot",rf[rf.numPadMinus=109]="numPadMinus",rf[rf.numPadMultiply=106]="numPadMultiply",rf[rf.numPadPlus=107]="numPadPlus",rf[rf.openBracket=219]="openBracket",rf[rf.pageDown=34]="pageDown",rf[rf.pageUp=33]="pageUp",rf[rf.period=190]="period",rf[rf.print=44]="print",rf[rf.quote=222]="quote",rf[rf.scrollLock=145]="scrollLock",rf[rf.shift=16]="shift",rf[rf.space=32]="space",rf[rf.tab=9]="tab",rf[rf.tilde=192]="tilde",rf[rf.windowsLeft=91]="windowsLeft",rf[rf.windowsOpera=219]="windowsOpera",rf[rf.windowsRight=92]="windowsRight";const nf="form-associated-proxy",af="ElementInternals"in window&&"setFormValue"in window.ElementInternals.prototype,sf=new WeakMap;class lf extends Tc{}class cf extends(function(e){const t=class extends e{constructor(...e){super(...e),this.dirtyValue=!1,this.disabled=!1,this.proxyEventsToBlock=["change","click"],this.proxyInitialized=!1,this.required=!1,this.initialValue=this.initialValue||"",this.elementInternals||(this.formResetCallback=this.formResetCallback.bind(this))}static get formAssociated(){return af}get validity(){return this.elementInternals?this.elementInternals.validity:this.proxy.validity}get form(){return this.elementInternals?this.elementInternals.form:this.proxy.form}get validationMessage(){return this.elementInternals?this.elementInternals.validationMessage:this.proxy.validationMessage}get willValidate(){return this.elementInternals?this.elementInternals.willValidate:this.proxy.willValidate}get labels(){if(this.elementInternals)return Object.freeze(Array.from(this.elementInternals.labels));if(this.proxy instanceof HTMLElement&&this.proxy.ownerDocument&&this.id){const e=this.proxy.labels,t=Array.from(this.proxy.getRootNode().querySelectorAll(`[for='${this.id}']`)),i=e?t.concat(Array.from(e)):t;return Object.freeze(i)}return n}valueChanged(e,t){this.dirtyValue=!0,this.proxy instanceof HTMLElement&&(this.proxy.value=this.value),this.currentValue=this.value,this.setFormValue(this.value),this.validate()}currentValueChanged(){this.value=this.currentValue}initialValueChanged(e,t){this.dirtyValue||(this.value=this.initialValue,this.dirtyValue=!1)}disabledChanged(e,t){this.proxy instanceof HTMLElement&&(this.proxy.disabled=this.disabled),h.queueUpdate((()=>this.classList.toggle("disabled",this.disabled)))}nameChanged(e,t){this.proxy instanceof HTMLElement&&(this.proxy.name=this.name)}requiredChanged(e,t){this.proxy instanceof HTMLElement&&(this.proxy.required=this.required),h.queueUpdate((()=>this.classList.toggle("required",this.required))),this.validate()}get elementInternals(){if(!af)return null;let e=sf.get(this);return e||(e=this.attachInternals(),sf.set(this,e)),e}connectedCallback(){super.connectedCallback(),this.addEventListener("keypress",this._keypressHandler),this.value||(this.value=this.initialValue,this.dirtyValue=!1),this.elementInternals||(this.attachProxy(),this.form&&this.form.addEventListener("reset",this.formResetCallback))}disconnectedCallback(){this.proxyEventsToBlock.forEach((e=>this.proxy.removeEventListener(e,this.stopPropagation))),!this.elementInternals&&this.form&&this.form.removeEventListener("reset",this.formResetCallback)}checkValidity(){return this.elementInternals?this.elementInternals.checkValidity():this.proxy.checkValidity()}reportValidity(){return this.elementInternals?this.elementInternals.reportValidity():this.proxy.reportValidity()}setValidity(e,t,i){this.elementInternals?this.elementInternals.setValidity(e,t,i):"string"==typeof t&&this.proxy.setCustomValidity(t)}formDisabledCallback(e){this.disabled=e}formResetCallback(){this.value=this.initialValue,this.dirtyValue=!1}attachProxy(){var e;this.proxyInitialized||(this.proxyInitialized=!0,this.proxy.style.display="none",this.proxyEventsToBlock.forEach((e=>this.proxy.addEventListener(e,this.stopPropagation))),this.proxy.disabled=this.disabled,this.proxy.required=this.required,"string"==typeof this.name&&(this.proxy.name=this.name),"string"==typeof this.value&&(this.proxy.value=this.value),this.proxy.setAttribute("slot",nf),this.proxySlot=document.createElement("slot"),this.proxySlot.setAttribute("name",nf)),null===(e=this.shadowRoot)||void 0===e||e.appendChild(this.proxySlot),this.appendChild(this.proxy)}detachProxy(){var e;this.removeChild(this.proxy),null===(e=this.shadowRoot)||void 0===e||e.removeChild(this.proxySlot)}validate(e){this.proxy instanceof HTMLElement&&this.setValidity(this.proxy.validity,this.proxy.validationMessage,e)}setFormValue(e,t){this.elementInternals&&this.elementInternals.setFormValue(e,t||e)}_keypressHandler(e){if("Enter"===e.key&&this.form instanceof HTMLFormElement){const e=this.form.querySelector("[type=submit]");null==e||e.click()}}stopPropagation(e){e.stopPropagation()}};return he({mode:"boolean"})(t.prototype,"disabled"),he({mode:"fromView",attribute:"value"})(t.prototype,"initialValue"),he({attribute:"current-value"})(t.prototype,"currentValue"),he(t.prototype,"name"),he({mode:"boolean"})(t.prototype,"required"),y(t.prototype,"value"),t}(lf)){constructor(){super(...arguments),this.proxy=document.createElement("input")}}class df extends cf{constructor(){super(...arguments),this.handleClick=e=>{var t;this.disabled&&(null===(t=this.defaultSlottedContent)||void 0===t?void 0:t.length)<=1&&e.stopPropagation()},this.handleSubmission=()=>{if(!this.form)return;const e=this.proxy.isConnected;e||this.attachProxy(),"function"==typeof this.form.requestSubmit?this.form.requestSubmit(this.proxy):this.proxy.click(),e||this.detachProxy()},this.handleFormReset=()=>{var e;null===(e=this.form)||void 0===e||e.reset()},this.handleUnsupportedDelegatesFocus=()=>{var e;window.ShadowRoot&&!window.ShadowRoot.prototype.hasOwnProperty("delegatesFocus")&&(null===(e=this.$fastController.definition.shadowOptions)||void 0===e?void 0:e.delegatesFocus)&&(this.focus=()=>{this.control.focus()})}}formactionChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.formAction=this.formaction)}formenctypeChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.formEnctype=this.formenctype)}formmethodChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.formMethod=this.formmethod)}formnovalidateChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.formNoValidate=this.formnovalidate)}formtargetChanged(){this.proxy instanceof HTMLInputElement&&(this.proxy.formTarget=this.formtarget)}typeChanged(e,t){this.proxy instanceof HTMLInputElement&&(this.proxy.type=this.type),"submit"===t&&this.addEventListener("click",this.handleSubmission),"submit"===e&&this.removeEventListener("click",this.handleSubmission),"reset"===t&&this.addEventListener("click",this.handleFormReset),"reset"===e&&this.removeEventListener("click",this.handleFormReset)}validate(){super.validate(this.control)}connectedCallback(){var e;super.connectedCallback(),this.proxy.setAttribute("type",this.type),this.handleUnsupportedDelegatesFocus();const t=Array.from(null===(e=this.control)||void 0===e?void 0:e.children);t&&t.forEach((e=>{e.addEventListener("click",this.handleClick)}))}disconnectedCallback(){var e;super.disconnectedCallback();const t=Array.from(null===(e=this.control)||void 0===e?void 0:e.children);t&&t.forEach((e=>{e.removeEventListener("click",this.handleClick)}))}}kl([he({mode:"boolean"})],df.prototype,"autofocus",void 0),kl([he({attribute:"form"})],df.prototype,"formId",void 0),kl([he],df.prototype,"formaction",void 0),kl([he],df.prototype,"formenctype",void 0),kl([he],df.prototype,"formmethod",void 0),kl([he({mode:"boolean"})],df.prototype,"formnovalidate",void 0),kl([he],df.prototype,"formtarget",void 0),kl([he],df.prototype,"type",void 0),kl([y],df.prototype,"defaultSlottedContent",void 0);class uf{}kl([he({attribute:"aria-expanded"})],uf.prototype,"ariaExpanded",void 0),kl([he({attribute:"aria-pressed"})],uf.prototype,"ariaPressed",void 0),Lh(uf,wh),Lh(df,xh,uf);class pf extends df{appearanceChanged(e,t){e!==t&&(this.classList.add(t),this.classList.remove(e))}connectedCallback(){super.connectedCallback(),this.appearance||(this.appearance="neutral")}defaultSlottedContentChanged(){const e=this.defaultSlottedContent.filter((e=>e.nodeType===Node.ELEMENT_NODE));1===e.length&&e[0]instanceof SVGElement?this.control.classList.add("icon-only"):this.control.classList.remove("icon-only")}}ed([he],pf.prototype,"appearance",void 0);const hf=pf.compose({baseName:"button",baseClass:df,template:(e,t)=>re`
    <button
        class="control"
        part="control"
        ?autofocus="${e=>e.autofocus}"
        ?disabled="${e=>e.disabled}"
        form="${e=>e.formId}"
        formaction="${e=>e.formaction}"
        formenctype="${e=>e.formenctype}"
        formmethod="${e=>e.formmethod}"
        formnovalidate="${e=>e.formnovalidate}"
        formtarget="${e=>e.formtarget}"
        name="${e=>e.name}"
        type="${e=>e.type}"
        value="${e=>e.value}"
        aria-atomic="${e=>e.ariaAtomic}"
        aria-busy="${e=>e.ariaBusy}"
        aria-controls="${e=>e.ariaControls}"
        aria-current="${e=>e.ariaCurrent}"
        aria-describedby="${e=>e.ariaDescribedby}"
        aria-details="${e=>e.ariaDetails}"
        aria-disabled="${e=>e.ariaDisabled}"
        aria-errormessage="${e=>e.ariaErrormessage}"
        aria-expanded="${e=>e.ariaExpanded}"
        aria-flowto="${e=>e.ariaFlowto}"
        aria-haspopup="${e=>e.ariaHaspopup}"
        aria-hidden="${e=>e.ariaHidden}"
        aria-invalid="${e=>e.ariaInvalid}"
        aria-keyshortcuts="${e=>e.ariaKeyshortcuts}"
        aria-label="${e=>e.ariaLabel}"
        aria-labelledby="${e=>e.ariaLabelledby}"
        aria-live="${e=>e.ariaLive}"
        aria-owns="${e=>e.ariaOwns}"
        aria-pressed="${e=>e.ariaPressed}"
        aria-relevant="${e=>e.ariaRelevant}"
        aria-roledescription="${e=>e.ariaRoledescription}"
        ${Mh("control")}
    >
        ${Dh(0,t)}
        <span class="content" part="content">
            <slot ${Gh("defaultSlottedContent")}></slot>
        </span>
        ${Ph(0,t)}
    </button>
`,styles:(e,t)=>I`
    :host([disabled]),
    :host([disabled]:hover),
    :host([disabled]:active) {
      opacity: ${fu};
      background-color: ${eh};
      cursor: ${"not-allowed"};
    }

    ${Hh()}
  `.withBehaviors(pd(I`
        :host([disabled]),
        :host([disabled]:hover),
        :host([disabled]:active),
        :host([disabled]) .control,
        :host([disabled]) .control:hover,
        :host([appearance='neutral'][disabled]:hover) .control {
          forced-color-adjust: none;
          background-color: ${fd.ButtonFace};
          border-color: ${fd.GrayText};
          color: ${fd.GrayText};
          opacity: 1;
        }
      `),Xh("accent",I`
        :host([appearance='accent'][disabled]),
        :host([appearance='accent'][disabled]:hover),
        :host([appearance='accent'][disabled]:active) {
          background: ${Wp};
        }

        ${zh}
      `.withBehaviors(pd(I`
            :host([appearance='accent'][disabled]) .control,
            :host([appearance='accent'][disabled]) .control:hover {
              background: ${fd.ButtonFace};
              border-color: ${fd.GrayText};
              color: ${fd.GrayText};
            }
          `))),Xh("lightweight",I`
        :host([appearance='lightweight'][disabled]:hover),
        :host([appearance='lightweight'][disabled]:active) {
          background-color: transparent;
          color: ${qp};
        }

        :host([appearance='lightweight'][disabled]) .content::before,
        :host([appearance='lightweight'][disabled]:hover) .content::before,
        :host([appearance='lightweight'][disabled]:active) .content::before {
          background: transparent;
        }

        ${Zh}
      `.withBehaviors(pd(I`
            :host([appearance='lightweight'][disabled]) .control {
              forced-color-adjust: none;
              color: ${fd.GrayText};
            }

            :host([appearance='lightweight'][disabled]) .control:hover .content::before {
              background: none;
            }
          `))),Xh("outline",I`
        :host([appearance='outline'][disabled]:hover),
        :host([appearance='outline'][disabled]:active) {
          background: transparent;
          border-color: ${ph};
        }

        ${Yh}
      `.withBehaviors(pd(I`
            :host([appearance='outline'][disabled]) .control {
              border-color: ${fd.GrayText};
            }
          `))),Xh("stealth",I`
        :host([appearance='stealth'][disabled]),
        :host([appearance='stealth'][disabled]:hover),
        :host([appearance='stealth'][disabled]:active) {
          background: ${sh};
        }

        ${Kh}
      `.withBehaviors(pd(I`
            :host([appearance='stealth'][disabled]),
            :host([appearance='stealth'][disabled]:hover) {
              background: ${fd.ButtonFace};
            }

            :host([appearance='stealth'][disabled]) .control {
              background: ${fd.ButtonFace};
              border-color: transparent;
              color: ${fd.GrayText};
            }
          `)))),shadowOptions:{delegatesFocus:!0}}),ff=(e,t)=>new Intl.NumberFormat(e,{style:"currency",...t}),gf=(ff("en-US",{currency:"USD",minimumFractionDigits:0,maximumFractionDigits:0}),ff(navigator.language,{currency:"USD"})),Ef=new Intl.NumberFormat;var Tf=function(e,t,i,o){var r,n=arguments.length,a=n<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,i):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,i,o);else for(var s=e.length-1;s>=0;s--)(r=e[s])&&(a=(n<3?r(a):n>3?r(t,i,a):r(t,i))||a);return n>3&&a&&Object.defineProperty(t,i,a),a};const mf=I`
  .rewardsRowStyle {
    padding: 14px 14px;
    display: grid;
    grid-template-columns: auto 1fr auto;
    grid-template-areas: 'icon points link';
    column-gap: 12px;
  }

  .rewardsRowStyle:hover,
  :focus {
    background-color: ${An.buttonBackgroundHover};
    outline: 0;
    text-decoration: none !important;
  }

  @media (prefers-color-scheme: dark) {
    .rewardsRowStyle:hover,
    :focus {
      background-color: ${An.darkModeButtonBackgroundHover};
      outline: 0;
    }
  }

  @media screen and (-ms-high-contrast: active) {
    .rewardsRowStyle:hover,
    :focus {
      background-color: highlight;
      outline: 0;
    }
  }

  .rewardsIcon {
    grid-area: icon;
    width: 36px;
    height: 36px;
    border-radius: 6px;
    box-sizing: border-box;
    background-color: rgba(4, 115, 206, 0.08);
    display: inline-grid;
    justify-content: center;
    align-content: center;
  }

  @media (prefers-color-scheme: dark) {
    .rewardsIcon {
      background-color: rgb(76, 89, 99);
    }
    .rewardsIcon path {
      fill: ${An.darkModeNormalText};
    }
  }

  .rewardsLabel {
    font-size: 12px;
    justify-self: start;
    align-self: start;
  }

  .rewardPoints {
    font-weight: 600;
    font-size: 14px;
    justify-self: start;
    align-self: end;
  }

  .rewardsLink {
    font-size: 12px;
    line-height: 16px;
    color: #115ea3;
    justify-self: end;
    align-self: end;
    text-decoration: none;
  }

  @media (prefers-color-scheme: dark) {
    .rewardsLink {
      color: #479ef5;
      border-bottom-color: #479ef5;
    }
  }

  .rewardsLink:hover,
  .rewardsLink:focus {
    background: none !important;
    text-decoration: underline !important;
  }
`,Nf=re`
  <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M12.5277 0.501221C13.3562 0.501221 14.0277 1.17279 14.0277 2.00122V3.39679C14.0277 4.33588 13.5015 5.19575 12.6652 5.62303L8.47646 7.76374C9.7019 8.32019 10.5541 9.55468 10.5541 10.9882C10.5541 12.943 8.96942 14.5277 7.0146 14.5277C5.05977 14.5277 3.47507 12.943 3.47507 10.9882C3.47507 9.55468 4.32729 8.32019 5.55273 7.76374L1.36398 5.62303C0.583481 5.22424 0.0730113 4.4486 0.00841761 3.58345L0.00146484 3.39679V2.00122C0.00146484 1.17279 0.673038 0.501221 1.50146 0.501221H12.5277ZM7.0146 8.4487C5.61206 8.4487 4.47507 9.58568 4.47507 10.9882C4.47507 12.3908 5.61206 13.5277 7.0146 13.5277C8.41714 13.5277 9.55412 12.3908 9.55412 10.9882C9.55412 9.58568 8.41714 8.4487 7.0146 8.4487ZM9.55346 1.50122H4.47446V6.08922L6.7871 7.27097C6.92998 7.34398 7.09921 7.34398 7.24209 7.27097L9.55346 6.08922V1.50122ZM3.47446 1.50122H1.50146C1.22532 1.50122 1.00146 1.72508 1.00146 2.00122V3.39679L1.00797 3.53654C1.05538 4.04443 1.35904 4.49753 1.81897 4.73253L3.47446 5.57822V1.50122ZM12.5277 1.50122H10.5535V5.57822L12.2102 4.73253C12.712 4.47617 13.0277 3.96024 13.0277 3.39679V2.00122C13.0277 1.72508 12.8039 1.50122 12.5277 1.50122Z"
      fill="#036AC4"
    />
  </svg>
`,_f=re`
  <svg width="16" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M8.49652 13.8028L14.7408 7.4994C16.4252 5.78856 16.4199 3.02549 14.7239 1.31248C13.0611 -0.367087 10.3961 -0.410511 8.69784 1.19085C8.66122 1.22538 8.62506 1.26067 8.58936 1.29673L7.99299 1.90026L7.38843 1.28963C7.35529 1.25615 7.32175 1.22333 7.28783 1.19115C5.58595 -0.422752 2.91654 -0.398074 1.26122 1.2739C-0.427101 2.9792 -0.418861 5.75003 1.27679 7.46274L7.55368 13.8028C7.81404 14.0657 8.23617 14.0657 8.49652 13.8028ZM9.3 2.00029C10.5964 0.691344 12.7025 0.692029 14.0133 2.01604C15.3253 3.34123 15.3272 5.47733 14.0292 6.7968L14.0282 6.79782L8.02517 12.8576L1.98743 6.75918C0.674081 5.43262 0.672858 3.28952 1.97185 1.97746C3.26525 0.671056 5.36984 0.672076 6.6778 1.99319L7.63801 2.96305C7.8338 3.16081 8.15338 3.16066 8.34898 2.96271L9.3 2.00029Z"
      fill="#036AC4"
    />
  </svg>
`,Of=re`
  ${le((e=>e.isVisible),re`
      <div
        class="rewardsRowStyle"
        tabindex="0"
        role="button"
        @click=${(e,t)=>e.onActionLinkClick(t.event)}
        @keydown=${(e,t)=>e.onActionAreaKeyDown(t.event)}
      >
        <div class="rewardsIcon">
          ${e=>e.rewards?.data?.is_give_mode?_f:Nf}
        </div>
        <div style="grid-area: points; display: inherit;">
          <label class="rewardsLabel">${_s.getString("miniWalletRewardsLabel","Microsoft Rewards")}</label>
          <label class="rewardPoints"
            >${e=>bs.miniWalletRewardsRowRewardsPointsBalance([Ef.format(e.rewards?.data?.total_points)])}</label
          >
        </div>
        <div style="grid-area: link; display: inherit;">
          <a class="rewardsLink" @click=${(e,t)=>e.onActionLinkClick(t.event)} href="#">${e=>e.actionText}</a>
        </div>
      </div>
    `)}
`;let bf=class extends Ie{constructor(){super(),this._isvisible=!1,Bs()&&(this.rewards=Ls(ws()))}get rewards(){return R.track(this,"rewards"),this._rewards}set rewards(e){this._rewards=e,R.notify(this,"rewards"),this.isVisible=ks(this._rewards?.data),this.isVisible&&(As(Is.kWalletCardRewardsImpression),Ss(this._rewards.event_type)?As(Is.kWalletCardRewardsImpression_FromCache):As(Is.kWalletCardRewardsImpression_NoCache),this._rewards?.data?.rewards_points>0?(this.actionText=_s.getString("miniWalletRewardsRedeemButtonText","Redeem"),As(Is.kWalletCardRewardsRedeemLinkImpression)):(this.actionText=_s.getString("miniWalletRewardsEarnButtonText","Earn"),As(Is.kWalletCardRewardsEarnLinkImpression)),this._rewards?.data?.is_give_mode?As(Is.kWalletCardRewardsImpression_GiveMode):As(Is.kWalletCardRewardsImpression_NonGiveMode))}get isVisible(){return R.track(this,"isVisible"),this._isvisible}set isVisible(e){this._isvisible=e,R.notify(this,"isVisible")}onActionAreaKeyDown(e){return e.key!==Ce&&e.key!==ve||this.onActionLinkClick(e),!0}onActionLinkClick(e){As(Is.kWalletCardRewardsClicked),this.rewards?.data.rewards_points>0?(As(Is.kWalletCardRewardsRedeemLinkClicked),this._rewards?.data?.is_give_mode?Dt.navigateToUrl(Re.microsoftRewardsGiveDashboardURL):Dt.navigateToUrl(Re.microsoftRewardsRedeemURL)):(As(Is.kWalletCardRewardsEarnLinkClicked),Dt.navigateToUrl(Re.microsoftRewardsDashboardURL)),this._rewards?.data?.is_give_mode?As(Is.kWalletCardRewardsClicked_GiveMode):As(Is.kWalletCardRewardsClicked_NonGiveMode),e.stopPropagation()}};Tf([y],bf.prototype,"actionText",void 0),bf=Tf([Ae({name:"rewards-row",template:Of,styles:mf})],bf);const If=I`
  .rebatesRowStyle {
    padding: 14px 14px;
    display: grid;
    grid-template-columns: auto 1fr auto;
    grid-template-areas: 'icon points link';
    column-gap: 12px;
  }

  .rebatesRowStyle:hover,
  :focus {
    background-color: ${An.buttonBackgroundHover};
    outline: 0;
    text-decoration: none !important;
  }

  @media (prefers-color-scheme: dark) {
    .rebatesRowStyle:hover,
    :focus {
      background-color: ${An.darkModeButtonBackgroundHover};
      outline: 0;
    }
  }

  @media screen and (-ms-high-contrast: active) {
    .rebatesRowStyle:hover,
    :focus {
      background-color: highlight;
      outline: 0;
    }
  }

  .rebatesIcon {
    grid-area: icon;
    width: 36px;
    height: 36px;
    border-radius: 6px;
    box-sizing: border-box;
    background-color: rgba(4, 115, 206, 0.08);
    display: inline-grid;
    justify-content: center;
    align-content: center;
  }

  @media (prefers-color-scheme: dark) {
    .rebatesIcon {
      background-color: rgb(76, 89, 99);
    }
    .rebatesIcon path,
    .pendingBalanceIcon path {
      fill: ${An.darkModeNormalText};
    }
    .availableBalanceIcon path {
      fill: '#75FA8D';
    }
  }

  .rebatesLabel {
    font-size: 12px;
    justify-self: start;
    align-self: start;
  }

  .rebatesPoints {
    font-weight: 600;
    font-size: 14px;
    justify-self: start;
    align-self: end;
  }

  .rebatesPointsContainer {
    display: flex;
    align-items: center;
  }

  .rebatesLink {
    font-size: 12px;
    line-height: 16px;
    color: #115ea3;
    justify-self: end;
    align-self: end;
    text-decoration: none;
  }

  @media (prefers-color-scheme: dark) {
    .rebatesLink {
      color: #479ef5;
      border-bottom-color: #479ef5;
    }
  }

  .rebatesLink:hover,
  .rebatesLink:focus {
    background: none !important;
    text-decoration: underline !important;
  }

  .pendingBalanceIcon,
  .availableBalanceIcon {
    width: 16px;
    height: 17px;
    gap: 5px;
  }

  .availableBalanceContainer {
    margin-right: 10px;
    display: flex;
    margin-top: 4px;
  }

  .pendingBalanceContainer {
    margin-top: 3px;
    display: flex;
  }

  .linkContainer {
    grid-area: link;
    display: inherit;
  }

  .linkContainerV2 {
    grid-area: link;
    display: inherit;
    padding-bottom: 4px;
  }
`,Af=re`
  ${le((e=>e.isVisible),re`
      <div
        class="rebatesRowStyle"
        tabindex="0"
        role="button"
        @click=${(e,t)=>e.onActionLinkClick(t.event)}
        @keydown=${(e,t)=>e.onActionAreaKeyDown(t.event)}
      >
        <div class="rebatesIcon">
          <svg width="18" height="12" viewBox="0 0 18 12" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M13.1881 1.14808C12.8129 0.767947 12.3012 0.553671 11.7671 0.55299L2.33005 0.540968C1.22448 0.539559 0.3275 1.4354 0.3275 2.54097L0.3275 9.53905C0.3275 10.6436 1.22293 11.5391 2.3275 11.5391H11.7049C12.2363 11.5391 12.7459 11.3275 13.1212 10.9512L16.6162 7.4458C17.3917 6.66803 17.3949 5.41045 16.6234 4.62876L13.1881 1.14808ZM11.7659 1.55299C12.0329 1.55333 12.2887 1.66047 12.4763 1.85053L15.9117 5.33121C16.2974 5.72206 16.2958 6.35085 15.9081 6.73973L12.413 10.2451C12.2254 10.4333 11.9706 10.5391 11.7049 10.5391H2.3275C1.77521 10.5391 1.3275 10.0913 1.3275 9.53905L1.3275 2.54097C1.3275 1.98818 1.77599 1.54026 2.32877 1.54097L11.7659 1.55299Z"
              fill="#036AC4"
            />
          </svg>
        </div>
        <div style="grid-area: points; display: inherit;">
          <label class="rebatesLabel">${_s.getString("miniWalletRebatesLabel","Microsoft Cashback")}</label>
          <div class="rebatesPointsContainer">
            ${e=>e.getAvailableBalanceContent(e.rebates?.data)}
            ${e=>e.getPendingBalanceContent(e.rebates?.data)}
          </div>
        </div>
        <div class=${Rs()?"linkContainerV2":"linkContainer"}>
          <a class="rebatesLink" @click=${(e,t)=>e.onActionLinkClick(t.event)} href="#">
            ${e=>e.rebates?.data?.available_balance>0?_s.getString("miniWalletRebatesGetPaidButtonText","Get Paid"):_s.getString("miniWalletRebatesExploreDealsButtonText","Explore deals")}
          </a>
        </div>
      </div>
    `)}
`;let Cf=class extends Ie{constructor(){super(),this._isvisible=!1,Bs()&&(this.rebates=Fs(ws()))}get rebates(){return R.track(this,"rebates"),this._rebates}set rebates(e){this._rebates=e,R.notify(this,"rebates"),this.isVisible=Ms(this._rebates?.data),this.isVisible&&(As(Is.kWalletCardRebatesImpression),Ss(this._rebates.event_type)?As(Is.kWalletCardRebatesImpression_FromCache):As(Is.kWalletCardRebatesImpression_NoCache))}get isVisible(){return R.track(this,"isVisible"),this._isvisible}set isVisible(e){this._isvisible=e,R.notify(this,"isVisible")}onActionLinkClick(e){As(Is.kWalletCardRebatesValueClicked),Dt.navigateToUrl(this.rebates?.data?.available_balance>0?Re.microsoftRebatesPayoutURL:Re.microsoftRebatesDealsURL),e.stopPropagation()}onActionAreaKeyDown(e){return e.key!==Ce&&e.key!==ve||this.onActionLinkClick(e),!0}getAvailableBalanceContent(e){const t=re`<label class="rebatesPoints"
      >${e?.formatted_available_balance?e?.formatted_available_balance:gf.format(e?.available_balance)}</label
    >`;return Rs()&&e?.available_balance>0?re`<div
        class="availableBalanceContainer"
        title="${_s.getString("miniWalletCashbackEarnedBalanceTooltip","Available balance")}"
      >
        <div>
          <svg class="availableBalanceIcon" viewBox="0 -2 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M8 2C11.3137 2 14 4.68629 14 8C14 11.3137 11.3137 14 8 14C4.68629 14 2 11.3137 2 8C2 4.68629 4.68629 2 8 2ZM8 3C5.23858 3 3 5.23858 3 8C3 10.7614 5.23858 13 8 13C10.7614 13 13 10.7614 13 8C13 5.23858 10.7614 3 8 3ZM7.24953 9.04242L10.1203 6.16398C10.3153 5.96846 10.6319 5.96803 10.8274 6.16304C11.0012 6.33637 11.0208 6.60577 10.8861 6.80082L10.8283 6.87014L7.60403 10.1031C7.43053 10.277 7.16082 10.2965 6.96576 10.1615L6.89645 10.1036L5.14645 8.35355C4.95118 8.15829 4.95118 7.84171 5.14645 7.64645C5.32001 7.47288 5.58944 7.4536 5.78431 7.58859L5.85355 7.64645L7.24953 9.04242L10.1203 6.16398L7.24953 9.04242Z"
              fill="#107C10"
            />
          </svg>
        </div>
        <div>${t}</div>
      </div>`:Rs()&&e?.pending_balance>0?void 0:t}getPendingBalanceContent(e){if(Rs()&&e?.pending_balance>0)return re`<div
        title="${_s.getString("miniWalletCashbackPendingBalanceTooltip","Pending balance")}"
        class="pendingBalanceContainer"
      >
        <div>
          <svg class="pendingBalanceIcon" viewBox="0 -2 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M3.00256 3.41029C2.97853 2.58375 3.801 2 4.68127 2H11.3208C12.201 2 13.0235 2.58371 12.9995 3.41023C12.9742 4.28185 12.7596 4.94514 12.433 5.47277C12.1071 5.9991 11.6851 6.36524 11.292 6.65632C11.1562 6.75692 11.0165 6.85364 10.8893 6.94173C10.8378 6.97738 10.7883 7.01162 10.742 7.04413C10.5733 7.16248 10.4348 7.26546 10.3217 7.36898C10.1093 7.56333 10.0016 7.74392 10.0016 8C10.0016 8.25626 10.1092 8.43691 10.3212 8.63116C10.4341 8.73465 10.5725 8.83759 10.7409 8.95592C10.7873 8.98852 10.8368 9.02287 10.8885 9.05864C11.0154 9.14662 11.1547 9.24321 11.2902 9.34362C11.6829 9.63464 12.1046 10.0006 12.4304 10.5267C12.7571 11.0541 12.9721 11.7171 12.9984 12.5883C13.0234 13.4154 12.2005 14 11.3196 14H4.67923C3.79837 14 2.97547 13.4154 3.00056 12.5883C3.02699 11.7169 3.24232 11.0539 3.56937 10.5265C3.89561 10.0005 4.31773 9.63447 4.71078 9.34348C4.84637 9.2431 4.98581 9.14654 5.11283 9.05859C5.16453 9.02279 5.21417 8.98841 5.26066 8.95579C5.42924 8.83746 5.56771 8.73451 5.68076 8.63101C5.89296 8.43675 6.00056 8.25617 6.00051 8.00007C6.00047 7.74397 5.89275 7.56335 5.68034 7.36899C5.5672 7.26546 5.42864 7.16248 5.25999 7.04413C5.21367 7.01162 5.16424 6.97739 5.11277 6.94175C4.98554 6.85365 4.84585 6.75693 4.70999 6.65632C4.31691 6.36524 3.89491 5.99911 3.56909 5.47278C3.24247 4.94516 3.0279 4.28189 3.00256 3.41029ZM4.68127 2.85714C4.27678 2.85714 3.99455 3.11489 4.00252 3.38894C4.02454 4.14654 4.20837 4.67637 4.45274 5.07111C4.6979 5.46714 5.01945 5.75263 5.36402 6.00779C5.48686 6.09875 5.60539 6.18082 5.72713 6.26509C5.7818 6.30294 5.83712 6.34124 5.89376 6.38099C6.06867 6.50374 6.2499 6.63619 6.41044 6.78309C6.74541 7.08959 7.00069 7.46982 7.00078 7.99993C7.00088 8.52996 6.74589 8.9102 6.41103 9.21676C6.25055 9.36367 6.06938 9.49613 5.89448 9.61889C5.83759 9.65882 5.78204 9.69728 5.72713 9.73529C5.60566 9.81939 5.48736 9.90129 5.36471 9.99209C5.02005 10.2472 4.69825 10.5327 4.45267 10.9288C4.20789 11.3235 4.02346 11.8532 4.00049 12.6106C3.99218 12.8848 4.27442 13.1429 4.67923 13.1429H11.3196C11.7244 13.1429 12.0067 12.8848 11.9984 12.6105C11.9756 11.8531 11.7914 11.3233 11.5469 10.9286C11.3016 10.5325 10.9802 10.2471 10.6359 9.99194C10.5134 9.90112 10.3951 9.8192 10.2738 9.73508C10.2189 9.6971 10.1635 9.65866 10.1067 9.61876C9.93197 9.496 9.75095 9.36354 9.59062 9.21661C9.25604 8.91004 9.00133 8.52987 9.00133 8C9.00133 7.46987 9.25658 7.08961 9.59155 6.78309C9.75209 6.63619 9.93332 6.50373 10.1082 6.38098C10.1649 6.34122 10.2202 6.30291 10.2749 6.26505C10.3967 6.18078 10.5152 6.09873 10.638 6.00777C10.9826 5.75261 11.3042 5.46712 11.5493 5.07109C11.7937 4.67635 11.9775 4.14652 11.9995 3.38891C12.0075 3.11487 11.7253 2.85714 11.3208 2.85714H4.68127Z"
              fill="#616161"
            />
          </svg>
        </div>
        <div>
          <label class="rebatesPoints"
            >${e?.formatted_pending_balance?e?.formatted_pending_balance:gf.format(e?.pending_balance)}</label
          >
        </div>
      </div>`}};Cf=function(e,t,i,o){var r,n=arguments.length,a=n<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,i):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,i,o);else for(var s=e.length-1;s>=0;s--)(r=e[s])&&(a=(n<3?r(a):n>3?r(t,i,a):r(t,i))||a);return n>3&&a&&Object.defineProperty(t,i,a),a}([Ae({name:"rebates-row",template:Af,styles:If})],Cf);var vf=function(e,t,i,o){var r,n=arguments.length,a=n<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,i):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,i,o);else for(var s=e.length-1;s>=0;s--)(r=e[s])&&(a=(n<3?r(a):n>3?r(t,i,a):r(t,i))||a);return n>3&&a&&Object.defineProperty(t,i,a),a};const Rf=I`
  .etreeRowStyle {
    padding: 14px 14px;
    display: grid;
    grid-template-columns: auto 1fr auto;
    grid-template-areas: 'icon points link';
    column-gap: 12px;
  }

  .etreeRowStyle:hover,
  :focus {
    background-color: ${An.buttonBackgroundHover};
    outline: 0;
    text-decoration: none !important;
  }

  @media (prefers-color-scheme: dark) {
    .etreeRowStyle:hover,
    :focus {
      background-color: ${An.darkModeButtonBackgroundHover};
      outline: 0;
    }
  }

  @media screen and (-ms-high-contrast: active) {
    .etreeRowStyle:hover,
    :focus {
      background-color: highlight;
      outline: 0;
    }
  }

  .etreeIcon {
    grid-area: icon;
    width: 36px;
    height: 36px;
    border-radius: 6px;
    box-sizing: border-box;
    background-color: rgba(4, 115, 206, 0.08);
    display: inline-grid;
    justify-content: center;
    align-content: center;
    border-bottom: 1px solid transparent;
  }

  @media (prefers-color-scheme: dark) {
    .etreeIcon {
      background-color: rgb(76, 89, 99);
    }
    .etreeIcon path {
      fill: ${An.darkModeNormalText};
    }
  }

  .etreeLabel {
    font-size: 12px;
    justify-self: start;
    align-self: start;
  }

  .etreeDescription {
    font-weight: 600;
    font-size: 14px;
    justify-self: start;
    align-self: end;
  }

  .etreeLink {
    font-size: 12px;
    line-height: 16px;
    color: #115ea3;
    justify-self: end;
    align-self: end;
    text-decoration: none;
  }

  @media (prefers-color-scheme: dark) {
    .etreeLink {
      color: #479ef5;
      border-bottom-color: #479ef5;
    }
  }

  .etreeLink:hover,
  .etreeLink:focus {
    background: none !important;
    text-decoration: underline !important;
  }
`,yf=re`
  ${le((e=>e.isVisible),re`
      <div
        class="etreeRowStyle"
        tabindex="0"
        role="button"
        @click=${(e,t)=>e.onActionLinkClick(t.event)}
        @keydown=${(e,t)=>e.onActionAreaKeyDown(t.event)}
      >
        <div class="etreeIcon">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M3.96407 7.16962C4.31087 5.44567 5.51414 4.03165 7.11099 3.39047C7.96353 2.53174 9.145 2 10.4507 2C12.127 2 13.5986 2.87652 14.4322 4.19625C15.9853 4.66029 17.1175 6.09989 17.1175 7.80392C17.1175 7.89049 17.1146 7.97639 17.1088 8.06151C17.653 8.71434 17.9804 9.55423 17.9804 10.4706C17.9804 12.5237 16.337 14.1929 14.2939 14.2345V14.2353H14.2223L14.2157 14.2353L14.2091 14.2353H6.77355L6.76471 14.2353C4.68552 14.2353 3 12.3391 3 10C3 8.91196 3.3647 7.91974 3.96407 7.16962ZM4.92438 7.65257C5.13743 6.12325 6.14013 4.84673 7.5073 4.24815C8.23578 3.44534 9.28749 2.9412 10.4569 2.9412C11.958 2.9412 13.265 3.77172 13.9436 4.9983C15.2421 5.44 16.1763 6.66974 16.1763 8.11769C16.1763 8.19857 16.1734 8.27877 16.1677 8.3582C16.7504 8.87543 17.1176 9.63012 17.1176 10.4706C17.1176 12.03 15.8535 13.2941 14.2941 13.2941H14.294V13.2941H7.18294L7.18302 13.2937C5.38789 13.2657 3.94129 11.8018 3.94129 9.99999C3.94129 9.08109 4.31754 8.25007 4.92438 7.65257ZM8.64702 14.2353V17C8.64702 17.5522 9.09473 18 9.64702 18H11.4117C11.964 18 12.4117 17.5522 12.4117 17V14.2353H11.4709V16.6588C11.4709 16.8797 11.2919 17.0588 11.0709 17.0588H9.98859C9.76767 17.0588 9.58859 16.8797 9.58859 16.6588V14.2353H8.64702Z"
              fill="#3267FA"
            />
          </svg>
        </div>
        <div style="grid-area: points; display: inherit;">
          <label class="etreeLabel">${_s.getString("miniWalletEtreeLabel","E-tree")}</label>
          <label class="etreeDescription">${e=>e.descriptionText}</label>
        </div>
        <div style="grid-area: link; display: inherit;">
          <a class="etreeLink" @click=${(e,t)=>e.onActionLinkClick(t.event)} href="#">${e=>e.actionText}</a>
        </div>
      </div>
    `)}
`;let Sf=class extends Ie{constructor(){super(),this._isvisible=!1,Bs()&&(this.etree=Ws(ws()))}get etree(){return R.track(this,"etree"),this._etree}set etree(e){this._etree=e,R.notify(this,"etree"),this.isVisible=xs(this._etree?.data),this.isVisible&&As(Is.kWalletCardEtreeImpression),Ps(this._etree?.data)?Ds(this._etree?.data)?(this.descriptionText=_s.getStringFWithDefaultValue("miniWalletEtreeLevelLabel","Level $1",this._etree?.data?.etree_level),this.actionText=_s.getString("miniWalletEtreeViewCertificateButtonText","View certificate"),As(Is.kWalletCardEtreeCertificateLinkImpression)):(this.descriptionText=_s.getStringFWithDefaultValue("miniWalletEtreeLevelLabel","Level $1",this._etree?.data?.etree_level),this.actionText=_s.valueExists("miniWalletEtreeGrowMyTreeButtonText")?_s.getString("miniWalletEtreeGrowMyTreeButtonText","Grow my tree"):_s.getString("miniWalletEtreeGoToPlantButtonText","Go to plant"),As(Is.kWalletCardEtreeLevelLinkImpression)):(this.descriptionText=_s.valueExists("miniWalletEtreePlantRealTreeLabel")?_s.getString("miniWalletEtreePlantRealTreeLabel","Plant a real tree"):_s.getString("miniWalletEtreeEnrollLabel","Get 10 drops"),this.actionText=_s.valueExists("miniWalletEtreeGrowMyTreeButtonText")?_s.getString("miniWalletEtreeGrowMyTreeButtonText","Grow my tree"):_s.getString("miniWalletEtreeEnrollButtonText","Get started"),As(Is.kWalletCardEtreeEnrollLinkImpression))}get isVisible(){return R.track(this,"isVisible"),this._isvisible}set isVisible(e){this._isvisible=e,R.notify(this,"isVisible")}onActionLinkClick(e){As(Is.kWalletCardEtreeClicked),Ps(this._etree?.data)?Ds(this._etree?.data)?As(Is.kWalletCardEtreeClicked_Certificate):As(Is.kWalletCardEtreeClicked_Level):As(Is.kWalletCardEtreeClicked_Enroll),Dt.navigateToUrl(Ct(At(Re.walletEtreeURL,"profileflyout"),"MiniWalletEtree")),e.stopPropagation()}onActionAreaKeyDown(e){return e.key!==Ce&&e.key!==ve||this.onActionLinkClick(e),!0}};vf([y],Sf.prototype,"actionText",void 0),vf([y],Sf.prototype,"descriptionText",void 0),Sf=vf([Ae({name:"etree-row",template:yf,styles:Rf})],Sf);Xc.getOrCreate(undefined).withPrefix("fluent").register(Sh(),Jh(),tf(),hf());const wf=e=>{e.preventDefault()},kf=I`
  .root {
    width: 298px;
    background-color: ${An.whiteBackground};
    color: var(--neutral-foreground-rest);
    direction: ${ke?.valueExists("textdirection")&&"rtl"===ke.getValue("textdirection")?"rtl":"ltr"};
  }

  .root fluent-divider {
    margin: 0px;
  }

  @media (prefers-color-scheme: dark) {
    .root {
      background-color: ${An.darkMiniWalletBackground};
    }

    .root fluent-divider {
      border-color: ${An.miniWalletDividerDarkMode};
    }
  }
`,Mf=re`
  <fluent-design-system-provider use-defaults>
    <div class="root">
      ${re`<wallet-header :content_flags=${e=>e.content_flags}></wallet-header> `}
      ${le((e=>e.isAnyRowVisible),re`<fluent-divider></fluent-divider>`)}
      ${re`<rewards-row :rewards=${e=>e.rewards}></rewards-row> `}
      ${le((e=>e.isRebatesRowVisible),re`<rebates-row :rebates=${e=>e.rebates}></rebates-row> `)}
      ${le((e=>e.isEtreeRowVisible),re`<etree-row :etree=${e=>e.etree}></etree-row> `)}
      ${le((e=>e.isNotificationVisible),re` <fluent-divider></fluent-divider
          ><notification-container
            :notification=${e=>e.notification}
            :appName=${e=>e.appName}
          ></notification-container>`)}
    </div>
  </fluent-design-system-provider>
`;let xf=class extends Ie{constructor(){super(),this._isNotificationVisible=!1,this._isAnyRowVisible=!1,this._isRebatesRowVisible=!1,this._isEtreeRowVisible=!1,window.addEventListener("error",(function(e){e.error&&gt(e,"mini-wallet")})),this.appName=ct.MiniWallet,function(e,t){Rc[e]=Rc[e]||{};const i=Cc++;Rc[e][i]=t}("mini-wallet-data-updated",(e=>{this.parseWalletContentsData(e)})),this.parseWalletContentsData(ws())}get content_flags(){return R.track(this,"content_flags"),this._content_flags}set content_flags(e){this._content_flags=e,R.notify(this,"content_flags")}get notification(){return R.track(this,"notification"),this._notification}set notification(e){this._notification=e,R.notify(this,"notification")}get isNotificationVisible(){return R.track(this,"isNotificationVisible"),this._isNotificationVisible}set isNotificationVisible(e){this._isNotificationVisible=e,R.notify(this,"isNotificationVisible")}get rewards(){return R.track(this,"rewards"),this._rewards}set rewards(e){this._rewards=e,R.notify(this,"rewards")}get rebates(){return R.track(this,"rewards"),this._rebates}set rebates(e){this._rebates=e,R.notify(this,"rewards")}get etree(){return R.track(this,"etree"),this._etree}set etree(e){this._etree=e,R.notify(this,"etree")}get isAnyRowVisible(){return R.track(this,"isAnyRowVisible"),this._isAnyRowVisible}set isAnyRowVisible(e){this._isAnyRowVisible=e,R.notify(this,"isAnyRowVisible")}get isRebatesRowVisible(){return R.track(this,"isRebatesRowVisible"),this._isRebatesRowVisible}set isRebatesRowVisible(e){this._isRebatesRowVisible=e,R.notify(this,"isRebatesRowVisible")}get isEtreeRowVisible(){return R.track(this,"isEtreeRowVisible"),this._isEtreeRowVisible}set isEtreeRowVisible(e){this._isEtreeRowVisible=e,R.notify(this,"isEtreeRowVisible")}connectedCallback(){super.connectedCallback();const e=window.matchMedia("(prefers-color-scheme: dark)"),t=e=>{const t=e.matches?cu.DarkMode:cu.LightMode,i=this.shadowRoot.querySelector("fluent-design-system-provider");Vu.setValueFor(i,t)};t(e),e.addEventListener("change",t),document.addEventListener("contextmenu",wf),As(Is.kWalletCardImpression)}updateNotificationData(e){this.notification=e?.should_show_notification&&null!=e.notification_data?(e=>{const t=e.type;switch(t){case st.CardExpiring:case st.CardExpired:case st.RoamCard:case st.CardTokenizationEligible:{const i=JSON.parse(e.data);return It(!1===i.isLocal,it(i.network)),{type:t,dataKey:e.key,data:null,previewData:i}}case st.PackageTracking:case st.Rebates:case st.PasswordLeakage:case st.UpcomingHotelReservations:case st.FeaturePromotion:case st.DonationSummary:return{type:t,dataKey:e.key,previewData:JSON.parse(e.data)};case st.PersonalizedOffersAvailable:return{type:t,dataKey:e.key,data:null,previewData:JSON.parse(e.data)};case st.SignupCryptoWallet:case st.EtreeCampaign:case st.PWAPromotion:return{type:t,dataKey:e.key};case st.Etree:case st.DonationTrendNpo:return{type:t,dataKey:e.key,data:e?.data?JSON.parse(e?.data):null}}})(e.notification_data):null,this.isNotificationVisible=null!=this.notification}parseWalletContentsData(e){this.updateNotificationData(e),this.rewards=Ls(e),this.rebates=Fs(e),this.etree=Ws(e),this.content_flags=e?.content_flags,this.isRebatesRowVisible=this.shouldDisplayRebatesRow(),this.isEtreeRowVisible=this.shouldDisplayEtreeRow(),this.isAnyRowVisible=ks(this.rewards?.data)||this.isRebatesRowVisible||this.isEtreeRowVisible}shouldDisplayRebatesRow(){return!!Ms(this.rebates?.data)}shouldDisplayEtreeRow(){return!(this.isRebatesRowVisible&&this.isNotificationVisible||!xs(this.etree?.data)||(e=this.content_flags,!function(e,t){return(e&t)>0}(e,Cs.Etree)));var e}disconnectedCallback(){window.removeEventListener("error",(e=>gt(e,"mini-wallet"))),document.removeEventListener("contextmenu",wf)}};xf=function(e,t,i,o){var r,n=arguments.length,a=n<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,i):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,i,o);else for(var s=e.length-1;s>=0;s--)(r=e[s])&&(a=(n<3?r(a):n>3?r(t,i,a):r(t,i))||a);return n>3&&a&&Object.defineProperty(t,i,a),a}([Ae({name:"mini-wallet",template:Mf,styles:kf})],xf)})()})();