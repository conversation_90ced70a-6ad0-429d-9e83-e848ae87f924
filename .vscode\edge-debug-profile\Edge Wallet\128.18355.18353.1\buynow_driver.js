!function(){"use strict";var e={};let t;e.d=function(t,o){for(var i in o)e.o(o,i)&&!e.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:o[i]})},e.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)};const o="BuyNowInitialized";let i=function(e){return e.IsBuyNowReady="IsBuyNowReady",e.BuyNowSubscribeNow="BuyNowSubscribeNow",e.BuyNowQuickAuthFinished="BuyNowQuickAuthFinished",e.BuyNowQuickAuthFailed="BuyNowQuickAuthFailed",e.LoadLocalCards="LoadLocalCards",e.BuyNowIsReady="BuyNowIsReady",e.BuyNowStartQuickAuth="BuyNowStartQuickAuth",e.BuyNowCheckoutFinished="BuyNowCheckoutFinished",e.LoadLocalCardsFinished="LoadLocalCardsFinished",e}({}),s=function(e){return e.EntryPointReady="EntryPointReady",e.StartBuyNow="StartBuyNow",e.VisitPublisher="VisitPublisher",e.MerchantAuthSucceeded="MerchantAuthSucceeded",e.MerchantAuthFailed="MerchantAuthFailed",e.LoadLocalCards="LoadLocalCards",e}({});const a="BuyNowRuntime";window[a]=new class{notifyHostPage(e,t){window.dispatchEvent(new CustomEvent(e,{detail:t}))}listenOnHostPageEvent(e,t){window.addEventListener(e,(e=>{t(e)}))}handleQuickAuthFinished(e){if(e.detail){const t=JSON.parse(e.detail);"skipped"===t.type?window[a].postMessageToHost(s.MerchantAuthFailed,[]):"display"!==t.type||t.displayed?"dismissed"===t.type&&"credential_returned"===t.reason&&window[a].postMessageToHost(s.MerchantAuthSucceeded,[]):window[a].postMessageToHost(s.MerchantAuthFailed,[])}}handleQuickAuthFailed(e){window[a].postMessageToHost(s.MerchantAuthFailed,[])}handleIsBuyNowReady(){window[a].notifyHostPage(i.BuyNowIsReady,"Ready"),window[a].postMessageToHost(s.EntryPointReady,[])}handleBuyNowSubscribeNow(e){window[a].postMessageToHost(s.StartBuyNow,[e.detail]),t=JSON.parse(e.detail)}handleLoadLocalCards(){window[a].postMessageToHost(s.LoadLocalCards,[])}raiseMessageFromHost(e){const o=e.shift(),s=JSON.parse(e[0]).event;if(o&&"StartMerchantAuth"===o)this.listenOnHostPageEvent(i.BuyNowQuickAuthFinished,this.handleQuickAuthFinished),this.listenOnHostPageEvent(i.BuyNowQuickAuthFailed,this.handleQuickAuthFailed),this.notifyHostPage(i.BuyNowStartQuickAuth,s);else if(o&&"VisitPublisher"===o){if(t&&t.plans){if(t.originalSubLink)return void window.open(t.originalSubLink,"_blank").focus();let e=0;for(let o=0;o<t.length;o+=1)if(t.plans[o].isPromotion){e=o;break}if(t.plans[e].url){const o=new URL(t.plans[e].url);window.open(o.origin,"_blank").focus()}}}else o&&"CheckoutFinished"===o?window[a].notifyHostPage(i.BuyNowCheckoutFinished,s):o&&"LocalCardsFinished"==o&&window[a].notifyHostPage(i.LoadLocalCardsFinished,s)}postMessageToHost(e,t){try{walletBuyNowNativeHandler&&walletBuyNowNativeHandler.sendMessageToHost(e,t)}catch(e){}}initialize(e){return window[o]||(this.listenOnHostPageEvent(i.IsBuyNowReady,this.handleIsBuyNowReady),this.listenOnHostPageEvent(i.BuyNowSubscribeNow,this.handleBuyNowSubscribeNow),this.listenOnHostPageEvent(i.LoadLocalCards,this.handleLoadLocalCards),window[o]=!0),!0}}}();