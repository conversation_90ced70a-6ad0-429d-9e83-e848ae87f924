# 🔧 Guide de Dépannage VS Code - Tunisie Telecom Scores Hub

## 🚨 Problème avec start-dev-server

### 📋 Symptômes
- La tâche "start-dev-server" ne fonctionne pas
- Erreur lors du démarrage avec F5
- Le serveur Vite ne démarre pas depuis VS Code

### 🔍 Causes possibles
1. **Problème PowerShell** : Restrictions d'exécution
2. **Chemin de workspace** : Structure de dossiers imbriquée
3. **Dépendances manquantes** : node_modules incomplet
4. **Configuration VS Code** : Paramètres incorrects

## ✅ Solutions (par ordre de priorité)

### **Solution 1 : Démarrage manuel (Recommandé)**

1. **Ouvrir le terminal intégré VS Code** (Ctrl + `)
2. **Naviguer vers le projet** :
   ```bash
   cd tunisie-telecom-scores-hub-main
   ```
3. **Démarrer le serveur** :
   ```bash
   npm run dev
   ```
4. **Utiliser le débogage "Launch Edge - Manual Start"**

### **Solution 2 : Utiliser CMD au lieu de PowerShell**

1. **Changer le terminal par défaut** :
   - Ctrl + Shift + P
   - Taper "Terminal: Select Default Profile"
   - Choisir "Command Prompt"

2. **Redémarrer VS Code** et essayer F5

### **Solution 3 : Configuration PowerShell**

Si vous devez utiliser PowerShell :
```powershell
# Exécuter en tant qu'administrateur
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### **Solution 4 : Réinstaller les dépendances**

```bash
cd tunisie-telecom-scores-hub-main
rm -rf node_modules package-lock.json
npm install
```

### **Solution 5 : Utiliser un port différent**

Si le port 5173 est occupé :
```bash
npx vite --port 3001
```
Puis modifier les URLs dans launch.json

## 🎯 Configurations VS Code mises à jour

### **launch.json** - Configurations de débogage disponibles :

1. **"Launch Edge with Auth (Recommended)"** - Avec authentification
2. **"Launch Chrome with Auth"** - Alternative Chrome
3. **"Launch Edge - Simple (No Auth)"** - Sans authentification
4. **"Launch Edge - Manual Start"** - Démarrage manuel (nouveau)

### **tasks.json** - Tâches disponibles :

1. **"start-dev-server"** - Démarrage automatique
2. **"stop-dev-server"** - Arrêt du serveur
3. **"start-vite-simple"** - Démarrage simple (nouveau)

## 🚀 Méthodes de démarrage

### **Méthode 1 : Débogage automatique**
1. Appuyer sur **F5**
2. Choisir "Launch Edge - Simple (No Auth)"
3. VS Code démarre automatiquement le serveur

### **Méthode 2 : Démarrage manuel + débogage**
1. **Terminal** : `cd tunisie-telecom-scores-hub-main && npm run dev`
2. **F5** → "Launch Edge - Manual Start"

### **Méthode 3 : Complètement manuel**
1. **Terminal** : `cd tunisie-telecom-scores-hub-main && npm run dev`
2. **Navigateur** : http://localhost:5173/

## 🔧 Commandes de dépannage

### **Vérifier l'installation** :
```bash
node --version    # Doit afficher v18+ 
npm --version     # Doit afficher 8+
npx vite --version # Doit afficher la version Vite
```

### **Nettoyer et réinstaller** :
```bash
cd tunisie-telecom-scores-hub-main
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

### **Tester Vite directement** :
```bash
cd tunisie-telecom-scores-hub-main
npx vite --host 0.0.0.0 --port 5173
```

## 📱 Test de l'application

Une fois le serveur démarré, vous devriez voir :

1. **Page d'accueil** : Formulaire de recherche client
2. **Logo Tunisie Telecom** : En haut à gauche
3. **Champs de saisie** : Nom*, Prénom*, Téléphone, Numéro client
4. **Bouton** : "Rechercher et voir le score"

### **Test complet** :
1. Saisir : Nom "Dupont", Prénom "Jean"
2. Cliquer "Rechercher et voir le score"
3. Voir le dashboard avec le score calculé
4. Utiliser "Nouvelle recherche" pour revenir

## 🆘 Si rien ne fonctionne

### **Alternative : Utiliser un autre éditeur**
- **WebStorm** : Meilleure gestion des projets Node.js
- **Sublime Text** + Terminal séparé
- **Notepad++** + Terminal séparé

### **Alternative : Ligne de commande pure**
```bash
# Terminal/CMD
cd tunisie-telecom-scores-hub-main
npm run dev

# Navigateur
http://localhost:5173/
```

## 📞 Support

Si le problème persiste :
1. Vérifier les logs dans le terminal VS Code
2. Redémarrer VS Code complètement
3. Redémarrer l'ordinateur (en dernier recours)

---

**Note** : L'application fonctionne parfaitement, le problème est uniquement lié à la configuration VS Code/PowerShell.
