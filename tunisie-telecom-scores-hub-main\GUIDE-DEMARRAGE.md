# 🚀 Guide de Démarrage - Tunisie Telecom Scores Hub

## ✅ Nettoyage Lovable AI - TERMINÉ !

Toutes les références à Lovable AI ont été **complètement supprimées** du projet. L'application utilise maintenant le logo officiel de Tunisie Telecom.

## 🔧 Installation et Démarrage

### Prérequis
- Node.js (version 18 ou supérieure)
- npm ou yarn

### Étapes d'installation

#### Option 1 : Installation automatique
```bash
# Exécuter le script d'installation
./install.bat
```

#### Option 2 : Installation manuelle
```bash
# 1. Nettoyer le cache npm
npm cache clean --force

# 2. Supprimer node_modules (si nécessaire)
rm -rf node_modules package-lock.json

# 3. Installer les dépendances
npm install

# 4. Démarrer le serveur de développement
npm run dev
```

#### Option 3 : Utiliser Yarn (recommandé si npm pose problème)
```bash
# Installer yarn globalement
npm install -g yarn

# Installer les dépendances
yarn install

# Démarrer le serveur
yarn dev
```

### 🌐 Accès à l'application

Une fois le serveur démarré, l'application sera accessible sur :
- **Local** : http://localhost:5173/
- **Réseau** : Utiliser `--host` pour exposer sur le réseau

### 🎨 Fonctionnalités

- ✅ **Logo officiel Tunisie Telecom**
- ✅ **Interface moderne avec Tailwind CSS**
- ✅ **Gestion des clients et scores**
- ✅ **Tableau de bord interactif**
- ✅ **Authentification sécurisée**

### 🔧 Dépannage

#### Problème : "Cannot find module '@radix-ui/react-tooltip'"
**Solution** : Les dépendances ne sont pas installées correctement
```bash
npm install @radix-ui/react-tooltip
# ou réinstaller toutes les dépendances
npm install
```

#### Problème : "Cannot find module '@/components/...'"
**Solution** : Configuration des alias TypeScript
- Vérifier que `vite.config.ts` existe avec la configuration des alias
- Vérifier que `tsconfig.json` contient les paths

#### Problème : Serveur ne démarre pas
**Solutions** :
1. Vérifier que Node.js est installé : `node --version`
2. Vérifier que npm fonctionne : `npm --version`
3. Essayer avec yarn : `yarn dev`
4. Redémarrer le terminal/IDE

### 📁 Structure du projet

```
tunisie-telecom-scores-hub-main/
├── src/
│   ├── components/          # Composants React
│   │   ├── ui/             # Composants UI (shadcn/ui)
│   │   ├── Header.tsx      # En-tête avec logo TT
│   │   └── ...
│   ├── pages/              # Pages de l'application
│   └── lib/                # Utilitaires
├── public/
│   └── assets/
│       └── tunisie-telecom-official-logo.svg
├── package.json            # Dépendances
├── vite.config.ts         # Configuration Vite
├── tailwind.config.ts     # Configuration Tailwind
└── tsconfig.json          # Configuration TypeScript
```

### 🎯 Prochaines étapes

1. **Démarrer le serveur** : `npm run dev`
2. **Ouvrir l'application** : http://localhost:5173/
3. **Personnaliser** selon vos besoins
4. **Déployer** en production avec `npm run build`

### 📞 Support

Pour toute question technique, vérifiez :
1. Les logs de la console
2. Les erreurs dans le terminal
3. La configuration des dépendances

---

**Note** : Le nettoyage de Lovable AI est terminé. L'application est maintenant 100% Tunisie Telecom !
