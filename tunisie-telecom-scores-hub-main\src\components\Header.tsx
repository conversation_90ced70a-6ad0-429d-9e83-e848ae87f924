
import { User, LogOut } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface HeaderProps {
  isAuthenticated: boolean;
  onLogin: () => void;
  onLogout: () => void;
  onProfile: () => void;
  userName?: string;
}

const Header = ({ isAuthenticated, onLogin, onLogout, onProfile, userName }: HeaderProps) => {
  return (
    <header className="bg-white shadow-lg border-b-4 border-tunisietelecom-blue">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-16 h-12 flex items-center justify-center">
              <img
                src="/assets/tunisie-telecom-official-logo.svg"
                alt="Tunisie Telecom Logo"
                className="h-12 w-auto object-contain"
              />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-tunisietelecom-darkgray">
                Tunisie Telecom
              </h1>
              <p className="text-sm text-gray-500">Gestion des Scores Clients</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            {isAuthenticated ? (
              <>
                <span className="text-tunisietelecom-darkgray font-medium">
                  Bonjour, {userName}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onProfile}
                  className="border-tunisietelecom-blue text-tunisietelecom-blue hover:bg-tunisietelecom-blue hover:text-white"
                >
                  <User size={16} className="mr-2" />
                  Profil
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onLogout}
                  className="border-red-500 text-red-500 hover:bg-red-500 hover:text-white"
                >
                  <LogOut size={16} className="mr-2" />
                  Déconnexion
                </Button>
              </>
            ) : (
              <Button
                onClick={onLogin}
                className="bg-tunisietelecom-blue hover:bg-tunisietelecom-darkblue text-white"
              >
                Se connecter
              </Button>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
